{"version": 3, "sources": ["../../refractor/lang/perl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = perl\nperl.displayName = 'perl'\nperl.aliases = []\nfunction perl(Prism) {\n  ;(function (Prism) {\n    var brackets =\n      /(?:\\((?:[^()\\\\]|\\\\[\\s\\S])*\\)|\\{(?:[^{}\\\\]|\\\\[\\s\\S])*\\}|\\[(?:[^[\\]\\\\]|\\\\[\\s\\S])*\\]|<(?:[^<>\\\\]|\\\\[\\s\\S])*>)/\n        .source\n    Prism.languages.perl = {\n      comment: [\n        {\n          // POD\n          pattern: /(^\\s*)=\\w[\\s\\S]*?=cut.*/m,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\$])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      // TODO Could be nice to handle Heredoc too.\n      string: [\n        {\n          pattern: RegExp(\n            /\\b(?:q|qq|qw|qx)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // q/.../\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source, // q a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source, // q(...)\n                // q{...}\n                // q[...]\n                // q<...>\n                brackets\n              ].join('|') +\n              ')'\n          ),\n          greedy: true\n        }, // \"...\", `...`\n        {\n          pattern: /(\"|`)(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/,\n          greedy: true\n        }, // '...'\n        // FIXME Multi-line single-quoted strings are not supported as they would break variables containing '\n        {\n          pattern: /'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n          greedy: true\n        }\n      ],\n      regex: [\n        {\n          pattern: RegExp(\n            /\\b(?:m|qr)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // m/.../\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\1)[^\\\\]|\\\\[\\s\\S])*\\1/.source, // m a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/.source, // m(...)\n                // m{...}\n                // m[...]\n                // m<...>\n                brackets\n              ].join('|') +\n              ')' +\n              /[msixpodualngc]*/.source\n          ),\n          greedy: true\n        }, // The lookbehinds prevent -s from breaking\n        {\n          pattern: RegExp(\n            /(^|[^-])\\b(?:s|tr|y)(?![a-zA-Z0-9])\\s*/.source +\n              '(?:' +\n              [\n                // s/.../.../\n                // eslint-disable-next-line regexp/strict\n                /([^a-zA-Z0-9\\s{(\\[<])(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2(?:(?!\\2)[^\\\\]|\\\\[\\s\\S])*\\2/\n                  .source, // s a...a...a\n                // eslint-disable-next-line regexp/strict\n                /([a-zA-Z0-9])(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3(?:(?!\\3)[^\\\\]|\\\\[\\s\\S])*\\3/\n                  .source, // s(...)(...)\n                // s{...}{...}\n                // s[...][...]\n                // s<...><...>\n                // s(...)[...]\n                brackets + /\\s*/.source + brackets\n              ].join('|') +\n              ')' +\n              /[msixpodualngcer]*/.source\n          ),\n          lookbehind: true,\n          greedy: true\n        }, // /.../\n        // The look-ahead tries to prevent two divisions on\n        // the same line from being highlighted as regex.\n        // This does not support multi-line regex.\n        {\n          pattern:\n            /\\/(?:[^\\/\\\\\\r\\n]|\\\\.)*\\/[msixpodualngc]*(?=\\s*(?:$|[\\r\\n,.;})&|\\-+*~<>!?^]|(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|x|xor)\\b))/,\n          greedy: true\n        }\n      ],\n      // FIXME Not sure about the handling of ::, ', and #\n      variable: [\n        // ${^POSTMATCH}\n        /[&*$@%]\\{\\^[A-Z]+\\}/, // $^V\n        /[&*$@%]\\^[A-Z_]/, // ${...}\n        /[&*$@%]#?(?=\\{)/, // $foo\n        /[&*$@%]#?(?:(?:::)*'?(?!\\d)[\\w$]+(?![\\w$]))+(?:::)*/, // $1\n        /[&*$@%]\\d+/, // $_, @_, %!\n        // The negative lookahead prevents from breaking the %= operator\n        /(?!%=)[$@%][!\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^_`{|}~]/\n      ],\n      filehandle: {\n        // <>, <FOO>, _\n        pattern: /<(?![<=])\\S*?>|\\b_\\b/,\n        alias: 'symbol'\n      },\n      'v-string': {\n        // v1.2, 1.2.3\n        pattern: /v\\d+(?:\\.\\d+)*|\\d+(?:\\.\\d+){2,}/,\n        alias: 'string'\n      },\n      function: {\n        pattern: /(\\bsub[ \\t]+)\\w+/,\n        lookbehind: true\n      },\n      keyword:\n        /\\b(?:any|break|continue|default|delete|die|do|else|elsif|eval|for|foreach|given|goto|if|last|local|my|next|our|package|print|redo|require|return|say|state|sub|switch|undef|unless|until|use|when|while)\\b/,\n      number:\n        /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)\\b/,\n      operator:\n        /-[rwxoRWXOezsfdlpSbctugkTBMAC]\\b|\\+[+=]?|-[-=>]?|\\*\\*?=?|\\/\\/?=?|=[=~>]?|~[~=]?|\\|\\|?=?|&&?=?|<(?:=>?|<=?)?|>>?=?|![~=]?|[%^]=?|\\.(?:=|\\.\\.?)?|[\\\\?]|\\bx(?:=|\\b)|\\b(?:and|cmp|eq|ge|gt|le|lt|ne|not|or|xor)\\b/,\n      punctuation: /[{}[\\];(),:]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF,6GACG;AACL,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,SAAS;AAAA,YACP;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA;AAAA,UAEA,QAAQ;AAAA,YACN;AAAA,cACE,SAAS;AAAA,gBACP,qCAAqC,SACnC,QACA;AAAA;AAAA,kBAEE,mDAAmD;AAAA;AAAA;AAAA,kBAEnD,2CAA2C;AAAA;AAAA;AAAA;AAAA;AAAA,kBAI3C;AAAA,gBACF,EAAE,KAAK,GAAG,IACV;AAAA,cACJ;AAAA,cACA,QAAQ;AAAA,YACV;AAAA;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA;AAAA;AAAA,YAEA;AAAA,cACE,SAAS;AAAA,cACT,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,OAAO;AAAA,YACL;AAAA,cACE,SAAS;AAAA,gBACP,+BAA+B,SAC7B,QACA;AAAA;AAAA,kBAEE,mDAAmD;AAAA;AAAA;AAAA,kBAEnD,2CAA2C;AAAA;AAAA;AAAA;AAAA;AAAA,kBAI3C;AAAA,gBACF,EAAE,KAAK,GAAG,IACV,MACA,mBAAmB;AAAA,cACvB;AAAA,cACA,QAAQ;AAAA,YACV;AAAA;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,yCAAyC,SACvC,QACA;AAAA;AAAA;AAAA,kBAGE,8EACG;AAAA;AAAA;AAAA,kBAEH,sEACG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKH,WAAW,MAAM,SAAS;AAAA,gBAC5B,EAAE,KAAK,GAAG,IACV,MACA,qBAAqB;AAAA,cACzB;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA;AAAA;AAAA;AAAA;AAAA,YAIA;AAAA,cACE,SACE;AAAA,cACF,QAAQ;AAAA,YACV;AAAA,UACF;AAAA;AAAA,UAEA,UAAU;AAAA;AAAA,YAER;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA,YACA;AAAA;AAAA;AAAA,YAEA;AAAA,UACF;AAAA,UACA,YAAY;AAAA;AAAA,YAEV,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,YAAY;AAAA;AAAA,YAEV,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,SACE;AAAA,UACF,QACE;AAAA,UACF,UACE;AAAA,UACF,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}