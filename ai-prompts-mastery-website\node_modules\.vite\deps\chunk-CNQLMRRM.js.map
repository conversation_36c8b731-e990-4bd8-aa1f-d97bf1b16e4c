{"version": 3, "sources": ["../../refractor/lang/squirrel.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = squirrel\nsquirrel.displayName = 'squirrel'\nsquirrel.aliases = []\nfunction squirrel(Prism) {\n  Prism.languages.squirrel = Prism.languages.extend('clike', {\n    comment: [\n      Prism.languages.clike['comment'][0],\n      {\n        pattern: /(^|[^\\\\:])(?:\\/\\/|#).*/,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    string: {\n      pattern: /(^|[^\\\\\"'@])(?:@\"(?:[^\"]|\"\")*\"(?!\")|\"(?:[^\\\\\\r\\n\"]|\\\\.)*\")/,\n      lookbehind: true,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(\\b(?:class|enum|extends|instanceof)\\s+)\\w+(?:\\.\\w+)*/,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    keyword:\n      /\\b(?:__FILE__|__LINE__|base|break|case|catch|class|clone|const|constructor|continue|default|delete|else|enum|extends|for|foreach|function|if|in|instanceof|local|null|resume|return|static|switch|this|throw|try|typeof|while|yield)\\b/,\n    number: /\\b(?:0x[0-9a-fA-F]+|\\d+(?:\\.(?:\\d+|[eE][+-]?\\d+))?)\\b/,\n    operator: /\\+\\+|--|<=>|<[-<]|>>>?|&&?|\\|\\|?|[-+*/%!=<>]=?|[~^]|::?/,\n    punctuation: /[(){}\\[\\],;.]/\n  })\n  Prism.languages.insertBefore('squirrel', 'string', {\n    char: {\n      pattern: /(^|[^\\\\\"'])'(?:[^\\\\']|\\\\(?:[xuU][0-9a-fA-F]{0,8}|[\\s\\S]))'/,\n      lookbehind: true,\n      greedy: true\n    }\n  })\n  Prism.languages.insertBefore('squirrel', 'operator', {\n    'attribute-punctuation': {\n      pattern: /<\\/|\\/>/,\n      alias: 'important'\n    },\n    lambda: {\n      pattern: /@(?=\\()/,\n      alias: 'operator'\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB,YAAM,UAAU,WAAW,MAAM,UAAU,OAAO,SAAS;AAAA,QACzD,SAAS;AAAA,UACP,MAAM,UAAU,MAAM,SAAS,EAAE,CAAC;AAAA,UAClC;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,MACf,CAAC;AACD,YAAM,UAAU,aAAa,YAAY,UAAU;AAAA,QACjD,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,YAAY,YAAY;AAAA,QACnD,yBAAyB;AAAA,UACvB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}