{"version": 3, "sources": ["../../highlight.js/lib/languages/capnproto.js"], "sourcesContent": ["/*\nLanguage: Cap’n Proto\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: Cap’n Proto message definition format\nWebsite: https://capnproto.org/capnp-tool.html\nCategory: protocols\n*/\n\n/** @type LanguageFn */\nfunction capnproto(hljs) {\n  return {\n    name: 'Cap’n Proto',\n    aliases: ['capnp'],\n    keywords: {\n      keyword:\n        'struct enum interface union group import using const annotation extends in of on as with from fixed',\n      built_in:\n        'Void Bool Int8 Int16 Int32 Int64 UInt8 UInt16 UInt32 UInt64 Float32 Float64 ' +\n        'Text Data AnyPointer AnyStruct Capability List',\n      literal:\n        'true false'\n    },\n    contains: [\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      hljs.HASH_COMMENT_MODE,\n      {\n        className: 'meta',\n        begin: /@0x[\\w\\d]{16};/,\n        illegal: /\\n/\n      },\n      {\n        className: 'symbol',\n        begin: /@\\d+\\b/\n      },\n      {\n        className: 'class',\n        beginKeywords: 'struct enum',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [hljs.inherit(hljs.TITLE_MODE, {\n          starts: {\n            endsWithParent: true,\n            excludeEnd: true\n          } // hack: eating everything after the first title\n        })]\n      },\n      {\n        className: 'class',\n        beginKeywords: 'interface',\n        end: /\\{/,\n        illegal: /\\n/,\n        contains: [hljs.inherit(hljs.TITLE_MODE, {\n          starts: {\n            endsWithParent: true,\n            excludeEnd: true\n          } // hack: eating everything after the first title\n        })]\n      }\n    ]\n  };\n}\n\nmodule.exports = capnproto;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,UAAU,MAAM;AACvB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,OAAO;AAAA,QACjB,UAAU;AAAA,UACR,SACE;AAAA,UACF,UACE;AAAA,UAEF,SACE;AAAA,QACJ;AAAA,QACA,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,SAAS;AAAA,UACX;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAC,KAAK,QAAQ,KAAK,YAAY;AAAA,cACvC,QAAQ;AAAA,gBACN,gBAAgB;AAAA,gBAChB,YAAY;AAAA,cACd;AAAA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,SAAS;AAAA,YACT,UAAU,CAAC,KAAK,QAAQ,KAAK,YAAY;AAAA,cACvC,QAAQ;AAAA,gBACN,gBAAgB;AAAA,gBAChB,YAAY;AAAA,cACd;AAAA;AAAA,YACF,CAAC,CAAC;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}