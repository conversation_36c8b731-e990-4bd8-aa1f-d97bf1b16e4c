{"version": 3, "sources": ["../../highlight.js/lib/languages/profile.js"], "sourcesContent": ["/*\nLanguage: Python profiler\nDescription: Python profiler results\nAuthor: <PERSON> <<EMAIL>>\n*/\n\nfunction profile(hljs) {\n  return {\n    name: 'Python profiler',\n    contains: [\n      hljs.C_NUMBER_MODE,\n      {\n        begin: '[a-zA-Z_][\\\\da-zA-Z_]+\\\\.[\\\\da-zA-Z_]{1,3}',\n        end: ':',\n        excludeEnd: true\n      },\n      {\n        begin: '(ncalls|tottime|cumtime)',\n        end: '$',\n        keywords: 'ncalls tottime|10 cumtime|10 filename',\n        relevance: 10\n      },\n      {\n        begin: 'function calls',\n        end: '$',\n        contains: [ hljs.C_NUMBER_MODE ],\n        relevance: 10\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: '\\\\(',\n        end: '\\\\)$',\n        excludeBegin: true,\n        excludeEnd: true,\n        relevance: 0\n      }\n    ]\n  };\n}\n\nmodule.exports = profile;\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,aAAS,QAAQ,MAAM;AACrB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,UACd;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,YACV,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAE,KAAK,aAAc;AAAA,YAC/B,WAAW;AAAA,UACb;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,cAAc;AAAA,YACd,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}