{"name": "@rollup/plugin-terser", "version": "0.4.4", "publishConfig": {"access": "public"}, "description": "Generate minified bundle", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/terser"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/terser#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "dist/cjs/index.js", "module": "dist/es/index.js", "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "engines": {"node": ">=14.0.0"}, "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose && pnpm test:ts", "prebuild": "del-cli dist", "prepare": "if [ ! -d 'dist' ]; then pnpm build; fi", "prerelease": "pnpm build", "pretest": "pnpm build", "release": "pnpm --workspace-root plugin:release --pkg $npm_package_name", "test": "ava", "test:ts": "tsc types/index.d.ts test/types.ts --noEmit"}, "files": ["dist", "!dist/**/*.map", "src", "types", "README.md"], "keywords": ["rollup", "plugin", "terser", "minify", "npm", "modules"], "peerDependencies": {"rollup": "^2.0.0||^3.0.0||^4.0.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}}, "dependencies": {"serialize-javascript": "^6.0.1", "smob": "^1.0.0", "terser": "^5.17.4"}, "devDependencies": {"@types/serialize-javascript": "^5.0.2", "rollup": "^4.0.0-24", "typescript": "^4.8.3"}, "types": "./types/index.d.ts"}