{"version": 3, "names": ["_checkPrivateRedeclaration", "obj", "privateCollection", "has", "TypeError"], "sources": ["../../src/helpers/checkPrivateRedeclaration.ts"], "sourcesContent": ["/* @minVersion 7.14.1 */\n\nexport default function _checkPrivateRedeclaration(\n  obj: object,\n  privateCollection: WeakMap<object, unknown> | WeakSet<object>,\n) {\n  if (privateCollection.has(obj)) {\n    throw new TypeError(\n      \"Cannot initialize the same private elements twice on an object\",\n    );\n  }\n}\n"], "mappings": ";;;;;;AAEe,SAASA,0BAA0BA,CAChDC,GAAW,EACXC,iBAA6D,EAC7D;EACA,IAAIA,iBAAiB,CAACC,GAAG,CAACF,GAAG,CAAC,EAAE;IAC9B,MAAM,IAAIG,SAAS,CACjB,gEACF,CAAC;EACH;AACF", "ignoreList": []}