{"version": 3, "sources": ["../../refractor/lang/scala.js"], "sourcesContent": ["'use strict'\nvar refractorJava = require('./java.js')\nmodule.exports = scala\nscala.displayName = 'scala'\nscala.aliases = []\nfunction scala(Prism) {\n  Prism.register(refractorJava)\n  Prism.languages.scala = Prism.languages.extend('java', {\n    'triple-quoted-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /(\"|')(?:\\\\.|(?!\\1)[^\\\\\\r\\n])*\\1/,\n      greedy: true\n    },\n    keyword:\n      /<-|=>|\\b(?:abstract|case|catch|class|def|do|else|extends|final|finally|for|forSome|if|implicit|import|lazy|match|new|null|object|override|package|private|protected|return|sealed|self|super|this|throw|trait|try|type|val|var|while|with|yield)\\b/,\n    number:\n      /\\b0x(?:[\\da-f]*\\.)?[\\da-f]+|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e\\d+)?[dfl]?/i,\n    builtin:\n      /\\b(?:Any|AnyRef|AnyVal|Boolean|Byte|Char|Double|Float|Int|Long|Nothing|Short|String|Unit)\\b/,\n    symbol: /'[^\\d\\s\\\\]\\w*/\n  })\n  Prism.languages.insertBefore('scala', 'triple-quoted-string', {\n    'string-interpolation': {\n      pattern:\n        /\\b[a-z]\\w*(?:\"\"\"(?:[^$]|\\$(?:[^{]|\\{(?:[^{}]|\\{[^{}]*\\})*\\}))*?\"\"\"|\"(?:[^$\"\\r\\n]|\\$(?:[^{]|\\{(?:[^{}]|\\{[^{}]*\\})*\\}))*\")/i,\n      greedy: true,\n      inside: {\n        id: {\n          pattern: /^\\w+/,\n          greedy: true,\n          alias: 'function'\n        },\n        escape: {\n          pattern: /\\\\\\$\"|\\$[$\"]/,\n          greedy: true,\n          alias: 'symbol'\n        },\n        interpolation: {\n          pattern: /\\$(?:\\w+|\\{(?:[^{}]|\\{[^{}]*\\})*\\})/,\n          greedy: true,\n          inside: {\n            punctuation: /^\\$\\{?|\\}$/,\n            expression: {\n              pattern: /[\\s\\S]+/,\n              inside: Prism.languages.scala\n            }\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  delete Prism.languages.scala['class-name']\n  delete Prism.languages.scala['function']\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,gBAAgB;AACpB,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,SAAS,aAAa;AAC5B,YAAM,UAAU,QAAQ,MAAM,UAAU,OAAO,QAAQ;AAAA,QACrD,wBAAwB;AAAA,UACtB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,QACE;AAAA,QACF,SACE;AAAA,QACF,QAAQ;AAAA,MACV,CAAC;AACD,YAAM,UAAU,aAAa,SAAS,wBAAwB;AAAA,QAC5D,wBAAwB;AAAA,UACtB,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,IAAI;AAAA,cACF,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA,eAAe;AAAA,cACb,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,YAAY;AAAA,kBACV,SAAS;AAAA,kBACT,QAAQ,MAAM,UAAU;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO,MAAM,UAAU,MAAM,YAAY;AACzC,aAAO,MAAM,UAAU,MAAM,UAAU;AAAA,IACzC;AAAA;AAAA;", "names": []}