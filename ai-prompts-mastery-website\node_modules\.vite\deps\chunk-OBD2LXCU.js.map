{"version": 3, "sources": ["../../refractor/lang/js-templates.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsTemplates\njsTemplates.displayName = 'jsTemplates'\njsTemplates.aliases = []\nfunction jsTemplates(Prism) {\n  ;(function (Prism) {\n    var templateString = Prism.languages.javascript['template-string'] // see the pattern in prism-javascript.js\n    var templateLiteralPattern = templateString.pattern.source\n    var interpolationObject = templateString.inside['interpolation']\n    var interpolationPunctuationObject =\n      interpolationObject.inside['interpolation-punctuation']\n    var interpolationPattern = interpolationObject.pattern.source\n    /**\n     * Creates a new pattern to match a template string with a special tag.\n     *\n     * This will return `undefined` if there is no grammar with the given language id.\n     *\n     * @param {string} language The language id of the embedded language. E.g. `markdown`.\n     * @param {string} tag The regex pattern to match the tag.\n     * @returns {object | undefined}\n     * @example\n     * createTemplate('css', /\\bcss/.source);\n     */\n    function createTemplate(language, tag) {\n      if (!Prism.languages[language]) {\n        return undefined\n      }\n      return {\n        pattern: RegExp('((?:' + tag + ')\\\\s*)' + templateLiteralPattern),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'template-punctuation': {\n            pattern: /^`|`$/,\n            alias: 'string'\n          },\n          'embedded-code': {\n            pattern: /[\\s\\S]+/,\n            alias: language\n          }\n        }\n      }\n    }\n    Prism.languages.javascript['template-string'] = [\n      // styled-jsx:\n      //   css`a { color: #25F; }`\n      // styled-components:\n      //   styled.h1`color: red;`\n      createTemplate(\n        'css',\n        /\\b(?:styled(?:\\([^)]*\\))?(?:\\s*\\.\\s*\\w+(?:\\([^)]*\\))*)*|css(?:\\s*\\.\\s*(?:global|resolve))?|createGlobalStyle|keyframes)/\n          .source\n      ), // html`<p></p>`\n      // div.innerHTML = `<p></p>`\n      createTemplate('html', /\\bhtml|\\.\\s*(?:inner|outer)HTML\\s*\\+?=/.source), // svg`<path fill=\"#fff\" d=\"M55.37 ...\"/>`\n      createTemplate('svg', /\\bsvg/.source), // md`# h1`, markdown`## h2`\n      createTemplate('markdown', /\\b(?:markdown|md)/.source), // gql`...`, graphql`...`, graphql.experimental`...`\n      createTemplate(\n        'graphql',\n        /\\b(?:gql|graphql(?:\\s*\\.\\s*experimental)?)/.source\n      ), // sql`...`\n      createTemplate('sql', /\\bsql/.source), // vanilla template string\n      templateString\n    ].filter(Boolean)\n    /**\n     * Returns a specific placeholder literal for the given language.\n     *\n     * @param {number} counter\n     * @param {string} language\n     * @returns {string}\n     */\n    function getPlaceholder(counter, language) {\n      return '___' + language.toUpperCase() + '_' + counter + '___'\n    }\n    /**\n     * Returns the tokens of `Prism.tokenize` but also runs the `before-tokenize` and `after-tokenize` hooks.\n     *\n     * @param {string} code\n     * @param {any} grammar\n     * @param {string} language\n     * @returns {(string|Token)[]}\n     */\n    function tokenizeWithHooks(code, grammar, language) {\n      var env = {\n        code: code,\n        grammar: grammar,\n        language: language\n      }\n      Prism.hooks.run('before-tokenize', env)\n      env.tokens = Prism.tokenize(env.code, env.grammar)\n      Prism.hooks.run('after-tokenize', env)\n      return env.tokens\n    }\n    /**\n     * Returns the token of the given JavaScript interpolation expression.\n     *\n     * @param {string} expression The code of the expression. E.g. `\"${42}\"`\n     * @returns {Token}\n     */\n    function tokenizeInterpolationExpression(expression) {\n      var tempGrammar = {}\n      tempGrammar['interpolation-punctuation'] = interpolationPunctuationObject\n      /** @type {Array} */\n      var tokens = Prism.tokenize(expression, tempGrammar)\n      if (tokens.length === 3) {\n        /**\n         * The token array will look like this\n         * [\n         *     [\"interpolation-punctuation\", \"${\"]\n         *     \"...\" // JavaScript expression of the interpolation\n         *     [\"interpolation-punctuation\", \"}\"]\n         * ]\n         */\n        var args = [1, 1]\n        args.push.apply(\n          args,\n          tokenizeWithHooks(tokens[1], Prism.languages.javascript, 'javascript')\n        )\n        tokens.splice.apply(tokens, args)\n      }\n      return new Prism.Token(\n        'interpolation',\n        tokens,\n        interpolationObject.alias,\n        expression\n      )\n    }\n    /**\n     * Tokenizes the given code with support for JavaScript interpolation expressions mixed in.\n     *\n     * This function has 3 phases:\n     *\n     * 1. Replace all JavaScript interpolation expression with a placeholder.\n     *    The placeholder will have the syntax of a identify of the target language.\n     * 2. Tokenize the code with placeholders.\n     * 3. Tokenize the interpolation expressions and re-insert them into the tokenize code.\n     *    The insertion only works if a placeholder hasn't been \"ripped apart\" meaning that the placeholder has been\n     *    tokenized as two tokens by the grammar of the embedded language.\n     *\n     * @param {string} code\n     * @param {object} grammar\n     * @param {string} language\n     * @returns {Token}\n     */\n    function tokenizeEmbedded(code, grammar, language) {\n      // 1. First filter out all interpolations\n      // because they might be escaped, we need a lookbehind, so we use Prism\n      /** @type {(Token|string)[]} */\n      var _tokens = Prism.tokenize(code, {\n        interpolation: {\n          pattern: RegExp(interpolationPattern),\n          lookbehind: true\n        }\n      }) // replace all interpolations with a placeholder which is not in the code already\n      var placeholderCounter = 0\n      /** @type {Object<string, string>} */\n      var placeholderMap = {}\n      var embeddedCode = _tokens\n        .map(function (token) {\n          if (typeof token === 'string') {\n            return token\n          } else {\n            var interpolationExpression = token.content\n            var placeholder\n            while (\n              code.indexOf(\n                (placeholder = getPlaceholder(placeholderCounter++, language))\n              ) !== -1\n            ) {\n              /* noop */\n            }\n            placeholderMap[placeholder] = interpolationExpression\n            return placeholder\n          }\n        })\n        .join('') // 2. Tokenize the embedded code\n      var embeddedTokens = tokenizeWithHooks(embeddedCode, grammar, language) // 3. Re-insert the interpolation\n      var placeholders = Object.keys(placeholderMap)\n      placeholderCounter = 0\n      /**\n       *\n       * @param {(Token|string)[]} tokens\n       * @returns {void}\n       */\n      function walkTokens(tokens) {\n        for (var i = 0; i < tokens.length; i++) {\n          if (placeholderCounter >= placeholders.length) {\n            return\n          }\n          var token = tokens[i]\n          if (typeof token === 'string' || typeof token.content === 'string') {\n            var placeholder = placeholders[placeholderCounter]\n            var s =\n              typeof token === 'string'\n                ? token\n                : /** @type {string} */\n                  token.content\n            var index = s.indexOf(placeholder)\n            if (index !== -1) {\n              ++placeholderCounter\n              var before = s.substring(0, index)\n              var middle = tokenizeInterpolationExpression(\n                placeholderMap[placeholder]\n              )\n              var after = s.substring(index + placeholder.length)\n              var replacement = []\n              if (before) {\n                replacement.push(before)\n              }\n              replacement.push(middle)\n              if (after) {\n                var afterTokens = [after]\n                walkTokens(afterTokens)\n                replacement.push.apply(replacement, afterTokens)\n              }\n              if (typeof token === 'string') {\n                tokens.splice.apply(tokens, [i, 1].concat(replacement))\n                i += replacement.length - 1\n              } else {\n                token.content = replacement\n              }\n            }\n          } else {\n            var content = token.content\n            if (Array.isArray(content)) {\n              walkTokens(content)\n            } else {\n              walkTokens([content])\n            }\n          }\n        }\n      }\n      walkTokens(embeddedTokens)\n      return new Prism.Token(\n        language,\n        embeddedTokens,\n        'language-' + language,\n        code\n      )\n    }\n    /**\n     * The languages for which JS templating will handle tagged template literals.\n     *\n     * JS templating isn't active for only JavaScript but also related languages like TypeScript, JSX, and TSX.\n     */\n    var supportedLanguages = {\n      javascript: true,\n      js: true,\n      typescript: true,\n      ts: true,\n      jsx: true,\n      tsx: true\n    }\n    Prism.hooks.add('after-tokenize', function (env) {\n      if (!(env.language in supportedLanguages)) {\n        return\n      }\n      /**\n       * Finds and tokenizes all template strings with an embedded languages.\n       *\n       * @param {(Token | string)[]} tokens\n       * @returns {void}\n       */\n      function findTemplateStrings(tokens) {\n        for (var i = 0, l = tokens.length; i < l; i++) {\n          var token = tokens[i]\n          if (typeof token === 'string') {\n            continue\n          }\n          var content = token.content\n          if (!Array.isArray(content)) {\n            if (typeof content !== 'string') {\n              findTemplateStrings([content])\n            }\n            continue\n          }\n          if (token.type === 'template-string') {\n            /**\n             * A JavaScript template-string token will look like this:\n             *\n             * [\"template-string\", [\n             *     [\"template-punctuation\", \"`\"],\n             *     (\n             *         An array of \"string\" and \"interpolation\" tokens. This is the simple string case.\n             *         or\n             *         [\"embedded-code\", \"...\"] This is the token containing the embedded code.\n             *                                  It also has an alias which is the language of the embedded code.\n             *     ),\n             *     [\"template-punctuation\", \"`\"]\n             * ]]\n             */\n            var embedded = content[1]\n            if (\n              content.length === 3 &&\n              typeof embedded !== 'string' &&\n              embedded.type === 'embedded-code'\n            ) {\n              // get string content\n              var code = stringContent(embedded)\n              var alias = embedded.alias\n              var language = Array.isArray(alias) ? alias[0] : alias\n              var grammar = Prism.languages[language]\n              if (!grammar) {\n                // the embedded language isn't registered.\n                continue\n              }\n              content[1] = tokenizeEmbedded(code, grammar, language)\n            }\n          } else {\n            findTemplateStrings(content)\n          }\n        }\n      }\n      findTemplateStrings(env.tokens)\n    })\n    /**\n     * Returns the string content of a token or token stream.\n     *\n     * @param {string | Token | (string | Token)[]} value\n     * @returns {string}\n     */\n    function stringContent(value) {\n      if (typeof value === 'string') {\n        return value\n      } else if (Array.isArray(value)) {\n        return value.map(stringContent).join('')\n      } else {\n        return stringContent(value.content)\n      }\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,gBAAY,cAAc;AAC1B,gBAAY,UAAU,CAAC;AACvB,aAAS,YAAY,OAAO;AAC1B;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,iBAAiBA,OAAM,UAAU,WAAW,iBAAiB;AACjE,YAAI,yBAAyB,eAAe,QAAQ;AACpD,YAAI,sBAAsB,eAAe,OAAO,eAAe;AAC/D,YAAI,iCACF,oBAAoB,OAAO,2BAA2B;AACxD,YAAI,uBAAuB,oBAAoB,QAAQ;AAYvD,iBAAS,eAAe,UAAU,KAAK;AACrC,cAAI,CAACA,OAAM,UAAU,QAAQ,GAAG;AAC9B,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,YACL,SAAS,OAAO,SAAS,MAAM,WAAW,sBAAsB;AAAA,YAChE,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,wBAAwB;AAAA,gBACtB,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,iBAAiB;AAAA,gBACf,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,WAAW,iBAAiB,IAAI;AAAA;AAAA;AAAA;AAAA;AAAA,UAK9C;AAAA,YACE;AAAA,YACA,0HACG;AAAA,UACL;AAAA;AAAA;AAAA,UAEA,eAAe,QAAQ,yCAAyC,MAAM;AAAA;AAAA,UACtE,eAAe,OAAO,QAAQ,MAAM;AAAA;AAAA,UACpC,eAAe,YAAY,oBAAoB,MAAM;AAAA;AAAA,UACrD;AAAA,YACE;AAAA,YACA,6CAA6C;AAAA,UAC/C;AAAA;AAAA,UACA,eAAe,OAAO,QAAQ,MAAM;AAAA;AAAA,UACpC;AAAA,QACF,EAAE,OAAO,OAAO;AAQhB,iBAAS,eAAe,SAAS,UAAU;AACzC,iBAAO,QAAQ,SAAS,YAAY,IAAI,MAAM,UAAU;AAAA,QAC1D;AASA,iBAAS,kBAAkB,MAAM,SAAS,UAAU;AAClD,cAAI,MAAM;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,UAAAA,OAAM,MAAM,IAAI,mBAAmB,GAAG;AACtC,cAAI,SAASA,OAAM,SAAS,IAAI,MAAM,IAAI,OAAO;AACjD,UAAAA,OAAM,MAAM,IAAI,kBAAkB,GAAG;AACrC,iBAAO,IAAI;AAAA,QACb;AAOA,iBAAS,gCAAgC,YAAY;AACnD,cAAI,cAAc,CAAC;AACnB,sBAAY,2BAA2B,IAAI;AAE3C,cAAI,SAASA,OAAM,SAAS,YAAY,WAAW;AACnD,cAAI,OAAO,WAAW,GAAG;AASvB,gBAAI,OAAO,CAAC,GAAG,CAAC;AAChB,iBAAK,KAAK;AAAA,cACR;AAAA,cACA,kBAAkB,OAAO,CAAC,GAAGA,OAAM,UAAU,YAAY,YAAY;AAAA,YACvE;AACA,mBAAO,OAAO,MAAM,QAAQ,IAAI;AAAA,UAClC;AACA,iBAAO,IAAIA,OAAM;AAAA,YACf;AAAA,YACA;AAAA,YACA,oBAAoB;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAkBA,iBAAS,iBAAiB,MAAM,SAAS,UAAU;AAIjD,cAAI,UAAUA,OAAM,SAAS,MAAM;AAAA,YACjC,eAAe;AAAA,cACb,SAAS,OAAO,oBAAoB;AAAA,cACpC,YAAY;AAAA,YACd;AAAA,UACF,CAAC;AACD,cAAI,qBAAqB;AAEzB,cAAI,iBAAiB,CAAC;AACtB,cAAI,eAAe,QAChB,IAAI,SAAU,OAAO;AACpB,gBAAI,OAAO,UAAU,UAAU;AAC7B,qBAAO;AAAA,YACT,OAAO;AACL,kBAAI,0BAA0B,MAAM;AACpC,kBAAI;AACJ,qBACE,KAAK;AAAA,gBACF,cAAc,eAAe,sBAAsB,QAAQ;AAAA,cAC9D,MAAM,IACN;AAAA,cAEF;AACA,6BAAe,WAAW,IAAI;AAC9B,qBAAO;AAAA,YACT;AAAA,UACF,CAAC,EACA,KAAK,EAAE;AACV,cAAI,iBAAiB,kBAAkB,cAAc,SAAS,QAAQ;AACtE,cAAI,eAAe,OAAO,KAAK,cAAc;AAC7C,+BAAqB;AAMrB,mBAAS,WAAW,QAAQ;AAC1B,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,kBAAI,sBAAsB,aAAa,QAAQ;AAC7C;AAAA,cACF;AACA,kBAAI,QAAQ,OAAO,CAAC;AACpB,kBAAI,OAAO,UAAU,YAAY,OAAO,MAAM,YAAY,UAAU;AAClE,oBAAI,cAAc,aAAa,kBAAkB;AACjD,oBAAI,IACF,OAAO,UAAU,WACb;AAAA;AAAA,kBAEA,MAAM;AAAA;AACZ,oBAAI,QAAQ,EAAE,QAAQ,WAAW;AACjC,oBAAI,UAAU,IAAI;AAChB,oBAAE;AACF,sBAAI,SAAS,EAAE,UAAU,GAAG,KAAK;AACjC,sBAAI,SAAS;AAAA,oBACX,eAAe,WAAW;AAAA,kBAC5B;AACA,sBAAI,QAAQ,EAAE,UAAU,QAAQ,YAAY,MAAM;AAClD,sBAAI,cAAc,CAAC;AACnB,sBAAI,QAAQ;AACV,gCAAY,KAAK,MAAM;AAAA,kBACzB;AACA,8BAAY,KAAK,MAAM;AACvB,sBAAI,OAAO;AACT,wBAAI,cAAc,CAAC,KAAK;AACxB,+BAAW,WAAW;AACtB,gCAAY,KAAK,MAAM,aAAa,WAAW;AAAA,kBACjD;AACA,sBAAI,OAAO,UAAU,UAAU;AAC7B,2BAAO,OAAO,MAAM,QAAQ,CAAC,GAAG,CAAC,EAAE,OAAO,WAAW,CAAC;AACtD,yBAAK,YAAY,SAAS;AAAA,kBAC5B,OAAO;AACL,0BAAM,UAAU;AAAA,kBAClB;AAAA,gBACF;AAAA,cACF,OAAO;AACL,oBAAI,UAAU,MAAM;AACpB,oBAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,6BAAW,OAAO;AAAA,gBACpB,OAAO;AACL,6BAAW,CAAC,OAAO,CAAC;AAAA,gBACtB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,qBAAW,cAAc;AACzB,iBAAO,IAAIA,OAAM;AAAA,YACf;AAAA,YACA;AAAA,YACA,cAAc;AAAA,YACd;AAAA,UACF;AAAA,QACF;AAMA,YAAI,qBAAqB;AAAA,UACvB,YAAY;AAAA,UACZ,IAAI;AAAA,UACJ,YAAY;AAAA,UACZ,IAAI;AAAA,UACJ,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AACA,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,cAAI,EAAE,IAAI,YAAY,qBAAqB;AACzC;AAAA,UACF;AAOA,mBAAS,oBAAoB,QAAQ;AACnC,qBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,kBAAI,QAAQ,OAAO,CAAC;AACpB,kBAAI,OAAO,UAAU,UAAU;AAC7B;AAAA,cACF;AACA,kBAAI,UAAU,MAAM;AACpB,kBAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,oBAAI,OAAO,YAAY,UAAU;AAC/B,sCAAoB,CAAC,OAAO,CAAC;AAAA,gBAC/B;AACA;AAAA,cACF;AACA,kBAAI,MAAM,SAAS,mBAAmB;AAepC,oBAAI,WAAW,QAAQ,CAAC;AACxB,oBACE,QAAQ,WAAW,KACnB,OAAO,aAAa,YACpB,SAAS,SAAS,iBAClB;AAEA,sBAAI,OAAO,cAAc,QAAQ;AACjC,sBAAI,QAAQ,SAAS;AACrB,sBAAI,WAAW,MAAM,QAAQ,KAAK,IAAI,MAAM,CAAC,IAAI;AACjD,sBAAI,UAAUA,OAAM,UAAU,QAAQ;AACtC,sBAAI,CAAC,SAAS;AAEZ;AAAA,kBACF;AACA,0BAAQ,CAAC,IAAI,iBAAiB,MAAM,SAAS,QAAQ;AAAA,gBACvD;AAAA,cACF,OAAO;AACL,oCAAoB,OAAO;AAAA,cAC7B;AAAA,YACF;AAAA,UACF;AACA,8BAAoB,IAAI,MAAM;AAAA,QAChC,CAAC;AAOD,iBAAS,cAAc,OAAO;AAC5B,cAAI,OAAO,UAAU,UAAU;AAC7B,mBAAO;AAAA,UACT,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,mBAAO,MAAM,IAAI,aAAa,EAAE,KAAK,EAAE;AAAA,UACzC,OAAO;AACL,mBAAO,cAAc,MAAM,OAAO;AAAA,UACpC;AAAA,QACF;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}