{"version": 3, "sources": ["../../highlight.js/lib/languages/pgsql.js"], "sourcesContent": ["/*\nLanguage: PostgreSQL and PL/pgSQL\nAuthor: <PERSON><PERSON> (<EMAIL>)\nWebsite: https://www.postgresql.org/docs/11/sql.html\nDescription:\n    This language incorporates both PostgreSQL SQL dialect and PL/pgSQL language.\n    It is based on PostgreSQL version 11. Some notes:\n    - Text in double-dollar-strings is _always_ interpreted as some programming code. Text\n      in ordinary quotes is _never_ interpreted that way and highlighted just as a string.\n    - There are quite a bit \"special cases\". That's because many keywords are not strictly\n      they are keywords in some contexts and ordinary identifiers in others. Only some\n      of such cases are handled; you still can get some of your identifiers highlighted\n      wrong way.\n    - Function names deliberately are not highlighted. There is no way to tell function\n      call from other constructs, hence we can't highlight _all_ function names. And\n      some names highlighted while others not looks ugly.\n*/\n\nfunction pgsql(hljs) {\n  const COMMENT_MODE = hljs.COMMENT('--', '$');\n  const UNQUOTED_IDENT = '[a-zA-Z_][a-zA-Z_0-9$]*';\n  const DOLLAR_STRING = '\\\\$([a-zA-Z_]?|[a-zA-Z_][a-zA-Z_0-9]*)\\\\$';\n  const LABEL = '<<\\\\s*' + UNQUOTED_IDENT + '\\\\s*>>';\n\n  const SQL_KW =\n    // https://www.postgresql.org/docs/11/static/sql-keywords-appendix.html\n    // https://www.postgresql.org/docs/11/static/sql-commands.html\n    // SQL commands (starting words)\n    'ABORT ALTER ANALYZE BEGIN CALL CHECKPOINT|10 CLOSE CLUSTER COMMENT COMMIT COPY CREATE DEALLOCATE DECLARE ' +\n    'DELETE DISCARD DO DROP END EXECUTE EXPLAIN FETCH GRANT IMPORT INSERT LISTEN LOAD LOCK MOVE NOTIFY ' +\n    'PREPARE REASSIGN|10 REFRESH REINDEX RELEASE RESET REVOKE ROLLBACK SAVEPOINT SECURITY SELECT SET SHOW ' +\n    'START TRUNCATE UNLISTEN|10 UPDATE VACUUM|10 VALUES ' +\n    // SQL commands (others)\n    'AGGREGATE COLLATION CONVERSION|10 DATABASE DEFAULT PRIVILEGES DOMAIN TRIGGER EXTENSION FOREIGN ' +\n    'WRAPPER|10 TABLE FUNCTION GROUP LANGUAGE LARGE OBJECT MATERIALIZED VIEW OPERATOR CLASS ' +\n    'FAMILY POLICY PUBLICATION|10 ROLE RULE SCHEMA SEQUENCE SERVER STATISTICS SUBSCRIPTION SYSTEM ' +\n    'TABLESPACE CONFIGURATION DICTIONARY PARSER TEMPLATE TYPE USER MAPPING PREPARED ACCESS ' +\n    'METHOD CAST AS TRANSFORM TRANSACTION OWNED TO INTO SESSION AUTHORIZATION ' +\n    'INDEX PROCEDURE ASSERTION ' +\n    // additional reserved key words\n    'ALL ANALYSE AND ANY ARRAY ASC ASYMMETRIC|10 BOTH CASE CHECK ' +\n    'COLLATE COLUMN CONCURRENTLY|10 CONSTRAINT CROSS ' +\n    'DEFERRABLE RANGE ' +\n    'DESC DISTINCT ELSE EXCEPT FOR FREEZE|10 FROM FULL HAVING ' +\n    'ILIKE IN INITIALLY INNER INTERSECT IS ISNULL JOIN LATERAL LEADING LIKE LIMIT ' +\n    'NATURAL NOT NOTNULL NULL OFFSET ON ONLY OR ORDER OUTER OVERLAPS PLACING PRIMARY ' +\n    'REFERENCES RETURNING SIMILAR SOME SYMMETRIC TABLESAMPLE THEN ' +\n    'TRAILING UNION UNIQUE USING VARIADIC|10 VERBOSE WHEN WHERE WINDOW WITH ' +\n    // some of non-reserved (which are used in clauses or as PL/pgSQL keyword)\n    'BY RETURNS INOUT OUT SETOF|10 IF STRICT CURRENT CONTINUE OWNER LOCATION OVER PARTITION WITHIN ' +\n    'BETWEEN ESCAPE EXTERNAL INVOKER DEFINER WORK RENAME VERSION CONNECTION CONNECT ' +\n    'TABLES TEMP TEMPORARY FUNCTIONS SEQUENCES TYPES SCHEMAS OPTION CASCADE RESTRICT ADD ADMIN ' +\n    'EXISTS VALID VALIDATE ENABLE DISABLE REPLICA|10 ALWAYS PASSING COLUMNS PATH ' +\n    'REF VALUE OVERRIDING IMMUTABLE STABLE VOLATILE BEFORE AFTER EACH ROW PROCEDURAL ' +\n    'ROUTINE NO HANDLER VALIDATOR OPTIONS STORAGE OIDS|10 WITHOUT INHERIT DEPENDS CALLED ' +\n    'INPUT LEAKPROOF|10 COST ROWS NOWAIT SEARCH UNTIL ENCRYPTED|10 PASSWORD CONFLICT|10 ' +\n    'INSTEAD INHERITS CHARACTERISTICS WRITE CURSOR ALSO STATEMENT SHARE EXCLUSIVE INLINE ' +\n    'ISOLATION REPEATABLE READ COMMITTED SERIALIZABLE UNCOMMITTED LOCAL GLOBAL SQL PROCEDURES ' +\n    'RECURSIVE SNAPSHOT ROLLUP CUBE TRUSTED|10 INCLUDE FOLLOWING PRECEDING UNBOUNDED RANGE GROUPS ' +\n    'UNENCRYPTED|10 SYSID FORMAT DELIMITER HEADER QUOTE ENCODING FILTER OFF ' +\n    // some parameters of VACUUM/ANALYZE/EXPLAIN\n    'FORCE_QUOTE FORCE_NOT_NULL FORCE_NULL COSTS BUFFERS TIMING SUMMARY DISABLE_PAGE_SKIPPING ' +\n    //\n    'RESTART CYCLE GENERATED IDENTITY DEFERRED IMMEDIATE LEVEL LOGGED UNLOGGED ' +\n    'OF NOTHING NONE EXCLUDE ATTRIBUTE ' +\n    // from GRANT (not keywords actually)\n    'USAGE ROUTINES ' +\n    // actually literals, but look better this way (due to IS TRUE, IS FALSE, ISNULL etc)\n    'TRUE FALSE NAN INFINITY ';\n\n  const ROLE_ATTRS = // only those not in keywrods already\n    'SUPERUSER NOSUPERUSER CREATEDB NOCREATEDB CREATEROLE NOCREATEROLE INHERIT NOINHERIT ' +\n    'LOGIN NOLOGIN REPLICATION NOREPLICATION BYPASSRLS NOBYPASSRLS ';\n\n  const PLPGSQL_KW =\n    'ALIAS BEGIN CONSTANT DECLARE END EXCEPTION RETURN PERFORM|10 RAISE GET DIAGNOSTICS ' +\n    'STACKED|10 FOREACH LOOP ELSIF EXIT WHILE REVERSE SLICE DEBUG LOG INFO NOTICE WARNING ASSERT ' +\n    'OPEN ';\n\n  const TYPES =\n    // https://www.postgresql.org/docs/11/static/datatype.html\n    'BIGINT INT8 BIGSERIAL SERIAL8 BIT VARYING VARBIT BOOLEAN BOOL BOX BYTEA CHARACTER CHAR VARCHAR ' +\n    'CIDR CIRCLE DATE DOUBLE PRECISION FLOAT8 FLOAT INET INTEGER INT INT4 INTERVAL JSON JSONB LINE LSEG|10 ' +\n    'MACADDR MACADDR8 MONEY NUMERIC DEC DECIMAL PATH POINT POLYGON REAL FLOAT4 SMALLINT INT2 ' +\n    'SMALLSERIAL|10 SERIAL2|10 SERIAL|10 SERIAL4|10 TEXT TIME ZONE TIMETZ|10 TIMESTAMP TIMESTAMPTZ|10 TSQUERY|10 TSVECTOR|10 ' +\n    'TXID_SNAPSHOT|10 UUID XML NATIONAL NCHAR ' +\n    'INT4RANGE|10 INT8RANGE|10 NUMRANGE|10 TSRANGE|10 TSTZRANGE|10 DATERANGE|10 ' +\n    // pseudotypes\n    'ANYELEMENT ANYARRAY ANYNONARRAY ANYENUM ANYRANGE CSTRING INTERNAL ' +\n    'RECORD PG_DDL_COMMAND VOID UNKNOWN OPAQUE REFCURSOR ' +\n    // spec. type\n    'NAME ' +\n    // OID-types\n    'OID REGPROC|10 REGPROCEDURE|10 REGOPER|10 REGOPERATOR|10 REGCLASS|10 REGTYPE|10 REGROLE|10 ' +\n    'REGNAMESPACE|10 REGCONFIG|10 REGDICTIONARY|10 ';// +\n\n  const TYPES_RE =\n    TYPES.trim()\n      .split(' ')\n      .map(function(val) { return val.split('|')[0]; })\n      .join('|');\n\n  const SQL_BI =\n    'CURRENT_TIME CURRENT_TIMESTAMP CURRENT_USER CURRENT_CATALOG|10 CURRENT_DATE LOCALTIME LOCALTIMESTAMP ' +\n    'CURRENT_ROLE|10 CURRENT_SCHEMA|10 SESSION_USER PUBLIC ';\n\n  const PLPGSQL_BI =\n    'FOUND NEW OLD TG_NAME|10 TG_WHEN|10 TG_LEVEL|10 TG_OP|10 TG_RELID|10 TG_RELNAME|10 ' +\n    'TG_TABLE_NAME|10 TG_TABLE_SCHEMA|10 TG_NARGS|10 TG_ARGV|10 TG_EVENT|10 TG_TAG|10 ' +\n    // get diagnostics\n    'ROW_COUNT RESULT_OID|10 PG_CONTEXT|10 RETURNED_SQLSTATE COLUMN_NAME CONSTRAINT_NAME ' +\n    'PG_DATATYPE_NAME|10 MESSAGE_TEXT TABLE_NAME SCHEMA_NAME PG_EXCEPTION_DETAIL|10 ' +\n    'PG_EXCEPTION_HINT|10 PG_EXCEPTION_CONTEXT|10 ';\n\n  const PLPGSQL_EXCEPTIONS =\n    // exceptions https://www.postgresql.org/docs/current/static/errcodes-appendix.html\n    'SQLSTATE SQLERRM|10 ' +\n    'SUCCESSFUL_COMPLETION WARNING DYNAMIC_RESULT_SETS_RETURNED IMPLICIT_ZERO_BIT_PADDING ' +\n    'NULL_VALUE_ELIMINATED_IN_SET_FUNCTION PRIVILEGE_NOT_GRANTED PRIVILEGE_NOT_REVOKED ' +\n    'STRING_DATA_RIGHT_TRUNCATION DEPRECATED_FEATURE NO_DATA NO_ADDITIONAL_DYNAMIC_RESULT_SETS_RETURNED ' +\n    'SQL_STATEMENT_NOT_YET_COMPLETE CONNECTION_EXCEPTION CONNECTION_DOES_NOT_EXIST CONNECTION_FAILURE ' +\n    'SQLCLIENT_UNABLE_TO_ESTABLISH_SQLCONNECTION SQLSERVER_REJECTED_ESTABLISHMENT_OF_SQLCONNECTION ' +\n    'TRANSACTION_RESOLUTION_UNKNOWN PROTOCOL_VIOLATION TRIGGERED_ACTION_EXCEPTION FEATURE_NOT_SUPPORTED ' +\n    'INVALID_TRANSACTION_INITIATION LOCATOR_EXCEPTION INVALID_LOCATOR_SPECIFICATION INVALID_GRANTOR ' +\n    'INVALID_GRANT_OPERATION INVALID_ROLE_SPECIFICATION DIAGNOSTICS_EXCEPTION ' +\n    'STACKED_DIAGNOSTICS_ACCESSED_WITHOUT_ACTIVE_HANDLER CASE_NOT_FOUND CARDINALITY_VIOLATION ' +\n    'DATA_EXCEPTION ARRAY_SUBSCRIPT_ERROR CHARACTER_NOT_IN_REPERTOIRE DATETIME_FIELD_OVERFLOW ' +\n    'DIVISION_BY_ZERO ERROR_IN_ASSIGNMENT ESCAPE_CHARACTER_CONFLICT INDICATOR_OVERFLOW ' +\n    'INTERVAL_FIELD_OVERFLOW INVALID_ARGUMENT_FOR_LOGARITHM INVALID_ARGUMENT_FOR_NTILE_FUNCTION ' +\n    'INVALID_ARGUMENT_FOR_NTH_VALUE_FUNCTION INVALID_ARGUMENT_FOR_POWER_FUNCTION ' +\n    'INVALID_ARGUMENT_FOR_WIDTH_BUCKET_FUNCTION INVALID_CHARACTER_VALUE_FOR_CAST ' +\n    'INVALID_DATETIME_FORMAT INVALID_ESCAPE_CHARACTER INVALID_ESCAPE_OCTET INVALID_ESCAPE_SEQUENCE ' +\n    'NONSTANDARD_USE_OF_ESCAPE_CHARACTER INVALID_INDICATOR_PARAMETER_VALUE INVALID_PARAMETER_VALUE ' +\n    'INVALID_REGULAR_EXPRESSION INVALID_ROW_COUNT_IN_LIMIT_CLAUSE ' +\n    'INVALID_ROW_COUNT_IN_RESULT_OFFSET_CLAUSE INVALID_TABLESAMPLE_ARGUMENT INVALID_TABLESAMPLE_REPEAT ' +\n    'INVALID_TIME_ZONE_DISPLACEMENT_VALUE INVALID_USE_OF_ESCAPE_CHARACTER MOST_SPECIFIC_TYPE_MISMATCH ' +\n    'NULL_VALUE_NOT_ALLOWED NULL_VALUE_NO_INDICATOR_PARAMETER NUMERIC_VALUE_OUT_OF_RANGE ' +\n    'SEQUENCE_GENERATOR_LIMIT_EXCEEDED STRING_DATA_LENGTH_MISMATCH STRING_DATA_RIGHT_TRUNCATION ' +\n    'SUBSTRING_ERROR TRIM_ERROR UNTERMINATED_C_STRING ZERO_LENGTH_CHARACTER_STRING ' +\n    'FLOATING_POINT_EXCEPTION INVALID_TEXT_REPRESENTATION INVALID_BINARY_REPRESENTATION ' +\n    'BAD_COPY_FILE_FORMAT UNTRANSLATABLE_CHARACTER NOT_AN_XML_DOCUMENT INVALID_XML_DOCUMENT ' +\n    'INVALID_XML_CONTENT INVALID_XML_COMMENT INVALID_XML_PROCESSING_INSTRUCTION ' +\n    'INTEGRITY_CONSTRAINT_VIOLATION RESTRICT_VIOLATION NOT_NULL_VIOLATION FOREIGN_KEY_VIOLATION ' +\n    'UNIQUE_VIOLATION CHECK_VIOLATION EXCLUSION_VIOLATION INVALID_CURSOR_STATE ' +\n    'INVALID_TRANSACTION_STATE ACTIVE_SQL_TRANSACTION BRANCH_TRANSACTION_ALREADY_ACTIVE ' +\n    'HELD_CURSOR_REQUIRES_SAME_ISOLATION_LEVEL INAPPROPRIATE_ACCESS_MODE_FOR_BRANCH_TRANSACTION ' +\n    'INAPPROPRIATE_ISOLATION_LEVEL_FOR_BRANCH_TRANSACTION ' +\n    'NO_ACTIVE_SQL_TRANSACTION_FOR_BRANCH_TRANSACTION READ_ONLY_SQL_TRANSACTION ' +\n    'SCHEMA_AND_DATA_STATEMENT_MIXING_NOT_SUPPORTED NO_ACTIVE_SQL_TRANSACTION ' +\n    'IN_FAILED_SQL_TRANSACTION IDLE_IN_TRANSACTION_SESSION_TIMEOUT INVALID_SQL_STATEMENT_NAME ' +\n    'TRIGGERED_DATA_CHANGE_VIOLATION INVALID_AUTHORIZATION_SPECIFICATION INVALID_PASSWORD ' +\n    'DEPENDENT_PRIVILEGE_DESCRIPTORS_STILL_EXIST DEPENDENT_OBJECTS_STILL_EXIST ' +\n    'INVALID_TRANSACTION_TERMINATION SQL_ROUTINE_EXCEPTION FUNCTION_EXECUTED_NO_RETURN_STATEMENT ' +\n    'MODIFYING_SQL_DATA_NOT_PERMITTED PROHIBITED_SQL_STATEMENT_ATTEMPTED ' +\n    'READING_SQL_DATA_NOT_PERMITTED INVALID_CURSOR_NAME EXTERNAL_ROUTINE_EXCEPTION ' +\n    'CONTAINING_SQL_NOT_PERMITTED MODIFYING_SQL_DATA_NOT_PERMITTED ' +\n    'PROHIBITED_SQL_STATEMENT_ATTEMPTED READING_SQL_DATA_NOT_PERMITTED ' +\n    'EXTERNAL_ROUTINE_INVOCATION_EXCEPTION INVALID_SQLSTATE_RETURNED NULL_VALUE_NOT_ALLOWED ' +\n    'TRIGGER_PROTOCOL_VIOLATED SRF_PROTOCOL_VIOLATED EVENT_TRIGGER_PROTOCOL_VIOLATED ' +\n    'SAVEPOINT_EXCEPTION INVALID_SAVEPOINT_SPECIFICATION INVALID_CATALOG_NAME ' +\n    'INVALID_SCHEMA_NAME TRANSACTION_ROLLBACK TRANSACTION_INTEGRITY_CONSTRAINT_VIOLATION ' +\n    'SERIALIZATION_FAILURE STATEMENT_COMPLETION_UNKNOWN DEADLOCK_DETECTED ' +\n    'SYNTAX_ERROR_OR_ACCESS_RULE_VIOLATION SYNTAX_ERROR INSUFFICIENT_PRIVILEGE CANNOT_COERCE ' +\n    'GROUPING_ERROR WINDOWING_ERROR INVALID_RECURSION INVALID_FOREIGN_KEY INVALID_NAME ' +\n    'NAME_TOO_LONG RESERVED_NAME DATATYPE_MISMATCH INDETERMINATE_DATATYPE COLLATION_MISMATCH ' +\n    'INDETERMINATE_COLLATION WRONG_OBJECT_TYPE GENERATED_ALWAYS UNDEFINED_COLUMN ' +\n    'UNDEFINED_FUNCTION UNDEFINED_TABLE UNDEFINED_PARAMETER UNDEFINED_OBJECT ' +\n    'DUPLICATE_COLUMN DUPLICATE_CURSOR DUPLICATE_DATABASE DUPLICATE_FUNCTION ' +\n    'DUPLICATE_PREPARED_STATEMENT DUPLICATE_SCHEMA DUPLICATE_TABLE DUPLICATE_ALIAS ' +\n    'DUPLICATE_OBJECT AMBIGUOUS_COLUMN AMBIGUOUS_FUNCTION AMBIGUOUS_PARAMETER AMBIGUOUS_ALIAS ' +\n    'INVALID_COLUMN_REFERENCE INVALID_COLUMN_DEFINITION INVALID_CURSOR_DEFINITION ' +\n    'INVALID_DATABASE_DEFINITION INVALID_FUNCTION_DEFINITION ' +\n    'INVALID_PREPARED_STATEMENT_DEFINITION INVALID_SCHEMA_DEFINITION INVALID_TABLE_DEFINITION ' +\n    'INVALID_OBJECT_DEFINITION WITH_CHECK_OPTION_VIOLATION INSUFFICIENT_RESOURCES DISK_FULL ' +\n    'OUT_OF_MEMORY TOO_MANY_CONNECTIONS CONFIGURATION_LIMIT_EXCEEDED PROGRAM_LIMIT_EXCEEDED ' +\n    'STATEMENT_TOO_COMPLEX TOO_MANY_COLUMNS TOO_MANY_ARGUMENTS OBJECT_NOT_IN_PREREQUISITE_STATE ' +\n    'OBJECT_IN_USE CANT_CHANGE_RUNTIME_PARAM LOCK_NOT_AVAILABLE OPERATOR_INTERVENTION ' +\n    'QUERY_CANCELED ADMIN_SHUTDOWN CRASH_SHUTDOWN CANNOT_CONNECT_NOW DATABASE_DROPPED ' +\n    'SYSTEM_ERROR IO_ERROR UNDEFINED_FILE DUPLICATE_FILE SNAPSHOT_TOO_OLD CONFIG_FILE_ERROR ' +\n    'LOCK_FILE_EXISTS FDW_ERROR FDW_COLUMN_NAME_NOT_FOUND FDW_DYNAMIC_PARAMETER_VALUE_NEEDED ' +\n    'FDW_FUNCTION_SEQUENCE_ERROR FDW_INCONSISTENT_DESCRIPTOR_INFORMATION ' +\n    'FDW_INVALID_ATTRIBUTE_VALUE FDW_INVALID_COLUMN_NAME FDW_INVALID_COLUMN_NUMBER ' +\n    'FDW_INVALID_DATA_TYPE FDW_INVALID_DATA_TYPE_DESCRIPTORS ' +\n    'FDW_INVALID_DESCRIPTOR_FIELD_IDENTIFIER FDW_INVALID_HANDLE FDW_INVALID_OPTION_INDEX ' +\n    'FDW_INVALID_OPTION_NAME FDW_INVALID_STRING_LENGTH_OR_BUFFER_LENGTH ' +\n    'FDW_INVALID_STRING_FORMAT FDW_INVALID_USE_OF_NULL_POINTER FDW_TOO_MANY_HANDLES ' +\n    'FDW_OUT_OF_MEMORY FDW_NO_SCHEMAS FDW_OPTION_NAME_NOT_FOUND FDW_REPLY_HANDLE ' +\n    'FDW_SCHEMA_NOT_FOUND FDW_TABLE_NOT_FOUND FDW_UNABLE_TO_CREATE_EXECUTION ' +\n    'FDW_UNABLE_TO_CREATE_REPLY FDW_UNABLE_TO_ESTABLISH_CONNECTION PLPGSQL_ERROR ' +\n    'RAISE_EXCEPTION NO_DATA_FOUND TOO_MANY_ROWS ASSERT_FAILURE INTERNAL_ERROR DATA_CORRUPTED ' +\n    'INDEX_CORRUPTED ';\n\n  const FUNCTIONS =\n    // https://www.postgresql.org/docs/11/static/functions-aggregate.html\n    'ARRAY_AGG AVG BIT_AND BIT_OR BOOL_AND BOOL_OR COUNT EVERY JSON_AGG JSONB_AGG JSON_OBJECT_AGG ' +\n    'JSONB_OBJECT_AGG MAX MIN MODE STRING_AGG SUM XMLAGG ' +\n    'CORR COVAR_POP COVAR_SAMP REGR_AVGX REGR_AVGY REGR_COUNT REGR_INTERCEPT REGR_R2 REGR_SLOPE ' +\n    'REGR_SXX REGR_SXY REGR_SYY STDDEV STDDEV_POP STDDEV_SAMP VARIANCE VAR_POP VAR_SAMP ' +\n    'PERCENTILE_CONT PERCENTILE_DISC ' +\n    // https://www.postgresql.org/docs/11/static/functions-window.html\n    'ROW_NUMBER RANK DENSE_RANK PERCENT_RANK CUME_DIST NTILE LAG LEAD FIRST_VALUE LAST_VALUE NTH_VALUE ' +\n    // https://www.postgresql.org/docs/11/static/functions-comparison.html\n    'NUM_NONNULLS NUM_NULLS ' +\n    // https://www.postgresql.org/docs/11/static/functions-math.html\n    'ABS CBRT CEIL CEILING DEGREES DIV EXP FLOOR LN LOG MOD PI POWER RADIANS ROUND SCALE SIGN SQRT ' +\n    'TRUNC WIDTH_BUCKET ' +\n    'RANDOM SETSEED ' +\n    'ACOS ACOSD ASIN ASIND ATAN ATAND ATAN2 ATAN2D COS COSD COT COTD SIN SIND TAN TAND ' +\n    // https://www.postgresql.org/docs/11/static/functions-string.html\n    'BIT_LENGTH CHAR_LENGTH CHARACTER_LENGTH LOWER OCTET_LENGTH OVERLAY POSITION SUBSTRING TREAT TRIM UPPER ' +\n    'ASCII BTRIM CHR CONCAT CONCAT_WS CONVERT CONVERT_FROM CONVERT_TO DECODE ENCODE INITCAP ' +\n    'LEFT LENGTH LPAD LTRIM MD5 PARSE_IDENT PG_CLIENT_ENCODING QUOTE_IDENT|10 QUOTE_LITERAL|10 ' +\n    'QUOTE_NULLABLE|10 REGEXP_MATCH REGEXP_MATCHES REGEXP_REPLACE REGEXP_SPLIT_TO_ARRAY ' +\n    'REGEXP_SPLIT_TO_TABLE REPEAT REPLACE REVERSE RIGHT RPAD RTRIM SPLIT_PART STRPOS SUBSTR ' +\n    'TO_ASCII TO_HEX TRANSLATE ' +\n    // https://www.postgresql.org/docs/11/static/functions-binarystring.html\n    'OCTET_LENGTH GET_BIT GET_BYTE SET_BIT SET_BYTE ' +\n    // https://www.postgresql.org/docs/11/static/functions-formatting.html\n    'TO_CHAR TO_DATE TO_NUMBER TO_TIMESTAMP ' +\n    // https://www.postgresql.org/docs/11/static/functions-datetime.html\n    'AGE CLOCK_TIMESTAMP|10 DATE_PART DATE_TRUNC ISFINITE JUSTIFY_DAYS JUSTIFY_HOURS JUSTIFY_INTERVAL ' +\n    'MAKE_DATE MAKE_INTERVAL|10 MAKE_TIME MAKE_TIMESTAMP|10 MAKE_TIMESTAMPTZ|10 NOW STATEMENT_TIMESTAMP|10 ' +\n    'TIMEOFDAY TRANSACTION_TIMESTAMP|10 ' +\n    // https://www.postgresql.org/docs/11/static/functions-enum.html\n    'ENUM_FIRST ENUM_LAST ENUM_RANGE ' +\n    // https://www.postgresql.org/docs/11/static/functions-geometry.html\n    'AREA CENTER DIAMETER HEIGHT ISCLOSED ISOPEN NPOINTS PCLOSE POPEN RADIUS WIDTH ' +\n    'BOX BOUND_BOX CIRCLE LINE LSEG PATH POLYGON ' +\n    // https://www.postgresql.org/docs/11/static/functions-net.html\n    'ABBREV BROADCAST HOST HOSTMASK MASKLEN NETMASK NETWORK SET_MASKLEN TEXT INET_SAME_FAMILY ' +\n    'INET_MERGE MACADDR8_SET7BIT ' +\n    // https://www.postgresql.org/docs/11/static/functions-textsearch.html\n    'ARRAY_TO_TSVECTOR GET_CURRENT_TS_CONFIG NUMNODE PLAINTO_TSQUERY PHRASETO_TSQUERY WEBSEARCH_TO_TSQUERY ' +\n    'QUERYTREE SETWEIGHT STRIP TO_TSQUERY TO_TSVECTOR JSON_TO_TSVECTOR JSONB_TO_TSVECTOR TS_DELETE ' +\n    'TS_FILTER TS_HEADLINE TS_RANK TS_RANK_CD TS_REWRITE TSQUERY_PHRASE TSVECTOR_TO_ARRAY ' +\n    'TSVECTOR_UPDATE_TRIGGER TSVECTOR_UPDATE_TRIGGER_COLUMN ' +\n    // https://www.postgresql.org/docs/11/static/functions-xml.html\n    'XMLCOMMENT XMLCONCAT XMLELEMENT XMLFOREST XMLPI XMLROOT ' +\n    'XMLEXISTS XML_IS_WELL_FORMED XML_IS_WELL_FORMED_DOCUMENT XML_IS_WELL_FORMED_CONTENT ' +\n    'XPATH XPATH_EXISTS XMLTABLE XMLNAMESPACES ' +\n    'TABLE_TO_XML TABLE_TO_XMLSCHEMA TABLE_TO_XML_AND_XMLSCHEMA ' +\n    'QUERY_TO_XML QUERY_TO_XMLSCHEMA QUERY_TO_XML_AND_XMLSCHEMA ' +\n    'CURSOR_TO_XML CURSOR_TO_XMLSCHEMA ' +\n    'SCHEMA_TO_XML SCHEMA_TO_XMLSCHEMA SCHEMA_TO_XML_AND_XMLSCHEMA ' +\n    'DATABASE_TO_XML DATABASE_TO_XMLSCHEMA DATABASE_TO_XML_AND_XMLSCHEMA ' +\n    'XMLATTRIBUTES ' +\n    // https://www.postgresql.org/docs/11/static/functions-json.html\n    'TO_JSON TO_JSONB ARRAY_TO_JSON ROW_TO_JSON JSON_BUILD_ARRAY JSONB_BUILD_ARRAY JSON_BUILD_OBJECT ' +\n    'JSONB_BUILD_OBJECT JSON_OBJECT JSONB_OBJECT JSON_ARRAY_LENGTH JSONB_ARRAY_LENGTH JSON_EACH ' +\n    'JSONB_EACH JSON_EACH_TEXT JSONB_EACH_TEXT JSON_EXTRACT_PATH JSONB_EXTRACT_PATH ' +\n    'JSON_OBJECT_KEYS JSONB_OBJECT_KEYS JSON_POPULATE_RECORD JSONB_POPULATE_RECORD JSON_POPULATE_RECORDSET ' +\n    'JSONB_POPULATE_RECORDSET JSON_ARRAY_ELEMENTS JSONB_ARRAY_ELEMENTS JSON_ARRAY_ELEMENTS_TEXT ' +\n    'JSONB_ARRAY_ELEMENTS_TEXT JSON_TYPEOF JSONB_TYPEOF JSON_TO_RECORD JSONB_TO_RECORD JSON_TO_RECORDSET ' +\n    'JSONB_TO_RECORDSET JSON_STRIP_NULLS JSONB_STRIP_NULLS JSONB_SET JSONB_INSERT JSONB_PRETTY ' +\n    // https://www.postgresql.org/docs/11/static/functions-sequence.html\n    'CURRVAL LASTVAL NEXTVAL SETVAL ' +\n    // https://www.postgresql.org/docs/11/static/functions-conditional.html\n    'COALESCE NULLIF GREATEST LEAST ' +\n    // https://www.postgresql.org/docs/11/static/functions-array.html\n    'ARRAY_APPEND ARRAY_CAT ARRAY_NDIMS ARRAY_DIMS ARRAY_FILL ARRAY_LENGTH ARRAY_LOWER ARRAY_POSITION ' +\n    'ARRAY_POSITIONS ARRAY_PREPEND ARRAY_REMOVE ARRAY_REPLACE ARRAY_TO_STRING ARRAY_UPPER CARDINALITY ' +\n    'STRING_TO_ARRAY UNNEST ' +\n    // https://www.postgresql.org/docs/11/static/functions-range.html\n    'ISEMPTY LOWER_INC UPPER_INC LOWER_INF UPPER_INF RANGE_MERGE ' +\n    // https://www.postgresql.org/docs/11/static/functions-srf.html\n    'GENERATE_SERIES GENERATE_SUBSCRIPTS ' +\n    // https://www.postgresql.org/docs/11/static/functions-info.html\n    'CURRENT_DATABASE CURRENT_QUERY CURRENT_SCHEMA|10 CURRENT_SCHEMAS|10 INET_CLIENT_ADDR INET_CLIENT_PORT ' +\n    'INET_SERVER_ADDR INET_SERVER_PORT ROW_SECURITY_ACTIVE FORMAT_TYPE ' +\n    'TO_REGCLASS TO_REGPROC TO_REGPROCEDURE TO_REGOPER TO_REGOPERATOR TO_REGTYPE TO_REGNAMESPACE TO_REGROLE ' +\n    'COL_DESCRIPTION OBJ_DESCRIPTION SHOBJ_DESCRIPTION ' +\n    'TXID_CURRENT TXID_CURRENT_IF_ASSIGNED TXID_CURRENT_SNAPSHOT TXID_SNAPSHOT_XIP TXID_SNAPSHOT_XMAX ' +\n    'TXID_SNAPSHOT_XMIN TXID_VISIBLE_IN_SNAPSHOT TXID_STATUS ' +\n    // https://www.postgresql.org/docs/11/static/functions-admin.html\n    'CURRENT_SETTING SET_CONFIG BRIN_SUMMARIZE_NEW_VALUES BRIN_SUMMARIZE_RANGE BRIN_DESUMMARIZE_RANGE ' +\n    'GIN_CLEAN_PENDING_LIST ' +\n    // https://www.postgresql.org/docs/11/static/functions-trigger.html\n    'SUPPRESS_REDUNDANT_UPDATES_TRIGGER ' +\n    // ihttps://www.postgresql.org/docs/devel/static/lo-funcs.html\n    'LO_FROM_BYTEA LO_PUT LO_GET LO_CREAT LO_CREATE LO_UNLINK LO_IMPORT LO_EXPORT LOREAD LOWRITE ' +\n    //\n    'GROUPING CAST ';\n\n  const FUNCTIONS_RE =\n      FUNCTIONS.trim()\n        .split(' ')\n        .map(function(val) { return val.split('|')[0]; })\n        .join('|');\n\n  return {\n    name: 'PostgreSQL',\n    aliases: [\n      'postgres',\n      'postgresql'\n    ],\n    case_insensitive: true,\n    keywords: {\n      keyword:\n            SQL_KW + PLPGSQL_KW + ROLE_ATTRS,\n      built_in:\n            SQL_BI + PLPGSQL_BI + PLPGSQL_EXCEPTIONS\n    },\n    // Forbid some cunstructs from other languages to improve autodetect. In fact\n    // \"[a-z]:\" is legal (as part of array slice), but improbabal.\n    illegal: /:==|\\W\\s*\\(\\*|(^|\\s)\\$[a-z]|\\{\\{|[a-z]:\\s*$|\\.\\.\\.|TO:|DO:/,\n    contains: [\n      // special handling of some words, which are reserved only in some contexts\n      {\n        className: 'keyword',\n        variants: [\n          {\n            begin: /\\bTEXT\\s*SEARCH\\b/\n          },\n          {\n            begin: /\\b(PRIMARY|FOREIGN|FOR(\\s+NO)?)\\s+KEY\\b/\n          },\n          {\n            begin: /\\bPARALLEL\\s+(UNSAFE|RESTRICTED|SAFE)\\b/\n          },\n          {\n            begin: /\\bSTORAGE\\s+(PLAIN|EXTERNAL|EXTENDED|MAIN)\\b/\n          },\n          {\n            begin: /\\bMATCH\\s+(FULL|PARTIAL|SIMPLE)\\b/\n          },\n          {\n            begin: /\\bNULLS\\s+(FIRST|LAST)\\b/\n          },\n          {\n            begin: /\\bEVENT\\s+TRIGGER\\b/\n          },\n          {\n            begin: /\\b(MAPPING|OR)\\s+REPLACE\\b/\n          },\n          {\n            begin: /\\b(FROM|TO)\\s+(PROGRAM|STDIN|STDOUT)\\b/\n          },\n          {\n            begin: /\\b(SHARE|EXCLUSIVE)\\s+MODE\\b/\n          },\n          {\n            begin: /\\b(LEFT|RIGHT)\\s+(OUTER\\s+)?JOIN\\b/\n          },\n          {\n            begin: /\\b(FETCH|MOVE)\\s+(NEXT|PRIOR|FIRST|LAST|ABSOLUTE|RELATIVE|FORWARD|BACKWARD)\\b/\n          },\n          {\n            begin: /\\bPRESERVE\\s+ROWS\\b/\n          },\n          {\n            begin: /\\bDISCARD\\s+PLANS\\b/\n          },\n          {\n            begin: /\\bREFERENCING\\s+(OLD|NEW)\\b/\n          },\n          {\n            begin: /\\bSKIP\\s+LOCKED\\b/\n          },\n          {\n            begin: /\\bGROUPING\\s+SETS\\b/\n          },\n          {\n            begin: /\\b(BINARY|INSENSITIVE|SCROLL|NO\\s+SCROLL)\\s+(CURSOR|FOR)\\b/\n          },\n          {\n            begin: /\\b(WITH|WITHOUT)\\s+HOLD\\b/\n          },\n          {\n            begin: /\\bWITH\\s+(CASCADED|LOCAL)\\s+CHECK\\s+OPTION\\b/\n          },\n          {\n            begin: /\\bEXCLUDE\\s+(TIES|NO\\s+OTHERS)\\b/\n          },\n          {\n            begin: /\\bFORMAT\\s+(TEXT|XML|JSON|YAML)\\b/\n          },\n          {\n            begin: /\\bSET\\s+((SESSION|LOCAL)\\s+)?NAMES\\b/\n          },\n          {\n            begin: /\\bIS\\s+(NOT\\s+)?UNKNOWN\\b/\n          },\n          {\n            begin: /\\bSECURITY\\s+LABEL\\b/\n          },\n          {\n            begin: /\\bSTANDALONE\\s+(YES|NO|NO\\s+VALUE)\\b/\n          },\n          {\n            begin: /\\bWITH\\s+(NO\\s+)?DATA\\b/\n          },\n          {\n            begin: /\\b(FOREIGN|SET)\\s+DATA\\b/\n          },\n          {\n            begin: /\\bSET\\s+(CATALOG|CONSTRAINTS)\\b/\n          },\n          {\n            begin: /\\b(WITH|FOR)\\s+ORDINALITY\\b/\n          },\n          {\n            begin: /\\bIS\\s+(NOT\\s+)?DOCUMENT\\b/\n          },\n          {\n            begin: /\\bXML\\s+OPTION\\s+(DOCUMENT|CONTENT)\\b/\n          },\n          {\n            begin: /\\b(STRIP|PRESERVE)\\s+WHITESPACE\\b/\n          },\n          {\n            begin: /\\bNO\\s+(ACTION|MAXVALUE|MINVALUE)\\b/\n          },\n          {\n            begin: /\\bPARTITION\\s+BY\\s+(RANGE|LIST|HASH)\\b/\n          },\n          {\n            begin: /\\bAT\\s+TIME\\s+ZONE\\b/\n          },\n          {\n            begin: /\\bGRANTED\\s+BY\\b/\n          },\n          {\n            begin: /\\bRETURN\\s+(QUERY|NEXT)\\b/\n          },\n          {\n            begin: /\\b(ATTACH|DETACH)\\s+PARTITION\\b/\n          },\n          {\n            begin: /\\bFORCE\\s+ROW\\s+LEVEL\\s+SECURITY\\b/\n          },\n          {\n            begin: /\\b(INCLUDING|EXCLUDING)\\s+(COMMENTS|CONSTRAINTS|DEFAULTS|IDENTITY|INDEXES|STATISTICS|STORAGE|ALL)\\b/\n          },\n          {\n            begin: /\\bAS\\s+(ASSIGNMENT|IMPLICIT|PERMISSIVE|RESTRICTIVE|ENUM|RANGE)\\b/\n          }\n        ]\n      },\n      // functions named as keywords, followed by '('\n      {\n        begin: /\\b(FORMAT|FAMILY|VERSION)\\s*\\(/\n        // keywords: { built_in: 'FORMAT FAMILY VERSION' }\n      },\n      // INCLUDE ( ... ) in index_parameters in CREATE TABLE\n      {\n        begin: /\\bINCLUDE\\s*\\(/,\n        keywords: 'INCLUDE'\n      },\n      // not highlight RANGE if not in frame_clause (not 100% correct, but seems satisfactory)\n      {\n        begin: /\\bRANGE(?!\\s*(BETWEEN|UNBOUNDED|CURRENT|[-0-9]+))/\n      },\n      // disable highlighting in commands CREATE AGGREGATE/COLLATION/DATABASE/OPERTOR/TEXT SEARCH .../TYPE\n      // and in PL/pgSQL RAISE ... USING\n      {\n        begin: /\\b(VERSION|OWNER|TEMPLATE|TABLESPACE|CONNECTION\\s+LIMIT|PROCEDURE|RESTRICT|JOIN|PARSER|COPY|START|END|COLLATION|INPUT|ANALYZE|STORAGE|LIKE|DEFAULT|DELIMITER|ENCODING|COLUMN|CONSTRAINT|TABLE|SCHEMA)\\s*=/\n      },\n      // PG_smth; HAS_some_PRIVILEGE\n      {\n        // className: 'built_in',\n        begin: /\\b(PG_\\w+?|HAS_[A-Z_]+_PRIVILEGE)\\b/,\n        relevance: 10\n      },\n      // extract\n      {\n        begin: /\\bEXTRACT\\s*\\(/,\n        end: /\\bFROM\\b/,\n        returnEnd: true,\n        keywords: {\n          // built_in: 'EXTRACT',\n          type: 'CENTURY DAY DECADE DOW DOY EPOCH HOUR ISODOW ISOYEAR MICROSECONDS ' +\n                        'MILLENNIUM MILLISECONDS MINUTE MONTH QUARTER SECOND TIMEZONE TIMEZONE_HOUR ' +\n                        'TIMEZONE_MINUTE WEEK YEAR'\n        }\n      },\n      // xmlelement, xmlpi - special NAME\n      {\n        begin: /\\b(XMLELEMENT|XMLPI)\\s*\\(\\s*NAME/,\n        keywords: {\n          // built_in: 'XMLELEMENT XMLPI',\n          keyword: 'NAME'\n        }\n      },\n      // xmlparse, xmlserialize\n      {\n        begin: /\\b(XMLPARSE|XMLSERIALIZE)\\s*\\(\\s*(DOCUMENT|CONTENT)/,\n        keywords: {\n          // built_in: 'XMLPARSE XMLSERIALIZE',\n          keyword: 'DOCUMENT CONTENT'\n        }\n      },\n      // Sequences. We actually skip everything between CACHE|INCREMENT|MAXVALUE|MINVALUE and\n      // nearest following numeric constant. Without with trick we find a lot of \"keywords\"\n      // in 'avrasm' autodetection test...\n      {\n        beginKeywords: 'CACHE INCREMENT MAXVALUE MINVALUE',\n        end: hljs.C_NUMBER_RE,\n        returnEnd: true,\n        keywords: 'BY CACHE INCREMENT MAXVALUE MINVALUE'\n      },\n      // WITH|WITHOUT TIME ZONE as part of datatype\n      {\n        className: 'type',\n        begin: /\\b(WITH|WITHOUT)\\s+TIME\\s+ZONE\\b/\n      },\n      // INTERVAL optional fields\n      {\n        className: 'type',\n        begin: /\\bINTERVAL\\s+(YEAR|MONTH|DAY|HOUR|MINUTE|SECOND)(\\s+TO\\s+(MONTH|HOUR|MINUTE|SECOND))?\\b/\n      },\n      // Pseudo-types which allowed only as return type\n      {\n        begin: /\\bRETURNS\\s+(LANGUAGE_HANDLER|TRIGGER|EVENT_TRIGGER|FDW_HANDLER|INDEX_AM_HANDLER|TSM_HANDLER)\\b/,\n        keywords: {\n          keyword: 'RETURNS',\n          type: 'LANGUAGE_HANDLER TRIGGER EVENT_TRIGGER FDW_HANDLER INDEX_AM_HANDLER TSM_HANDLER'\n        }\n      },\n      // Known functions - only when followed by '('\n      {\n        begin: '\\\\b(' + FUNCTIONS_RE + ')\\\\s*\\\\('\n        // keywords: { built_in: FUNCTIONS }\n      },\n      // Types\n      {\n        begin: '\\\\.(' + TYPES_RE + ')\\\\b' // prevent highlight as type, say, 'oid' in 'pgclass.oid'\n      },\n      {\n        begin: '\\\\b(' + TYPES_RE + ')\\\\s+PATH\\\\b', // in XMLTABLE\n        keywords: {\n          keyword: 'PATH', // hopefully no one would use PATH type in XMLTABLE...\n          type: TYPES.replace('PATH ', '')\n        }\n      },\n      {\n        className: 'type',\n        begin: '\\\\b(' + TYPES_RE + ')\\\\b'\n      },\n      // Strings, see https://www.postgresql.org/docs/11/static/sql-syntax-lexical.html#SQL-SYNTAX-CONSTANTS\n      {\n        className: 'string',\n        begin: '\\'',\n        end: '\\'',\n        contains: [\n          {\n            begin: '\\'\\''\n          }\n        ]\n      },\n      {\n        className: 'string',\n        begin: '(e|E|u&|U&)\\'',\n        end: '\\'',\n        contains: [\n          {\n            begin: '\\\\\\\\.'\n          }\n        ],\n        relevance: 10\n      },\n      hljs.END_SAME_AS_BEGIN({\n        begin: DOLLAR_STRING,\n        end: DOLLAR_STRING,\n        contains: [\n          {\n            // actually we want them all except SQL; listed are those with known implementations\n            // and XML + JSON just in case\n            subLanguage: [\n              'pgsql',\n              'perl',\n              'python',\n              'tcl',\n              'r',\n              'lua',\n              'java',\n              'php',\n              'ruby',\n              'bash',\n              'scheme',\n              'xml',\n              'json'\n            ],\n            endsWithParent: true\n          }\n        ]\n      }),\n      // identifiers in quotes\n      {\n        begin: '\"',\n        end: '\"',\n        contains: [\n          {\n            begin: '\"\"'\n          }\n        ]\n      },\n      // numbers\n      hljs.C_NUMBER_MODE,\n      // comments\n      hljs.C_BLOCK_COMMENT_MODE,\n      COMMENT_MODE,\n      // PL/pgSQL staff\n      // %ROWTYPE, %TYPE, $n\n      {\n        className: 'meta',\n        variants: [\n          { // %TYPE, %ROWTYPE\n            begin: '%(ROW)?TYPE',\n            relevance: 10\n          },\n          { // $n\n            begin: '\\\\$\\\\d+'\n          },\n          { // #compiler option\n            begin: '^#\\\\w',\n            end: '$'\n          }\n        ]\n      },\n      // <<labeles>>\n      {\n        className: 'symbol',\n        begin: LABEL,\n        relevance: 10\n      }\n    ]\n  };\n}\n\nmodule.exports = pgsql;\n"], "mappings": ";;;;;AAAA;AAAA;AAkBA,aAAS,MAAM,MAAM;AACnB,YAAM,eAAe,KAAK,QAAQ,MAAM,GAAG;AAC3C,YAAM,iBAAiB;AACvB,YAAM,gBAAgB;AACtB,YAAM,QAAQ,WAAW,iBAAiB;AAE1C,YAAM;AAAA;AAAA;AAAA;AAAA,QAIJ;AAAA;AA0CF,YAAM;AAAA;AAAA,QACJ;AAAA;AAGF,YAAM,aACJ;AAIF,YAAM;AAAA;AAAA,QAEJ;AAAA;AAeF,YAAM,WACJ,MAAM,KAAK,EACR,MAAM,GAAG,EACT,IAAI,SAAS,KAAK;AAAE,eAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAAA,MAAG,CAAC,EAC/C,KAAK,GAAG;AAEb,YAAM,SACJ;AAGF,YAAM,aACJ;AAOF,YAAM;AAAA;AAAA,QAEJ;AAAA;AA4EF,YAAM;AAAA;AAAA,QAEJ;AAAA;AAyFF,YAAM,eACF,UAAU,KAAK,EACZ,MAAM,GAAG,EACT,IAAI,SAAS,KAAK;AAAE,eAAO,IAAI,MAAM,GAAG,EAAE,CAAC;AAAA,MAAG,CAAC,EAC/C,KAAK,GAAG;AAEf,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,SACM,SAAS,aAAa;AAAA,UAC5B,UACM,SAAS,aAAa;AAAA,QAC9B;AAAA;AAAA;AAAA,QAGA,SAAS;AAAA,QACT,UAAU;AAAA;AAAA,UAER;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA;AAAA,UAET;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,UACZ;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,UAAU;AAAA;AAAA,cAER,MAAM;AAAA,YAGR;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA;AAAA,cAER,SAAS;AAAA,YACX;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA;AAAA,cAER,SAAS;AAAA,YACX;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA;AAAA,YACE,eAAe;AAAA,YACf,KAAK,KAAK;AAAA,YACV,WAAW;AAAA,YACX,UAAU;AAAA,UACZ;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,YACP,UAAU;AAAA,cACR,SAAS;AAAA,cACT,MAAM;AAAA,YACR;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,OAAO,SAAS,eAAe;AAAA;AAAA,UAEjC;AAAA;AAAA,UAEA;AAAA,YACE,OAAO,SAAS,WAAW;AAAA;AAAA,UAC7B;AAAA,UACA;AAAA,YACE,OAAO,SAAS,WAAW;AAAA;AAAA,YAC3B,UAAU;AAAA,cACR,SAAS;AAAA;AAAA,cACT,MAAM,MAAM,QAAQ,SAAS,EAAE;AAAA,YACjC;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO,SAAS,WAAW;AAAA,UAC7B;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,YACA,WAAW;AAAA,UACb;AAAA,UACA,KAAK,kBAAkB;AAAA,YACrB,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA;AAAA;AAAA,gBAGE,aAAa;AAAA,kBACX;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,kBACA;AAAA,gBACF;AAAA,gBACA,gBAAgB;AAAA,cAClB;AAAA,YACF;AAAA,UACF,CAAC;AAAA;AAAA,UAED;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,KAAK;AAAA;AAAA,UAEL,KAAK;AAAA,UACL;AAAA;AAAA;AAAA,UAGA;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}