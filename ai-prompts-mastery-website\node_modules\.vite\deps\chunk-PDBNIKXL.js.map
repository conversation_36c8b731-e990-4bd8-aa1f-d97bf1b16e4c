{"version": 3, "sources": ["../../highlight.js/lib/languages/ini.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/**\n * Any of the passed expresssions may match\n *\n * Creates a huge this | this | that | that match\n * @param {(RegExp | string)[] } args\n * @returns {string}\n */\nfunction either(...args) {\n  const joined = '(' + args.map((x) => source(x)).join(\"|\") + \")\";\n  return joined;\n}\n\n/*\nLanguage: TOML, also INI\nDescription: TOML aims to be a minimal configuration file format that's easy to read due to obvious semantics.\nContributors: <PERSON> <<EMAIL>>\nCategory: common, config\nWebsite: https://github.com/toml-lang/toml\n*/\n\nfunction ini(hljs) {\n  const NUMBERS = {\n    className: 'number',\n    relevance: 0,\n    variants: [\n      {\n        begin: /([+-]+)?[\\d]+_[\\d_]+/\n      },\n      {\n        begin: hljs.NUMBER_RE\n      }\n    ]\n  };\n  const COMMENTS = hljs.COMMENT();\n  COMMENTS.variants = [\n    {\n      begin: /;/,\n      end: /$/\n    },\n    {\n      begin: /#/,\n      end: /$/\n    }\n  ];\n  const VARIABLES = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$[\\w\\d\"][\\w\\d_]*/\n      },\n      {\n        begin: /\\$\\{(.*?)\\}/\n      }\n    ]\n  };\n  const LITERALS = {\n    className: 'literal',\n    begin: /\\bon|off|true|false|yes|no\\b/\n  };\n  const STRINGS = {\n    className: \"string\",\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [\n      {\n        begin: \"'''\",\n        end: \"'''\",\n        relevance: 10\n      },\n      {\n        begin: '\"\"\"',\n        end: '\"\"\"',\n        relevance: 10\n      },\n      {\n        begin: '\"',\n        end: '\"'\n      },\n      {\n        begin: \"'\",\n        end: \"'\"\n      }\n    ]\n  };\n  const ARRAY = {\n    begin: /\\[/,\n    end: /\\]/,\n    contains: [\n      COMMENTS,\n      LITERALS,\n      VARIABLES,\n      STRINGS,\n      NUMBERS,\n      'self'\n    ],\n    relevance: 0\n  };\n\n  const BARE_KEY = /[A-Za-z0-9_-]+/;\n  const QUOTED_KEY_DOUBLE_QUOTE = /\"(\\\\\"|[^\"])*\"/;\n  const QUOTED_KEY_SINGLE_QUOTE = /'[^']*'/;\n  const ANY_KEY = either(\n    BARE_KEY, QUOTED_KEY_DOUBLE_QUOTE, QUOTED_KEY_SINGLE_QUOTE\n  );\n  const DOTTED_KEY = concat(\n    ANY_KEY, '(\\\\s*\\\\.\\\\s*', ANY_KEY, ')*',\n    lookahead(/\\s*=\\s*[^#\\s]/)\n  );\n\n  return {\n    name: 'TOML, also INI',\n    aliases: ['toml'],\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [\n      COMMENTS,\n      {\n        className: 'section',\n        begin: /\\[+/,\n        end: /\\]+/\n      },\n      {\n        begin: DOTTED_KEY,\n        className: 'attr',\n        starts: {\n          end: /$/,\n          contains: [\n            COMMENTS,\n            ARRAY,\n            LITERALS,\n            VARIABLES,\n            STRINGS,\n            NUMBERS\n          ]\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = ini;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AASA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,MAAM,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;AAC5D,aAAO;AAAA,IACT;AAUA,aAAS,IAAI,MAAM;AACjB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO,KAAK;AAAA,UACd;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW,KAAK,QAAQ;AAC9B,eAAS,WAAW;AAAA,QAClB;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,QACA;AAAA,UACE,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,MACF;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU,CAAC,KAAK,gBAAgB;AAAA,QAChC,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AACA,YAAM,QAAQ;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,WAAW;AACjB,YAAM,0BAA0B;AAChC,YAAM,0BAA0B;AAChC,YAAM,UAAU;AAAA,QACd;AAAA,QAAU;AAAA,QAAyB;AAAA,MACrC;AACA,YAAM,aAAa;AAAA,QACjB;AAAA,QAAS;AAAA,QAAgB;AAAA,QAAS;AAAA,QAClC,UAAU,eAAe;AAAA,MAC3B;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,MAAM;AAAA,QAChB,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,UAAU;AAAA,gBACR;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,gBACA;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}