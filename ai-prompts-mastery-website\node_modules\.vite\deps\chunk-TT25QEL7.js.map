{"version": 3, "sources": ["../../refractor/lang/typescript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = typescript\ntypescript.displayName = 'typescript'\ntypescript.aliases = ['ts']\nfunction typescript(Prism) {\n  ;(function (Prism) {\n    Prism.languages.typescript = Prism.languages.extend('javascript', {\n      'class-name': {\n        pattern:\n          /(\\b(?:class|extends|implements|instanceof|interface|new|type)\\s+)(?!keyof\\b)(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*(?:\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>)?/,\n        lookbehind: true,\n        greedy: true,\n        inside: null // see below\n      },\n      builtin:\n        /\\b(?:Array|Function|Promise|any|boolean|console|never|number|string|symbol|unknown)\\b/\n    }) // The keywords TypeScript adds to JavaScript\n    Prism.languages.typescript.keyword.push(\n      /\\b(?:abstract|declare|is|keyof|readonly|require)\\b/, // keywords that have to be followed by an identifier\n      /\\b(?:asserts|infer|interface|module|namespace|type)\\b(?=\\s*(?:[{_$a-zA-Z\\xA0-\\uFFFF]|$))/, // This is for `import type *, {}`\n      /\\btype\\b(?=\\s*(?:[\\{*]|$))/\n    ) // doesn't work with TS because TS is too complex\n    delete Prism.languages.typescript['parameter']\n    delete Prism.languages.typescript['literal-property'] // a version of typescript specifically for highlighting types\n    var typeInside = Prism.languages.extend('typescript', {})\n    delete typeInside['class-name']\n    Prism.languages.typescript['class-name'].inside = typeInside\n    Prism.languages.insertBefore('typescript', 'function', {\n      decorator: {\n        pattern: /@[$\\w\\xA0-\\uFFFF]+/,\n        inside: {\n          at: {\n            pattern: /^@/,\n            alias: 'operator'\n          },\n          function: /^[\\s\\S]+/\n        }\n      },\n      'generic-function': {\n        // e.g. foo<T extends \"bar\" | \"baz\">( ...\n        pattern:\n          /#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*\\s*<(?:[^<>]|<(?:[^<>]|<[^<>]*>)*>)*>(?=\\s*\\()/,\n        greedy: true,\n        inside: {\n          function: /^#?(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/,\n          generic: {\n            pattern: /<[\\s\\S]+/,\n            // everything after the first <\n            alias: 'class-name',\n            inside: typeInside\n          }\n        }\n      }\n    })\n    Prism.languages.ts = Prism.languages.typescript\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC,IAAI;AAC1B,aAAS,WAAW,OAAO;AACzB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,aAAaA,OAAM,UAAU,OAAO,cAAc;AAAA,UAChE,cAAc;AAAA,YACZ,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA;AAAA,UACV;AAAA,UACA,SACE;AAAA,QACJ,CAAC;AACD,QAAAA,OAAM,UAAU,WAAW,QAAQ;AAAA,UACjC;AAAA;AAAA,UACA;AAAA;AAAA,UACA;AAAA,QACF;AACA,eAAOA,OAAM,UAAU,WAAW,WAAW;AAC7C,eAAOA,OAAM,UAAU,WAAW,kBAAkB;AACpD,YAAI,aAAaA,OAAM,UAAU,OAAO,cAAc,CAAC,CAAC;AACxD,eAAO,WAAW,YAAY;AAC9B,QAAAA,OAAM,UAAU,WAAW,YAAY,EAAE,SAAS;AAClD,QAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,UACrD,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,IAAI;AAAA,gBACF,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,oBAAoB;AAAA;AAAA,YAElB,SACE;AAAA,YACF,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,UAAU;AAAA,cACV,SAAS;AAAA,gBACP,SAAS;AAAA;AAAA,gBAET,OAAO;AAAA,gBACP,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,KAAKA,OAAM,UAAU;AAAA,MACvC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}