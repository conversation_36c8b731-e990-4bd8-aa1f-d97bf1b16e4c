{"version": 3, "sources": ["../../refractor/lang/openqasm.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = openqasm\nopenqasm.displayName = 'openqasm'\nopenqasm.aliases = ['qasm']\nfunction openqasm(Prism) {\n  // https://qiskit.github.io/openqasm/grammar/index.html\n  Prism.languages.openqasm = {\n    comment: /\\/\\*[\\s\\S]*?\\*\\/|\\/\\/.*/,\n    string: {\n      pattern: /\"[^\"\\r\\n\\t]*\"|'[^'\\r\\n\\t]*'/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:CX|OPENQASM|U|barrier|boxas|boxto|break|const|continue|ctrl|def|defcal|defcalgrammar|delay|else|end|for|gate|gphase|if|in|include|inv|kernel|lengthof|let|measure|pow|reset|return|rotary|stretchinf|while)\\b|#pragma\\b/,\n    'class-name':\n      /\\b(?:angle|bit|bool|creg|fixed|float|int|length|qreg|qubit|stretch|uint)\\b/,\n    function: /\\b(?:cos|exp|ln|popcount|rotl|rotr|sin|sqrt|tan)\\b(?=\\s*\\()/,\n    constant: /\\b(?:euler|pi|tau)\\b|π|𝜏|ℇ/,\n    number: {\n      pattern:\n        /(^|[^.\\w$])(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?(?:dt|ns|us|µs|ms|s)?/i,\n      lookbehind: true\n    },\n    operator: /->|>>=?|<<=?|&&|\\|\\||\\+\\+|--|[!=<>&|~^+\\-*/%]=?|@/,\n    punctuation: /[(){}\\[\\];,:.]/\n  }\n  Prism.languages.qasm = Prism.languages.openqasm\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC,MAAM;AAC1B,aAAS,SAAS,OAAO;AAEvB,YAAM,UAAU,WAAW;AAAA,QACzB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,cACE;AAAA,QACF,UAAU;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,UACN,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,OAAO,MAAM,UAAU;AAAA,IACzC;AAAA;AAAA;", "names": []}