import * as ts from 'typescript';
/**
 * @param type Type being checked by name.
 * @param allowAny Whether to consider `any` and `unknown` to match.
 * @param allowedNames Symbol names checking on the type.
 * @param matchAnyInstead Whether to instead just check if any parts match, rather than all parts.
 * @returns Whether the type is, extends, or contains the allowed names (or all matches the allowed names, if mustMatchAll is true).
 */
export declare function containsAllTypesByName(type: ts.Type, allowAny: boolean, allowedNames: Set<string>, matchAnyInstead?: boolean): boolean;
//# sourceMappingURL=containsAllTypesByName.d.ts.map