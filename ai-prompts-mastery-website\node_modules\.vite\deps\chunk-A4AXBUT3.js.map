{"version": 3, "sources": ["../../highlight.js/lib/languages/lua.js"], "sourcesContent": ["/*\nLanguage: Lua\nDescription: Lua is a powerful, efficient, lightweight, embeddable scripting language.\nAuthor: <PERSON> <<EMAIL>>\nCategory: common, scripting\nWebsite: https://www.lua.org\n*/\n\nfunction lua(hljs) {\n  const OPENING_LONG_BRACKET = '\\\\[=*\\\\[';\n  const CLOSING_LONG_BRACKET = '\\\\]=*\\\\]';\n  const LONG_BRACKETS = {\n    begin: OPENING_LONG_BRACKET,\n    end: CLOSING_LONG_BRACKET,\n    contains: ['self']\n  };\n  const COMMENTS = [\n    hljs.COMMENT('--(?!' + OPENING_LONG_BRACKET + ')', '$'),\n    hljs.COMMENT(\n      '--' + OPENING_LONG_BRACKET,\n      CLOSING_LONG_BRACKET,\n      {\n        contains: [LONG_BRACKETS],\n        relevance: 10\n      }\n    )\n  ];\n  return {\n    name: '<PERSON><PERSON>',\n    keywords: {\n      $pattern: hljs.UNDERSCORE_IDENT_RE,\n      literal: \"true false nil\",\n      keyword: \"and break do else elseif end for goto if in local not or repeat return then until while\",\n      built_in:\n        // Metatags and globals:\n        '_G _ENV _VERSION __index __newindex __mode __call __metatable __tostring __len ' +\n        '__gc __add __sub __mul __div __mod __pow __concat __unm __eq __lt __le assert ' +\n        // Standard methods and properties:\n        'collectgarbage dofile error getfenv getmetatable ipairs load loadfile loadstring ' +\n        'module next pairs pcall print rawequal rawget rawset require select setfenv ' +\n        'setmetatable tonumber tostring type unpack xpcall arg self ' +\n        // Library methods and properties (one line per library):\n        'coroutine resume yield status wrap create running debug getupvalue ' +\n        'debug sethook getmetatable gethook setmetatable setlocal traceback setfenv getinfo setupvalue getlocal getregistry getfenv ' +\n        'io lines write close flush open output type read stderr stdin input stdout popen tmpfile ' +\n        'math log max acos huge ldexp pi cos tanh pow deg tan cosh sinh random randomseed frexp ceil floor rad abs sqrt modf asin min mod fmod log10 atan2 exp sin atan ' +\n        'os exit setlocale date getenv difftime remove time clock tmpname rename execute package preload loadlib loaded loaders cpath config path seeall ' +\n        'string sub upper len gfind rep find match char dump gmatch reverse byte format gsub lower ' +\n        'table setn insert getn foreachi maxn foreach concat sort remove'\n    },\n    contains: COMMENTS.concat([\n      {\n        className: 'function',\n        beginKeywords: 'function',\n        end: '\\\\)',\n        contains: [\n          hljs.inherit(hljs.TITLE_MODE, {\n            begin: '([_a-zA-Z]\\\\w*\\\\.)*([_a-zA-Z]\\\\w*:)?[_a-zA-Z]\\\\w*'\n          }),\n          {\n            className: 'params',\n            begin: '\\\\(',\n            endsWithParent: true,\n            contains: COMMENTS\n          }\n        ].concat(COMMENTS)\n      },\n      hljs.C_NUMBER_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      {\n        className: 'string',\n        begin: OPENING_LONG_BRACKET,\n        end: CLOSING_LONG_BRACKET,\n        contains: [LONG_BRACKETS],\n        relevance: 5\n      }\n    ])\n  };\n}\n\nmodule.exports = lua;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,uBAAuB;AAC7B,YAAM,uBAAuB;AAC7B,YAAM,gBAAgB;AAAA,QACpB,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAC,MAAM;AAAA,MACnB;AACA,YAAM,WAAW;AAAA,QACf,KAAK,QAAQ,UAAU,uBAAuB,KAAK,GAAG;AAAA,QACtD,KAAK;AAAA,UACH,OAAO;AAAA,UACP;AAAA,UACA;AAAA,YACE,UAAU,CAAC,aAAa;AAAA,YACxB,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,UAAU,KAAK;AAAA,UACf,SAAS;AAAA,UACT,SAAS;AAAA,UACT;AAAA;AAAA,YAEE;AAAA;AAAA,QAcJ;AAAA,QACA,UAAU,SAAS,OAAO;AAAA,UACxB;AAAA,YACE,WAAW;AAAA,YACX,eAAe;AAAA,YACf,KAAK;AAAA,YACL,UAAU;AAAA,cACR,KAAK,QAAQ,KAAK,YAAY;AAAA,gBAC5B,OAAO;AAAA,cACT,CAAC;AAAA,cACD;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,gBAAgB;AAAA,gBAChB,UAAU;AAAA,cACZ;AAAA,YACF,EAAE,OAAO,QAAQ;AAAA,UACnB;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC,aAAa;AAAA,YACxB,WAAW;AAAA,UACb;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}