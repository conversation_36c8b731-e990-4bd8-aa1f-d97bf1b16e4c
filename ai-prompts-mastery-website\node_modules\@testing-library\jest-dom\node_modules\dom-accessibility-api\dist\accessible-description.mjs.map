{"version": 3, "file": "accessible-description.mjs", "names": ["computeTextAlternative", "queryIdRefs", "computeAccessibleDescription", "root", "options", "arguments", "length", "undefined", "description", "map", "element", "_objectSpread", "compute", "join", "ariaDescription", "getAttribute", "title"], "sources": ["../sources/accessible-description.ts"], "sourcesContent": ["import {\n\tcomputeTextAlternative,\n\tComputeTextAlternativeOptions,\n} from \"./accessible-name-and-description\";\nimport { queryIdRefs } from \"./util\";\n\n/**\n * @param root\n * @param options\n * @returns\n */\nexport function computeAccessibleDescription(\n\troot: Element,\n\toptions: ComputeTextAlternativeOptions = {},\n): string {\n\tlet description = queryIdRefs(root, \"aria-describedby\")\n\t\t.map((element) => {\n\t\t\treturn computeTextAlternative(element, {\n\t\t\t\t...options,\n\t\t\t\tcompute: \"description\",\n\t\t\t});\n\t\t})\n\t\t.join(\" \");\n\n\t// TODO: Technically we need to make sure that node wasn't used for the accessible name\n\t//       This causes `description_1.0_combobox-focusable-manual` to fail\n\n\t// https://w3c.github.io/aria/#aria-description\n\t// mentions that aria-description should only be calculated if aria-describedby didn't provide\n\t// a description\n\tif (description === \"\") {\n\t\tconst ariaDescription = root.getAttribute(\"aria-description\");\n\t\tdescription = ariaDescription === null ? \"\" : ariaDescription;\n\t}\n\n\t// https://www.w3.org/TR/html-aam-1.0/#accessible-name-and-description-computation\n\t// says for so many elements to use the `title` that we assume all elements are considered\n\tif (description === \"\") {\n\t\tconst title = root.getAttribute(\"title\");\n\t\tdescription = title === null ? \"\" : title;\n\t}\n\n\treturn description;\n}\n"], "mappings": ";;;;;;AAAA,SACCA,sBAAsB,QAEhB,uCAAmC;AAC1C,SAASC,WAAW,QAAQ,YAAQ;;AAEpC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAC3CC,IAAa,EAEJ;EAAA,IADTC,OAAsC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAE3C,IAAIG,WAAW,GAAGP,WAAW,CAACE,IAAI,EAAE,kBAAkB,CAAC,CACrDM,GAAG,CAAC,UAACC,OAAO,EAAK;IACjB,OAAOV,sBAAsB,CAACU,OAAO,EAAAC,aAAA,CAAAA,aAAA,KACjCP,OAAO;MACVQ,OAAO,EAAE;IAAa,EACtB,CAAC;EACH,CAAC,CAAC,CACDC,IAAI,CAAC,GAAG,CAAC;;EAEX;EACA;;EAEA;EACA;EACA;EACA,IAAIL,WAAW,KAAK,EAAE,EAAE;IACvB,IAAMM,eAAe,GAAGX,IAAI,CAACY,YAAY,CAAC,kBAAkB,CAAC;IAC7DP,WAAW,GAAGM,eAAe,KAAK,IAAI,GAAG,EAAE,GAAGA,eAAe;EAC9D;;EAEA;EACA;EACA,IAAIN,WAAW,KAAK,EAAE,EAAE;IACvB,IAAMQ,KAAK,GAAGb,IAAI,CAACY,YAAY,CAAC,OAAO,CAAC;IACxCP,WAAW,GAAGQ,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK;EAC1C;EAEA,OAAOR,WAAW;AACnB"}