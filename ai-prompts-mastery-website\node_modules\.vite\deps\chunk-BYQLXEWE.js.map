{"version": 3, "sources": ["../../refractor/lang/pcaxis.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pcaxis\npcaxis.displayName = 'pcaxis'\npcaxis.aliases = ['px']\nfunction pcaxis(Prism) {\n  Prism.languages.pcaxis = {\n    string: /\"[^\"]*\"/,\n    keyword: {\n      pattern:\n        /((?:^|;)\\s*)[-A-Z\\d]+(?:\\s*\\[[-\\w]+\\])?(?:\\s*\\(\"[^\"]*\"(?:,\\s*\"[^\"]*\")*\\))?(?=\\s*=)/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        keyword: /^[-A-Z\\d]+/,\n        language: {\n          pattern: /^(\\s*)\\[[-\\w]+\\]/,\n          lookbehind: true,\n          inside: {\n            punctuation: /^\\[|\\]$/,\n            property: /[-\\w]+/\n          }\n        },\n        'sub-key': {\n          pattern: /^(\\s*)\\S[\\s\\S]*/,\n          lookbehind: true,\n          inside: {\n            parameter: {\n              pattern: /\"[^\"]*\"/,\n              alias: 'property'\n            },\n            punctuation: /^\\(|\\)$|,/\n          }\n        }\n      }\n    },\n    operator: /=/,\n    tlist: {\n      pattern:\n        /TLIST\\s*\\(\\s*\\w+(?:(?:\\s*,\\s*\"[^\"]*\")+|\\s*,\\s*\"[^\"]*\"-\"[^\"]*\")?\\s*\\)/,\n      greedy: true,\n      inside: {\n        function: /^TLIST/,\n        property: {\n          pattern: /^(\\s*\\(\\s*)\\w+/,\n          lookbehind: true\n        },\n        string: /\"[^\"]*\"/,\n        punctuation: /[(),]/,\n        operator: /-/\n      }\n    },\n    punctuation: /[;,]/,\n    number: {\n      pattern: /(^|\\s)\\d+(?:\\.\\d+)?(?!\\S)/,\n      lookbehind: true\n    },\n    boolean: /NO|YES/\n  }\n  Prism.languages.px = Prism.languages.pcaxis\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC,IAAI;AACtB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA,QACvB,QAAQ;AAAA,QACR,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,aAAa;AAAA,gBACb,UAAU;AAAA,cACZ;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,OAAO;AAAA,UACL,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,UAAU;AAAA,cACR,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,QAAQ;AAAA,YACR,aAAa;AAAA,YACb,UAAU;AAAA,UACZ;AAAA,QACF;AAAA,QACA,aAAa;AAAA,QACb,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,SAAS;AAAA,MACX;AACA,YAAM,UAAU,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA;AAAA;", "names": []}