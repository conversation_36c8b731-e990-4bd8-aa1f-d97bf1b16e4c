{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "manipulateOptions", "_", "parser", "plugins", "push", "visitor", "AssignmentExpression", "path", "node", "scope", "operator", "left", "right", "operatorTrunc", "slice", "t", "LOGICAL_OPERATORS", "includes", "lhs", "cloneNode", "isMemberExpression", "object", "property", "computed", "memo", "maybeGenerateMemoised", "assignmentExpression", "replaceWith", "logicalExpression"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-logical-assignment-operators\",\n    manipulateOptions: process.env.BABEL_8_BREAKING\n      ? undefined\n      : (_, parser) => parser.plugins.push(\"logicalAssignment\"),\n\n    visitor: {\n      AssignmentExpression(path) {\n        const { node, scope } = path;\n        const { operator, left, right } = node;\n        const operatorTrunc = operator.slice(0, -1);\n        if (!t.LOGICAL_OPERATORS.includes(operatorTrunc)) {\n          return;\n        }\n\n        const lhs = t.cloneNode(left) as t.Identifier | t.MemberExpression;\n        if (t.isMemberExpression(left)) {\n          const { object, property, computed } = left;\n          const memo = scope.maybeGenerateMemoised(object);\n          if (memo) {\n            left.object = memo;\n            (lhs as t.MemberExpression).object = t.assignmentExpression(\n              \"=\",\n              t.cloneNode(memo),\n              // object must not be Super when `memo` is an identifier\n              // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n              object as t.Expression,\n            );\n          }\n\n          if (computed) {\n            const memo = scope.maybeGenerateMemoised(property);\n            if (memo) {\n              left.property = memo;\n              (lhs as t.MemberExpression).property = t.assignmentExpression(\n                \"=\",\n                t.cloneNode(memo),\n                // @ts-expect-error todo(flow->ts): property can be t.PrivateName\n                property,\n              );\n            }\n          }\n        }\n\n        path.replaceWith(\n          t.logicalExpression(\n            // @ts-expect-error operatorTrunc has been tested by t.LOGICAL_OPERATORS\n            operatorTrunc,\n            lhs,\n            t.assignmentExpression(\"=\", left, right),\n          ),\n        );\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,uCAAoB,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,wCAAwC;IAC9CC,iBAAiB,EAEbA,CAACC,CAAC,EAAEC,MAAM,KAAKA,MAAM,CAACC,OAAO,CAACC,IAAI,CAAC,mBAAmB,CAAC;IAE3DC,OAAO,EAAE;MACPC,oBAAoBA,CAACC,IAAI,EAAE;QACzB,MAAM;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGF,IAAI;QAC5B,MAAM;UAAEG,QAAQ;UAAEC,IAAI;UAAEC;QAAM,CAAC,GAAGJ,IAAI;QACtC,MAAMK,aAAa,GAAGH,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC3C,IAAI,CAACC,WAAC,CAACC,iBAAiB,CAACC,QAAQ,CAACJ,aAAa,CAAC,EAAE;UAChD;QACF;QAEA,MAAMK,GAAG,GAAGH,WAAC,CAACI,SAAS,CAACR,IAAI,CAAsC;QAClE,IAAII,WAAC,CAACK,kBAAkB,CAACT,IAAI,CAAC,EAAE;UAC9B,MAAM;YAAEU,MAAM;YAAEC,QAAQ;YAAEC;UAAS,CAAC,GAAGZ,IAAI;UAC3C,MAAMa,IAAI,GAAGf,KAAK,CAACgB,qBAAqB,CAACJ,MAAM,CAAC;UAChD,IAAIG,IAAI,EAAE;YACRb,IAAI,CAACU,MAAM,GAAGG,IAAI;YACjBN,GAAG,CAAwBG,MAAM,GAAGN,WAAC,CAACW,oBAAoB,CACzD,GAAG,EACHX,WAAC,CAACI,SAAS,CAACK,IAAI,CAAC,EAGjBH,MACF,CAAC;UACH;UAEA,IAAIE,QAAQ,EAAE;YACZ,MAAMC,IAAI,GAAGf,KAAK,CAACgB,qBAAqB,CAACH,QAAQ,CAAC;YAClD,IAAIE,IAAI,EAAE;cACRb,IAAI,CAACW,QAAQ,GAAGE,IAAI;cACnBN,GAAG,CAAwBI,QAAQ,GAAGP,WAAC,CAACW,oBAAoB,CAC3D,GAAG,EACHX,WAAC,CAACI,SAAS,CAACK,IAAI,CAAC,EAEjBF,QACF,CAAC;YACH;UACF;QACF;QAEAf,IAAI,CAACoB,WAAW,CACdZ,WAAC,CAACa,iBAAiB,CAEjBf,aAAa,EACbK,GAAG,EACHH,WAAC,CAACW,oBAAoB,CAAC,GAAG,EAAEf,IAAI,EAAEC,KAAK,CACzC,CACF,CAAC;MACH;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}