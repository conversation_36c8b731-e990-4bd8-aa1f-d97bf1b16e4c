{"name": "deep-is", "version": "0.1.4", "description": "node's assert.deepEqual algorithm except for NaN being equal to NaN", "main": "index.js", "directories": {"lib": ".", "example": "example", "test": "test"}, "scripts": {"test": "tape test/*.js"}, "devDependencies": {"tape": "~1.0.2"}, "repository": {"type": "git", "url": "http://github.com/thlorenz/deep-is.git"}, "keywords": ["equality", "equal", "compare"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://thlorenz.com"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": {"ie": [6, 7, 8, 9], "ff": [3.5, 10, 15], "chrome": [10, 22], "safari": [5.1], "opera": [12]}}}