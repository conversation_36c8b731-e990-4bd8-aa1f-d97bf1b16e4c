{"version": 3, "sources": ["../../refractor/lang/typoscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = typoscript\ntyposcript.displayName = 'typoscript'\ntyposcript.aliases = ['tsconfig']\nfunction typoscript(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:ACT|ACTIFSUB|CARRAY|CASE|CLEARGIF|COA|COA_INT|CONSTANTS|CONTENT|CUR|EDITPANEL|EFFECT|EXT|FILE|FLUIDTEMPLATE|FORM|FRAME|FRAMESET|GIFBUILDER|GMENU|GMENU_FOLDOUT|GMENU_LAYERS|GP|HMENU|HRULER|HTML|IENV|IFSUB|IMAGE|IMGMENU|IMGMENUITEM|IMGTEXT|IMG_RESOURCE|INCLUDE_TYPOSCRIPT|JSMENU|JSMENUITEM|LLL|LOAD_REGISTER|NO|PAGE|RECORDS|RESTORE_REGISTER|TEMPLATE|TEXT|TMENU|TMENUITEM|TMENU_LAYERS|USER|USER_INT|_GIFBUILDER|global|globalString|globalVar)\\b/\n    Prism.languages.typoscript = {\n      comment: [\n        {\n          // multiline comments /* */\n          pattern: /(^|[^\\\\])\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n          lookbehind: true\n        },\n        {\n          // double-slash comments - ignored when backslashes or colon is found in front\n          // also ignored whenever directly after an equal-sign, because it would probably be an url without protocol\n          pattern: /(^|[^\\\\:= \\t]|(?:^|[^= \\t])[ \\t]+)\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          // hash comments - ignored when leading quote is found for hex colors in strings\n          pattern: /(^|[^\"'])#.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      function: [\n        {\n          // old include style\n          pattern:\n            /<INCLUDE_TYPOSCRIPT:\\s*source\\s*=\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')\\s*>/,\n          inside: {\n            string: {\n              pattern: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/,\n              inside: {\n                keyword: keywords\n              }\n            },\n            keyword: {\n              pattern: /INCLUDE_TYPOSCRIPT/\n            }\n          }\n        },\n        {\n          // new include style\n          pattern: /@import\\s*(?:\"[^\"\\r\\n]*\"|'[^'\\r\\n]*')/,\n          inside: {\n            string: /\"[^\"\\r\\n]*\"|'[^'\\r\\n]*'/\n          }\n        }\n      ],\n      string: {\n        pattern: /^([^=]*=[< ]?)(?:(?!\\]\\n).)*/,\n        lookbehind: true,\n        inside: {\n          function: /\\{\\$.*\\}/,\n          // constants include\n          keyword: keywords,\n          number: /^\\d+$/,\n          punctuation: /[,|:]/\n        }\n      },\n      keyword: keywords,\n      number: {\n        // special highlighting for indexes of arrays in tags\n        pattern: /\\b\\d+\\s*[.{=]/,\n        inside: {\n          operator: /[.{=]/\n        }\n      },\n      tag: {\n        pattern: /\\.?[-\\w\\\\]+\\.?/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      punctuation: /[{}[\\];(),.:|]/,\n      operator: /[<>]=?|[!=]=?=?|--?|\\+\\+?|&&?|\\|\\|?|[?*/~^%]/\n    }\n    Prism.languages.tsconfig = Prism.languages.typoscript\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,eAAW,cAAc;AACzB,eAAW,UAAU,CAAC,UAAU;AAChC,aAAS,WAAW,OAAO;AACzB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF;AACF,QAAAA,OAAM,UAAU,aAAa;AAAA,UAC3B,SAAS;AAAA,YACP;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR;AAAA;AAAA,cAEE,SACE;AAAA,cACF,QAAQ;AAAA,gBACN,QAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,QAAQ;AAAA,oBACN,SAAS;AAAA,kBACX;AAAA,gBACF;AAAA,gBACA,SAAS;AAAA,kBACP,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,QAAQ;AAAA,cACV;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,UAAU;AAAA;AAAA,cAEV,SAAS;AAAA,cACT,QAAQ;AAAA,cACR,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,QAAQ;AAAA;AAAA,YAEN,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,KAAK;AAAA,YACH,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AACA,QAAAA,OAAM,UAAU,WAAWA,OAAM,UAAU;AAAA,MAC7C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}