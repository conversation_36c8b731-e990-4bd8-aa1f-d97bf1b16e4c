{"version": 3, "sources": ["../../highlight.js/lib/languages/ceylon.js"], "sourcesContent": ["/*\nLanguage: Ceylon\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://ceylon-lang.org\n*/\n\n/** @type LanguageFn */\nfunction ceylon(hljs) {\n  // 2.3. Identifiers and keywords\n  const KEYWORDS =\n    'assembly module package import alias class interface object given value ' +\n    'assign void function new of extends satisfies abstracts in out return ' +\n    'break continue throw assert dynamic if else switch case for while try ' +\n    'catch finally then let this outer super is exists nonempty';\n  // 7.4.1 Declaration Modifiers\n  const DECLARATION_MODIFIERS =\n    'shared abstract formal default actual variable late native deprecated ' +\n    'final sealed annotation suppressWarnings small';\n  // 7.4.2 Documentation\n  const DOCUMENTATION =\n    'doc by license see throws tagged';\n  const SUBST = {\n    className: 'subst',\n    excludeBegin: true,\n    excludeEnd: true,\n    begin: /``/,\n    end: /``/,\n    keywords: KEYWORDS,\n    relevance: 10\n  };\n  const EXPRESSIONS = [\n    {\n      // verbatim string\n      className: 'string',\n      begin: '\"\"\"',\n      end: '\"\"\"',\n      relevance: 10\n    },\n    {\n      // string literal or template\n      className: 'string',\n      begin: '\"',\n      end: '\"',\n      contains: [SUBST]\n    },\n    {\n      // character literal\n      className: 'string',\n      begin: \"'\",\n      end: \"'\"\n    },\n    {\n      // numeric literal\n      className: 'number',\n      begin: '#[0-9a-fA-F_]+|\\\\$[01_]+|[0-9_]+(?:\\\\.[0-9_](?:[eE][+-]?\\\\d+)?)?[kMGTPmunpf]?',\n      relevance: 0\n    }\n  ];\n  SUBST.contains = EXPRESSIONS;\n\n  return {\n    name: 'Ceylon',\n    keywords: {\n      keyword: KEYWORDS + ' ' + DECLARATION_MODIFIERS,\n      meta: DOCUMENTATION\n    },\n    illegal: '\\\\$[^01]|#[^0-9a-fA-F]',\n    contains: [\n      hljs.C_LINE_COMMENT_MODE,\n      hljs.COMMENT('/\\\\*', '\\\\*/', {\n        contains: ['self']\n      }),\n      {\n        // compiler annotation\n        className: 'meta',\n        begin: '@[a-z]\\\\w*(?::\"[^\"]*\")?'\n      }\n    ].concat(EXPRESSIONS)\n  };\n}\n\nmodule.exports = ceylon;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,OAAO,MAAM;AAEpB,YAAM,WACJ;AAKF,YAAM,wBACJ;AAGF,YAAM,gBACJ;AACF,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,QACV,WAAW;AAAA,MACb;AACA,YAAM,cAAc;AAAA,QAClB;AAAA;AAAA,UAEE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,WAAW;AAAA,QACb;AAAA,QACA;AAAA;AAAA,UAEE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,UACL,UAAU,CAAC,KAAK;AAAA,QAClB;AAAA,QACA;AAAA;AAAA,UAEE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,KAAK;AAAA,QACP;AAAA,QACA;AAAA;AAAA,UAEE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,WAAW;AAEjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SAAS,WAAW,MAAM;AAAA,UAC1B,MAAM;AAAA,QACR;AAAA,QACA,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK,QAAQ,QAAQ,QAAQ;AAAA,YAC3B,UAAU,CAAC,MAAM;AAAA,UACnB,CAAC;AAAA,UACD;AAAA;AAAA,YAEE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF,EAAE,OAAO,WAAW;AAAA,MACtB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}