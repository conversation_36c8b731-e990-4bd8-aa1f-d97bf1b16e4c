!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react"),require("react-dom"),require("react-dom/client"),require("react-dom/test-utils")):"function"==typeof define&&define.amd?define(["exports","react","react-dom","react-dom/client","react-dom/test-utils"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryReact={},e.React,e.ReactDOM,e.ReactDOMClient,e.ReactTestUtils)}(this,(function(e,t,r,n,o){"use strict";function a(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}function i(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}function l(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var u=i(t),s=a(r),c=i(n),d=i(o),p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function f(e){var t=e.default;if("function"==typeof t){var r=function(){return t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var m={},b={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>`[${38+e};5;${t}m`},r=function(e){return void 0===e&&(e=0),(t,r,n)=>`[${38+e};2;${t};${r};${n}m`};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,o]of Object.entries(r))n[t]={open:`[${o[0]}m`,close:`[${o[1]}m`},r[t]=n[t],e.set(o[0],o[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(b);var y={};Object.defineProperty(y,"__esModule",{value:!0}),y.printIteratorEntries=function(e,t,r,n,o,a,i){void 0===i&&(i=": ");let l="",u=e.next();if(!u.done){l+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){l+=s+a(u.value[0],t,s,n,o)+i+a(u.value[1],t,s,n,o),u=e.next(),u.done?t.min||(l+=","):l+=","+t.spacingInner}l+=t.spacingOuter+r}return l},y.printIteratorValues=function(e,t,r,n,o,a){let i="",l=e.next();if(!l.done){i+=t.spacingOuter;const u=r+t.indent;for(;!l.done;)i+=u+a(l.value,t,u,n,o),l=e.next(),l.done?t.min||(i+=","):i+=","+t.spacingInner;i+=t.spacingOuter+r}return i},y.printListItems=function(e,t,r,n,o,a){let i="";if(e.length){i+=t.spacingOuter;const l=r+t.indent;for(let r=0;r<e.length;r++)i+=l,r in e&&(i+=a(e[r],t,l,n,o)),r<e.length-1?i+=","+t.spacingInner:t.min||(i+=",");i+=t.spacingOuter+r}return i},y.printObjectProperties=function(e,t,r,n,o,a){let i="";const l=v(e,t.compareKeys);if(l.length){i+=t.spacingOuter;const u=r+t.indent;for(let r=0;r<l.length;r++){const s=l[r];i+=u+a(s,t,u,n,o)+": "+a(e[s],t,u,n,o),r<l.length-1?i+=","+t.spacingInner:t.min||(i+=",")}i+=t.spacingOuter+r}return i};const v=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var h={};Object.defineProperty(h,"__esModule",{value:!0}),h.test=h.serialize=h.default=void 0;var g=y,P="undefined"!=typeof globalThis?globalThis:void 0!==P?P:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),C=P["jest-symbol-do-not-touch"]||P.Symbol;const w="function"==typeof C&&C.for?C.for("jest.asymmetricMatcher"):1267621,q=" ",E=(e,t,r,n,o,a)=>{const i=e.toString();return"ArrayContaining"===i||"ArrayNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+q+"["+(0,g.printListItems)(e.sample,t,r,n,o,a)+"]":"ObjectContaining"===i||"ObjectNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+q+"{"+(0,g.printObjectProperties)(e.sample,t,r,n,o,a)+"}":"StringMatching"===i||"StringNotMatching"===i||"StringContaining"===i||"StringNotContaining"===i?i+q+a(e.sample,t,r,n,o):e.toAsymmetricMatcher()};h.serialize=E;const x=e=>e&&e.$$typeof===w;h.test=x;var O={serialize:E,test:x};h.default=O;var R={};Object.defineProperty(R,"__esModule",{value:!0}),R.test=R.serialize=R.default=void 0;var j=A((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),S=A(b.exports);function A(e){return e&&e.__esModule?e:{default:e}}const _=e=>"string"==typeof e&&!!e.match((0,j.default)());R.test=_;const T=(e,t,r,n,o,a)=>a(e.replace((0,j.default)(),(e=>{switch(e){case S.default.red.close:case S.default.green.close:case S.default.cyan.close:case S.default.gray.close:case S.default.white.close:case S.default.yellow.close:case S.default.bgRed.close:case S.default.bgGreen.close:case S.default.bgYellow.close:case S.default.inverse.close:case S.default.dim.close:case S.default.bold.close:case S.default.reset.open:case S.default.reset.close:return"</>";case S.default.red.open:return"<red>";case S.default.green.open:return"<green>";case S.default.cyan.open:return"<cyan>";case S.default.gray.open:return"<gray>";case S.default.white.open:return"<white>";case S.default.yellow.open:return"<yellow>";case S.default.bgRed.open:return"<bgRed>";case S.default.bgGreen.open:return"<bgGreen>";case S.default.bgYellow.open:return"<bgYellow>";case S.default.inverse.open:return"<inverse>";case S.default.dim.open:return"<dim>";case S.default.bold.open:return"<bold>";default:return""}})),t,r,n,o);R.serialize=T;var M={serialize:T,test:_};R.default=M;var I={};Object.defineProperty(I,"__esModule",{value:!0}),I.test=I.serialize=I.default=void 0;var B=y;const k=["DOMStringMap","NamedNodeMap"],F=/^(HTML\w*Collection|NodeList)$/,N=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==k.indexOf(t)||F.test(t));var t};I.test=N;const L=(e,t,r,n,o,a)=>{const i=e.constructor.name;return++n>t.maxDepth?"["+i+"]":(t.min?"":i+" ")+(-1!==k.indexOf(i)?"{"+(0,B.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,o,a)+"}":"["+(0,B.printListItems)(Array.from(e),t,r,n,o,a)+"]")};I.serialize=L;var U={serialize:L,test:N};I.default=U;var D={},H={},$={};Object.defineProperty($,"__esModule",{value:!0}),$.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(H,"__esModule",{value:!0}),H.printText=H.printProps=H.printElementAsLeaf=H.printElement=H.printComment=H.printChildren=void 0;var W,z=(W=$)&&W.__esModule?W:{default:W};H.printProps=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")};H.printChildren=(e,t,r,n,o,a)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?V(e,t):a(e,t,r,n,o)))).join("");const V=(e,t)=>{const r=t.colors.content;return r.open+(0,z.default)(e)+r.close};H.printText=V;H.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,z.default)(e)+"--\x3e"+r.close};H.printElement=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close};H.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(D,"__esModule",{value:!0}),D.test=D.serialize=D.default=void 0;var G=H;const J=/^((HTML|SVG)\w*)?Element$/,Q=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(J.test(t)||o)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function X(e){return 11===e.nodeType}D.test=Q;const K=(e,t,r,n,o,a)=>{if(function(e){return 3===e.nodeType}(e))return(0,G.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,G.printComment)(e.data,t);const i=X(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,G.printElementAsLeaf)(i,t):(0,G.printElement)(i,(0,G.printProps)(X(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),X(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,o,a),(0,G.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,o,a),t,r)};D.serialize=K;var Y={serialize:K,test:Q};D.default=Y;var Z={};Object.defineProperty(Z,"__esModule",{value:!0}),Z.test=Z.serialize=Z.default=void 0;var ee=y;const te="@@__IMMUTABLE_ORDERED__@@",re=e=>"Immutable."+e,ne=e=>"["+e+"]",oe=" ";const ae=(e,t,r,n,o,a,i)=>++n>t.maxDepth?ne(re(i)):re(i)+oe+"["+(0,ee.printIteratorValues)(e.values(),t,r,n,o,a)+"]",ie=(e,t,r,n,o,a)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,o,a,i)=>++n>t.maxDepth?ne(re(i)):re(i)+oe+"{"+(0,ee.printIteratorEntries)(e.entries(),t,r,n,o,a)+"}")(e,t,r,n,o,a,e[te]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?ae(e,t,r,n,o,a,"List"):e["@@__IMMUTABLE_SET__@@"]?ae(e,t,r,n,o,a,e[te]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?ae(e,t,r,n,o,a,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,o,a)=>{const i=re("Seq");return++n>t.maxDepth?ne(i):e["@@__IMMUTABLE_KEYED__@@"]?i+oe+"{"+(e._iter||e._object?(0,ee.printIteratorEntries)(e.entries(),t,r,n,o,a):"…")+"}":i+oe+"["+(e._iter||e._array||e._collection||e._iterable?(0,ee.printIteratorValues)(e.values(),t,r,n,o,a):"…")+"]"})(e,t,r,n,o,a):((e,t,r,n,o,a)=>{const i=re(e._name||"Record");return++n>t.maxDepth?ne(i):i+oe+"{"+(0,ee.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,o,a)+"}"})(e,t,r,n,o,a);Z.serialize=ie;const le=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);Z.test=le;var ue={serialize:ie,test:le};Z.default=ue;var se,ce={},de={exports:{}},pe={};!function(e){e.exports=function(){if(se)return pe;se=1;var e=60103,t=60106,r=60107,n=60108,o=60114,a=60109,i=60110,l=60112,u=60113,s=60120,c=60115,d=60116,p=60121,f=60122,m=60117,b=60129,y=60131;if("function"==typeof Symbol&&Symbol.for){var v=Symbol.for;e=v("react.element"),t=v("react.portal"),r=v("react.fragment"),n=v("react.strict_mode"),o=v("react.profiler"),a=v("react.provider"),i=v("react.context"),l=v("react.forward_ref"),u=v("react.suspense"),s=v("react.suspense_list"),c=v("react.memo"),d=v("react.lazy"),p=v("react.block"),f=v("react.server.block"),m=v("react.fundamental"),b=v("react.debug_trace_mode"),y=v("react.legacy_hidden")}function h(p){if("object"==typeof p&&null!==p){var f=p.$$typeof;switch(f){case e:switch(p=p.type){case r:case o:case n:case u:case s:return p;default:switch(p=p&&p.$$typeof){case i:case l:case d:case c:case a:return p;default:return f}}case t:return f}}}var g=a,P=e,C=l,w=r,q=d,E=c,x=t,O=o,R=n,j=u;return pe.ContextConsumer=i,pe.ContextProvider=g,pe.Element=P,pe.ForwardRef=C,pe.Fragment=w,pe.Lazy=q,pe.Memo=E,pe.Portal=x,pe.Profiler=O,pe.StrictMode=R,pe.Suspense=j,pe.isAsyncMode=function(){return!1},pe.isConcurrentMode=function(){return!1},pe.isContextConsumer=function(e){return h(e)===i},pe.isContextProvider=function(e){return h(e)===a},pe.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},pe.isForwardRef=function(e){return h(e)===l},pe.isFragment=function(e){return h(e)===r},pe.isLazy=function(e){return h(e)===d},pe.isMemo=function(e){return h(e)===c},pe.isPortal=function(e){return h(e)===t},pe.isProfiler=function(e){return h(e)===o},pe.isStrictMode=function(e){return h(e)===n},pe.isSuspense=function(e){return h(e)===u},pe.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===o||e===b||e===n||e===u||e===s||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===d||e.$$typeof===c||e.$$typeof===a||e.$$typeof===i||e.$$typeof===l||e.$$typeof===m||e.$$typeof===p||e[0]===f)},pe.typeOf=h,pe}()}(de),Object.defineProperty(ce,"__esModule",{value:!0}),ce.test=ce.serialize=ce.default=void 0;var fe=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=be(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}n.default=e,r&&r.set(e,n);return n}(de.exports),me=H;function be(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(be=function(e){return e?r:t})(e)}const ye=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{ye(e,t)})):null!=e&&!1!==e&&t.push(e),t},ve=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(fe.isFragment(e))return"React.Fragment";if(fe.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(fe.isContextProvider(e))return"Context.Provider";if(fe.isContextConsumer(e))return"Context.Consumer";if(fe.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(fe.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},he=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,me.printElementAsLeaf)(ve(e),t):(0,me.printElement)(ve(e),(0,me.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,o,a),(0,me.printChildren)(ye(e.props.children),t,r+t.indent,n,o,a),t,r);ce.serialize=he;const ge=e=>null!=e&&fe.isElement(e);ce.test=ge;var Pe={serialize:he,test:ge};ce.default=Pe;var Ce={};Object.defineProperty(Ce,"__esModule",{value:!0}),Ce.test=Ce.serialize=Ce.default=void 0;var we=H,qe="undefined"!=typeof globalThis?globalThis:void 0!==qe?qe:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),Ee=qe["jest-symbol-do-not-touch"]||qe.Symbol;const xe="function"==typeof Ee&&Ee.for?Ee.for("react.test.json"):245830487,Oe=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,we.printElementAsLeaf)(e.type,t):(0,we.printElement)(e.type,e.props?(0,we.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,o,a):"",e.children?(0,we.printChildren)(e.children,t,r+t.indent,n,o,a):"",t,r);Ce.serialize=Oe;const Re=e=>e&&e.$$typeof===xe;Ce.test=Re;var je={serialize:Oe,test:Re};Ce.default=je,Object.defineProperty(m,"__esModule",{value:!0});var Se=m.default=m.DEFAULT_OPTIONS=void 0,Ae=m.format=mt,_e=m.plugins=void 0,Te=De(b.exports),Me=y,Ie=De(h),Be=De(R),ke=De(I),Fe=De(D),Ne=De(Z),Le=De(ce),Ue=De(Ce);function De(e){return e&&e.__esModule?e:{default:e}}const He=Object.prototype.toString,$e=Date.prototype.toISOString,We=Error.prototype.toString,ze=RegExp.prototype.toString,Ve=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",Ge=e=>"undefined"!=typeof window&&e===window,Je=/^Symbol\((.*)\)(.*)$/,Qe=/\n/gi;class Xe extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function Ke(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function Ye(e){return String(e).replace(Je,"Symbol($1)")}function Ze(e){return"["+We.call(e)+"]"}function et(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const o=typeof e;if("number"===o)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===o)return function(e){return String(`${e}n`)}(e);if("string"===o)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===o)return Ke(e,t);if("symbol"===o)return Ye(e);const a=He.call(e);return"[object WeakMap]"===a?"WeakMap {}":"[object WeakSet]"===a?"WeakSet {}":"[object Function]"===a||"[object GeneratorFunction]"===a?Ke(e,t):"[object Symbol]"===a?Ye(e):"[object Date]"===a?isNaN(+e)?"Date { NaN }":$e.call(e):"[object Error]"===a?Ze(e):"[object RegExp]"===a?r?ze.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):ze.call(e):e instanceof Error?Ze(e):null}function tt(e,t,r,n,o,a){if(-1!==o.indexOf(e))return"[Circular]";(o=o.slice()).push(e);const i=++n>t.maxDepth,l=t.min;if(t.callToJSON&&!i&&e.toJSON&&"function"==typeof e.toJSON&&!a)return ot(e.toJSON(),t,r,n,o,!0);const u=He.call(e);return"[object Arguments]"===u?i?"[Arguments]":(l?"":"Arguments ")+"["+(0,Me.printListItems)(e,t,r,n,o,ot)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?i?"["+e.constructor.name+"]":(l?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,Me.printListItems)(e,t,r,n,o,ot)+"]":"[object Map]"===u?i?"[Map]":"Map {"+(0,Me.printIteratorEntries)(e.entries(),t,r,n,o,ot," => ")+"}":"[object Set]"===u?i?"[Set]":"Set {"+(0,Me.printIteratorValues)(e.values(),t,r,n,o,ot)+"}":i||Ge(e)?"["+Ve(e)+"]":(l?"":t.printBasicPrototype||"Object"!==Ve(e)?Ve(e)+" ":"")+"{"+(0,Me.printObjectProperties)(e,t,r,n,o,ot)+"}"}function rt(e,t,r,n,o,a){let i;try{i=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,o,a,ot):e.print(t,(e=>ot(e,r,n,o,a)),(e=>{const t=n+r.indent;return t+e.replace(Qe,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new Xe(e.message,e.stack)}if("string"!=typeof i)throw new Error(`pretty-format: Plugin must return type "string" but instead returned "${typeof i}".`);return i}function nt(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new Xe(e.message,e.stack)}return null}function ot(e,t,r,n,o,a){const i=nt(t.plugins,e);if(null!==i)return rt(i,e,t,r,n,o);const l=et(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==l?l:tt(e,t,r,n,o,a)}const at={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},it=Object.keys(at),lt={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:at};var ut=m.DEFAULT_OPTIONS=lt;const st=e=>it.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:at[r],o=n&&Te.default[n];if(!o||"string"!=typeof o.close||"string"!=typeof o.open)throw new Error(`pretty-format: Option "theme" has a key "${r}" whose value "${n}" is undefined in ansi-styles.`);return t[r]=o,t}),Object.create(null)),ct=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:lt.printFunctionName,dt=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:lt.escapeRegex,pt=e=>e&&void 0!==e.escapeString?e.escapeString:lt.escapeString,ft=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:lt.callToJSON,colors:e&&e.highlight?st(e):it.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:lt.compareKeys,escapeRegex:dt(e),escapeString:pt(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:lt.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:lt.maxDepth,min:e&&void 0!==e.min?e.min:lt.min,plugins:e&&void 0!==e.plugins?e.plugins:lt.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:ct(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function mt(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!lt.hasOwnProperty(e))throw new Error(`pretty-format: Unknown option "${e}".`)})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error(`pretty-format: Option "theme" must be of type "object" but instead received "${typeof e.theme}".`)}}(t),t.plugins)){const r=nt(t.plugins,e);if(null!==r)return rt(r,e,ft(t),"",0,[])}const r=et(e,ct(t),dt(t),pt(t));return null!==r?r:tt(e,ft(t),"",0,[])}const bt={AsymmetricMatcher:Ie.default,ConvertAnsi:Be.default,DOMCollection:ke.default,DOMElement:Fe.default,Immutable:Ne.default,ReactElement:Le.default,ReactTestComponent:Ue.default};_e=m.plugins=bt;var yt=mt;Se=m.default=yt;var vt=l({__proto__:null,get DEFAULT_OPTIONS(){return ut},format:Ae,get plugins(){return _e},get default(){return Se}},[m]),ht=Object.prototype.toString;function gt(e){return"function"==typeof e||"[object Function]"===ht.call(e)}var Pt=Math.pow(2,53)-1;function Ct(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),Pt)}function wt(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!gt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var o,a=Ct(n.length),i=gt(r)?Object(new r(a)):new Array(a),l=0;l<a;)o=n[l],i[l]=t?t(o,l):o,l+=1;return i.length=a,i}function qt(e){return qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},qt(e)}function Et(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,xt(n.key),n)}}function xt(e){var t=function(e,t){if("object"!==qt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==qt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===qt(t)?t:String(t)}var Ot=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=xt(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&Et(t.prototype,r),n&&Et(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Rt="undefined"==typeof Set?Set:Ot;function jt(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var St={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},At={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function _t(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=At[t])&&void 0!==n&&n.has(r))}))}(e,t)}function Tt(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=St[jt(e)];if(void 0!==t)return t;switch(jt(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||_t(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||_t(e,r||""))return r}return t}function Mt(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function It(e){return Mt(e)&&"caption"===jt(e)}function Bt(e){return Mt(e)&&"input"===jt(e)}function kt(e){return Mt(e)&&"legend"===jt(e)}function Ft(e){return function(e){return Mt(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===jt(e)}function Nt(e,t){if(Mt(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function Lt(e,t){return!!Mt(e)&&-1!==t.indexOf(Tt(e))}function Ut(e,t){if(!Mt(e))return!1;if("range"===t)return Lt(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Dt(e,t){var r=wt(e.querySelectorAll(t));return Nt(e,"aria-owns").forEach((function(e){r.push.apply(r,wt(e.querySelectorAll(t)))})),r}function Ht(e){return Mt(t=e)&&"select"===jt(t)?e.selectedOptions||Dt(e,"[selected]"):Dt(e,'[aria-selected="true"]');var t}function $t(e){return Bt(e)||Mt(t=e)&&"textarea"===jt(t)?e.value:e.textContent||"";var t}function Wt(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function zt(e){var t=jt(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Vt(e){if(zt(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&Mt(e)){var r=Vt(e);null!==r&&(t=r)}})),t}function Gt(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Vt(e)}function Jt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new Rt,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),o=t.compute,a=void 0===o?"name":o,i=t.computedStyleSupportsPseudoElements,l=void 0===i?void 0!==t.getComputedStyle:i,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,c=t.hidden,d=void 0!==c&&c;function p(e,t){var r,n,o="";if(Mt(e)&&l){var a=Wt(s(e,"::before"));o="".concat(a," ").concat(o)}if((function(e){return Mt(e)&&"slot"===jt(e)}(e)?0===(n=(r=e).assignedNodes()).length?wt(r.childNodes):n:wt(e.childNodes).concat(Nt(e,"aria-owns"))).forEach((function(e){var r=b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(Mt(e)?s(e).getPropertyValue("display"):"inline")?" ":"";o+="".concat(n).concat(r).concat(n)})),Mt(e)&&l){var i=Wt(s(e,"::after"));o="".concat(o," ").concat(i)}return o.trim()}function f(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function m(e){if(!Mt(e))return null;if(function(e){return Mt(e)&&"fieldset"===jt(e)}(e)){r.add(e);for(var t=wt(e.childNodes),n=0;n<t.length;n+=1){var o=t[n];if(kt(o))return b(o,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return Mt(e)&&"table"===jt(e)}(e)){r.add(e);for(var a=wt(e.childNodes),i=0;i<a.length;i+=1){var l=a[i];if(It(l))return b(l,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return Mt(e)&&"svg"===jt(e)}(e)){r.add(e);for(var u=wt(e.childNodes),s=0;s<u.length;s+=1){var c=u[s];if(Ft(c))return c.textContent}return null}if("img"===jt(e)||"area"===jt(e)){var d=f(e,"alt");if(null!==d)return d}else if(function(e){return Mt(e)&&"optgroup"===jt(e)}(e)){var m=f(e,"label");if(null!==m)return m}}if(Bt(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var y=f(e,"value");if(null!==y)return y;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var v,h,g=null===(h=(v=e).labels)?h:void 0!==h?wt(h):zt(v)?wt(v.ownerDocument.querySelectorAll("label")).filter((function(e){return Gt(e)===v})):null;if(null!==g&&0!==g.length)return r.add(e),wt(g).map((function(e){return b(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(Bt(e)&&"image"===e.type){var P=f(e,"alt");if(null!==P)return P;var C=f(e,"title");return null!==C?C:"Submit Query"}if(Lt(e,["button"])){var w=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==w)return w}return null}function b(e,t){if(r.has(e))return"";if(!d&&function(e,t){if(!Mt(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=Mt(e)?e.getAttributeNode("aria-labelledby"):null,o=null===n||r.has(n)?[]:Nt(e,"aria-labelledby");if("name"===a&&!t.isReferenced&&o.length>0)return r.add(n),o.map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var i,l=t.recursion&&(Lt(i=e,["button","combobox","listbox","textbox"])||Ut(i,"range"))&&"name"===a;if(!l){var u=(Mt(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===a)return r.add(e),u;if(!function(e){return Lt(e,["none","presentation"])}(e)){var c=m(e);if(null!==c)return r.add(e),c}}if(Lt(e,["menu"]))return r.add(e),"";if(l||t.isEmbeddedInLabel||t.isReferenced){if(Lt(e,["combobox","listbox"])){r.add(e);var y=Ht(e);return 0===y.length?Bt(e)?e.value:"":wt(y).map((function(e){return b(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(Ut(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(Lt(e,["textbox"]))return r.add(e),$t(e)}if(function(e){return Lt(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||Mt(e)&&t.isReferenced||function(e){return It(e)}(e)){var v=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==v)return r.add(e),v}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return Mt(e)?f(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return b(e,{isEmbeddedInLabel:!1,isReferenced:"description"===a,recursion:!1}).trim().replace(/\s\s+/g," ")}function Qt(e){return Qt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Qt(e)}function Xt(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function Kt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Xt(Object(r),!0).forEach((function(t){Yt(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Xt(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Yt(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Qt(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Qt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Qt(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Zt(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=Nt(e,"aria-describedby").map((function(e){return Jt(e,Kt(Kt({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function er(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Lt(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":Jt(e,t)}var tr={},rr={},nr={},or={};Object.defineProperty(or,"__esModule",{value:!0}),or.default=void 0;var ar=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};or.default=ar,Object.defineProperty(nr,"__esModule",{value:!0}),nr.default=function(e,t){"function"==typeof Symbol&&"symbol"===lr(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:ir.default.bind(t)});return e};var ir=function(e){return e&&e.__esModule?e:{default:e}}(or);function lr(e){return lr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},lr(e)}Object.defineProperty(rr,"__esModule",{value:!0}),rr.default=void 0;var ur=function(e){return e&&e.__esModule?e:{default:e}}(nr);function sr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||cr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function cr(e,t){if(e){if("string"==typeof e)return dr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?dr(e,t):void 0}}function dr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var pr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],fr={entries:function(){return pr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=cr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(pr);try{for(n.s();!(t=n.n()).done;){var o=sr(t.value,2),a=o[0],i=o[1];e.call(r,i,a,pr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=pr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!fr.get(e)},keys:function(){return pr.map((function(e){return sr(e,1)[0]}))},values:function(){return pr.map((function(e){return sr(e,2)[1]}))}},mr=(0,ur.default)(fr,fr.entries());rr.default=mr;var br={};Object.defineProperty(br,"__esModule",{value:!0}),br.default=void 0;var yr=function(e){return e&&e.__esModule?e:{default:e}}(nr);function vr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||hr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hr(e,t){if(e){if("string"==typeof e)return gr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?gr(e,t):void 0}}function gr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Pr=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],Cr={entries:function(){return Pr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=hr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Pr);try{for(n.s();!(t=n.n()).done;){var o=vr(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Pr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Pr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Cr.get(e)},keys:function(){return Pr.map((function(e){return vr(e,1)[0]}))},values:function(){return Pr.map((function(e){return vr(e,2)[1]}))}},wr=(0,yr.default)(Cr,Cr.entries());br.default=wr;var qr={},Er={},xr={};Object.defineProperty(xr,"__esModule",{value:!0}),xr.default=void 0;var Or={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};xr.default=Or;var Rr={};Object.defineProperty(Rr,"__esModule",{value:!0}),Rr.default=void 0;var jr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Rr.default=jr;var Sr={};Object.defineProperty(Sr,"__esModule",{value:!0}),Sr.default=void 0;var Ar={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};Sr.default=Ar;var _r={};Object.defineProperty(_r,"__esModule",{value:!0}),_r.default=void 0;var Tr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_r.default=Tr;var Mr={};Object.defineProperty(Mr,"__esModule",{value:!0}),Mr.default=void 0;var Ir={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Mr.default=Ir;var Br={};Object.defineProperty(Br,"__esModule",{value:!0}),Br.default=void 0;var kr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Br.default=kr;var Fr={};Object.defineProperty(Fr,"__esModule",{value:!0}),Fr.default=void 0;var Nr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Fr.default=Nr;var Lr={};Object.defineProperty(Lr,"__esModule",{value:!0}),Lr.default=void 0;var Ur={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Lr.default=Ur;var Dr={};Object.defineProperty(Dr,"__esModule",{value:!0}),Dr.default=void 0;var Hr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};Dr.default=Hr;var $r={};Object.defineProperty($r,"__esModule",{value:!0}),$r.default=void 0;var Wr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};$r.default=Wr;var zr={};Object.defineProperty(zr,"__esModule",{value:!0}),zr.default=void 0;var Vr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};zr.default=Vr;var Gr={};Object.defineProperty(Gr,"__esModule",{value:!0}),Gr.default=void 0;var Jr={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Gr.default=Jr,Object.defineProperty(Er,"__esModule",{value:!0}),Er.default=void 0;var Qr=un(xr),Xr=un(Rr),Kr=un(Sr),Yr=un(_r),Zr=un(Mr),en=un(Br),tn=un(Fr),rn=un(Lr),nn=un(Dr),on=un($r),an=un(zr),ln=un(Gr);function un(e){return e&&e.__esModule?e:{default:e}}var sn=[["command",Qr.default],["composite",Xr.default],["input",Kr.default],["landmark",Yr.default],["range",Zr.default],["roletype",en.default],["section",tn.default],["sectionhead",rn.default],["select",nn.default],["structure",on.default],["widget",an.default],["window",ln.default]];Er.default=sn;var cn={},dn={};Object.defineProperty(dn,"__esModule",{value:!0}),dn.default=void 0;var pn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};dn.default=pn;var fn={};Object.defineProperty(fn,"__esModule",{value:!0}),fn.default=void 0;var mn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};fn.default=mn;var bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var yn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};bn.default=yn;var vn={};Object.defineProperty(vn,"__esModule",{value:!0}),vn.default=void 0;var hn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};vn.default=hn;var gn={};Object.defineProperty(gn,"__esModule",{value:!0}),gn.default=void 0;var Pn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};gn.default=Pn;var Cn={};Object.defineProperty(Cn,"__esModule",{value:!0}),Cn.default=void 0;var wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Cn.default=wn;var qn={};Object.defineProperty(qn,"__esModule",{value:!0}),qn.default=void 0;var En={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};qn.default=En;var xn={};Object.defineProperty(xn,"__esModule",{value:!0}),xn.default=void 0;var On={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xn.default=On;var Rn={};Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.default=void 0;var jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Rn.default=jn;var Sn={};Object.defineProperty(Sn,"__esModule",{value:!0}),Sn.default=void 0;var An={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};Sn.default=An;var _n={};Object.defineProperty(_n,"__esModule",{value:!0}),_n.default=void 0;var Tn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_n.default=Tn;var Mn={};Object.defineProperty(Mn,"__esModule",{value:!0}),Mn.default=void 0;var In={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Mn.default=In;var Bn={};Object.defineProperty(Bn,"__esModule",{value:!0}),Bn.default=void 0;var kn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};Bn.default=kn;var Fn={};Object.defineProperty(Fn,"__esModule",{value:!0}),Fn.default=void 0;var Nn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Fn.default=Nn;var Ln={};Object.defineProperty(Ln,"__esModule",{value:!0}),Ln.default=void 0;var Un={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ln.default=Un;var Dn={};Object.defineProperty(Dn,"__esModule",{value:!0}),Dn.default=void 0;var Hn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Dn.default=Hn;var $n={};Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Wn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};$n.default=Wn;var zn={};Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Vn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};zn.default=Vn;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.default=void 0;var Jn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Gn.default=Jn;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var Xn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Qn.default=Xn;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var Yn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Kn.default=Yn;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0}),Zn.default=void 0;var eo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Zn.default=eo;var to={};Object.defineProperty(to,"__esModule",{value:!0}),to.default=void 0;var ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};to.default=ro;var no={};Object.defineProperty(no,"__esModule",{value:!0}),no.default=void 0;var oo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};no.default=oo;var ao={};Object.defineProperty(ao,"__esModule",{value:!0}),ao.default=void 0;var io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ao.default=io;var lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.default=void 0;var uo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};lo.default=uo;var so={};Object.defineProperty(so,"__esModule",{value:!0}),so.default=void 0;var co={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};so.default=co;var po={};Object.defineProperty(po,"__esModule",{value:!0}),po.default=void 0;var fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};po.default=fo;var mo={};Object.defineProperty(mo,"__esModule",{value:!0}),mo.default=void 0;var bo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};mo.default=bo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0}),yo.default=void 0;var vo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};yo.default=vo;var ho={};Object.defineProperty(ho,"__esModule",{value:!0}),ho.default=void 0;var go={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ho.default=go;var Po={};Object.defineProperty(Po,"__esModule",{value:!0}),Po.default=void 0;var Co={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Po.default=Co;var wo={};Object.defineProperty(wo,"__esModule",{value:!0}),wo.default=void 0;var qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};wo.default=qo;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var xo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Eo.default=xo;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var Ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Oo.default=Ro;var jo={};Object.defineProperty(jo,"__esModule",{value:!0}),jo.default=void 0;var So={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};jo.default=So;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var _o={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ao.default=_o;var To={};Object.defineProperty(To,"__esModule",{value:!0}),To.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};To.default=Mo;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Io.default=Bo;var ko={};Object.defineProperty(ko,"__esModule",{value:!0}),ko.default=void 0;var Fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ko.default=Fo;var No={};Object.defineProperty(No,"__esModule",{value:!0}),No.default=void 0;var Lo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};No.default=Lo;var Uo={};Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.default=void 0;var Do={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Uo.default=Do;var Ho={};Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.default=void 0;var $o={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};Ho.default=$o;var Wo={};Object.defineProperty(Wo,"__esModule",{value:!0}),Wo.default=void 0;var zo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};Wo.default=zo;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.default=void 0;var Go={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};Vo.default=Go;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Jo.default=Qo;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Xo.default=Ko;var Yo={};Object.defineProperty(Yo,"__esModule",{value:!0}),Yo.default=void 0;var Zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yo.default=Zo;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ra.default=na;var oa={};Object.defineProperty(oa,"__esModule",{value:!0}),oa.default=void 0;var aa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};oa.default=aa;var ia={};Object.defineProperty(ia,"__esModule",{value:!0}),ia.default=void 0;var la={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};ia.default=la;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};ua.default=sa;var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=void 0;var da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ca.default=da;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var fa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};pa.default=fa;var ma={};Object.defineProperty(ma,"__esModule",{value:!0}),ma.default=void 0;var ba={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};ma.default=ba;var ya={};Object.defineProperty(ya,"__esModule",{value:!0}),ya.default=void 0;var va={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};ya.default=va;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};ha.default=ga;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ca={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};Pa.default=Ca;var wa={};Object.defineProperty(wa,"__esModule",{value:!0}),wa.default=void 0;var qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};wa.default=qa;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.default=void 0;var xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};Ea.default=xa;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0}),Oa.default=void 0;var Ra={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Oa.default=Ra;var ja={};Object.defineProperty(ja,"__esModule",{value:!0}),ja.default=void 0;var Sa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};ja.default=Sa;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.default=void 0;var _a={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};Aa.default=_a;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.default=void 0;var Ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ta.default=Ma;var Ia={};Object.defineProperty(Ia,"__esModule",{value:!0}),Ia.default=void 0;var Ba={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ia.default=Ba;var ka={};Object.defineProperty(ka,"__esModule",{value:!0}),ka.default=void 0;var Fa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ka.default=Fa;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Na.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};Ua.default=Da;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var $a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};Ha.default=$a;var Wa={};Object.defineProperty(Wa,"__esModule",{value:!0}),Wa.default=void 0;var za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Wa.default=za;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};Va.default=Ga;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0}),Ja.default=void 0;var Qa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ja.default=Qa;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.default=void 0;var Ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Xa.default=Ka;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};Ya.default=Za;var ei={};Object.defineProperty(ei,"__esModule",{value:!0}),ei.default=void 0;var ti={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ei.default=ti;var ri={};Object.defineProperty(ri,"__esModule",{value:!0}),ri.default=void 0;var ni={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};ri.default=ni;var oi={};Object.defineProperty(oi,"__esModule",{value:!0}),oi.default=void 0;var ai={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};oi.default=ai;var ii={};Object.defineProperty(ii,"__esModule",{value:!0}),ii.default=void 0;var li={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ii.default=li;var ui={};Object.defineProperty(ui,"__esModule",{value:!0}),ui.default=void 0;var si={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ui.default=si;var ci={};Object.defineProperty(ci,"__esModule",{value:!0}),ci.default=void 0;var di={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};ci.default=di;var pi={};Object.defineProperty(pi,"__esModule",{value:!0}),pi.default=void 0;var fi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};pi.default=fi,Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var mi=Hl(dn),bi=Hl(fn),yi=Hl(bn),vi=Hl(vn),hi=Hl(gn),gi=Hl(Cn),Pi=Hl(qn),Ci=Hl(xn),wi=Hl(Rn),qi=Hl(Sn),Ei=Hl(_n),xi=Hl(Mn),Oi=Hl(Bn),Ri=Hl(Fn),ji=Hl(Ln),Si=Hl(Dn),Ai=Hl($n),_i=Hl(zn),Ti=Hl(Gn),Mi=Hl(Qn),Ii=Hl(Kn),Bi=Hl(Zn),ki=Hl(to),Fi=Hl(no),Ni=Hl(ao),Li=Hl(lo),Ui=Hl(so),Di=Hl(po),Hi=Hl(mo),$i=Hl(yo),Wi=Hl(ho),zi=Hl(Po),Vi=Hl(wo),Gi=Hl(Eo),Ji=Hl(Oo),Qi=Hl(jo),Xi=Hl(Ao),Ki=Hl(To),Yi=Hl(Io),Zi=Hl(ko),el=Hl(No),tl=Hl(Uo),rl=Hl(Ho),nl=Hl(Wo),ol=Hl(Vo),al=Hl(Jo),il=Hl(Xo),ll=Hl(Yo),ul=Hl(ea),sl=Hl(ra),cl=Hl(oa),dl=Hl(ia),pl=Hl(ua),fl=Hl(ca),ml=Hl(pa),bl=Hl(ma),yl=Hl(ya),vl=Hl(ha),hl=Hl(Pa),gl=Hl(wa),Pl=Hl(Ea),Cl=Hl(Oa),wl=Hl(ja),ql=Hl(Aa),El=Hl(Ta),xl=Hl(Ia),Ol=Hl(ka),Rl=Hl(Na),jl=Hl(Ua),Sl=Hl(Ha),Al=Hl(Wa),_l=Hl(Va),Tl=Hl(Ja),Ml=Hl(Xa),Il=Hl(Ya),Bl=Hl(ei),kl=Hl(ri),Fl=Hl(oi),Nl=Hl(ii),Ll=Hl(ui),Ul=Hl(ci),Dl=Hl(pi);function Hl(e){return e&&e.__esModule?e:{default:e}}var $l=[["alert",mi.default],["alertdialog",bi.default],["application",yi.default],["article",vi.default],["banner",hi.default],["blockquote",gi.default],["button",Pi.default],["caption",Ci.default],["cell",wi.default],["checkbox",qi.default],["code",Ei.default],["columnheader",xi.default],["combobox",Oi.default],["complementary",Ri.default],["contentinfo",ji.default],["definition",Si.default],["deletion",Ai.default],["dialog",_i.default],["directory",Ti.default],["document",Mi.default],["emphasis",Ii.default],["feed",Bi.default],["figure",ki.default],["form",Fi.default],["generic",Ni.default],["grid",Li.default],["gridcell",Ui.default],["group",Di.default],["heading",Hi.default],["img",$i.default],["insertion",Wi.default],["link",zi.default],["list",Vi.default],["listbox",Gi.default],["listitem",Ji.default],["log",Qi.default],["main",Xi.default],["marquee",Ki.default],["math",Yi.default],["menu",Zi.default],["menubar",el.default],["menuitem",tl.default],["menuitemcheckbox",rl.default],["menuitemradio",nl.default],["meter",ol.default],["navigation",al.default],["none",il.default],["note",ll.default],["option",ul.default],["paragraph",sl.default],["presentation",cl.default],["progressbar",dl.default],["radio",pl.default],["radiogroup",fl.default],["region",ml.default],["row",bl.default],["rowgroup",yl.default],["rowheader",vl.default],["scrollbar",hl.default],["search",gl.default],["searchbox",Pl.default],["separator",Cl.default],["slider",wl.default],["spinbutton",ql.default],["status",El.default],["strong",xl.default],["subscript",Ol.default],["superscript",Rl.default],["switch",jl.default],["tab",Sl.default],["table",Al.default],["tablist",_l.default],["tabpanel",Tl.default],["term",Ml.default],["textbox",Il.default],["time",Bl.default],["timer",kl.default],["toolbar",Fl.default],["tooltip",Nl.default],["tree",Ll.default],["treegrid",Ul.default],["treeitem",Dl.default]];cn.default=$l;var Wl={},zl={};Object.defineProperty(zl,"__esModule",{value:!0}),zl.default=void 0;var Vl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};zl.default=Vl;var Gl={};Object.defineProperty(Gl,"__esModule",{value:!0}),Gl.default=void 0;var Jl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Gl.default=Jl;var Ql={};Object.defineProperty(Ql,"__esModule",{value:!0}),Ql.default=void 0;var Xl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ql.default=Xl;var Kl={};Object.defineProperty(Kl,"__esModule",{value:!0}),Kl.default=void 0;var Yl={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Kl.default=Yl;var Zl={};Object.defineProperty(Zl,"__esModule",{value:!0}),Zl.default=void 0;var eu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Zl.default=eu;var tu={};Object.defineProperty(tu,"__esModule",{value:!0}),tu.default=void 0;var ru={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};tu.default=ru;var nu={};Object.defineProperty(nu,"__esModule",{value:!0}),nu.default=void 0;var ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};nu.default=ou;var au={};Object.defineProperty(au,"__esModule",{value:!0}),au.default=void 0;var iu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};au.default=iu;var lu={};Object.defineProperty(lu,"__esModule",{value:!0}),lu.default=void 0;var uu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};lu.default=uu;var su={};Object.defineProperty(su,"__esModule",{value:!0}),su.default=void 0;var cu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};su.default=cu;var du={};Object.defineProperty(du,"__esModule",{value:!0}),du.default=void 0;var pu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};du.default=pu;var fu={};Object.defineProperty(fu,"__esModule",{value:!0}),fu.default=void 0;var mu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};fu.default=mu;var bu={};Object.defineProperty(bu,"__esModule",{value:!0}),bu.default=void 0;var yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bu.default=yu;var vu={};Object.defineProperty(vu,"__esModule",{value:!0}),vu.default=void 0;var hu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};vu.default=hu;var gu={};Object.defineProperty(gu,"__esModule",{value:!0}),gu.default=void 0;var Pu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};gu.default=Pu;var Cu={};Object.defineProperty(Cu,"__esModule",{value:!0}),Cu.default=void 0;var wu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Cu.default=wu;var qu={};Object.defineProperty(qu,"__esModule",{value:!0}),qu.default=void 0;var Eu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};qu.default=Eu;var xu={};Object.defineProperty(xu,"__esModule",{value:!0}),xu.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xu.default=Ou;var Ru={};Object.defineProperty(Ru,"__esModule",{value:!0}),Ru.default=void 0;var ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ru.default=ju;var Su={};Object.defineProperty(Su,"__esModule",{value:!0}),Su.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Su.default=Au;var _u={};Object.defineProperty(_u,"__esModule",{value:!0}),_u.default=void 0;var Tu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};_u.default=Tu;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Iu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Mu.default=Iu;var Bu={};Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.default=void 0;var ku={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bu.default=ku;var Fu={};Object.defineProperty(Fu,"__esModule",{value:!0}),Fu.default=void 0;var Nu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Fu.default=Nu;var Lu={};Object.defineProperty(Lu,"__esModule",{value:!0}),Lu.default=void 0;var Uu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Lu.default=Uu;var Du={};Object.defineProperty(Du,"__esModule",{value:!0}),Du.default=void 0;var Hu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Du.default=Hu;var $u={};Object.defineProperty($u,"__esModule",{value:!0}),$u.default=void 0;var Wu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};$u.default=Wu;var zu={};Object.defineProperty(zu,"__esModule",{value:!0}),zu.default=void 0;var Vu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};zu.default=Vu;var Gu={};Object.defineProperty(Gu,"__esModule",{value:!0}),Gu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};Gu.default=Ju;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Xu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};Qu.default=Xu;var Ku={};Object.defineProperty(Ku,"__esModule",{value:!0}),Ku.default=void 0;var Yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Ku.default=Yu;var Zu={};Object.defineProperty(Zu,"__esModule",{value:!0}),Zu.default=void 0;var es={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Zu.default=es;var ts={};Object.defineProperty(ts,"__esModule",{value:!0}),ts.default=void 0;var rs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ts.default=rs;var ns={};Object.defineProperty(ns,"__esModule",{value:!0}),ns.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ns.default=os;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};as.default=is;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ls.default=us;var ss={};Object.defineProperty(ss,"__esModule",{value:!0}),ss.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};ss.default=cs;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};ds.default=ps;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var ms={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};fs.default=ms,Object.defineProperty(Wl,"__esModule",{value:!0}),Wl.default=void 0;var bs=ec(zl),ys=ec(Gl),vs=ec(Ql),hs=ec(Kl),gs=ec(Zl),Ps=ec(tu),Cs=ec(nu),ws=ec(au),qs=ec(lu),Es=ec(su),xs=ec(du),Os=ec(fu),Rs=ec(bu),js=ec(vu),Ss=ec(gu),As=ec(Cu),_s=ec(qu),Ts=ec(xu),Ms=ec(Ru),Is=ec(Su),Bs=ec(_u),ks=ec(Mu),Fs=ec(Bu),Ns=ec(Fu),Ls=ec(Lu),Us=ec(Du),Ds=ec($u),Hs=ec(zu),$s=ec(Gu),Ws=ec(Qu),zs=ec(Ku),Vs=ec(Zu),Gs=ec(ts),Js=ec(ns),Qs=ec(as),Xs=ec(ls),Ks=ec(ss),Ys=ec(ds),Zs=ec(fs);function ec(e){return e&&e.__esModule?e:{default:e}}var tc=[["doc-abstract",bs.default],["doc-acknowledgments",ys.default],["doc-afterword",vs.default],["doc-appendix",hs.default],["doc-backlink",gs.default],["doc-biblioentry",Ps.default],["doc-bibliography",Cs.default],["doc-biblioref",ws.default],["doc-chapter",qs.default],["doc-colophon",Es.default],["doc-conclusion",xs.default],["doc-cover",Os.default],["doc-credit",Rs.default],["doc-credits",js.default],["doc-dedication",Ss.default],["doc-endnote",As.default],["doc-endnotes",_s.default],["doc-epigraph",Ts.default],["doc-epilogue",Ms.default],["doc-errata",Is.default],["doc-example",Bs.default],["doc-footnote",ks.default],["doc-foreword",Fs.default],["doc-glossary",Ns.default],["doc-glossref",Ls.default],["doc-index",Us.default],["doc-introduction",Ds.default],["doc-noteref",Hs.default],["doc-notice",$s.default],["doc-pagebreak",Ws.default],["doc-pagelist",zs.default],["doc-part",Vs.default],["doc-preface",Gs.default],["doc-prologue",Js.default],["doc-pullquote",Qs.default],["doc-qna",Xs.default],["doc-subtitle",Ks.default],["doc-tip",Ys.default],["doc-toc",Zs.default]];Wl.default=tc;var rc={},nc={};Object.defineProperty(nc,"__esModule",{value:!0}),nc.default=void 0;var oc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};nc.default=oc;var ac={};Object.defineProperty(ac,"__esModule",{value:!0}),ac.default=void 0;var ic={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};ac.default=ic;var lc={};Object.defineProperty(lc,"__esModule",{value:!0}),lc.default=void 0;var uc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};lc.default=uc,Object.defineProperty(rc,"__esModule",{value:!0}),rc.default=void 0;var sc=pc(nc),cc=pc(ac),dc=pc(lc);function pc(e){return e&&e.__esModule?e:{default:e}}var fc=[["graphics-document",sc.default],["graphics-object",cc.default],["graphics-symbol",dc.default]];rc.default=fc,Object.defineProperty(qr,"__esModule",{value:!0}),qr.default=void 0;var mc=gc(Er),bc=gc(cn),yc=gc(Wl),vc=gc(rc),hc=gc(nr);function gc(e){return e&&e.__esModule?e:{default:e}}function Pc(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Cc(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=qc(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}function wc(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||qc(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function qc(e,t){if(e){if("string"==typeof e)return Ec(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Ec(e,t):void 0}}function Ec(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var xc=[].concat(mc.default,bc.default,yc.default,vc.default);xc.forEach((function(e){var t,r=wc(e,2)[1],n=Cc(r.superClass);try{for(n.s();!(t=n.n()).done;){var o,a=Cc(t.value);try{var i=function(){var e=o.value,t=xc.find((function(t){return wc(t,1)[0]===e}));if(t)for(var n=t[1],a=0,i=Object.keys(n.props);a<i.length;a++){var l=i[a];Object.prototype.hasOwnProperty.call(r.props,l)||Object.assign(r.props,Pc({},l,n.props[l]))}};for(a.s();!(o=a.n()).done;)i()}catch(e){a.e(e)}finally{a.f()}}}catch(e){n.e(e)}finally{n.f()}}));var Oc={entries:function(){return xc},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=Cc(xc);try{for(n.s();!(t=n.n()).done;){var o=wc(t.value,2),a=o[0],i=o[1];e.call(r,i,a,xc)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=xc.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Oc.get(e)},keys:function(){return xc.map((function(e){return wc(e,1)[0]}))},values:function(){return xc.map((function(e){return wc(e,2)[1]}))}},Rc=(0,hc.default)(Oc,Oc.entries());qr.default=Rc;var jc,Sc,Ac={},_c=Object.prototype.toString,Tc=function(e){var t=_c.call(e),r="[object Arguments]"===t;return r||(r="[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Function]"===_c.call(e.callee)),r};var Mc=Array.prototype.slice,Ic=Tc,Bc=Object.keys,kc=Bc?function(e){return Bc(e)}:function(){if(Sc)return jc;var e;if(Sc=1,!Object.keys){var t=Object.prototype.hasOwnProperty,r=Object.prototype.toString,n=Object.prototype.propertyIsEnumerable,o=!n.call({toString:null},"toString"),a=n.call((function(){}),"prototype"),i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],l=function(e){var t=e.constructor;return t&&t.prototype===e},u={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},s=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!u["$"+e]&&t.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{l(window[e])}catch(e){return!0}}catch(e){return!0}return!1}();e=function(e){var n=null!==e&&"object"==typeof e,u="[object Function]"===r.call(e),c=Tc(e),d=n&&"[object String]"===r.call(e),p=[];if(!n&&!u&&!c)throw new TypeError("Object.keys called on a non-object");if(d&&e.length>0&&!t.call(e,0))for(var f=0;f<e.length;++f)p.push(String(f));if(c&&e.length>0)for(var m=0;m<e.length;++m)p.push(String(m));else for(var b in e)a&&u&&"prototype"===b||!t.call(e,b)||p.push(String(b));if(o)for(var y=function(e){if("undefined"==typeof window||!s)return l(e);try{return l(e)}catch(e){return!1}}(e),v=0;v<i.length;++v)y&&"constructor"===i[v]||!t.call(e,i[v])||p.push(i[v]);return p}}return jc=e}(),Fc=Object.keys;kc.shim=function(){if(Object.keys){var e=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);e||(Object.keys=function(e){return Ic(e)?Fc(Mc.call(e)):Fc(e)})}else Object.keys=kc;return Object.keys||kc};var Nc,Lc,Uc=kc,Dc=Error,Hc=EvalError,$c=RangeError,Wc=ReferenceError,zc=SyntaxError,Vc=TypeError,Gc=URIError;function Jc(){return Lc||(Lc=1,Nc=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}),Nc}var Qc,Xc="undefined"!=typeof Symbol&&Symbol,Kc=Jc(),Yc=function(){return"function"==typeof Xc&&("function"==typeof Symbol&&("symbol"==typeof Xc("foo")&&("symbol"==typeof Symbol("bar")&&Kc())))},Zc={__proto__:null,foo:{}},ed=Object,td=Object.prototype.toString,rd=Math.max,nd=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var o=0;o<t.length;o+=1)r[o+e.length]=t[o];return r},od=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==td.apply(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,n=function(e,t){for(var r=[],n=t||0,o=0;n<e.length;n+=1,o+=1)r[o]=e[n];return r}(arguments,1),o=rd(0,t.length-n.length),a=[],i=0;i<o;i++)a[i]="$"+i;if(r=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(a,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var o=t.apply(this,nd(n,arguments));return Object(o)===o?o:this}return t.apply(e,nd(n,arguments))})),t.prototype){var l=function(){};l.prototype=t.prototype,r.prototype=new l,l.prototype=null}return r},ad=Function.prototype.bind||od,id=Function.prototype.call,ld=Object.prototype.hasOwnProperty,ud=ad.call(id,ld),sd=Dc,cd=Hc,dd=$c,pd=Wc,fd=zc,md=Vc,bd=Gc,yd=Function,vd=function(e){try{return yd('"use strict"; return ('+e+").constructor;")()}catch(e){}},hd=Object.getOwnPropertyDescriptor;if(hd)try{hd({},"")}catch(e){hd=null}var gd=function(){throw new md},Pd=hd?function(){try{return gd}catch(e){try{return hd(arguments,"callee").get}catch(e){return gd}}}():gd,Cd=Yc(),wd={__proto__:Zc}.foo===Zc.foo&&!(Zc instanceof ed),qd=Object.getPrototypeOf||(wd?function(e){return e.__proto__}:null),Ed={},xd="undefined"!=typeof Uint8Array&&qd?qd(Uint8Array):Qc,Od={__proto__:null,"%AggregateError%":"undefined"==typeof AggregateError?Qc:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?Qc:ArrayBuffer,"%ArrayIteratorPrototype%":Cd&&qd?qd([][Symbol.iterator]()):Qc,"%AsyncFromSyncIteratorPrototype%":Qc,"%AsyncFunction%":Ed,"%AsyncGenerator%":Ed,"%AsyncGeneratorFunction%":Ed,"%AsyncIteratorPrototype%":Ed,"%Atomics%":"undefined"==typeof Atomics?Qc:Atomics,"%BigInt%":"undefined"==typeof BigInt?Qc:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?Qc:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?Qc:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?Qc:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":sd,"%eval%":eval,"%EvalError%":cd,"%Float32Array%":"undefined"==typeof Float32Array?Qc:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?Qc:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?Qc:FinalizationRegistry,"%Function%":yd,"%GeneratorFunction%":Ed,"%Int8Array%":"undefined"==typeof Int8Array?Qc:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?Qc:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?Qc:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Cd&&qd?qd(qd([][Symbol.iterator]())):Qc,"%JSON%":"object"==typeof JSON?JSON:Qc,"%Map%":"undefined"==typeof Map?Qc:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&Cd&&qd?qd((new Map)[Symbol.iterator]()):Qc,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?Qc:Promise,"%Proxy%":"undefined"==typeof Proxy?Qc:Proxy,"%RangeError%":dd,"%ReferenceError%":pd,"%Reflect%":"undefined"==typeof Reflect?Qc:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?Qc:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&Cd&&qd?qd((new Set)[Symbol.iterator]()):Qc,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?Qc:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Cd&&qd?qd(""[Symbol.iterator]()):Qc,"%Symbol%":Cd?Symbol:Qc,"%SyntaxError%":fd,"%ThrowTypeError%":Pd,"%TypedArray%":xd,"%TypeError%":md,"%Uint8Array%":"undefined"==typeof Uint8Array?Qc:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?Qc:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?Qc:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?Qc:Uint32Array,"%URIError%":bd,"%WeakMap%":"undefined"==typeof WeakMap?Qc:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?Qc:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?Qc:WeakSet};if(qd)try{null.error}catch(e){var Rd=qd(qd(e));Od["%Error.prototype%"]=Rd}var jd,Sd,Ad=function e(t){var r;if("%AsyncFunction%"===t)r=vd("async function () {}");else if("%GeneratorFunction%"===t)r=vd("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=vd("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&qd&&(r=qd(o.prototype))}return Od[t]=r,r},_d={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Td=ad,Md=ud,Id=Td.call(Function.call,Array.prototype.concat),Bd=Td.call(Function.apply,Array.prototype.splice),kd=Td.call(Function.call,String.prototype.replace),Fd=Td.call(Function.call,String.prototype.slice),Nd=Td.call(Function.call,RegExp.prototype.exec),Ld=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,Ud=/\\(\\)?/g,Dd=function(e,t){var r,n=e;if(Md(_d,n)&&(n="%"+(r=_d[n])[0]+"%"),Md(Od,n)){var o=Od[n];if(o===Ed&&(o=Ad(n)),void 0===o&&!t)throw new md("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new fd("intrinsic "+e+" does not exist!")},Hd=function(e,t){if("string"!=typeof e||0===e.length)throw new md("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new md('"allowMissing" argument must be a boolean');if(null===Nd(/^%?[^%]*%?$/,e))throw new fd("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=Fd(e,0,1),r=Fd(e,-1);if("%"===t&&"%"!==r)throw new fd("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new fd("invalid intrinsic syntax, expected opening `%`");var n=[];return kd(e,Ld,(function(e,t,r,o){n[n.length]=r?kd(o,Ud,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",o=Dd("%"+n+"%",t),a=o.name,i=o.value,l=!1,u=o.alias;u&&(n=u[0],Bd(r,Id([0,1],u)));for(var s=1,c=!0;s<r.length;s+=1){var d=r[s],p=Fd(d,0,1),f=Fd(d,-1);if(('"'===p||"'"===p||"`"===p||'"'===f||"'"===f||"`"===f)&&p!==f)throw new fd("property names with quotes must have matching quotes");if("constructor"!==d&&c||(l=!0),Md(Od,a="%"+(n+="."+d)+"%"))i=Od[a];else if(null!=i){if(!(d in i)){if(!t)throw new md("base intrinsic for "+e+" exists, but the property is not available.");return}if(hd&&s+1>=r.length){var m=hd(i,d);i=(c=!!m)&&"get"in m&&!("originalValue"in m.get)?m.get:i[d]}else c=Md(i,d),i=i[d];c&&!l&&(Od[a]=i)}}return i};function $d(){if(Sd)return jd;Sd=1;var e=Hd("%Object.defineProperty%",!0)||!1;if(e)try{e({},"a",{value:1})}catch(t){e=!1}return jd=e}var Wd=Hd("%Object.getOwnPropertyDescriptor%",!0);if(Wd)try{Wd([],"length")}catch(e){Wd=null}var zd=Wd,Vd=$d(),Gd=zc,Jd=Vc,Qd=zd,Xd=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Jd("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new Jd("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new Jd("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new Jd("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new Jd("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new Jd("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,a=arguments.length>5?arguments[5]:null,i=arguments.length>6&&arguments[6],l=!!Qd&&Qd(e,t);if(Vd)Vd(e,t,{configurable:null===a&&l?l.configurable:!a,enumerable:null===n&&l?l.enumerable:!n,value:r,writable:null===o&&l?l.writable:!o});else{if(!i&&(n||o||a))throw new Gd("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}},Kd=$d(),Yd=function(){return!!Kd};Yd.hasArrayLengthDefineBug=function(){if(!Kd)return null;try{return 1!==Kd([],"length",{value:1}).length}catch(e){return!0}};var Zd=Yd,ep=Uc,tp="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),rp=Object.prototype.toString,np=Array.prototype.concat,op=Xd,ap=Zd(),ip=function(e,t,r,n){if(t in e)if(!0===n){if(e[t]===r)return}else if("function"!=typeof(o=n)||"[object Function]"!==rp.call(o)||!n())return;var o;ap?op(e,t,r,!0):op(e,t,r)},lp=function(e,t){var r=arguments.length>2?arguments[2]:{},n=ep(t);tp&&(n=np.call(n,Object.getOwnPropertySymbols(t)));for(var o=0;o<n.length;o+=1)ip(e,n[o],t[n[o]],r[n[o]])};lp.supportsDescriptors=!!ap;var up=lp,sp={exports:{}},cp=Hd,dp=Xd,pp=Zd(),fp=zd,mp=Vc,bp=cp("%Math.floor%");!function(e){var t=ad,r=Hd,n=r("%Function.prototype.apply%"),o=r("%Function.prototype.call%"),a=r("%Reflect.apply%",!0)||t.call(o,n),i=$d(),l=r("%Math.max%");e.exports=function(e){if("function"!=typeof e)throw new Vc("a function is required");return function(e,t){if("function"!=typeof e)throw new mp("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||bp(t)!==t)throw new mp("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in e&&fp){var a=fp(e,"length");a&&!a.configurable&&(n=!1),a&&!a.writable&&(o=!1)}return(n||o||!r)&&(pp?dp(e,"length",t,!0,!0):dp(e,"length",t)),e}(a(t,o,arguments),1+l(0,e.length-(arguments.length-1)),!0)};var u=function(){return a(t,n,arguments)};i?i(e.exports,"apply",{value:u}):e.exports.apply=u}(sp);var yp=Hd,vp=sp.exports,hp=vp(yp("String.prototype.indexOf")),gp=function(e,t){var r=yp(e,!!t);return"function"==typeof r&&hp(e,".prototype.")>-1?vp(r):r},Pp=Uc,Cp=Jc()(),wp=gp,qp=Object,Ep=wp("Array.prototype.push"),xp=wp("Object.prototype.propertyIsEnumerable"),Op=Cp?Object.getOwnPropertySymbols:null,Rp=function(e){if(null==e)throw new TypeError("target must be an object");var t=qp(e);if(1===arguments.length)return t;for(var r=1;r<arguments.length;++r){var n=qp(arguments[r]),o=Pp(n),a=Cp&&(Object.getOwnPropertySymbols||Op);if(a)for(var i=a(n),l=0;l<i.length;++l){var u=i[l];xp(n,u)&&Ep(o,u)}for(var s=0;s<o.length;++s){var c=o[s];if(xp(n,c)){var d=n[c];t[c]=d}}}return t},jp=Rp,Sp=function(){return Object.assign?function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",t=e.split(""),r={},n=0;n<t.length;++n)r[t[n]]=t[n];var o=Object.assign({},r),a="";for(var i in o)a+=i;return e!==a}()||function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}return!1}()?jp:Object.assign:jp},Ap=up,_p=Sp,Tp=up,Mp=Rp,Ip=Sp,Bp=function(){var e=_p();return Ap(Object,{assign:e},{assign:function(){return Object.assign!==e}}),e},kp=sp.exports.apply(Ip()),Fp=function(){return kp(Object,arguments)};Tp(Fp,{getPolyfill:Ip,implementation:Mp,shim:Bp});var Np=Fp,Lp=function(){return"string"==typeof function(){}.name},Up=Object.getOwnPropertyDescriptor;if(Up)try{Up([],"length")}catch(e){Up=null}Lp.functionsHaveConfigurableNames=function(){if(!Lp()||!Up)return!1;var e=Up((function(){}),"name");return!!e&&!!e.configurable};var Dp=Function.prototype.bind;Lp.boundFunctionsHaveNames=function(){return Lp()&&"function"==typeof Dp&&""!==function(){}.bind().name};var Hp=Lp,$p=Xd,Wp=Zd(),zp=Hp.functionsHaveConfigurableNames(),Vp=Vc,Gp=function(e,t){if("function"!=typeof e)throw new Vp("`fn` is not a function");return arguments.length>2&&!!arguments[2]&&!zp||(Wp?$p(e,"name",t,!0,!0):$p(e,"name",t)),e},Jp=Vc,Qp=Object,Xp=Gp((function(){if(null==this||this!==Qp(this))throw new Jp("RegExp.prototype.flags getter called on non-object");var e="";return this.hasIndices&&(e+="d"),this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),this.dotAll&&(e+="s"),this.unicode&&(e+="u"),this.unicodeSets&&(e+="v"),this.sticky&&(e+="y"),e}),"get flags",!0),Kp=Xp,Yp=up.supportsDescriptors,Zp=Object.getOwnPropertyDescriptor,ef=function(){if(Yp&&"gim"===/a/gim.flags){var e=Zp(RegExp.prototype,"flags");if(e&&"function"==typeof e.get&&"boolean"==typeof RegExp.prototype.dotAll&&"boolean"==typeof RegExp.prototype.hasIndices){var t="",r={};if(Object.defineProperty(r,"hasIndices",{get:function(){t+="d"}}),Object.defineProperty(r,"sticky",{get:function(){t+="y"}}),"dy"===t)return e.get}}return Kp},tf=up.supportsDescriptors,rf=ef,nf=Object.getOwnPropertyDescriptor,of=Object.defineProperty,af=TypeError,lf=Object.getPrototypeOf,uf=/a/,sf=up,cf=Xp,df=ef,pf=function(){if(!tf||!lf)throw new af("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var e=rf(),t=lf(uf),r=nf(t,"flags");return r&&r.get===e||of(t,"flags",{configurable:!0,enumerable:!1,get:e}),e},ff=(0,sp.exports)(df());sf(ff,{getPolyfill:df,implementation:cf,shim:pf});var mf=ff,bf={exports:{}},yf=Jc(),vf=function(){return yf()&&!!Symbol.toStringTag},hf=vf(),gf=gp("Object.prototype.toString"),Pf=function(e){return!(hf&&e&&"object"==typeof e&&Symbol.toStringTag in e)&&"[object Arguments]"===gf(e)},Cf=function(e){return!!Pf(e)||null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Array]"!==gf(e)&&"[object Function]"===gf(e.callee)},wf=function(){return Pf(arguments)}();Pf.isLegacyArguments=Cf;var qf=wf?Pf:Cf,Ef=f(Object.freeze({__proto__:null,default:{}})),xf="function"==typeof Map&&Map.prototype,Of=Object.getOwnPropertyDescriptor&&xf?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Rf=xf&&Of&&"function"==typeof Of.get?Of.get:null,jf=xf&&Map.prototype.forEach,Sf="function"==typeof Set&&Set.prototype,Af=Object.getOwnPropertyDescriptor&&Sf?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,_f=Sf&&Af&&"function"==typeof Af.get?Af.get:null,Tf=Sf&&Set.prototype.forEach,Mf="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,If="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,Bf="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,kf=Boolean.prototype.valueOf,Ff=Object.prototype.toString,Nf=Function.prototype.toString,Lf=String.prototype.match,Uf=String.prototype.slice,Df=String.prototype.replace,Hf=String.prototype.toUpperCase,$f=String.prototype.toLowerCase,Wf=RegExp.prototype.test,zf=Array.prototype.concat,Vf=Array.prototype.join,Gf=Array.prototype.slice,Jf=Math.floor,Qf="function"==typeof BigInt?BigInt.prototype.valueOf:null,Xf=Object.getOwnPropertySymbols,Kf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,Yf="function"==typeof Symbol&&"object"==typeof Symbol.iterator,Zf="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===Yf||"symbol")?Symbol.toStringTag:null,em=Object.prototype.propertyIsEnumerable,tm=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function rm(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||Wf.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-Jf(-e):Jf(e);if(n!==e){var o=String(n),a=Uf.call(t,o.length+1);return Df.call(o,r,"$&_")+"."+Df.call(Df.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return Df.call(t,r,"$&_")}var nm=Ef,om=nm.custom,am=cm(om)?om:null;function im(e,t,r){var n="double"===(r.quoteStyle||t)?'"':"'";return n+e+n}function lm(e){return Df.call(String(e),/"/g,"&quot;")}function um(e){return!("[object Array]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}function sm(e){return!("[object RegExp]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}function cm(e){if(Yf)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!Kf)return!1;try{return Kf.call(e),!0}catch(e){}return!1}var dm=Object.prototype.hasOwnProperty||function(e){return e in this};function pm(e,t){return dm.call(e,t)}function fm(e){return Ff.call(e)}function mm(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function bm(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return bm(Uf.call(e,0,t.maxStringLength),t)+n}return im(Df.call(Df.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,ym),"single",t)}function ym(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+Hf.call(t.toString(16))}function vm(e){return"Object("+e+")"}function hm(e){return e+" { ? }"}function gm(e,t,r,n){return e+" ("+t+") {"+(n?Pm(r,n):Vf.call(r,", "))+"}"}function Pm(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+Vf.call(e,","+r)+"\n"+t.prev}function Cm(e,t){var r=um(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=pm(e,o)?t(e[o],e):""}var a,i="function"==typeof Xf?Xf(e):[];if(Yf){a={};for(var l=0;l<i.length;l++)a["$"+i[l]]=i[l]}for(var u in e)pm(e,u)&&(r&&String(Number(u))===u&&u<e.length||Yf&&a["$"+u]instanceof Symbol||(Wf.call(/[^\w$]/,u)?n.push(t(u,e)+": "+t(e[u],e)):n.push(u+": "+t(e[u],e))));if("function"==typeof Xf)for(var s=0;s<i.length;s++)em.call(e,i[s])&&n.push("["+t(i[s])+"]: "+t(e[i[s]],e));return n}var wm=Hd,qm=gp,Em=function e(t,r,n,o){var a=r||{};if(pm(a,"quoteStyle")&&"single"!==a.quoteStyle&&"double"!==a.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(pm(a,"maxStringLength")&&("number"==typeof a.maxStringLength?a.maxStringLength<0&&a.maxStringLength!==1/0:null!==a.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=!pm(a,"customInspect")||a.customInspect;if("boolean"!=typeof i&&"symbol"!==i)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(pm(a,"indent")&&null!==a.indent&&"\t"!==a.indent&&!(parseInt(a.indent,10)===a.indent&&a.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(pm(a,"numericSeparator")&&"boolean"!=typeof a.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var l=a.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return bm(t,a);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var u=String(t);return l?rm(t,u):u}if("bigint"==typeof t){var s=String(t)+"n";return l?rm(t,s):s}var c=void 0===a.depth?5:a.depth;if(void 0===n&&(n=0),n>=c&&c>0&&"object"==typeof t)return um(t)?"[Array]":"[Object]";var d=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=Vf.call(Array(e.indent+1)," ")}return{base:r,prev:Vf.call(Array(t+1),r)}}(a,n);if(void 0===o)o=[];else if(mm(o,t)>=0)return"[Circular]";function f(t,r,i){if(r&&(o=Gf.call(o)).push(r),i){var l={depth:a.depth};return pm(a,"quoteStyle")&&(l.quoteStyle=a.quoteStyle),e(t,l,n+1,o)}return e(t,a,n+1,o)}if("function"==typeof t&&!sm(t)){var m=function(e){if(e.name)return e.name;var t=Lf.call(Nf.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),b=Cm(t,f);return"[Function"+(m?": "+m:" (anonymous)")+"]"+(b.length>0?" { "+Vf.call(b,", ")+" }":"")}if(cm(t)){var y=Yf?Df.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):Kf.call(t);return"object"!=typeof t||Yf?y:vm(y)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var v="<"+$f.call(String(t.nodeName)),h=t.attributes||[],g=0;g<h.length;g++)v+=" "+h[g].name+"="+im(lm(h[g].value),"double",a);return v+=">",t.childNodes&&t.childNodes.length&&(v+="..."),v+="</"+$f.call(String(t.nodeName))+">"}if(um(t)){if(0===t.length)return"[]";var P=Cm(t,f);return d&&!function(e){for(var t=0;t<e.length;t++)if(mm(e[t],"\n")>=0)return!1;return!0}(P)?"["+Pm(P,d)+"]":"[ "+Vf.call(P,", ")+" ]"}if(function(e){return!("[object Error]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}(t)){var C=Cm(t,f);return"cause"in Error.prototype||!("cause"in t)||em.call(t,"cause")?0===C.length?"["+String(t)+"]":"{ ["+String(t)+"] "+Vf.call(C,", ")+" }":"{ ["+String(t)+"] "+Vf.call(zf.call("[cause]: "+f(t.cause),C),", ")+" }"}if("object"==typeof t&&i){if(am&&"function"==typeof t[am]&&nm)return nm(t,{depth:c-n});if("symbol"!==i&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!Rf||!e||"object"!=typeof e)return!1;try{Rf.call(e);try{_f.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var w=[];return jf&&jf.call(t,(function(e,r){w.push(f(r,t,!0)+" => "+f(e,t))})),gm("Map",Rf.call(t),w,d)}if(function(e){if(!_f||!e||"object"!=typeof e)return!1;try{_f.call(e);try{Rf.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var q=[];return Tf&&Tf.call(t,(function(e){q.push(f(e,t))})),gm("Set",_f.call(t),q,d)}if(function(e){if(!Mf||!e||"object"!=typeof e)return!1;try{Mf.call(e,Mf);try{If.call(e,If)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return hm("WeakMap");if(function(e){if(!If||!e||"object"!=typeof e)return!1;try{If.call(e,If);try{Mf.call(e,Mf)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return hm("WeakSet");if(function(e){if(!Bf||!e||"object"!=typeof e)return!1;try{return Bf.call(e),!0}catch(e){}return!1}(t))return hm("WeakRef");if(function(e){return!("[object Number]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}(t))return vm(f(Number(t)));if(function(e){if(!e||"object"!=typeof e||!Qf)return!1;try{return Qf.call(e),!0}catch(e){}return!1}(t))return vm(f(Qf.call(t)));if(function(e){return!("[object Boolean]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}(t))return vm(kf.call(t));if(function(e){return!("[object String]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}(t))return vm(f(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===p)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==fm(e)||Zf&&"object"==typeof e&&Zf in e)}(t)&&!sm(t)){var E=Cm(t,f),x=tm?tm(t)===Object.prototype:t instanceof Object||t.constructor===Object,O=t instanceof Object?"":"null prototype",R=!x&&Zf&&Object(t)===t&&Zf in t?Uf.call(fm(t),8,-1):O?"Object":"",j=(x||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(R||O?"["+Vf.call(zf.call([],R||[],O||[]),": ")+"] ":"");return 0===E.length?j+"{}":d?j+"{"+Pm(E,d)+"}":j+"{ "+Vf.call(E,", ")+" }"}return String(t)},xm=Vc,Om=wm("%WeakMap%",!0),Rm=wm("%Map%",!0),jm=qm("WeakMap.prototype.get",!0),Sm=qm("WeakMap.prototype.set",!0),Am=qm("WeakMap.prototype.has",!0),_m=qm("Map.prototype.get",!0),Tm=qm("Map.prototype.set",!0),Mm=qm("Map.prototype.has",!0),Im=function(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r,r},Bm=function(){var e,t,r,n={assert:function(e){if(!n.has(e))throw new xm("Side channel does not contain "+Em(e))},get:function(n){if(Om&&n&&("object"==typeof n||"function"==typeof n)){if(e)return jm(e,n)}else if(Rm){if(t)return _m(t,n)}else if(r)return function(e,t){var r=Im(e,t);return r&&r.value}(r,n)},has:function(n){if(Om&&n&&("object"==typeof n||"function"==typeof n)){if(e)return Am(e,n)}else if(Rm){if(t)return Mm(t,n)}else if(r)return function(e,t){return!!Im(e,t)}(r,n);return!1},set:function(n,o){Om&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new Om),Sm(e,n,o)):Rm?(t||(t=new Rm),Tm(t,n,o)):(r||(r={key:{},next:null}),function(e,t,r){var n=Im(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(r,n,o))}};return n},km=ud,Fm=Bm(),Nm=Vc,Lm={assert:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Nm("`O` is not an object");if("string"!=typeof t)throw new Nm("`slot` must be a string");if(Fm.assert(e),!Lm.has(e,t))throw new Nm("`"+t+"` is not present on `O`")},get:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Nm("`O` is not an object");if("string"!=typeof t)throw new Nm("`slot` must be a string");var r=Fm.get(e);return r&&r["$"+t]},has:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Nm("`O` is not an object");if("string"!=typeof t)throw new Nm("`slot` must be a string");var r=Fm.get(e);return!!r&&km(r,"$"+t)},set:function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new Nm("`O` is not an object");if("string"!=typeof t)throw new Nm("`slot` must be a string");var n=Fm.get(e);n||(n={},Fm.set(e,n)),n["$"+t]=r}};Object.freeze&&Object.freeze(Lm);var Um,Dm=Lm,Hm=SyntaxError,$m="object"==typeof StopIteration?StopIteration:null,Wm={}.toString,zm=Array.isArray||function(e){return"[object Array]"==Wm.call(e)},Vm=String.prototype.valueOf,Gm=Object.prototype.toString,Jm=vf(),Qm=function(e){return"string"==typeof e||"object"==typeof e&&(Jm?function(e){try{return Vm.call(e),!0}catch(e){return!1}}(e):"[object String]"===Gm.call(e))},Xm="function"==typeof Map&&Map.prototype?Map:null,Km="function"==typeof Set&&Set.prototype?Set:null;Xm||(Um=function(){return!1});var Ym=Xm?Map.prototype.has:null,Zm=Km?Set.prototype.has:null;Um||Ym||(Um=function(){return!1});var eb,tb=Um||function(e){if(!e||"object"!=typeof e)return!1;try{if(Ym.call(e),Zm)try{Zm.call(e)}catch(e){return!0}return e instanceof Xm}catch(e){}return!1},rb="function"==typeof Map&&Map.prototype?Map:null,nb="function"==typeof Set&&Set.prototype?Set:null;nb||(eb=function(){return!1});var ob=rb?Map.prototype.has:null,ab=nb?Set.prototype.has:null;eb||ab||(eb=function(){return!1});var ib=eb||function(e){if(!e||"object"!=typeof e)return!1;try{if(ab.call(e),ob)try{ob.call(e)}catch(e){return!0}return e instanceof nb}catch(e){}return!1},lb=qf,ub=function(e){if(!$m)throw new Hm("this environment lacks StopIteration");Dm.set(e,"[[Done]]",!1);var t={next:function(){var e=Dm.get(this,"[[Iterator]]"),t=Dm.get(e,"[[Done]]");try{return{done:t,value:t?void 0:e.next()}}catch(t){if(Dm.set(e,"[[Done]]",!0),t!==$m)throw t;return{done:!0,value:void 0}}}};return Dm.set(t,"[[Iterator]]",e),t};if(Yc()||Jc()()){var sb=Symbol.iterator;bf.exports=function(e){return null!=e&&void 0!==e[sb]?e[sb]():lb(e)?Array.prototype[sb].call(e):void 0}}else{var cb=zm,db=Qm,pb=Hd,fb=pb("%Map%",!0),mb=pb("%Set%",!0),bb=gp,yb=bb("Array.prototype.push"),vb=bb("String.prototype.charCodeAt"),hb=bb("String.prototype.slice"),gb=function(e){var t=0;return{next:function(){var r,n=t>=e.length;return n||(r=e[t],t+=1),{done:n,value:r}}}},Pb=function(e,t){if(cb(e)||lb(e))return gb(e);if(db(e)){var r=0;return{next:function(){var t=function(e,t){if(t+1>=e.length)return t+1;var r=vb(e,t);if(r<55296||r>56319)return t+1;var n=vb(e,t+1);return n<56320||n>57343?t+1:t+2}(e,r),n=hb(e,r,t);return r=t,{done:t>e.length,value:n}}}}return t&&void 0!==e["_es6-shim iterator_"]?e["_es6-shim iterator_"]():void 0};if(fb||mb){var Cb=tb,wb=ib,qb=bb("Map.prototype.forEach",!0),Eb=bb("Set.prototype.forEach",!0);if("undefined"==typeof process||!process.versions||!process.versions.node)var xb=bb("Map.prototype.iterator",!0),Ob=bb("Set.prototype.iterator",!0);var Rb=bb("Map.prototype.@@iterator",!0)||bb("Map.prototype._es6-shim iterator_",!0),jb=bb("Set.prototype.@@iterator",!0)||bb("Set.prototype._es6-shim iterator_",!0);bf.exports=function(e){return function(e){if(Cb(e)){if(xb)return ub(xb(e));if(Rb)return Rb(e);if(qb){var t=[];return qb(e,(function(e,r){yb(t,[r,e])})),gb(t)}}if(wb(e)){if(Ob)return ub(Ob(e));if(jb)return jb(e);if(Eb){var r=[];return Eb(e,(function(e){yb(r,e)})),gb(r)}}}(e)||Pb(e)}}else bf.exports=function(e){if(null!=e)return Pb(e,!0)}}var Sb=function(e){return e!=e},Ab=function(e,t){return 0===e&&0===t?1/e==1/t:e===t||!(!Sb(e)||!Sb(t))},_b=Ab,Tb=function(){return"function"==typeof Object.is?Object.is:_b},Mb=Tb,Ib=up,Bb=up,kb=Ab,Fb=Tb,Nb=function(){var e=Mb();return Ib(Object,{is:e},{is:function(){return Object.is!==e}}),e},Lb=(0,sp.exports)(Fb(),Object);Bb(Lb,{getPolyfill:Fb,implementation:kb,shim:Nb});var Ub,Db,Hb,$b,Wb=Lb,zb=sp.exports,Vb=gp,Gb=Hd("%ArrayBuffer%",!0),Jb=Vb("ArrayBuffer.prototype.byteLength",!0),Qb=Vb("Object.prototype.toString"),Xb=!!Gb&&!Jb&&new Gb(0).slice,Kb=!!Xb&&zb(Xb),Yb=Jb||Kb?function(e){if(!e||"object"!=typeof e)return!1;try{return Jb?Jb(e):Kb(e,0),!0}catch(e){return!1}}:Gb?function(e){return"[object ArrayBuffer]"===Qb(e)}:function(){return!1},Zb=Date.prototype.getDay,ey=Object.prototype.toString,ty=vf(),ry=gp,ny=vf();if(ny){Ub=ry("Object.prototype.hasOwnProperty"),Db=ry("RegExp.prototype.exec"),Hb={};var oy=function(){throw Hb};$b={toString:oy,valueOf:oy},"symbol"==typeof Symbol.toPrimitive&&($b[Symbol.toPrimitive]=oy)}var ay=ry("Object.prototype.toString"),iy=Object.getOwnPropertyDescriptor,ly=ny?function(e){if(!e||"object"!=typeof e)return!1;var t=iy(e,"lastIndex");if(!(t&&Ub(t,"value")))return!1;try{Db(e,$b)}catch(e){return e===Hb}}:function(e){return!(!e||"object"!=typeof e&&"function"!=typeof e)&&"[object RegExp]"===ay(e)},uy=gp("SharedArrayBuffer.prototype.byteLength",!0),sy=uy?function(e){if(!e||"object"!=typeof e)return!1;try{return uy(e),!0}catch(e){return!1}}:function(){return!1},cy=Number.prototype.toString,dy=Object.prototype.toString,py=vf(),fy=gp,my=fy("Boolean.prototype.toString"),by=fy("Object.prototype.toString"),yy=vf(),vy={exports:{}},hy=Object.prototype.toString;if(Yc()){var gy=Symbol.prototype.toString,Py=/^Symbol\(.*\)$/;vy.exports=function(e){if("symbol"==typeof e)return!0;if("[object Symbol]"!==hy.call(e))return!1;try{return function(e){return"symbol"==typeof e.valueOf()&&Py.test(gy.call(e))}(e)}catch(e){return!1}}}else vy.exports=function(e){return!1};var Cy={exports:{}},wy="undefined"!=typeof BigInt&&BigInt;if("function"==typeof wy&&"function"==typeof BigInt&&"bigint"==typeof wy(42)&&"bigint"==typeof BigInt(42)){var qy=BigInt.prototype.valueOf;Cy.exports=function(e){return null!=e&&"boolean"!=typeof e&&"string"!=typeof e&&"number"!=typeof e&&"symbol"!=typeof e&&"function"!=typeof e&&("bigint"==typeof e||function(e){try{return qy.call(e),!0}catch(e){}return!1}(e))}}else Cy.exports=function(e){return!1};var Ey,xy=Qm,Oy=function(e){return"number"==typeof e||"object"==typeof e&&(py?function(e){try{return cy.call(e),!0}catch(e){return!1}}(e):"[object Number]"===dy.call(e))},Ry=function(e){return"boolean"==typeof e||null!==e&&"object"==typeof e&&(yy&&Symbol.toStringTag in e?function(e){try{return my(e),!0}catch(e){return!1}}(e):"[object Boolean]"===by(e))},jy=vy.exports,Sy=Cy.exports,Ay="function"==typeof WeakMap&&WeakMap.prototype?WeakMap:null,_y="function"==typeof WeakSet&&WeakSet.prototype?WeakSet:null;Ay||(Ey=function(){return!1});var Ty=Ay?Ay.prototype.has:null,My=_y?_y.prototype.has:null;Ey||Ty||(Ey=function(){return!1});var Iy=Ey||function(e){if(!e||"object"!=typeof e)return!1;try{if(Ty.call(e,Ty),My)try{My.call(e,My)}catch(e){return!0}return e instanceof Ay}catch(e){}return!1},By={exports:{}},ky=gp,Fy=Hd("%WeakSet%",!0),Ny=ky("WeakSet.prototype.has",!0);if(Ny){var Ly=ky("WeakMap.prototype.has",!0);By.exports=function(e){if(!e||"object"!=typeof e)return!1;try{if(Ny(e,Ny),Ly)try{Ly(e,Ly)}catch(e){return!0}return e instanceof Fy}catch(e){}return!1}}else By.exports=function(){return!1};var Uy,Dy,Hy=tb,$y=ib,Wy=Iy,zy=By.exports,Vy=Function.prototype.toString,Gy="object"==typeof Reflect&&null!==Reflect&&Reflect.apply;if("function"==typeof Gy&&"function"==typeof Object.defineProperty)try{Uy=Object.defineProperty({},"length",{get:function(){throw Dy}}),Dy={},Gy((function(){throw 42}),null,Uy)}catch(e){e!==Dy&&(Gy=null)}else Gy=null;var Jy=/^\s*class\b/,Qy=function(e){try{var t=Vy.call(e);return Jy.test(t)}catch(e){return!1}},Xy=function(e){try{return!Qy(e)&&(Vy.call(e),!0)}catch(e){return!1}},Ky=Object.prototype.toString,Yy="function"==typeof Symbol&&!!Symbol.toStringTag,Zy=!(0 in[,]),ev=function(){return!1};if("object"==typeof document){var tv=document.all;Ky.call(tv)===Ky.call(document.all)&&(ev=function(e){if((Zy||!e)&&(void 0===e||"object"==typeof e))try{var t=Ky.call(e);return("[object HTMLAllCollection]"===t||"[object HTML document.all class]"===t||"[object HTMLCollection]"===t||"[object Object]"===t)&&null==e("")}catch(e){}return!1})}var rv=Gy?function(e){if(ev(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;try{Gy(e,null,Uy)}catch(e){if(e!==Dy)return!1}return!Qy(e)&&Xy(e)}:function(e){if(ev(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if(Yy)return Xy(e);if(Qy(e))return!1;var t=Ky.call(e);return!("[object Function]"!==t&&"[object GeneratorFunction]"!==t&&!/^\[object HTML/.test(t))&&Xy(e)},nv=rv,ov=Object.prototype.toString,av=Object.prototype.hasOwnProperty,iv=function(e,t,r){if(!nv(t))throw new TypeError("iterator must be a function");var n;arguments.length>=3&&(n=r),"[object Array]"===ov.call(e)?function(e,t,r){for(var n=0,o=e.length;n<o;n++)av.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n):"string"==typeof e?function(e,t,r){for(var n=0,o=e.length;n<o;n++)null==r?t(e.charAt(n),n,e):t.call(r,e.charAt(n),n,e)}(e,t,n):function(e,t,r){for(var n in e)av.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n)},lv=["Float32Array","Float64Array","Int8Array","Int16Array","Int32Array","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","BigInt64Array","BigUint64Array"],uv="undefined"==typeof globalThis?p:globalThis,sv=iv,cv=function(){for(var e=[],t=0;t<lv.length;t++)"function"==typeof uv[lv[t]]&&(e[e.length]=lv[t]);return e},dv=sp.exports,pv=gp,fv=zd,mv=pv("Object.prototype.toString"),bv=vf(),yv="undefined"==typeof globalThis?p:globalThis,vv=cv(),hv=pv("String.prototype.slice"),gv=Object.getPrototypeOf,Pv=pv("Array.prototype.indexOf",!0)||function(e,t){for(var r=0;r<e.length;r+=1)if(e[r]===t)return r;return-1},Cv={__proto__:null};sv(vv,bv&&fv&&gv?function(e){var t=new yv[e];if(Symbol.toStringTag in t){var r=gv(t),n=fv(r,Symbol.toStringTag);if(!n){var o=gv(r);n=fv(o,Symbol.toStringTag)}Cv["$"+e]=dv(n.get)}}:function(e){var t=new yv[e],r=t.slice||t.set;r&&(Cv["$"+e]=dv(r))});var wv=gp("ArrayBuffer.prototype.byteLength",!0),qv=Yb,Ev=Np,xv=gp,Ov=mf,Rv=Hd,jv=bf.exports,Sv=Bm,Av=Wb,_v=qf,Tv=zm,Mv=Yb,Iv=function(e){return"object"==typeof e&&null!==e&&(ty?function(e){try{return Zb.call(e),!0}catch(e){return!1}}(e):"[object Date]"===ey.call(e))},Bv=ly,kv=sy,Fv=Uc,Nv=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e?null:xy(e)?"String":Oy(e)?"Number":Ry(e)?"Boolean":jy(e)?"Symbol":Sy(e)?"BigInt":void 0},Lv=function(e){if(e&&"object"==typeof e){if(Hy(e))return"Map";if($y(e))return"Set";if(Wy(e))return"WeakMap";if(zy(e))return"WeakSet"}return!1},Uv=function(e){if(!e||"object"!=typeof e)return!1;if(!bv){var t=hv(mv(e),8,-1);return Pv(vv,t)>-1?t:"Object"===t&&function(e){var t=!1;return sv(Cv,(function(r,n){if(!t)try{r(e),t=hv(n,1)}catch(e){}})),t}(e)}return fv?function(e){var t=!1;return sv(Cv,(function(r,n){if(!t)try{"$"+r(e)===n&&(t=hv(n,1))}catch(e){}})),t}(e):null},Dv=function(e){return qv(e)?wv?wv(e):e.byteLength:NaN},Hv=xv("SharedArrayBuffer.prototype.byteLength",!0),$v=xv("Date.prototype.getTime"),Wv=Object.getPrototypeOf,zv=xv("Object.prototype.toString"),Vv=Rv("%Set%",!0),Gv=xv("Map.prototype.has",!0),Jv=xv("Map.prototype.get",!0),Qv=xv("Map.prototype.size",!0),Xv=xv("Set.prototype.add",!0),Kv=xv("Set.prototype.delete",!0),Yv=xv("Set.prototype.has",!0),Zv=xv("Set.prototype.size",!0);function eh(e,t,r,n){for(var o,a=jv(e);(o=a.next())&&!o.done;)if(ah(t,o.value,r,n))return Kv(e,o.value),!0;return!1}function th(e){return void 0===e?null:"object"!=typeof e?"symbol"!=typeof e&&("string"!=typeof e&&"number"!=typeof e||+e==+e):void 0}function rh(e,t,r,n,o,a){var i=th(r);if(null!=i)return i;var l=Jv(t,i),u=Ev({},o,{strict:!1});return!(void 0===l&&!Gv(t,i)||!ah(n,l,u,a))&&(!Gv(e,i)&&ah(n,l,u,a))}function nh(e,t,r){var n=th(r);return null!=n?n:Yv(t,n)&&!Yv(e,n)}function oh(e,t,r,n,o,a){for(var i,l,u=jv(e);(i=u.next())&&!i.done;)if(ah(r,l=i.value,o,a)&&ah(n,Jv(t,l),o,a))return Kv(e,l),!0;return!1}function ah(e,t,r,n){var o=r||{};if(o.strict?Av(e,t):e===t)return!0;if(Nv(e)!==Nv(t))return!1;if(!e||!t||"object"!=typeof e&&"object"!=typeof t)return o.strict?Av(e,t):e==t;var a,i=n.has(e),l=n.has(t);if(i&&l){if(n.get(e)===n.get(t))return!0}else a={};return i||n.set(e,a),l||n.set(t,a),function(e,t,r,n){var o,a;if(typeof e!=typeof t)return!1;if(null==e||null==t)return!1;if(zv(e)!==zv(t))return!1;if(_v(e)!==_v(t))return!1;var i=Tv(e),l=Tv(t);if(i!==l)return!1;var u=e instanceof Error,s=t instanceof Error;if(u!==s)return!1;if((u||s)&&(e.name!==t.name||e.message!==t.message))return!1;var c=Bv(e),d=Bv(t);if(c!==d)return!1;if((c||d)&&(e.source!==t.source||Ov(e)!==Ov(t)))return!1;var p=Iv(e),f=Iv(t);if(p!==f)return!1;if((p||f)&&$v(e)!==$v(t))return!1;if(r.strict&&Wv&&Wv(e)!==Wv(t))return!1;var m=Uv(e),b=Uv(t);if(m!==b)return!1;if(m||b){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var y=ih(e),v=ih(t);if(y!==v)return!1;if(y||v){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var h=Mv(e),g=Mv(t);if(h!==g)return!1;if(h||g)return Dv(e)===Dv(t)&&("function"==typeof Uint8Array&&ah(new Uint8Array(e),new Uint8Array(t),r,n));var P=kv(e),C=kv(t);if(P!==C)return!1;if(P||C)return Hv(e)===Hv(t)&&("function"==typeof Uint8Array&&ah(new Uint8Array(e),new Uint8Array(t),r,n));if(typeof e!=typeof t)return!1;var w=Fv(e),q=Fv(t);if(w.length!==q.length)return!1;for(w.sort(),q.sort(),o=w.length-1;o>=0;o--)if(w[o]!=q[o])return!1;for(o=w.length-1;o>=0;o--)if(!ah(e[a=w[o]],t[a],r,n))return!1;var E=Lv(e),x=Lv(t);if(E!==x)return!1;if("Set"===E||"Set"===x)return function(e,t,r,n){if(Zv(e)!==Zv(t))return!1;var o,a,i,l=jv(e),u=jv(t);for(;(o=l.next())&&!o.done;)if(o.value&&"object"==typeof o.value)i||(i=new Vv),Xv(i,o.value);else if(!Yv(t,o.value)){if(r.strict)return!1;if(!nh(e,t,o.value))return!1;i||(i=new Vv),Xv(i,o.value)}if(i){for(;(a=u.next())&&!a.done;)if(a.value&&"object"==typeof a.value){if(!eh(i,a.value,r.strict,n))return!1}else if(!r.strict&&!Yv(e,a.value)&&!eh(i,a.value,r.strict,n))return!1;return 0===Zv(i)}return!0}(e,t,r,n);if("Map"===E)return function(e,t,r,n){if(Qv(e)!==Qv(t))return!1;var o,a,i,l,u,s,c=jv(e),d=jv(t);for(;(o=c.next())&&!o.done;)if(l=o.value[0],u=o.value[1],l&&"object"==typeof l)i||(i=new Vv),Xv(i,l);else if(void 0===(s=Jv(t,l))&&!Gv(t,l)||!ah(u,s,r,n)){if(r.strict)return!1;if(!rh(e,t,l,u,r,n))return!1;i||(i=new Vv),Xv(i,l)}if(i){for(;(a=d.next())&&!a.done;)if(l=a.value[0],s=a.value[1],l&&"object"==typeof l){if(!oh(i,e,l,s,r,n))return!1}else if(!(r.strict||e.has(l)&&ah(Jv(e,l),s,r,n)||oh(i,e,l,s,Ev({},r,{strict:!1}),n)))return!1;return 0===Zv(i)}return!0}(e,t,r,n);return!0}(e,t,o,n)}function ih(e){return!(!e||"object"!=typeof e||"number"!=typeof e.length)&&("function"==typeof e.copy&&"function"==typeof e.slice&&(!(e.length>0&&"number"!=typeof e[0])&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))))}Object.defineProperty(Ac,"__esModule",{value:!0}),Ac.default=void 0;var lh=ch((function(e,t,r){return ah(e,t,r,Sv())})),uh=ch(nr),sh=ch(qr);function ch(e){return e&&e.__esModule?e:{default:e}}function dh(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||ph(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ph(e,t){if(e){if("string"==typeof e)return fh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?fh(e,t):void 0}}function fh(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var mh=[],bh=sh.default.keys(),yh=0;yh<bh.length;yh++){var vh=bh[yh],hh=sh.default.get(vh);if(hh)for(var gh=[].concat(hh.baseConcepts,hh.relatedConcepts),Ph=0;Ph<gh.length;Ph++){var Ch=gh[Ph];if("HTML"===Ch.module){var wh=Ch.concept;wh&&function(){var e=JSON.stringify(wh),t=mh.find((function(t){return JSON.stringify(t[0])===e})),r=void 0;r=t?t[1]:[];for(var n=!0,o=0;o<r.length;o++)if(r[o]===vh){n=!1;break}n&&r.push(vh),mh.push([wh,r])}()}}}var qh={entries:function(){return mh},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=ph(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(mh);try{for(n.s();!(t=n.n()).done;){var o=dh(t.value,2),a=o[0],i=o[1];e.call(r,i,a,mh)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=mh.find((function(t){return(0,lh.default)(e,t[0])}));return t&&t[1]},has:function(e){return!!qh.get(e)},keys:function(){return mh.map((function(e){return dh(e,1)[0]}))},values:function(){return mh.map((function(e){return dh(e,2)[1]}))}},Eh=(0,uh.default)(qh,qh.entries());Ac.default=Eh;var xh={};Object.defineProperty(xh,"__esModule",{value:!0}),xh.default=void 0;var Oh=jh(nr),Rh=jh(qr);function jh(e){return e&&e.__esModule?e:{default:e}}function Sh(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||Ah(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ah(e,t){if(e){if("string"==typeof e)return _h(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_h(e,t):void 0}}function _h(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Th=[],Mh=Rh.default.keys(),Ih=function(e){var t=Mh[e],r=Rh.default.get(t);if(r)for(var n=[].concat(r.baseConcepts,r.relatedConcepts),o=0;o<n.length;o++){var a=n[o];if("HTML"===a.module){var i=a.concept;if(i){var l=Th.find((function(e){return e[0]===t})),u=void 0;(u=l?l[1]:[]).push(i),Th.push([t,u])}}}},Bh=0;Bh<Mh.length;Bh++)Ih(Bh);var kh={entries:function(){return Th},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Ah(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Th);try{for(n.s();!(t=n.n()).done;){var o=Sh(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Th)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Th.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!kh.get(e)},keys:function(){return Th.map((function(e){return Sh(e,1)[0]}))},values:function(){return Th.map((function(e){return Sh(e,2)[1]}))}},Fh=(0,Oh.default)(kh,kh.entries());xh.default=Fh,Object.defineProperty(tr,"__esModule",{value:!0});var Nh=tr.roles=Kh=tr.roleElements=tr.elementRoles=tr.dom=tr.aria=void 0,Lh=Wh(rr),Uh=Wh(br),Dh=Wh(qr),Hh=Wh(Ac),$h=Wh(xh);function Wh(e){return e&&e.__esModule?e:{default:e}}var zh=Lh.default;tr.aria=zh;var Vh=Uh.default;tr.dom=Vh;var Gh=Dh.default;Nh=tr.roles=Gh;var Jh=Hh.default,Qh=tr.elementRoles=Jh,Xh=$h.default,Kh=tr.roleElements=Xh,Yh={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function o(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var a={compressToBase64:function(e){if(null==e)return"";var r=a._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:a._decompress(e.length,32,(function(r){return o(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":a._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:a._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=a.compress(e),r=new Uint8Array(2*t.length),n=0,o=t.length;n<o;n++){var i=t.charCodeAt(n);r[2*n]=i>>>8,r[2*n+1]=i%256}return r},decompressFromUint8Array:function(t){if(null==t)return a.decompress(t);for(var r=new Array(t.length/2),n=0,o=r.length;n<o;n++)r[n]=256*t[2*n]+t[2*n+1];var i=[];return r.forEach((function(t){i.push(e(t))})),a.decompress(i.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),a._decompress(e.length,32,(function(t){return o(r,e.charAt(t))})))},compress:function(t){return a._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,o,a,i={},l={},u="",s="",c="",d=2,p=3,f=2,m=[],b=0,y=0;for(a=0;a<e.length;a+=1)if(u=e.charAt(a),Object.prototype.hasOwnProperty.call(i,u)||(i[u]=p++,l[u]=!0),s=c+u,Object.prototype.hasOwnProperty.call(i,s))c=s;else{if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)b<<=1,y==t-1?(y=0,m.push(r(b)),b=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)b=b<<1|o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++),i[s]=p++,c=String(u)}if(""!==c){if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)b<<=1,y==t-1?(y=0,m.push(r(b)),b=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)b=b<<1|o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(o=2,n=0;n<f;n++)b=b<<1|1&o,y==t-1?(y=0,m.push(r(b)),b=0):y++,o>>=1;for(;;){if(b<<=1,y==t-1){m.push(r(b));break}y++}return m.join("")},decompress:function(e){return null==e?"":""==e?null:a._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var o,a,i,l,u,s,c,d=[],p=4,f=4,m=3,b="",y=[],v={val:n(0),position:r,index:1};for(o=0;o<3;o+=1)d[o]=o;for(i=0,u=Math.pow(2,2),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 2:return""}for(d[3]=c,a=c,y.push(c);;){if(v.index>t)return"";for(i=0,u=Math.pow(2,m),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(c=i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 2:return y.join("")}if(0==p&&(p=Math.pow(2,m),m++),d[c])b=d[c];else{if(c!==f)return null;b=a+a.charAt(0)}y.push(b),d[f++]=a+b.charAt(0),a=b,0==--p&&(p=Math.pow(2,m),m++)}}};return a}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(Yh);var Zh=Yh.exports;function eg(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const tg=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")},rg=(e,t,r,n,o,a)=>e.map((e=>{const i="string"==typeof e?ng(e,t):a(e,t,r,n,o);return""===i&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+i})).join(""),ng=(e,t)=>{const r=t.colors.content;return r.open+eg(e)+r.close},og=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+eg(e)+"--\x3e"+r.close},ag=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close},ig=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},lg=3,ug=8,sg=11,cg=/^((HTML|SVG)\w*)?Element$/,dg=e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(cg.test(t)||o)||r===lg&&"Text"===t||r===ug&&"Comment"===t||r===sg&&"DocumentFragment"===t};function pg(e){return e.nodeType===sg}function fg(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&dg(e)},serialize:(t,r,n,o,a,i)=>{if(function(e){return e.nodeType===lg}(t))return ng(t.data,r);if(function(e){return e.nodeType===ug}(t))return og(t.data,r);const l=pg(t)?"DocumentFragment":t.tagName.toLowerCase();return++o>r.maxDepth?ig(l,r):ag(l,tg(pg(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),pg(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,o,a,i),rg(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,o,a,i),r,n)}}}let mg=null,bg=null,yg=null;try{const e=module&&module.require;bg=e.call(module,"fs").readFileSync,yg=e.call(module,"@babel/code-frame").codeFrameColumns,mg=e.call(module,"chalk")}catch{}function vg(){if(!bg||!yg)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),o=n.split(":"),[a,i,l]=[o[0],parseInt(o[1],10),parseInt(o[2],10)];let u="";try{u=bg(a,"utf-8")}catch{return""}const s=yg(u,{start:{line:i,column:l}},{highlightCode:!0,linesBelow:0});return mg.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const hg=3;function gg(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Pg(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function Cg(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function wg(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const qg=()=>{let e;try{var t;e=JSON.parse(null==(t=process)||null==(t=t.env)?void 0:t.COLORS)}catch(e){}return"boolean"==typeof e?e:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:Eg}=_e,xg=1,Og=8;function Rg(e){return e.nodeType!==Og&&(e.nodeType!==xg||!e.matches(Tg().defaultIgnore))}function jg(e,t,r){if(void 0===r&&(r={}),e||(e=Pg().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:o=Rg,...a}=r,i=Ae(e,{plugins:[fg(o),Eg],printFunctionName:!1,highlight:qg(),...a});return void 0!==t&&e.outerHTML.length>t?i.slice(0,t)+"...":i}const Sg=function(){const e=vg();e?console.log(jg(...arguments)+"\n\n"+e):console.log(jg(...arguments))};let Ag={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=jg(t),n=new Error([e,"Ignored nodes: comments, "+Ag.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function _g(e){"function"==typeof e&&(e=e(Ag)),Ag={...Ag,...e}}function Tg(){return Ag}const Mg=["button","meter","output","progress","select","textarea","input"];function Ig(e){return Mg.includes(e.nodeName.toLowerCase())?"":e.nodeType===hg?e.textContent:Array.from(e.childNodes).map((e=>Ig(e))).join("")}function Bg(e){let t;return t="label"===e.tagName.toLowerCase()?Ig(e):e.value||e.textContent,t}function kg(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function Fg(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const o=t.getAttribute("aria-labelledby"),a=o?o.split(" "):[];return a.length?a.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:Bg(r),formControl:null}:{content:"",formControl:null}})):Array.from(kg(t)).map((e=>({content:Bg(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function Ng(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Lg(e,t,r,n){if("string"!=typeof e)return!1;Ng(r);const o=n(e);return"string"==typeof r||"number"==typeof r?o.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(o,t):$g(r,o)}function Ug(e,t,r,n){if("string"!=typeof e)return!1;Ng(r);const o=n(e);return r instanceof Function?r(o,t):r instanceof RegExp?$g(r,o):o===String(r)}function Dg(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function Hg(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return Dg({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function $g(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function Wg(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===hg&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}const zg=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;return-1!==n.indexOf("undefined")?":not(["+t+"])":r?"["+t+'="'+r+'"]':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[o,a]of e.entries())n=[...n,{match:r(o),roles:Array.from(a),specificity:t(o)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(Qh);function Vg(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function Gg(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=Vg}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function Jg(e){for(const{match:t,roles:r}of zg)if(t(e))return[...r];return[]}function Qg(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===Gg(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):Jg(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function Xg(e,t){let{hidden:r,includeDescription:n}=t;const o=Qg(e,{hidden:r});return Object.entries(o).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const o="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+er(e,{computedStyleSupportsPseudoElements:Tg().computedStyleSupportsPseudoElements})+'":\n',r=jg(e.cloneNode(!1));if(n){return""+t+('Description "'+Zt(e,{computedStyleSupportsPseudoElements:Tg().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+o})).join("\n")}function Kg(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const Yg=Dg();function Zg(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function eP(e,t,r,n){let{variant:o,name:a}=n,i="";const l={},u=[["Role","TestId"].includes(e)?r:Zg(r)];a&&(l.name=Zg(a)),"Role"===e&&Gg(t)&&(l.hidden=!0,i="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(l).length>0&&u.push(l);const s=o+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:o,warning:i,toString(){i&&console.warn(i);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function tP(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function rP(e,t,r){var n,o;if(void 0===t&&(t="get"),e.matches(Tg().defaultIgnore))return;const a=null!=(n=e.getAttribute("role"))?n:null==(o=Jg(e))?void 0:o[0];if("generic"!==a&&tP("Role",r,a))return eP("Role",e,a,{variant:t,name:er(e,{computedStyleSupportsPseudoElements:Tg().computedStyleSupportsPseudoElements})});const i=Fg(document,e).map((e=>e.content)).join(" ");if(tP("LabelText",r,i))return eP("LabelText",e,i,{variant:t});const l=e.getAttribute("placeholder");if(tP("PlaceholderText",r,l))return eP("PlaceholderText",e,l,{variant:t});const u=Yg(Wg(e));if(tP("Text",r,u))return eP("Text",e,u,{variant:t});if(tP("DisplayValue",r,e.value))return eP("DisplayValue",e,Yg(e.value),{variant:t});const s=e.getAttribute("alt");if(tP("AltText",r,s))return eP("AltText",e,s,{variant:t});const c=e.getAttribute("title");if(tP("Title",r,c))return eP("Title",e,c,{variant:t});const d=e.getAttribute(Tg().testIdAttribute);return tP("TestId",r,d)?eP("TestId",e,d,{variant:t}):void 0}function nP(e,t){e.stack=t.stack.replace(t.message,e.message)}function oP(e,t){let{container:r=Pg(),timeout:n=Tg().asyncUtilTimeout,showOriginalStackTrace:o=Tg().showOriginalStackTrace,stackTraceError:a,interval:i=50,onTimeout:l=(e=>(Object.defineProperty(e,"message",{value:Tg().getElementError(e.message,r).message}),e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let c,d,p,f=!1,m="idle";const b=setTimeout((function(){let e;c?(e=c,o||"TestingLibraryElementError"!==e.name||nP(e,a)):(e=new Error("Timed out in waitFor."),o||nP(e,a)),v(l(e),null)}),n),y=gg();if(y){const{unstable_advanceTimersWrapper:e}=Tg();for(g();!f;){if(!gg()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||nP(e,a),void s(e)}if(await e((async()=>{jest.advanceTimersByTime(i)})),f)break;g()}}else{try{wg(r)}catch(e){return void s(e)}d=setInterval(h,i);const{MutationObserver:e}=Cg(r);p=new e(h),p.observe(r,u),g()}function v(e,r){f=!0,clearTimeout(b),y||(clearInterval(d),p.disconnect()),e?s(e):t(r)}function h(){if(gg()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||nP(e,a),s(e)}return g()}function g(){if("pending"!==m)try{const t=function(e){try{return Ag._disableExpensiveErrorDiagnostics=!0,e()}finally{Ag._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(m="pending",t.then((e=>{m="resolved",v(null,e)}),(e=>{m="rejected",c=e}))):v(null,t)}catch(e){c=e}}}))}function aP(e,t){const r=new Error("STACK_TRACE_MESSAGE");return Tg().asyncWrapper((()=>oP(e,{stackTraceError:r,...t})))}function iP(e,t){return Tg().getElementError(e,t)}function lP(e,t){return iP(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function uP(e,t,r,n){let{exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===n?{}:n;const u=o?Ug:Lg,s=Hg({collapseWhitespace:a,trim:i,normalizer:l});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function sP(e,t,r,n){const o=uP(e,t,r,n);if(o.length>1)throw lP("Found multiple elements by ["+e+"="+r+"]",t);return o[0]||null}function cP(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(i.length>1){const e=i.map((e=>iP(null,e).message)).join("\n\n");throw lP(t(r,...o)+"\n\nHere are the matching elements:\n\n"+e,r)}return i[0]||null}}function dP(e,t){return Tg().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function pP(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(!i.length)throw Tg().getElementError(t(r,...o),r);return i}}function fP(e){return(t,r,n,o)=>aP((()=>e(t,r,n)),{container:t,...o})}const mP=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=Tg().throwSuggestions}={}]=a.slice(-1);if(l&&u){const e=rP(l,r);if(e&&!t.endsWith(e.queryName))throw dP(e.toString(),n)}return l},bP=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=Tg().throwSuggestions}={}]=a.slice(-1);if(l.length&&u){const e=[...new Set(l.map((e=>{var t;return null==(t=rP(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(rP(l[0],r).queryName))throw dP(e[0],n)}return l};function yP(e,t,r){const n=mP(cP(e,t),e.name,"query"),o=pP(e,r),a=cP(o,t),i=mP(a,e.name,"get");return[n,bP(o,e.name.replace("query","get"),"getAll"),i,fP(bP(o,e.name,"findAll")),fP(mP(a,e.name,"find"))]}var vP=Object.freeze({__proto__:null,getElementError:iP,wrapAllByQueryWithSuggestion:bP,wrapSingleQueryWithSuggestion:mP,getMultipleElementsFoundError:lP,queryAllByAttribute:uP,queryByAttribute:sP,makeSingleQuery:cP,makeGetAllQuery:pP,makeFindQuery:fP,buildQueries:yP});const hP=function(e,t,r){let{exact:n=!0,trim:o,collapseWhitespace:a,normalizer:i}=void 0===r?{}:r;const l=n?Ug:Lg,u=Hg({collapseWhitespace:a,trim:o,normalizer:i}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:Bg(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return l(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},gP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===r?{}:r;wg(e);const u=o?Ug:Lg,s=Hg({collapseWhitespace:a,trim:i,normalizer:l}),c=Array.from(e.querySelectorAll("*")).filter((e=>kg(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,o)=>{const a=Fg(e,o,{selector:n});a.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const i=a.filter((e=>Boolean(e.content))).map((e=>e.content));return u(i.join(" "),o,t,s)&&r.push(o),i.length>1&&i.forEach(((e,n)=>{u(e,o,t,s)&&r.push(o);const a=[...i];a.splice(n,1),a.length>1&&u(a.join(" "),o,t,s)&&r.push(o)})),r}),[]).concat(uP("aria-label",e,t,{exact:o,normalizer:s}));return Array.from(new Set(c)).filter((e=>e.matches(n)))},PP=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];const a=gP(e,t,...n);if(!a.length){const r=hP(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?Tg().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):Tg().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw Tg().getElementError("Unable to find a label with the text of: "+t,e)}return a};const CP=(e,t)=>"Found multiple elements with the text of: "+t,wP=mP(cP(gP,CP),gP.name,"query"),qP=cP(PP,CP),EP=fP(bP(PP,PP.name,"findAll")),xP=fP(mP(qP,PP.name,"find")),OP=bP(PP,PP.name,"getAll"),RP=mP(qP,PP.name,"get"),jP=bP(gP,gP.name,"queryAll"),SP=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return wg(t[0]),uP("placeholder",...t)},AP=bP(SP,SP.name,"queryAll"),[_P,TP,MP,IP,BP]=yP(SP,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),kP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,ignore:l=Tg().defaultIgnore,normalizer:u}=void 0===r?{}:r;wg(e);const s=o?Ug:Lg,c=Hg({collapseWhitespace:a,trim:i,normalizer:u});let d=[];return"function"==typeof e.matches&&e.matches(n)&&(d=[e]),[...d,...Array.from(e.querySelectorAll(n))].filter((e=>!l||!e.matches(l))).filter((e=>s(Wg(e),e,t,c)))},FP=bP(kP,kP.name,"queryAll"),[NP,LP,UP,DP,HP]=yP(kP,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:o,normalizer:a,selector:i}=r,l=Hg({collapseWhitespace:n,trim:o,normalizer:a})(t.toString());return"Unable to find an element with the text: "+(l!==t.toString()?l+" (normalized from '"+t+"')":t)+("*"!==(null!=i?i:"*")?", which matches selector '"+i+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),$P=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;wg(e);const l=n?Ug:Lg,u=Hg({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>l(Wg(e),e,t,u)))}return l(e.value,e,t,u)}))},WP=bP($P,$P.name,"queryAll"),[zP,VP,GP,JP,QP]=yP($P,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),XP=/^(img|input|area|.+-.+)$/i,KP=function(e,t,r){return void 0===r&&(r={}),wg(e),uP("alt",e,t,r).filter((e=>XP.test(e.tagName)))},YP=bP(KP,KP.name,"queryAll"),[ZP,eC,tC,rC,nC]=yP(KP,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),oC=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;wg(e);const l=n?Ug:Lg,u=Hg({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>l(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&l(Wg(e),e,t,u)))},aC=bP(oC,oC.name,"queryAll"),[iC,lC,uC,sC,cC]=yP(oC,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+".")),dC=function(e,t,r){let{hidden:n=Tg().defaultHidden,name:o,description:a,queryFallbacks:i=!1,selected:l,busy:u,checked:s,pressed:c,current:d,level:p,expanded:f,value:{now:m,min:b,max:y,text:v}={}}=void 0===r?{}:r;var h,g,P,C,w,q,E,x,O,R;if((wg(e),void 0!==l)&&void 0===(null==(h=Nh.get(t))?void 0:h.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==u&&void 0===(null==(g=Nh.get(t))?void 0:g.props["aria-busy"]))throw new Error('"aria-busy" is not supported on role "'+t+'".');if(void 0!==s&&void 0===(null==(P=Nh.get(t))?void 0:P.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==c&&void 0===(null==(C=Nh.get(t))?void 0:C.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==d&&void 0===(null==(w=Nh.get(t))?void 0:w.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==p&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==m&&void 0===(null==(q=Nh.get(t))?void 0:q.props["aria-valuenow"]))throw new Error('"aria-valuenow" is not supported on role "'+t+'".');if(void 0!==y&&void 0===(null==(E=Nh.get(t))?void 0:E.props["aria-valuemax"]))throw new Error('"aria-valuemax" is not supported on role "'+t+'".');if(void 0!==b&&void 0===(null==(x=Nh.get(t))?void 0:x.props["aria-valuemin"]))throw new Error('"aria-valuemin" is not supported on role "'+t+'".');if(void 0!==v&&void 0===(null==(O=Nh.get(t))?void 0:O.props["aria-valuetext"]))throw new Error('"aria-valuetext" is not supported on role "'+t+'".');if(void 0!==f&&void 0===(null==(R=Nh.get(t))?void 0:R.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const j=new WeakMap;function S(e){return j.has(e)||j.set(e,Vg(e)),j.get(e)}return Array.from(e.querySelectorAll(function(e){var t;const r='*[role~="'+e+'"]',n=null!=(t=Kh.get(e))?t:new Set,o=new Set(Array.from(n).map((e=>{let{name:t}=e;return t})));return[r].concat(Array.from(o)).join(",")}(t))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(i)return r.split(" ").filter(Boolean).some((e=>e===t));const[n]=r.split(" ");return n===t}return Jg(e).some((e=>e===t))})).filter((e=>{if(void 0!==l)return l===function(e){return"OPTION"===e.tagName?e.selected:Kg(e,"aria-selected")}(e);if(void 0!==u)return u===function(e){return"true"===e.getAttribute("aria-busy")}(e);if(void 0!==s)return s===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:Kg(e,"aria-checked")}(e);if(void 0!==c)return c===function(e){return Kg(e,"aria-pressed")}(e);if(void 0!==d)return d===function(e){var t,r;return null!=(t=null!=(r=Kg(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e);if(void 0!==f)return f===function(e){return Kg(e,"aria-expanded")}(e);if(void 0!==p)return p===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e);if(void 0!==m||void 0!==y||void 0!==b||void 0!==v){let r=!0;var t;if(void 0!==m&&r&&(r=m===function(e){const t=e.getAttribute("aria-valuenow");return null===t?void 0:+t}(e)),void 0!==y&&r&&(r=y===function(e){const t=e.getAttribute("aria-valuemax");return null===t?void 0:+t}(e)),void 0!==b&&r&&(r=b===function(e){const t=e.getAttribute("aria-valuemin");return null===t?void 0:+t}(e)),void 0!==v)r&&(r=Ug(null!=(t=function(e){const t=e.getAttribute("aria-valuetext");return null===t?void 0:t}(e))?t:null,e,v,(e=>e)));return r}return!0})).filter((e=>void 0===o||Ug(er(e,{computedStyleSupportsPseudoElements:Tg().computedStyleSupportsPseudoElements}),e,o,(e=>e)))).filter((e=>void 0===a||Ug(Zt(e,{computedStyleSupportsPseudoElements:Tg().computedStyleSupportsPseudoElements}),e,a,(e=>e)))).filter((e=>!1!==n||!1===Gg(e,{isSubtreeInaccessible:S})))};const pC=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},fC=bP(dC,dC.name,"queryAll"),[mC,bC,yC,vC,hC]=yP(dC,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+pC(n)}),(function(e,t,r){let{hidden:n=Tg().defaultHidden,name:o,description:a}=void 0===r?{}:r;if(Tg()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+pC(o);let i,l="";Array.from(e.children).forEach((e=>{l+=Xg(e,{hidden:n,includeDescription:void 0!==a})})),i=0===l.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+l.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===o?"":"string"==typeof o?' and name "'+o+'"':" and name `"+o+"`";let s="";return s=void 0===a?"":"string"==typeof a?' and description "'+a+'"':" and description `"+a+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+i).trim()})),gC=()=>Tg().testIdAttribute,PC=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return wg(t[0]),uP(gC(),...t)},CC=bP(PC,PC.name,"queryAll"),[wC,qC,EC,xC,OC]=yP(PC,((e,t)=>"Found multiple elements by: ["+gC()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+gC()+'="'+t+'"]'));var RC=Object.freeze({__proto__:null,queryAllByLabelText:jP,queryByLabelText:wP,getAllByLabelText:OP,getByLabelText:RP,findAllByLabelText:EP,findByLabelText:xP,queryByPlaceholderText:_P,queryAllByPlaceholderText:AP,getByPlaceholderText:MP,getAllByPlaceholderText:TP,findAllByPlaceholderText:IP,findByPlaceholderText:BP,queryByText:NP,queryAllByText:FP,getByText:UP,getAllByText:LP,findAllByText:DP,findByText:HP,queryByDisplayValue:zP,queryAllByDisplayValue:WP,getByDisplayValue:GP,getAllByDisplayValue:VP,findAllByDisplayValue:JP,findByDisplayValue:QP,queryByAltText:ZP,queryAllByAltText:YP,getByAltText:tC,getAllByAltText:eC,findAllByAltText:rC,findByAltText:nC,queryByTitle:iC,queryAllByTitle:aC,getByTitle:uC,getAllByTitle:lC,findAllByTitle:sC,findByTitle:cC,queryByRole:mC,queryAllByRole:fC,getAllByRole:bC,getByRole:yC,findAllByRole:vC,findByRole:hC,queryByTestId:wC,queryAllByTestId:CC,getByTestId:EC,getAllByTestId:qC,findAllByTestId:xC,findByTestId:OC});function jC(e,t,r){return void 0===t&&(t=RC),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const o=t[n];return r[n]=o.bind(null,e),r}),r)}const SC=e=>!e||Array.isArray(e)&&!e.length;function AC(e){if(SC(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const _C={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}}},TC={doubleClick:"dblClick"};function MC(e,t){return Tg().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function IC(e,t,r,n){let{EventType:o="Event",defaultInit:a={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const i={...a,...r},{target:{value:l,files:u,...s}={}}=i;void 0!==l&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:o}=Object.getOwnPropertyDescriptor(n,"value")||{};if(o&&r!==o)o.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,l),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const c=Cg(t),d=c[o]||c.Event;let p;if("function"==typeof d)p=new d(e,i);else{p=c.document.createEvent(o);const{bubbles:t,cancelable:r,detail:n,...a}=i;p.initEvent(e,t,r,n),Object.keys(a).forEach((e=>{p[e]=a[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=i[e];"object"==typeof t&&("function"==typeof c.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new c.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}function BC(e){return"https://testing-playground.com/#markup="+(t=e,Zh.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}Object.keys(_C).forEach((e=>{const{EventType:t,defaultInit:r}=_C[e],n=e.toLowerCase();IC[e]=(e,o)=>IC(n,e,o,{EventType:t,defaultInit:r}),MC[e]=(t,r)=>MC(t,IC[e](t,r))})),Object.keys(TC).forEach((e=>{const t=TC[e];MC[e]=function(){return MC[t](...arguments)}}));const kC={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>Sg(e,t,r))):Sg(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Pg().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=BC(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},FC="undefined"!=typeof document&&document.body?jC(document.body,RC,kC):Object.keys(RC).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),kC),NC="function"==typeof u.act?u.act:d.act;function LC(){if("undefined"!=typeof globalThis)return globalThis;if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}function UC(e){LC().IS_REACT_ACT_ENVIRONMENT=e}function DC(){return LC().IS_REACT_ACT_ENVIRONMENT}const HC=($C=NC,e=>{const t=DC();UC(!0);try{let r=!1;const n=$C((()=>{const t=e();return null!==t&&"object"==typeof t&&"function"==typeof t.then&&(r=!0),t}));return r?{then:(e,r)=>{n.then((r=>{UC(t),e(r)}),(e=>{UC(t),r(e)}))}}:(UC(t),n)}catch(e){throw UC(t),e}});var $C;const WC=function(){return MC(...arguments)};Object.keys(MC).forEach((e=>{WC[e]=function(){return MC[e](...arguments)}}));const zC=WC.mouseEnter,VC=WC.mouseLeave;WC.mouseEnter=function(){return zC(...arguments),WC.mouseOver(...arguments)},WC.mouseLeave=function(){return VC(...arguments),WC.mouseOut(...arguments)};const GC=WC.pointerEnter,JC=WC.pointerLeave;WC.pointerEnter=function(){return GC(...arguments),WC.pointerOver(...arguments)},WC.pointerLeave=function(){return JC(...arguments),WC.pointerOut(...arguments)};const QC=WC.select;WC.select=(e,t)=>{QC(e,t),e.focus(),WC.keyUp(e,t)};const XC=WC.blur,KC=WC.focus;WC.blur=function(){return WC.focusOut(...arguments),XC(...arguments)},WC.focus=function(){return WC.focusIn(...arguments),KC(...arguments)};let YC={reactStrictMode:!1};function ZC(){return{...Tg(),...YC}}_g({unstable_advanceTimersWrapper:e=>HC(e),asyncWrapper:async e=>{const t=DC();UC(!1);try{const t=await e();return await new Promise((e=>{setTimeout((()=>{e()}),0),"undefined"==typeof jest||null===jest||!0!==setTimeout._isMockFunction&&!Object.prototype.hasOwnProperty.call(setTimeout,"clock")||jest.advanceTimersByTime(0)})),t}finally{UC(t)}},eventWrapper:e=>{let t;return HC((()=>{t=e()})),t}});const ew=new Set,tw=[];function rw(e){return ZC().reactStrictMode?u.createElement(u.StrictMode,null,e):e}function nw(e,t){return t?u.createElement(t,null,e):e}function ow(e,t){let r,{hydrate:n,ui:o,wrapper:a}=t;return n?HC((()=>{r=c.hydrateRoot(e,rw(nw(o,a)))})):r=c.createRoot(e),{hydrate(){if(!n)throw new Error("Attempted to hydrate a non-hydrateable root. This is a bug in `@testing-library/react`.")},render(e){r.render(e)},unmount(){r.unmount()}}}function aw(e){return{hydrate(t){s.default.hydrate(t,e)},render(t){s.default.render(t,e)},unmount(){s.default.unmountComponentAtNode(e)}}}function iw(e,t){let{baseElement:r,container:n,hydrate:o,queries:a,root:i,wrapper:l}=t;return HC((()=>{o?i.hydrate(rw(nw(e,l)),n):i.render(rw(nw(e,l)),n)})),{container:n,baseElement:r,debug:function(e,t,n){return void 0===e&&(e=r),Array.isArray(e)?e.forEach((e=>console.log(jg(e,t,n)))):console.log(jg(e,t,n))},unmount:()=>{HC((()=>{i.unmount()}))},rerender:e=>{iw(e,{container:n,baseElement:r,root:i,wrapper:l})},asFragment:()=>{if("function"==typeof document.createRange)return document.createRange().createContextualFragment(n.innerHTML);{const e=document.createElement("template");return e.innerHTML=n.innerHTML,e.content}},...jC(r,a)}}function lw(e,t){let r,{container:n,baseElement:o=n,legacyRoot:a=!1,queries:i,hydrate:l=!1,wrapper:u}=void 0===t?{}:t;if(a&&"function"!=typeof s.default.render){const e=new Error("`legacyRoot: true` is not supported in this version of React. Please use React 18 instead.");throw Error.captureStackTrace(e,lw),e}if(o||(o=document.body),n||(n=o.appendChild(document.createElement("div"))),ew.has(n))tw.forEach((e=>{e.container===n&&(r=e.root)}));else{r=(a?aw:ow)(n,{hydrate:l,ui:e,wrapper:u}),tw.push({container:n,root:r}),ew.add(n)}return iw(e,{container:n,baseElement:o,queries:i,hydrate:l,wrapper:u,root:r})}e.act=HC,e.buildQueries=yP,e.cleanup=function(){tw.forEach((e=>{let{root:t,container:r}=e;HC((()=>{t.unmount()})),r.parentNode===document.body&&document.body.removeChild(r)})),tw.length=0,ew.clear()},e.configure=function(e){"function"==typeof e&&(e=e(ZC()));const{reactStrictMode:t,...r}=e;_g(r),YC={...YC,reactStrictMode:t}},e.createEvent=IC,e.findAllByAltText=rC,e.findAllByDisplayValue=JP,e.findAllByLabelText=EP,e.findAllByPlaceholderText=IP,e.findAllByRole=vC,e.findAllByTestId=xC,e.findAllByText=DP,e.findAllByTitle=sC,e.findByAltText=nC,e.findByDisplayValue=QP,e.findByLabelText=xP,e.findByPlaceholderText=BP,e.findByRole=hC,e.findByTestId=OC,e.findByText=HP,e.findByTitle=cC,e.fireEvent=WC,e.getAllByAltText=eC,e.getAllByDisplayValue=VP,e.getAllByLabelText=OP,e.getAllByPlaceholderText=TP,e.getAllByRole=bC,e.getAllByTestId=qC,e.getAllByText=LP,e.getAllByTitle=lC,e.getByAltText=tC,e.getByDisplayValue=GP,e.getByLabelText=RP,e.getByPlaceholderText=MP,e.getByRole=yC,e.getByTestId=EC,e.getByText=UP,e.getByTitle=uC,e.getConfig=ZC,e.getDefaultNormalizer=Dg,e.getElementError=iP,e.getMultipleElementsFoundError=lP,e.getNodeText=Wg,e.getQueriesForElement=jC,e.getRoles=Qg,e.getSuggestedQuery=rP,e.isInaccessible=Gg,e.logDOM=Sg,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(Xg(e,{hidden:r}))},e.makeFindQuery=fP,e.makeGetAllQuery=pP,e.makeSingleQuery=cP,e.prettyDOM=jg,e.prettyFormat=vt,e.queries=RC,e.queryAllByAltText=YP,e.queryAllByAttribute=uP,e.queryAllByDisplayValue=WP,e.queryAllByLabelText=jP,e.queryAllByPlaceholderText=AP,e.queryAllByRole=fC,e.queryAllByTestId=CC,e.queryAllByText=FP,e.queryAllByTitle=aC,e.queryByAltText=ZP,e.queryByAttribute=sP,e.queryByDisplayValue=zP,e.queryByLabelText=wP,e.queryByPlaceholderText=_P,e.queryByRole=mC,e.queryByTestId=wC,e.queryByText=NP,e.queryByTitle=iC,e.queryHelpers=vP,e.render=lw,e.renderHook=function e(t,r){void 0===r&&(r={});const{initialProps:n,...o}=r;if(o.legacyRoot&&"function"!=typeof s.default.render){const t=new Error("`legacyRoot: true` is not supported in this version of React. Please use React 18 instead.");throw Error.captureStackTrace(t,e),t}const a=u.createRef();function i(e){let{renderCallbackProps:r}=e;const n=t(r);return u.useEffect((()=>{a.current=n})),null}const{rerender:l,unmount:c}=lw(u.createElement(i,{renderCallbackProps:n}),o);return{result:a,rerender:function(e){return l(u.createElement(i,{renderCallbackProps:e}))},unmount:c}},e.screen=FC,e.waitFor=aP,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){AC(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return AC(e()),aP((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!SC(t))throw r}),t)},e.within=jC,e.wrapAllByQueryWithSuggestion=bP,e.wrapSingleQueryWithSuggestion=mP,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=react.pure.umd.min.js.map
