{"version": 3, "sources": ["../../highlight.js/lib/languages/node-repl.js"], "sourcesContent": ["/*\nLanguage: Node REPL\nRequires: javascript.js\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction nodeRepl(hljs) {\n  return {\n    name: 'Node REPL',\n    contains: [\n      {\n        className: 'meta',\n        starts: {\n          // a space separates the REPL prefix from the actual code\n          // this is purely for cleaner HTML output\n          end: / |$/,\n          starts: {\n            end: '$',\n            subLanguage: 'javascript'\n          }\n        },\n        variants: [\n          {\n            begin: /^>(?=[ ]|$)/\n          },\n          {\n            begin: /^\\.\\.\\.(?=[ ]|$)/\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = nodeRepl;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM;AACtB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,QAAQ;AAAA;AAAA;AAAA,cAGN,KAAK;AAAA,cACL,QAAQ;AAAA,gBACN,KAAK;AAAA,gBACL,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}