{"version": 3, "sources": ["../../refractor/lang/java.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = java\njava.displayName = 'java'\njava.aliases = []\nfunction java(Prism) {\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:abstract|assert|boolean|break|byte|case|catch|char|class|const|continue|default|do|double|else|enum|exports|extends|final|finally|float|for|goto|if|implements|import|instanceof|int|interface|long|module|native|new|non-sealed|null|open|opens|package|permits|private|protected|provides|public|record|requires|return|sealed|short|static|strictfp|super|switch|synchronized|this|throw|throws|to|transient|transitive|try|uses|var|void|volatile|while|with|yield)\\b/ // full package (optional) + parent classes (optional)\n    var classNamePrefix = /(^|[^\\w.])(?:[a-z]\\w*\\s*\\.\\s*)*(?:[A-Z]\\w*\\s*\\.\\s*)*/\n      .source // based on the java naming conventions\n    var className = {\n      pattern: RegExp(classNamePrefix + /[A-Z](?:[\\d_A-Z]*[a-z]\\w*)?\\b/.source),\n      lookbehind: true,\n      inside: {\n        namespace: {\n          pattern: /^[a-z]\\w*(?:\\s*\\.\\s*[a-z]\\w*)*(?:\\s*\\.)?/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /\\./\n      }\n    }\n    Prism.languages.java = Prism.languages.extend('clike', {\n      string: {\n        pattern: /(^|[^\\\\])\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n        lookbehind: true,\n        greedy: true\n      },\n      'class-name': [\n        className,\n        {\n          // variables and parameters\n          // this to support class names (or generic parameters) which do not contain a lower case letter (also works for methods)\n          pattern: RegExp(\n            classNamePrefix + /[A-Z]\\w*(?=\\s+\\w+\\s*[;,=()])/.source\n          ),\n          lookbehind: true,\n          inside: className.inside\n        }\n      ],\n      keyword: keywords,\n      function: [\n        Prism.languages.clike.function,\n        {\n          pattern: /(::\\s*)[a-z_]\\w*/,\n          lookbehind: true\n        }\n      ],\n      number:\n        /\\b0b[01][01_]*L?\\b|\\b0x(?:\\.[\\da-f_p+-]+|[\\da-f_]+(?:\\.[\\da-f_p+-]+)?)\\b|(?:\\b\\d[\\d_]*(?:\\.[\\d_]*)?|\\B\\.\\d[\\d_]*)(?:e[+-]?\\d[\\d_]*)?[dfl]?/i,\n      operator: {\n        pattern:\n          /(^|[^.])(?:<<=?|>>>?=?|->|--|\\+\\+|&&|\\|\\||::|[?:~]|[-+*/%&|^!=<>]=?)/m,\n        lookbehind: true\n      }\n    })\n    Prism.languages.insertBefore('java', 'string', {\n      'triple-quoted-string': {\n        // http://openjdk.java.net/jeps/355#Description\n        pattern: /\"\"\"[ \\t]*[\\r\\n](?:(?:\"|\"\")?(?:\\\\.|[^\"\\\\]))*\"\"\"/,\n        greedy: true,\n        alias: 'string'\n      },\n      char: {\n        pattern: /'(?:\\\\.|[^'\\\\\\r\\n]){1,6}'/,\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('java', 'class-name', {\n      annotation: {\n        pattern: /(^|[^.])@\\w+(?:\\s*\\.\\s*\\w+)*/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      generics: {\n        pattern:\n          /<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&)|<(?:[\\w\\s,.?]|&(?!&))*>)*>)*>)*>/,\n        inside: {\n          'class-name': className,\n          keyword: keywords,\n          punctuation: /[<>(),.:]/,\n          operator: /[?&|]/\n        }\n      },\n      namespace: {\n        pattern: RegExp(\n          /(\\b(?:exports|import(?:\\s+static)?|module|open|opens|package|provides|requires|to|transitive|uses|with)\\s+)(?!<keyword>)[a-z]\\w*(?:\\.[a-z]\\w*)*\\.?/.source.replace(\n            /<keyword>/g,\n            function () {\n              return keywords.source\n            }\n          )\n        ),\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF;AACF,YAAI,kBAAkB,uDACnB;AACH,YAAI,YAAY;AAAA,UACd,SAAS,OAAO,kBAAkB,gCAAgC,MAAM;AAAA,UACxE,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,SAAS;AAAA,UACrD,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ;AAAA,YACA;AAAA;AAAA;AAAA,cAGE,SAAS;AAAA,gBACP,kBAAkB,+BAA+B;AAAA,cACnD;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ,UAAU;AAAA,YACpB;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,YACRA,OAAM,UAAU,MAAM;AAAA,YACtB;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,QACE;AAAA,UACF,UAAU;AAAA,YACR,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,UAC7C,wBAAwB;AAAA;AAAA,YAEtB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,cAAc;AAAA,UACjD,YAAY;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,YACR,SACE;AAAA,YACF,QAAQ;AAAA,cACN,cAAc;AAAA,cACd,SAAS;AAAA,cACT,aAAa;AAAA,cACb,UAAU;AAAA,YACZ;AAAA,UACF;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,cACP,qJAAqJ,OAAO;AAAA,gBAC1J;AAAA,gBACA,WAAY;AACV,yBAAO,SAAS;AAAA,gBAClB;AAAA,cACF;AAAA,YACF;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}