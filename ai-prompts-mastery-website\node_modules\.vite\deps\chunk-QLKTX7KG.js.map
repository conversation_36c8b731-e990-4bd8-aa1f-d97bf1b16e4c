{"version": 3, "sources": ["../../highlight.js/lib/languages/leaf.js"], "sourcesContent": ["/*\nLanguage: Leaf\nAuthor: <PERSON> <<EMAIL>>\nDescription: Based on the Leaf reference from https://vapor.github.io/documentation/guide/leaf.html.\n*/\n\nfunction leaf(hljs) {\n  return {\n    name: 'Leaf',\n    contains: [\n      {\n        className: 'function',\n        begin: '#+' + '[A-Za-z_0-9]*' + '\\\\(',\n        end: / \\{/,\n        returnBegin: true,\n        excludeEnd: true,\n        contains: [\n          {\n            className: 'keyword',\n            begin: '#+'\n          },\n          {\n            className: 'title',\n            begin: '[A-Za-z_][A-Za-z_0-9]*'\n          },\n          {\n            className: 'params',\n            begin: '\\\\(',\n            end: '\\\\)',\n            endsParent: true,\n            contains: [\n              {\n                className: 'string',\n                begin: '\"',\n                end: '\"'\n              },\n              {\n                className: 'variable',\n                begin: '[A-Za-z_][A-Za-z_0-9]*'\n              }\n            ]\n          }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = leaf;\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,aAAS,KAAK,MAAM;AAClB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,aAAa;AAAA,YACb,YAAY;AAAA,YACZ,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,KAAK;AAAA,gBACL,YAAY;AAAA,gBACZ,UAAU;AAAA,kBACR;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,WAAW;AAAA,oBACX,OAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}