{"version": 3, "names": ["_helperReplaceSupers", "require", "_core", "_traverse", "_helperAnnotateAsPure", "_inlineCallSuperHelpers", "buildConstructor", "classRef", "constructorBody", "node", "func", "t", "functionDeclaration", "cloneNode", "inherits", "transformClass", "path", "file", "builtinClasses", "isLoose", "assumptions", "supportUnicodeId", "classState", "parent", "undefined", "scope", "classId", "superName", "superReturns", "isDerived", "extendsNative", "construct", "userConstructor", "userConstructorPath", "hasConstructor", "body", "superThises", "pushedInherits", "pushedCreateClass", "protoAlias", "dynamic<PERSON>eys", "Map", "methods", "instance", "hasComputed", "list", "map", "static", "setState", "newState", "Object", "assign", "findThisesVisitor", "visitors", "environmentVisitor", "ThisExpression", "push", "createClassHelper", "args", "callExpression", "addHelper", "maybeCreateConstructor", "classBodyPath", "get", "isClassMethod", "kind", "params", "constructor", "template", "expression", "ast", "blockStatement", "unshiftContainer", "classMethod", "identifier", "buildBody", "pushBody", "verifyConstructor", "pushDescriptors", "classBodyPaths", "isClassProperty", "isClassPrivateProperty", "buildCodeFrameError", "decorators", "isConstructor", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "constant<PERSON>uper", "refToPreserve", "replace", "traverse", "ReturnStatement", "getFunctionParent", "isArrowFunctionExpression", "pushConstructor", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype", "wrapped", "replaceWith", "pushMethod", "pushInheritsToBody", "props", "placement", "length", "desc", "obj", "objectExpression", "objectProperty", "key", "properties", "arrayExpression", "nullLiteral", "lastNonNullIndex", "i", "is<PERSON>ull<PERSON>iteral", "slice", "returnStatement", "wrapSuperCall", "bareSuper", "thisRef", "bareSuperNode", "call", "superIsCallableConstructor", "arguments", "unshift", "thisExpression", "isSpreadElement", "isIdentifier", "argument", "name", "callee", "memberExpression", "logicalExpression", "_bareSuperNode$argume", "bareSuperNodeArguments", "addCallSuperHelper", "parentPath", "isExpressionStatement", "container", "assignmentExpression", "maxGuaranteedSuperBeforeIndex", "ref", "generateDeclaredUidIdentifier", "buildAssertThisInitialized", "bareSupers", "Super", "isCallExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "Math", "min", "type", "left", "isConditional", "test", "object", "guaranteedCalls", "Set", "thisPath", "isMemberExpression", "thisIndex", "exprPath", "isSequenceExpression", "<PERSON><PERSON><PERSON>", "isOptionalCallExpression", "has", "add", "wrapReturn", "returnArg", "thisExpr", "returnParams", "bodyPaths", "guaranteedSuperBefore<PERSON>inish", "pop", "isReturnStatement", "pushContainer", "returnPath", "processMethod", "<PERSON><PERSON><PERSON><PERSON>", "isNumericLiteral", "isBigIntLiteral", "stringLiteral", "String", "value", "toCom<PERSON><PERSON>ey", "isStringLiteral", "fn", "toExpression", "descriptor", "set", "setClassMethods", "insertProtoAliasOnce", "methodName", "computed", "isLiteral", "functionExpression", "id", "generator", "async", "expr", "expressionStatement", "inheritsComments", "generateUidIdentifier", "classProto", "protoDeclaration", "variableDeclaration", "variableDeclarator", "method", "directives", "hasInstanceDescriptors", "hasStaticDescriptors", "extractDynamicKeys", "elem", "isPure", "generateUidIdentifierBasedOnNode", "setupClosureParamsArgs", "closureParams", "closureArgs", "arg", "annotateAsPure", "param", "classTransformer", "superClass", "hasBinding", "noClassCalls", "isStrict", "isInStrictMode", "constructorOnly", "directive", "directiveLiteral", "arrowFunctionExpression"], "sources": ["../src/transformClass.ts"], "sourcesContent": ["import type { NodePath, Scope, File } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport { template, types as t } from \"@babel/core\";\nimport { visitors } from \"@babel/traverse\";\nimport annotateAsPure from \"@babel/helper-annotate-as-pure\";\n\nimport addCallSuperHelper from \"./inline-callSuper-helpers.ts\";\n\ntype ClassAssumptions = {\n  setClassMethods: boolean;\n  constantSuper: boolean;\n  superIsCallableConstructor: boolean;\n  noClassCalls: boolean;\n};\n\ntype ClassConstructor = t.ClassMethod & { kind: \"constructor\" };\n\nfunction buildConstructor(\n  classRef: t.Identifier,\n  constructorBody: t.BlockStatement,\n  node: t.Class,\n) {\n  const func = t.functionDeclaration(\n    t.cloneNode(classRef),\n    [],\n    constructorBody,\n  );\n  t.inherits(func, node);\n  return func;\n}\n\ntype Descriptor = {\n  key: t.Expression;\n  get?: t.Expression | null;\n  set?: t.Expression | null;\n  value?: t.Expression | null;\n  constructor?: t.Expression | null;\n};\n\ntype State = {\n  parent: t.Node;\n  scope: Scope;\n  node: t.Class;\n  path: NodePath<t.Class>;\n  file: File;\n\n  classId: t.Identifier | void;\n  classRef: t.Identifier;\n  superName: t.Expression | null;\n  superReturns: NodePath<t.ReturnStatement>[];\n  isDerived: boolean;\n  extendsNative: boolean;\n\n  construct: t.FunctionDeclaration;\n  constructorBody: t.BlockStatement;\n  userConstructor: ClassConstructor;\n  userConstructorPath: NodePath<ClassConstructor>;\n  hasConstructor: boolean;\n\n  body: t.Statement[];\n  superThises: NodePath<t.ThisExpression>[];\n  pushedInherits: boolean;\n  pushedCreateClass: boolean;\n  protoAlias: t.Identifier | null;\n  isLoose: boolean;\n\n  dynamicKeys: Map<string, t.Expression>;\n\n  methods: {\n    // 'list' is in the same order as the elements appear in the class body.\n    // if there aren't computed keys, we can safely reorder class elements\n    // and use 'map' to merge duplicates.\n    instance: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n    static: {\n      hasComputed: boolean;\n      list: Descriptor[];\n      map: Map<string, Descriptor>;\n    };\n  };\n};\n\ntype PropertyInfo = {\n  instance: t.ObjectExpression[] | null;\n  static: t.ObjectExpression[] | null;\n};\n\nexport default function transformClass(\n  path: NodePath<t.Class>,\n  file: File,\n  builtinClasses: ReadonlySet<string>,\n  isLoose: boolean,\n  assumptions: ClassAssumptions,\n  supportUnicodeId: boolean,\n) {\n  const classState: State = {\n    parent: undefined,\n    scope: undefined,\n    node: undefined,\n    path: undefined,\n    file: undefined,\n\n    classId: undefined,\n    classRef: undefined,\n    superName: null,\n    superReturns: [],\n    isDerived: false,\n    extendsNative: false,\n\n    construct: undefined,\n    constructorBody: undefined,\n    userConstructor: undefined,\n    userConstructorPath: undefined,\n    hasConstructor: false,\n\n    body: [],\n    superThises: [],\n    pushedInherits: false,\n    pushedCreateClass: false,\n    protoAlias: null,\n    isLoose: false,\n\n    dynamicKeys: new Map(),\n\n    methods: {\n      instance: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n      static: {\n        hasComputed: false,\n        list: [],\n        map: new Map(),\n      },\n    },\n  };\n\n  const setState = (newState: Partial<State>) => {\n    Object.assign(classState, newState);\n  };\n\n  const findThisesVisitor = visitors.environmentVisitor({\n    ThisExpression(path) {\n      classState.superThises.push(path);\n    },\n  });\n\n  function createClassHelper(args: t.Expression[]) {\n    return t.callExpression(classState.file.addHelper(\"createClass\"), args);\n  }\n\n  /**\n   * Creates a class constructor or bail out if there is one\n   */\n  function maybeCreateConstructor() {\n    const classBodyPath = classState.path.get(\"body\");\n    for (const path of classBodyPath.get(\"body\")) {\n      if (path.isClassMethod({ kind: \"constructor\" })) return;\n    }\n\n    let params: t.FunctionExpression[\"params\"], body;\n\n    if (classState.isDerived) {\n      const constructor = template.expression.ast`\n        (function () {\n          super(...arguments);\n        })\n      ` as t.FunctionExpression;\n      params = constructor.params;\n      body = constructor.body;\n    } else {\n      params = [];\n      body = t.blockStatement([]);\n    }\n\n    classBodyPath.unshiftContainer(\n      \"body\",\n      t.classMethod(\"constructor\", t.identifier(\"constructor\"), params, body),\n    );\n  }\n\n  function buildBody() {\n    maybeCreateConstructor();\n    pushBody();\n    verifyConstructor();\n\n    if (classState.userConstructor) {\n      const { constructorBody, userConstructor, construct } = classState;\n\n      constructorBody.body.push(...userConstructor.body.body);\n      t.inherits(construct, userConstructor);\n      t.inherits(constructorBody, userConstructor.body);\n    }\n\n    pushDescriptors();\n  }\n\n  function pushBody() {\n    const classBodyPaths: Array<any> = classState.path.get(\"body.body\");\n\n    for (const path of classBodyPaths) {\n      const node = path.node;\n\n      if (path.isClassProperty() || path.isClassPrivateProperty()) {\n        throw path.buildCodeFrameError(\"Missing class properties transform.\");\n      }\n\n      if (node.decorators) {\n        throw path.buildCodeFrameError(\n          \"Method has decorators, put the decorator plugin before the classes one.\",\n        );\n      }\n\n      if (t.isClassMethod(node)) {\n        const isConstructor = node.kind === \"constructor\";\n\n        const replaceSupers = new ReplaceSupers({\n          methodPath: path,\n          objectRef: classState.classRef,\n          superRef: classState.superName,\n          constantSuper: assumptions.constantSuper,\n          file: classState.file,\n          refToPreserve: classState.classRef,\n        });\n\n        replaceSupers.replace();\n\n        const superReturns: NodePath<t.ReturnStatement>[] = [];\n        path.traverse(\n          visitors.environmentVisitor({\n            ReturnStatement(path) {\n              if (!path.getFunctionParent().isArrowFunctionExpression()) {\n                superReturns.push(path);\n              }\n            },\n          }),\n        );\n\n        if (isConstructor) {\n          pushConstructor(superReturns, node as ClassConstructor, path);\n        } else {\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            path.ensureFunctionName ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n          }\n          path.ensureFunctionName(supportUnicodeId);\n          let wrapped;\n          if (node !== path.node) {\n            wrapped = path.node;\n            // The node has been wrapped. Reset it to the original once, but store the wrapper.\n            path.replaceWith(node);\n          }\n\n          pushMethod(node, wrapped);\n        }\n      }\n    }\n  }\n\n  function pushDescriptors() {\n    pushInheritsToBody();\n\n    const { body } = classState;\n\n    const props: PropertyInfo = {\n      instance: null,\n      static: null,\n    };\n\n    for (const placement of [\"static\", \"instance\"] as const) {\n      if (classState.methods[placement].list.length) {\n        props[placement] = classState.methods[placement].list.map(desc => {\n          const obj = t.objectExpression([\n            t.objectProperty(t.identifier(\"key\"), desc.key),\n          ]);\n\n          for (const kind of [\"get\", \"set\", \"value\"] as const) {\n            if (desc[kind] != null) {\n              obj.properties.push(\n                t.objectProperty(t.identifier(kind), desc[kind]),\n              );\n            }\n          }\n\n          return obj;\n        });\n      }\n    }\n\n    if (props.instance || props.static) {\n      let args = [\n        t.cloneNode(classState.classRef), // Constructor\n        props.instance ? t.arrayExpression(props.instance) : t.nullLiteral(), // instanceDescriptors\n        props.static ? t.arrayExpression(props.static) : t.nullLiteral(), // staticDescriptors\n      ];\n\n      let lastNonNullIndex = 0;\n      for (let i = 0; i < args.length; i++) {\n        if (!t.isNullLiteral(args[i])) lastNonNullIndex = i;\n      }\n      args = args.slice(0, lastNonNullIndex + 1);\n\n      body.push(t.returnStatement(createClassHelper(args)));\n      classState.pushedCreateClass = true;\n    }\n  }\n\n  function wrapSuperCall(\n    bareSuper: NodePath<t.CallExpression>,\n    superRef: t.Expression,\n    thisRef: () => t.Identifier,\n    body: NodePath<t.BlockStatement>,\n  ) {\n    const bareSuperNode = bareSuper.node;\n    let call;\n\n    if (assumptions.superIsCallableConstructor) {\n      bareSuperNode.arguments.unshift(t.thisExpression());\n      if (\n        bareSuperNode.arguments.length === 2 &&\n        t.isSpreadElement(bareSuperNode.arguments[1]) &&\n        t.isIdentifier(bareSuperNode.arguments[1].argument, {\n          name: \"arguments\",\n        })\n      ) {\n        // special case single arguments spread\n        bareSuperNode.arguments[1] = bareSuperNode.arguments[1].argument;\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"apply\"),\n        );\n      } else {\n        bareSuperNode.callee = t.memberExpression(\n          t.cloneNode(superRef),\n          t.identifier(\"call\"),\n        );\n      }\n\n      call = t.logicalExpression(\"||\", bareSuperNode, t.thisExpression());\n    } else {\n      const args: t.Expression[] = [\n        t.thisExpression(),\n        t.cloneNode(classState.classRef),\n      ];\n      if (bareSuperNode.arguments?.length) {\n        const bareSuperNodeArguments = bareSuperNode.arguments as (\n          | t.Expression\n          | t.SpreadElement\n        )[];\n\n        /**\n         * test262/test/language/expressions/super/call-spread-err-sngl-err-itr-get-get.js\n         *\n         * var iter = {};\n         * Object.defineProperty(iter, Symbol.iterator, {\n         *   get: function() {\n         *     throw new Test262Error();\n         *   }\n         * })\n         * super(...iter);\n         */\n\n        if (\n          bareSuperNodeArguments.length === 1 &&\n          t.isSpreadElement(bareSuperNodeArguments[0]) &&\n          t.isIdentifier(bareSuperNodeArguments[0].argument, {\n            name: \"arguments\",\n          })\n        ) {\n          args.push(bareSuperNodeArguments[0].argument);\n        } else {\n          args.push(t.arrayExpression(bareSuperNodeArguments));\n        }\n      }\n      call = t.callExpression(addCallSuperHelper(classState.file), args);\n    }\n\n    if (\n      bareSuper.parentPath.isExpressionStatement() &&\n      bareSuper.parentPath.container === body.node.body &&\n      body.node.body.length - 1 === bareSuper.parentPath.key\n    ) {\n      // this super call is the last statement in the body so we can just straight up\n      // turn it into a return\n\n      if (classState.superThises.length) {\n        call = t.assignmentExpression(\"=\", thisRef(), call);\n      }\n\n      bareSuper.parentPath.replaceWith(t.returnStatement(call));\n    } else {\n      bareSuper.replaceWith(t.assignmentExpression(\"=\", thisRef(), call));\n    }\n  }\n\n  function verifyConstructor() {\n    if (!classState.isDerived) return;\n\n    const path = classState.userConstructorPath;\n    const body = path.get(\"body\");\n\n    const constructorBody = path.get(\"body\");\n\n    let maxGuaranteedSuperBeforeIndex = constructorBody.node.body.length;\n\n    path.traverse(findThisesVisitor);\n\n    let thisRef = function () {\n      const ref = path.scope.generateDeclaredUidIdentifier(\"this\");\n      maxGuaranteedSuperBeforeIndex++;\n      thisRef = () => t.cloneNode(ref);\n      return ref;\n    };\n\n    const buildAssertThisInitialized = function () {\n      return t.callExpression(\n        classState.file.addHelper(\"assertThisInitialized\"),\n        [thisRef()],\n      );\n    };\n\n    const bareSupers: NodePath<t.CallExpression>[] = [];\n    path.traverse(\n      visitors.environmentVisitor({\n        Super(path) {\n          const { node, parentPath } = path;\n          if (parentPath.isCallExpression({ callee: node })) {\n            bareSupers.unshift(parentPath);\n          }\n        },\n      }),\n    );\n\n    for (const bareSuper of bareSupers) {\n      wrapSuperCall(bareSuper, classState.superName, thisRef, body);\n\n      if (maxGuaranteedSuperBeforeIndex >= 0) {\n        let lastParentPath: NodePath;\n        bareSuper.find(function (parentPath) {\n          // hit top so short circuit\n          if (parentPath === constructorBody) {\n            maxGuaranteedSuperBeforeIndex = Math.min(\n              maxGuaranteedSuperBeforeIndex,\n              lastParentPath.key as number,\n            );\n            return true;\n          }\n\n          const { type } = parentPath;\n          switch (type) {\n            case \"ExpressionStatement\":\n            case \"SequenceExpression\":\n            case \"AssignmentExpression\":\n            case \"BinaryExpression\":\n            case \"MemberExpression\":\n            case \"CallExpression\":\n            case \"NewExpression\":\n            case \"VariableDeclarator\":\n            case \"VariableDeclaration\":\n            case \"BlockStatement\":\n            case \"ArrayExpression\":\n            case \"ObjectExpression\":\n            case \"ObjectProperty\":\n            case \"TemplateLiteral\":\n              lastParentPath = parentPath;\n              return false;\n            default:\n              if (\n                (type === \"LogicalExpression\" &&\n                  parentPath.node.left === lastParentPath.node) ||\n                (parentPath.isConditional() &&\n                  parentPath.node.test === lastParentPath.node) ||\n                (type === \"OptionalCallExpression\" &&\n                  parentPath.node.callee === lastParentPath.node) ||\n                (type === \"OptionalMemberExpression\" &&\n                  parentPath.node.object === lastParentPath.node)\n              ) {\n                lastParentPath = parentPath;\n                return false;\n              }\n          }\n\n          maxGuaranteedSuperBeforeIndex = -1;\n          return true;\n        });\n      }\n    }\n\n    const guaranteedCalls = new Set<NodePath>();\n\n    for (const thisPath of classState.superThises) {\n      const { node, parentPath } = thisPath;\n      if (parentPath.isMemberExpression({ object: node })) {\n        thisPath.replaceWith(thisRef());\n        continue;\n      }\n\n      let thisIndex: number;\n      thisPath.find(function (parentPath) {\n        if (parentPath.parentPath === constructorBody) {\n          thisIndex = parentPath.key as number;\n          return true;\n        }\n      });\n\n      let exprPath: NodePath = thisPath.parentPath.isSequenceExpression()\n        ? thisPath.parentPath\n        : thisPath;\n      if (\n        exprPath.listKey === \"arguments\" &&\n        (exprPath.parentPath.isCallExpression() ||\n          exprPath.parentPath.isOptionalCallExpression())\n      ) {\n        exprPath = exprPath.parentPath;\n      } else {\n        exprPath = null;\n      }\n\n      if (\n        (maxGuaranteedSuperBeforeIndex !== -1 &&\n          thisIndex > maxGuaranteedSuperBeforeIndex) ||\n        guaranteedCalls.has(exprPath)\n      ) {\n        thisPath.replaceWith(thisRef());\n      } else {\n        if (exprPath) {\n          guaranteedCalls.add(exprPath);\n        }\n        thisPath.replaceWith(buildAssertThisInitialized());\n      }\n    }\n\n    let wrapReturn;\n\n    if (classState.isLoose) {\n      wrapReturn = (returnArg: t.Expression | void) => {\n        const thisExpr = buildAssertThisInitialized();\n        return returnArg\n          ? t.logicalExpression(\"||\", returnArg, thisExpr)\n          : thisExpr;\n      };\n    } else {\n      wrapReturn = (returnArg: t.Expression | undefined) => {\n        const returnParams: t.Expression[] = [thisRef()];\n        if (returnArg != null) {\n          returnParams.push(returnArg);\n        }\n        return t.callExpression(\n          classState.file.addHelper(\"possibleConstructorReturn\"),\n          returnParams,\n        );\n      };\n    }\n\n    // if we have a return as the last node in the body then we've already caught that\n    // return\n    const bodyPaths = body.get(\"body\");\n    const guaranteedSuperBeforeFinish =\n      maxGuaranteedSuperBeforeIndex !== -1 &&\n      maxGuaranteedSuperBeforeIndex < bodyPaths.length;\n    if (!bodyPaths.length || !bodyPaths.pop().isReturnStatement()) {\n      body.pushContainer(\n        \"body\",\n        t.returnStatement(\n          guaranteedSuperBeforeFinish\n            ? thisRef()\n            : buildAssertThisInitialized(),\n        ),\n      );\n    }\n\n    for (const returnPath of classState.superReturns) {\n      returnPath\n        .get(\"argument\")\n        .replaceWith(wrapReturn(returnPath.node.argument));\n    }\n  }\n\n  /**\n   * Push a method to its respective mutatorMap.\n   */\n  function pushMethod(node: t.ClassMethod, wrapped?: t.Expression) {\n    if (node.kind === \"method\") {\n      if (processMethod(node)) return;\n    }\n\n    const placement = node.static ? \"static\" : \"instance\";\n    const methods = classState.methods[placement];\n\n    const descKey = node.kind === \"method\" ? \"value\" : node.kind;\n    const key =\n      t.isNumericLiteral(node.key) || t.isBigIntLiteral(node.key)\n        ? t.stringLiteral(String(node.key.value))\n        : t.toComputedKey(node);\n    methods.hasComputed = !t.isStringLiteral(key);\n\n    const fn: t.Expression = wrapped ?? t.toExpression(node);\n\n    let descriptor: Descriptor;\n    if (\n      !methods.hasComputed &&\n      methods.map.has((key as t.StringLiteral).value)\n    ) {\n      descriptor = methods.map.get((key as t.StringLiteral).value);\n      descriptor[descKey] = fn;\n\n      if (descKey === \"value\") {\n        descriptor.get = null;\n        descriptor.set = null;\n      } else {\n        descriptor.value = null;\n      }\n    } else {\n      descriptor = {\n        key:\n          // private name has been handled in class-properties transform\n          key as t.Expression,\n        [descKey]: fn,\n      } as Descriptor;\n      methods.list.push(descriptor);\n\n      if (!methods.hasComputed) {\n        methods.map.set((key as t.StringLiteral).value, descriptor);\n      }\n    }\n  }\n\n  function processMethod(node: t.ClassMethod) {\n    if (assumptions.setClassMethods && !node.decorators) {\n      // use assignments instead of define properties for loose classes\n      let { classRef } = classState;\n      if (!node.static) {\n        insertProtoAliasOnce();\n        classRef = classState.protoAlias;\n      }\n      const methodName = t.memberExpression(\n        t.cloneNode(classRef),\n        node.key,\n        node.computed || t.isLiteral(node.key),\n      );\n\n      const func: t.Expression = t.functionExpression(\n        // @ts-expect-error We actually set and id through .ensureFunctionName\n        node.id,\n        // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n        node.params,\n        node.body,\n        node.generator,\n        node.async,\n      );\n      t.inherits(func, node);\n\n      const expr = t.expressionStatement(\n        t.assignmentExpression(\"=\", methodName, func),\n      );\n      t.inheritsComments(expr, node);\n      classState.body.push(expr);\n      return true;\n    }\n\n    return false;\n  }\n\n  function insertProtoAliasOnce() {\n    if (classState.protoAlias === null) {\n      setState({ protoAlias: classState.scope.generateUidIdentifier(\"proto\") });\n      const classProto = t.memberExpression(\n        classState.classRef,\n        t.identifier(\"prototype\"),\n      );\n      const protoDeclaration = t.variableDeclaration(\"var\", [\n        t.variableDeclarator(classState.protoAlias, classProto),\n      ]);\n\n      classState.body.push(protoDeclaration);\n    }\n  }\n\n  /**\n   * Replace the constructor body of our class.\n   */\n  function pushConstructor(\n    superReturns: NodePath<t.ReturnStatement>[],\n    method: ClassConstructor,\n    path: NodePath<ClassConstructor>,\n  ) {\n    setState({\n      userConstructorPath: path,\n      userConstructor: method,\n      hasConstructor: true,\n      superReturns,\n    });\n\n    const { construct } = classState;\n\n    t.inheritsComments(construct, method);\n\n    // @ts-expect-error Fixme: should throw when we see TSParameterProperty\n    construct.params = method.params;\n\n    t.inherits(construct.body, method.body);\n    construct.body.directives = method.body.directives;\n\n    // we haven't pushed any descriptors yet\n    // @ts-expect-error todo(flow->ts) maybe remove this block - properties from condition are not used anywhere else\n    if (classState.hasInstanceDescriptors || classState.hasStaticDescriptors) {\n      pushDescriptors();\n    }\n\n    pushInheritsToBody();\n  }\n\n  /**\n   * Push inherits helper to body.\n   */\n  function pushInheritsToBody() {\n    if (!classState.isDerived || classState.pushedInherits) return;\n\n    classState.pushedInherits = true;\n\n    // Unshift to ensure that the constructor inheritance is set up before\n    // any properties can be assigned to the prototype.\n\n    classState.body.unshift(\n      t.expressionStatement(\n        t.callExpression(\n          classState.file.addHelper(\n            classState.isLoose ? \"inheritsLoose\" : \"inherits\",\n          ),\n          [t.cloneNode(classState.classRef), t.cloneNode(classState.superName)],\n        ),\n      ),\n    );\n  }\n\n  function extractDynamicKeys() {\n    const { dynamicKeys, node, scope } = classState;\n\n    for (const elem of node.body.body) {\n      if (!t.isClassMethod(elem) || !elem.computed) continue;\n      if (scope.isPure(elem.key, /* constants only*/ true)) continue;\n\n      const id = scope.generateUidIdentifierBasedOnNode(elem.key);\n      dynamicKeys.set(id.name, elem.key);\n\n      elem.key = id;\n    }\n  }\n\n  function setupClosureParamsArgs() {\n    const { superName, dynamicKeys } = classState;\n    const closureParams = [];\n    const closureArgs = [];\n\n    if (classState.isDerived) {\n      let arg = t.cloneNode(superName);\n      if (classState.extendsNative) {\n        arg = t.callExpression(classState.file.addHelper(\"wrapNativeSuper\"), [\n          arg,\n        ]);\n        annotateAsPure(arg);\n      }\n\n      const param =\n        classState.scope.generateUidIdentifierBasedOnNode(superName);\n\n      closureParams.push(param);\n      closureArgs.push(arg);\n\n      setState({ superName: t.cloneNode(param) });\n    }\n\n    for (const [name, value] of dynamicKeys) {\n      closureParams.push(t.identifier(name));\n      closureArgs.push(value);\n    }\n\n    return { closureParams, closureArgs };\n  }\n\n  function classTransformer(\n    path: NodePath<t.Class>,\n    file: File,\n    builtinClasses: ReadonlySet<string>,\n    isLoose: boolean,\n  ) {\n    setState({\n      parent: path.parent,\n      scope: path.scope,\n      node: path.node,\n      path,\n      file,\n      isLoose,\n    });\n\n    setState({\n      classId: classState.node.id,\n      // this is the name of the binding that will **always** reference the class we've constructed\n      classRef: classState.node.id\n        ? t.identifier(classState.node.id.name)\n        : classState.scope.generateUidIdentifier(\"class\"),\n      superName: classState.node.superClass,\n      isDerived: !!classState.node.superClass,\n      constructorBody: t.blockStatement([]),\n    });\n\n    setState({\n      extendsNative:\n        t.isIdentifier(classState.superName) &&\n        builtinClasses.has(classState.superName.name) &&\n        !classState.scope.hasBinding(\n          classState.superName.name,\n          /* noGlobals */ true,\n        ),\n    });\n\n    const { classRef, node, constructorBody } = classState;\n\n    setState({\n      construct: buildConstructor(classRef, constructorBody, node),\n    });\n\n    extractDynamicKeys();\n\n    const { body } = classState;\n    const { closureParams, closureArgs } = setupClosureParamsArgs();\n\n    buildBody();\n\n    // make sure this class isn't directly called (with A() instead new A())\n    if (!assumptions.noClassCalls) {\n      constructorBody.body.unshift(\n        t.expressionStatement(\n          t.callExpression(classState.file.addHelper(\"classCallCheck\"), [\n            t.thisExpression(),\n            t.cloneNode(classState.classRef),\n          ]),\n        ),\n      );\n    }\n\n    const isStrict = path.isInStrictMode();\n    let constructorOnly = body.length === 0;\n    if (constructorOnly && !isStrict) {\n      for (const param of classState.construct.params) {\n        // It's illegal to put a use strict directive into the body of a function\n        // with non-simple parameters for some reason. So, we have to use a strict\n        // wrapper function.\n        if (!t.isIdentifier(param)) {\n          constructorOnly = false;\n          break;\n        }\n      }\n    }\n\n    const directives = constructorOnly\n      ? classState.construct.body.directives\n      : [];\n    if (!isStrict) {\n      directives.push(t.directive(t.directiveLiteral(\"use strict\")));\n    }\n\n    if (constructorOnly) {\n      // named class with only a constructor\n      const expr = t.toExpression(classState.construct);\n      return classState.isLoose ? expr : createClassHelper([expr]);\n    }\n\n    if (!classState.pushedCreateClass) {\n      body.push(\n        t.returnStatement(\n          classState.isLoose\n            ? t.cloneNode(classState.classRef)\n            : createClassHelper([t.cloneNode(classState.classRef)]),\n        ),\n      );\n    }\n\n    body.unshift(classState.construct);\n\n    const container = t.arrowFunctionExpression(\n      closureParams,\n      t.blockStatement(body, directives),\n    );\n    return t.callExpression(container, closureArgs);\n  }\n\n  return classTransformer(path, file, builtinClasses, isLoose);\n}\n"], "mappings": ";;;;;;AACA,IAAAA,oBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,SAAA,GAAAF,OAAA;AACA,IAAAG,qBAAA,GAAAH,OAAA;AAEA,IAAAI,uBAAA,GAAAJ,OAAA;AAWA,SAASK,gBAAgBA,CACvBC,QAAsB,EACtBC,eAAiC,EACjCC,IAAa,EACb;EACA,MAAMC,IAAI,GAAGC,WAAC,CAACC,mBAAmB,CAChCD,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrB,EAAE,EACFC,eACF,CAAC;EACDG,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;EACtB,OAAOC,IAAI;AACb;AA6De,SAASK,cAAcA,CACpCC,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChBC,WAA6B,EAC7BC,gBAAyB,EACzB;EACA,MAAMC,UAAiB,GAAG;IACxBC,MAAM,EAAEC,SAAS;IACjBC,KAAK,EAAED,SAAS;IAChBf,IAAI,EAAEe,SAAS;IACfR,IAAI,EAAEQ,SAAS;IACfP,IAAI,EAAEO,SAAS;IAEfE,OAAO,EAAEF,SAAS;IAClBjB,QAAQ,EAAEiB,SAAS;IACnBG,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE,KAAK;IAChBC,aAAa,EAAE,KAAK;IAEpBC,SAAS,EAAEP,SAAS;IACpBhB,eAAe,EAAEgB,SAAS;IAC1BQ,eAAe,EAAER,SAAS;IAC1BS,mBAAmB,EAAET,SAAS;IAC9BU,cAAc,EAAE,KAAK;IAErBC,IAAI,EAAE,EAAE;IACRC,WAAW,EAAE,EAAE;IACfC,cAAc,EAAE,KAAK;IACrBC,iBAAiB,EAAE,KAAK;IACxBC,UAAU,EAAE,IAAI;IAChBpB,OAAO,EAAE,KAAK;IAEdqB,WAAW,EAAE,IAAIC,GAAG,CAAC,CAAC;IAEtBC,OAAO,EAAE;MACPC,QAAQ,EAAE;QACRC,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf,CAAC;MACDM,MAAM,EAAE;QACNH,WAAW,EAAE,KAAK;QAClBC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE,IAAIL,GAAG,CAAC;MACf;IACF;EACF,CAAC;EAED,MAAMO,QAAQ,GAAIC,QAAwB,IAAK;IAC7CC,MAAM,CAACC,MAAM,CAAC7B,UAAU,EAAE2B,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMG,iBAAiB,GAAGC,kBAAQ,CAACC,kBAAkB,CAAC;IACpDC,cAAcA,CAACvC,IAAI,EAAE;MACnBM,UAAU,CAACc,WAAW,CAACoB,IAAI,CAACxC,IAAI,CAAC;IACnC;EACF,CAAC,CAAC;EAEF,SAASyC,iBAAiBA,CAACC,IAAoB,EAAE;IAC/C,OAAO/C,WAAC,CAACgD,cAAc,CAACrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,aAAa,CAAC,EAAEF,IAAI,CAAC;EACzE;EAKA,SAASG,sBAAsBA,CAAA,EAAG;IAChC,MAAMC,aAAa,GAAGxC,UAAU,CAACN,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAC;IACjD,KAAK,MAAM/C,IAAI,IAAI8C,aAAa,CAACC,GAAG,CAAC,MAAM,CAAC,EAAE;MAC5C,IAAI/C,IAAI,CAACgD,aAAa,CAAC;QAAEC,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;IACnD;IAEA,IAAIC,MAAsC,EAAE/B,IAAI;IAEhD,IAAIb,UAAU,CAACO,SAAS,EAAE;MACxB,MAAMsC,WAAW,GAAGC,cAAQ,CAACC,UAAU,CAACC,GAAG;AACjD;AACA;AACA;AACA,OAA+B;MACzBJ,MAAM,GAAGC,WAAW,CAACD,MAAM;MAC3B/B,IAAI,GAAGgC,WAAW,CAAChC,IAAI;IACzB,CAAC,MAAM;MACL+B,MAAM,GAAG,EAAE;MACX/B,IAAI,GAAGxB,WAAC,CAAC4D,cAAc,CAAC,EAAE,CAAC;IAC7B;IAEAT,aAAa,CAACU,gBAAgB,CAC5B,MAAM,EACN7D,WAAC,CAAC8D,WAAW,CAAC,aAAa,EAAE9D,WAAC,CAAC+D,UAAU,CAAC,aAAa,CAAC,EAAER,MAAM,EAAE/B,IAAI,CACxE,CAAC;EACH;EAEA,SAASwC,SAASA,CAAA,EAAG;IACnBd,sBAAsB,CAAC,CAAC;IACxBe,QAAQ,CAAC,CAAC;IACVC,iBAAiB,CAAC,CAAC;IAEnB,IAAIvD,UAAU,CAACU,eAAe,EAAE;MAC9B,MAAM;QAAExB,eAAe;QAAEwB,eAAe;QAAED;MAAU,CAAC,GAAGT,UAAU;MAElEd,eAAe,CAAC2B,IAAI,CAACqB,IAAI,CAAC,GAAGxB,eAAe,CAACG,IAAI,CAACA,IAAI,CAAC;MACvDxB,WAAC,CAACG,QAAQ,CAACiB,SAAS,EAAEC,eAAe,CAAC;MACtCrB,WAAC,CAACG,QAAQ,CAACN,eAAe,EAAEwB,eAAe,CAACG,IAAI,CAAC;IACnD;IAEA2C,eAAe,CAAC,CAAC;EACnB;EAEA,SAASF,QAAQA,CAAA,EAAG;IAClB,MAAMG,cAA0B,GAAGzD,UAAU,CAACN,IAAI,CAAC+C,GAAG,CAAC,WAAW,CAAC;IAEnE,KAAK,MAAM/C,IAAI,IAAI+D,cAAc,EAAE;MACjC,MAAMtE,IAAI,GAAGO,IAAI,CAACP,IAAI;MAEtB,IAAIO,IAAI,CAACgE,eAAe,CAAC,CAAC,IAAIhE,IAAI,CAACiE,sBAAsB,CAAC,CAAC,EAAE;QAC3D,MAAMjE,IAAI,CAACkE,mBAAmB,CAAC,qCAAqC,CAAC;MACvE;MAEA,IAAIzE,IAAI,CAAC0E,UAAU,EAAE;QACnB,MAAMnE,IAAI,CAACkE,mBAAmB,CAC5B,yEACF,CAAC;MACH;MAEA,IAAIvE,WAAC,CAACqD,aAAa,CAACvD,IAAI,CAAC,EAAE;QACzB,MAAM2E,aAAa,GAAG3E,IAAI,CAACwD,IAAI,KAAK,aAAa;QAEjD,MAAMoB,aAAa,GAAG,IAAIC,4BAAa,CAAC;UACtCC,UAAU,EAAEvE,IAAI;UAChBwE,SAAS,EAAElE,UAAU,CAACf,QAAQ;UAC9BkF,QAAQ,EAAEnE,UAAU,CAACK,SAAS;UAC9B+D,aAAa,EAAEtE,WAAW,CAACsE,aAAa;UACxCzE,IAAI,EAAEK,UAAU,CAACL,IAAI;UACrB0E,aAAa,EAAErE,UAAU,CAACf;QAC5B,CAAC,CAAC;QAEF8E,aAAa,CAACO,OAAO,CAAC,CAAC;QAEvB,MAAMhE,YAA2C,GAAG,EAAE;QACtDZ,IAAI,CAAC6E,QAAQ,CACXxC,kBAAQ,CAACC,kBAAkB,CAAC;UAC1BwC,eAAeA,CAAC9E,IAAI,EAAE;YACpB,IAAI,CAACA,IAAI,CAAC+E,iBAAiB,CAAC,CAAC,CAACC,yBAAyB,CAAC,CAAC,EAAE;cACzDpE,YAAY,CAAC4B,IAAI,CAACxC,IAAI,CAAC;YACzB;UACF;QACF,CAAC,CACH,CAAC;QAED,IAAIoE,aAAa,EAAE;UACjBa,eAAe,CAACrE,YAAY,EAAEnB,IAAI,EAAsBO,IAAI,CAAC;QAC/D,CAAC,MAAM;UAC4D;YAAA,IAAAkF,qBAAA;YAE/D,CAAAA,qBAAA,GAAAlF,IAAI,CAACmF,kBAAkB,YAAAD,qBAAA,GAAvBlF,IAAI,CAACmF,kBAAkB,GAErBlG,OAAO,CAAC,iBAAiB,CAAC,CAACmG,QAAQ,CAACC,SAAS,CAACF,kBAAkB;UACpE;UACAnF,IAAI,CAACmF,kBAAkB,CAAC9E,gBAAgB,CAAC;UACzC,IAAIiF,OAAO;UACX,IAAI7F,IAAI,KAAKO,IAAI,CAACP,IAAI,EAAE;YACtB6F,OAAO,GAAGtF,IAAI,CAACP,IAAI;YAEnBO,IAAI,CAACuF,WAAW,CAAC9F,IAAI,CAAC;UACxB;UAEA+F,UAAU,CAAC/F,IAAI,EAAE6F,OAAO,CAAC;QAC3B;MACF;IACF;EACF;EAEA,SAASxB,eAAeA,CAAA,EAAG;IACzB2B,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAEtE;IAAK,CAAC,GAAGb,UAAU;IAE3B,MAAMoF,KAAmB,GAAG;MAC1B/D,QAAQ,EAAE,IAAI;MACdI,MAAM,EAAE;IACV,CAAC;IAED,KAAK,MAAM4D,SAAS,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAW;MACvD,IAAIrF,UAAU,CAACoB,OAAO,CAACiE,SAAS,CAAC,CAAC9D,IAAI,CAAC+D,MAAM,EAAE;QAC7CF,KAAK,CAACC,SAAS,CAAC,GAAGrF,UAAU,CAACoB,OAAO,CAACiE,SAAS,CAAC,CAAC9D,IAAI,CAACC,GAAG,CAAC+D,IAAI,IAAI;UAChE,MAAMC,GAAG,GAAGnG,WAAC,CAACoG,gBAAgB,CAAC,CAC7BpG,WAAC,CAACqG,cAAc,CAACrG,WAAC,CAAC+D,UAAU,CAAC,KAAK,CAAC,EAAEmC,IAAI,CAACI,GAAG,CAAC,CAChD,CAAC;UAEF,KAAK,MAAMhD,IAAI,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,OAAO,CAAC,EAAW;YACnD,IAAI4C,IAAI,CAAC5C,IAAI,CAAC,IAAI,IAAI,EAAE;cACtB6C,GAAG,CAACI,UAAU,CAAC1D,IAAI,CACjB7C,WAAC,CAACqG,cAAc,CAACrG,WAAC,CAAC+D,UAAU,CAACT,IAAI,CAAC,EAAE4C,IAAI,CAAC5C,IAAI,CAAC,CACjD,CAAC;YACH;UACF;UAEA,OAAO6C,GAAG;QACZ,CAAC,CAAC;MACJ;IACF;IAEA,IAAIJ,KAAK,CAAC/D,QAAQ,IAAI+D,KAAK,CAAC3D,MAAM,EAAE;MAClC,IAAIW,IAAI,GAAG,CACT/C,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAChCmG,KAAK,CAAC/D,QAAQ,GAAGhC,WAAC,CAACwG,eAAe,CAACT,KAAK,CAAC/D,QAAQ,CAAC,GAAGhC,WAAC,CAACyG,WAAW,CAAC,CAAC,EACpEV,KAAK,CAAC3D,MAAM,GAAGpC,WAAC,CAACwG,eAAe,CAACT,KAAK,CAAC3D,MAAM,CAAC,GAAGpC,WAAC,CAACyG,WAAW,CAAC,CAAC,CACjE;MAED,IAAIC,gBAAgB,GAAG,CAAC;MACxB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG5D,IAAI,CAACkD,MAAM,EAAEU,CAAC,EAAE,EAAE;QACpC,IAAI,CAAC3G,WAAC,CAAC4G,aAAa,CAAC7D,IAAI,CAAC4D,CAAC,CAAC,CAAC,EAAED,gBAAgB,GAAGC,CAAC;MACrD;MACA5D,IAAI,GAAGA,IAAI,CAAC8D,KAAK,CAAC,CAAC,EAAEH,gBAAgB,GAAG,CAAC,CAAC;MAE1ClF,IAAI,CAACqB,IAAI,CAAC7C,WAAC,CAAC8G,eAAe,CAAChE,iBAAiB,CAACC,IAAI,CAAC,CAAC,CAAC;MACrDpC,UAAU,CAACgB,iBAAiB,GAAG,IAAI;IACrC;EACF;EAEA,SAASoF,aAAaA,CACpBC,SAAqC,EACrClC,QAAsB,EACtBmC,OAA2B,EAC3BzF,IAAgC,EAChC;IACA,MAAM0F,aAAa,GAAGF,SAAS,CAAClH,IAAI;IACpC,IAAIqH,IAAI;IAER,IAAI1G,WAAW,CAAC2G,0BAA0B,EAAE;MAC1CF,aAAa,CAACG,SAAS,CAACC,OAAO,CAACtH,WAAC,CAACuH,cAAc,CAAC,CAAC,CAAC;MACnD,IACEL,aAAa,CAACG,SAAS,CAACpB,MAAM,KAAK,CAAC,IACpCjG,WAAC,CAACwH,eAAe,CAACN,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAAC,IAC7CrH,WAAC,CAACyH,YAAY,CAACP,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ,EAAE;QAClDC,IAAI,EAAE;MACR,CAAC,CAAC,EACF;QAEAT,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,GAAGH,aAAa,CAACG,SAAS,CAAC,CAAC,CAAC,CAACK,QAAQ;QAChER,aAAa,CAACU,MAAM,GAAG5H,WAAC,CAAC6H,gBAAgB,CACvC7H,WAAC,CAACE,SAAS,CAAC4E,QAAQ,CAAC,EACrB9E,WAAC,CAAC+D,UAAU,CAAC,OAAO,CACtB,CAAC;MACH,CAAC,MAAM;QACLmD,aAAa,CAACU,MAAM,GAAG5H,WAAC,CAAC6H,gBAAgB,CACvC7H,WAAC,CAACE,SAAS,CAAC4E,QAAQ,CAAC,EACrB9E,WAAC,CAAC+D,UAAU,CAAC,MAAM,CACrB,CAAC;MACH;MAEAoD,IAAI,GAAGnH,WAAC,CAAC8H,iBAAiB,CAAC,IAAI,EAAEZ,aAAa,EAAElH,WAAC,CAACuH,cAAc,CAAC,CAAC,CAAC;IACrE,CAAC,MAAM;MAAA,IAAAQ,qBAAA;MACL,MAAMhF,IAAoB,GAAG,CAC3B/C,WAAC,CAACuH,cAAc,CAAC,CAAC,EAClBvH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC;MACD,KAAAmI,qBAAA,GAAIb,aAAa,CAACG,SAAS,aAAvBU,qBAAA,CAAyB9B,MAAM,EAAE;QACnC,MAAM+B,sBAAsB,GAAGd,aAAa,CAACG,SAG1C;QAcH,IACEW,sBAAsB,CAAC/B,MAAM,KAAK,CAAC,IACnCjG,WAAC,CAACwH,eAAe,CAACQ,sBAAsB,CAAC,CAAC,CAAC,CAAC,IAC5ChI,WAAC,CAACyH,YAAY,CAACO,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,EAAE;UACjDC,IAAI,EAAE;QACR,CAAC,CAAC,EACF;UACA5E,IAAI,CAACF,IAAI,CAACmF,sBAAsB,CAAC,CAAC,CAAC,CAACN,QAAQ,CAAC;QAC/C,CAAC,MAAM;UACL3E,IAAI,CAACF,IAAI,CAAC7C,WAAC,CAACwG,eAAe,CAACwB,sBAAsB,CAAC,CAAC;QACtD;MACF;MACAb,IAAI,GAAGnH,WAAC,CAACgD,cAAc,CAAC,IAAAiF,+BAAkB,EAACtH,UAAU,CAACL,IAAI,CAAC,EAAEyC,IAAI,CAAC;IACpE;IAEA,IACEiE,SAAS,CAACkB,UAAU,CAACC,qBAAqB,CAAC,CAAC,IAC5CnB,SAAS,CAACkB,UAAU,CAACE,SAAS,KAAK5G,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,IACjDA,IAAI,CAAC1B,IAAI,CAAC0B,IAAI,CAACyE,MAAM,GAAG,CAAC,KAAKe,SAAS,CAACkB,UAAU,CAAC5B,GAAG,EACtD;MAIA,IAAI3F,UAAU,CAACc,WAAW,CAACwE,MAAM,EAAE;QACjCkB,IAAI,GAAGnH,WAAC,CAACqI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC;MACrD;MAEAH,SAAS,CAACkB,UAAU,CAACtC,WAAW,CAAC5F,WAAC,CAAC8G,eAAe,CAACK,IAAI,CAAC,CAAC;IAC3D,CAAC,MAAM;MACLH,SAAS,CAACpB,WAAW,CAAC5F,WAAC,CAACqI,oBAAoB,CAAC,GAAG,EAAEpB,OAAO,CAAC,CAAC,EAAEE,IAAI,CAAC,CAAC;IACrE;EACF;EAEA,SAASjD,iBAAiBA,CAAA,EAAG;IAC3B,IAAI,CAACvD,UAAU,CAACO,SAAS,EAAE;IAE3B,MAAMb,IAAI,GAAGM,UAAU,CAACW,mBAAmB;IAC3C,MAAME,IAAI,GAAGnB,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAC;IAE7B,MAAMvD,eAAe,GAAGQ,IAAI,CAAC+C,GAAG,CAAC,MAAM,CAAC;IAExC,IAAIkF,6BAA6B,GAAGzI,eAAe,CAACC,IAAI,CAAC0B,IAAI,CAACyE,MAAM;IAEpE5F,IAAI,CAAC6E,QAAQ,CAACzC,iBAAiB,CAAC;IAEhC,IAAIwE,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,MAAMsB,GAAG,GAAGlI,IAAI,CAACS,KAAK,CAAC0H,6BAA6B,CAAC,MAAM,CAAC;MAC5DF,6BAA6B,EAAE;MAC/BrB,OAAO,GAAGA,CAAA,KAAMjH,WAAC,CAACE,SAAS,CAACqI,GAAG,CAAC;MAChC,OAAOA,GAAG;IACZ,CAAC;IAED,MAAME,0BAA0B,GAAG,SAAAA,CAAA,EAAY;MAC7C,OAAOzI,WAAC,CAACgD,cAAc,CACrBrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,uBAAuB,CAAC,EAClD,CAACgE,OAAO,CAAC,CAAC,CACZ,CAAC;IACH,CAAC;IAED,MAAMyB,UAAwC,GAAG,EAAE;IACnDrI,IAAI,CAAC6E,QAAQ,CACXxC,kBAAQ,CAACC,kBAAkB,CAAC;MAC1BgG,KAAKA,CAACtI,IAAI,EAAE;QACV,MAAM;UAAEP,IAAI;UAAEoI;QAAW,CAAC,GAAG7H,IAAI;QACjC,IAAI6H,UAAU,CAACU,gBAAgB,CAAC;UAAEhB,MAAM,EAAE9H;QAAK,CAAC,CAAC,EAAE;UACjD4I,UAAU,CAACpB,OAAO,CAACY,UAAU,CAAC;QAChC;MACF;IACF,CAAC,CACH,CAAC;IAED,KAAK,MAAMlB,SAAS,IAAI0B,UAAU,EAAE;MAClC3B,aAAa,CAACC,SAAS,EAAErG,UAAU,CAACK,SAAS,EAAEiG,OAAO,EAAEzF,IAAI,CAAC;MAE7D,IAAI8G,6BAA6B,IAAI,CAAC,EAAE;QACtC,IAAIO,cAAwB;QAC5B7B,SAAS,CAAC8B,IAAI,CAAC,UAAUZ,UAAU,EAAE;UAEnC,IAAIA,UAAU,KAAKrI,eAAe,EAAE;YAClCyI,6BAA6B,GAAGS,IAAI,CAACC,GAAG,CACtCV,6BAA6B,EAC7BO,cAAc,CAACvC,GACjB,CAAC;YACD,OAAO,IAAI;UACb;UAEA,MAAM;YAAE2C;UAAK,CAAC,GAAGf,UAAU;UAC3B,QAAQe,IAAI;YACV,KAAK,qBAAqB;YAC1B,KAAK,oBAAoB;YACzB,KAAK,sBAAsB;YAC3B,KAAK,kBAAkB;YACvB,KAAK,kBAAkB;YACvB,KAAK,gBAAgB;YACrB,KAAK,eAAe;YACpB,KAAK,oBAAoB;YACzB,KAAK,qBAAqB;YAC1B,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;YACtB,KAAK,kBAAkB;YACvB,KAAK,gBAAgB;YACrB,KAAK,iBAAiB;cACpBJ,cAAc,GAAGX,UAAU;cAC3B,OAAO,KAAK;YACd;cACE,IACGe,IAAI,KAAK,mBAAmB,IAC3Bf,UAAU,CAACpI,IAAI,CAACoJ,IAAI,KAAKL,cAAc,CAAC/I,IAAI,IAC7CoI,UAAU,CAACiB,aAAa,CAAC,CAAC,IACzBjB,UAAU,CAACpI,IAAI,CAACsJ,IAAI,KAAKP,cAAc,CAAC/I,IAAK,IAC9CmJ,IAAI,KAAK,wBAAwB,IAChCf,UAAU,CAACpI,IAAI,CAAC8H,MAAM,KAAKiB,cAAc,CAAC/I,IAAK,IAChDmJ,IAAI,KAAK,0BAA0B,IAClCf,UAAU,CAACpI,IAAI,CAACuJ,MAAM,KAAKR,cAAc,CAAC/I,IAAK,EACjD;gBACA+I,cAAc,GAAGX,UAAU;gBAC3B,OAAO,KAAK;cACd;UACJ;UAEAI,6BAA6B,GAAG,CAAC,CAAC;UAClC,OAAO,IAAI;QACb,CAAC,CAAC;MACJ;IACF;IAEA,MAAMgB,eAAe,GAAG,IAAIC,GAAG,CAAW,CAAC;IAE3C,KAAK,MAAMC,QAAQ,IAAI7I,UAAU,CAACc,WAAW,EAAE;MAC7C,MAAM;QAAE3B,IAAI;QAAEoI;MAAW,CAAC,GAAGsB,QAAQ;MACrC,IAAItB,UAAU,CAACuB,kBAAkB,CAAC;QAAEJ,MAAM,EAAEvJ;MAAK,CAAC,CAAC,EAAE;QACnD0J,QAAQ,CAAC5D,WAAW,CAACqB,OAAO,CAAC,CAAC,CAAC;QAC/B;MACF;MAEA,IAAIyC,SAAiB;MACrBF,QAAQ,CAACV,IAAI,CAAC,UAAUZ,UAAU,EAAE;QAClC,IAAIA,UAAU,CAACA,UAAU,KAAKrI,eAAe,EAAE;UAC7C6J,SAAS,GAAGxB,UAAU,CAAC5B,GAAa;UACpC,OAAO,IAAI;QACb;MACF,CAAC,CAAC;MAEF,IAAIqD,QAAkB,GAAGH,QAAQ,CAACtB,UAAU,CAAC0B,oBAAoB,CAAC,CAAC,GAC/DJ,QAAQ,CAACtB,UAAU,GACnBsB,QAAQ;MACZ,IACEG,QAAQ,CAACE,OAAO,KAAK,WAAW,KAC/BF,QAAQ,CAACzB,UAAU,CAACU,gBAAgB,CAAC,CAAC,IACrCe,QAAQ,CAACzB,UAAU,CAAC4B,wBAAwB,CAAC,CAAC,CAAC,EACjD;QACAH,QAAQ,GAAGA,QAAQ,CAACzB,UAAU;MAChC,CAAC,MAAM;QACLyB,QAAQ,GAAG,IAAI;MACjB;MAEA,IACGrB,6BAA6B,KAAK,CAAC,CAAC,IACnCoB,SAAS,GAAGpB,6BAA6B,IAC3CgB,eAAe,CAACS,GAAG,CAACJ,QAAQ,CAAC,EAC7B;QACAH,QAAQ,CAAC5D,WAAW,CAACqB,OAAO,CAAC,CAAC,CAAC;MACjC,CAAC,MAAM;QACL,IAAI0C,QAAQ,EAAE;UACZL,eAAe,CAACU,GAAG,CAACL,QAAQ,CAAC;QAC/B;QACAH,QAAQ,CAAC5D,WAAW,CAAC6C,0BAA0B,CAAC,CAAC,CAAC;MACpD;IACF;IAEA,IAAIwB,UAAU;IAEd,IAAItJ,UAAU,CAACH,OAAO,EAAE;MACtByJ,UAAU,GAAIC,SAA8B,IAAK;QAC/C,MAAMC,QAAQ,GAAG1B,0BAA0B,CAAC,CAAC;QAC7C,OAAOyB,SAAS,GACZlK,WAAC,CAAC8H,iBAAiB,CAAC,IAAI,EAAEoC,SAAS,EAAEC,QAAQ,CAAC,GAC9CA,QAAQ;MACd,CAAC;IACH,CAAC,MAAM;MACLF,UAAU,GAAIC,SAAmC,IAAK;QACpD,MAAME,YAA4B,GAAG,CAACnD,OAAO,CAAC,CAAC,CAAC;QAChD,IAAIiD,SAAS,IAAI,IAAI,EAAE;UACrBE,YAAY,CAACvH,IAAI,CAACqH,SAAS,CAAC;QAC9B;QACA,OAAOlK,WAAC,CAACgD,cAAc,CACrBrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,2BAA2B,CAAC,EACtDmH,YACF,CAAC;MACH,CAAC;IACH;IAIA,MAAMC,SAAS,GAAG7I,IAAI,CAAC4B,GAAG,CAAC,MAAM,CAAC;IAClC,MAAMkH,2BAA2B,GAC/BhC,6BAA6B,KAAK,CAAC,CAAC,IACpCA,6BAA6B,GAAG+B,SAAS,CAACpE,MAAM;IAClD,IAAI,CAACoE,SAAS,CAACpE,MAAM,IAAI,CAACoE,SAAS,CAACE,GAAG,CAAC,CAAC,CAACC,iBAAiB,CAAC,CAAC,EAAE;MAC7DhJ,IAAI,CAACiJ,aAAa,CAChB,MAAM,EACNzK,WAAC,CAAC8G,eAAe,CACfwD,2BAA2B,GACvBrD,OAAO,CAAC,CAAC,GACTwB,0BAA0B,CAAC,CACjC,CACF,CAAC;IACH;IAEA,KAAK,MAAMiC,UAAU,IAAI/J,UAAU,CAACM,YAAY,EAAE;MAChDyJ,UAAU,CACPtH,GAAG,CAAC,UAAU,CAAC,CACfwC,WAAW,CAACqE,UAAU,CAACS,UAAU,CAAC5K,IAAI,CAAC4H,QAAQ,CAAC,CAAC;IACtD;EACF;EAKA,SAAS7B,UAAUA,CAAC/F,IAAmB,EAAE6F,OAAsB,EAAE;IAC/D,IAAI7F,IAAI,CAACwD,IAAI,KAAK,QAAQ,EAAE;MAC1B,IAAIqH,aAAa,CAAC7K,IAAI,CAAC,EAAE;IAC3B;IAEA,MAAMkG,SAAS,GAAGlG,IAAI,CAACsC,MAAM,GAAG,QAAQ,GAAG,UAAU;IACrD,MAAML,OAAO,GAAGpB,UAAU,CAACoB,OAAO,CAACiE,SAAS,CAAC;IAE7C,MAAM4E,OAAO,GAAG9K,IAAI,CAACwD,IAAI,KAAK,QAAQ,GAAG,OAAO,GAAGxD,IAAI,CAACwD,IAAI;IAC5D,MAAMgD,GAAG,GACPtG,WAAC,CAAC6K,gBAAgB,CAAC/K,IAAI,CAACwG,GAAG,CAAC,IAAItG,WAAC,CAAC8K,eAAe,CAAChL,IAAI,CAACwG,GAAG,CAAC,GACvDtG,WAAC,CAAC+K,aAAa,CAACC,MAAM,CAAClL,IAAI,CAACwG,GAAG,CAAC2E,KAAK,CAAC,CAAC,GACvCjL,WAAC,CAACkL,aAAa,CAACpL,IAAI,CAAC;IAC3BiC,OAAO,CAACE,WAAW,GAAG,CAACjC,WAAC,CAACmL,eAAe,CAAC7E,GAAG,CAAC;IAE7C,MAAM8E,EAAgB,GAAGzF,OAAO,WAAPA,OAAO,GAAI3F,WAAC,CAACqL,YAAY,CAACvL,IAAI,CAAC;IAExD,IAAIwL,UAAsB;IAC1B,IACE,CAACvJ,OAAO,CAACE,WAAW,IACpBF,OAAO,CAACI,GAAG,CAAC4H,GAAG,CAAEzD,GAAG,CAAqB2E,KAAK,CAAC,EAC/C;MACAK,UAAU,GAAGvJ,OAAO,CAACI,GAAG,CAACiB,GAAG,CAAEkD,GAAG,CAAqB2E,KAAK,CAAC;MAC5DK,UAAU,CAACV,OAAO,CAAC,GAAGQ,EAAE;MAExB,IAAIR,OAAO,KAAK,OAAO,EAAE;QACvBU,UAAU,CAAClI,GAAG,GAAG,IAAI;QACrBkI,UAAU,CAACC,GAAG,GAAG,IAAI;MACvB,CAAC,MAAM;QACLD,UAAU,CAACL,KAAK,GAAG,IAAI;MACzB;IACF,CAAC,MAAM;MACLK,UAAU,GAAG;QACXhF,GAAG,EAEDA,GAAmB;QACrB,CAACsE,OAAO,GAAGQ;MACb,CAAe;MACfrJ,OAAO,CAACG,IAAI,CAACW,IAAI,CAACyI,UAAU,CAAC;MAE7B,IAAI,CAACvJ,OAAO,CAACE,WAAW,EAAE;QACxBF,OAAO,CAACI,GAAG,CAACoJ,GAAG,CAAEjF,GAAG,CAAqB2E,KAAK,EAAEK,UAAU,CAAC;MAC7D;IACF;EACF;EAEA,SAASX,aAAaA,CAAC7K,IAAmB,EAAE;IAC1C,IAAIW,WAAW,CAAC+K,eAAe,IAAI,CAAC1L,IAAI,CAAC0E,UAAU,EAAE;MAEnD,IAAI;QAAE5E;MAAS,CAAC,GAAGe,UAAU;MAC7B,IAAI,CAACb,IAAI,CAACsC,MAAM,EAAE;QAChBqJ,oBAAoB,CAAC,CAAC;QACtB7L,QAAQ,GAAGe,UAAU,CAACiB,UAAU;MAClC;MACA,MAAM8J,UAAU,GAAG1L,WAAC,CAAC6H,gBAAgB,CACnC7H,WAAC,CAACE,SAAS,CAACN,QAAQ,CAAC,EACrBE,IAAI,CAACwG,GAAG,EACRxG,IAAI,CAAC6L,QAAQ,IAAI3L,WAAC,CAAC4L,SAAS,CAAC9L,IAAI,CAACwG,GAAG,CACvC,CAAC;MAED,MAAMvG,IAAkB,GAAGC,WAAC,CAAC6L,kBAAkB,CAE7C/L,IAAI,CAACgM,EAAE,EAEPhM,IAAI,CAACyD,MAAM,EACXzD,IAAI,CAAC0B,IAAI,EACT1B,IAAI,CAACiM,SAAS,EACdjM,IAAI,CAACkM,KACP,CAAC;MACDhM,WAAC,CAACG,QAAQ,CAACJ,IAAI,EAAED,IAAI,CAAC;MAEtB,MAAMmM,IAAI,GAAGjM,WAAC,CAACkM,mBAAmB,CAChClM,WAAC,CAACqI,oBAAoB,CAAC,GAAG,EAAEqD,UAAU,EAAE3L,IAAI,CAC9C,CAAC;MACDC,WAAC,CAACmM,gBAAgB,CAACF,IAAI,EAAEnM,IAAI,CAAC;MAC9Ba,UAAU,CAACa,IAAI,CAACqB,IAAI,CAACoJ,IAAI,CAAC;MAC1B,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEA,SAASR,oBAAoBA,CAAA,EAAG;IAC9B,IAAI9K,UAAU,CAACiB,UAAU,KAAK,IAAI,EAAE;MAClCS,QAAQ,CAAC;QAAET,UAAU,EAAEjB,UAAU,CAACG,KAAK,CAACsL,qBAAqB,CAAC,OAAO;MAAE,CAAC,CAAC;MACzE,MAAMC,UAAU,GAAGrM,WAAC,CAAC6H,gBAAgB,CACnClH,UAAU,CAACf,QAAQ,EACnBI,WAAC,CAAC+D,UAAU,CAAC,WAAW,CAC1B,CAAC;MACD,MAAMuI,gBAAgB,GAAGtM,WAAC,CAACuM,mBAAmB,CAAC,KAAK,EAAE,CACpDvM,WAAC,CAACwM,kBAAkB,CAAC7L,UAAU,CAACiB,UAAU,EAAEyK,UAAU,CAAC,CACxD,CAAC;MAEF1L,UAAU,CAACa,IAAI,CAACqB,IAAI,CAACyJ,gBAAgB,CAAC;IACxC;EACF;EAKA,SAAShH,eAAeA,CACtBrE,YAA2C,EAC3CwL,MAAwB,EACxBpM,IAAgC,EAChC;IACAgC,QAAQ,CAAC;MACPf,mBAAmB,EAAEjB,IAAI;MACzBgB,eAAe,EAAEoL,MAAM;MACvBlL,cAAc,EAAE,IAAI;MACpBN;IACF,CAAC,CAAC;IAEF,MAAM;MAAEG;IAAU,CAAC,GAAGT,UAAU;IAEhCX,WAAC,CAACmM,gBAAgB,CAAC/K,SAAS,EAAEqL,MAAM,CAAC;IAGrCrL,SAAS,CAACmC,MAAM,GAAGkJ,MAAM,CAAClJ,MAAM;IAEhCvD,WAAC,CAACG,QAAQ,CAACiB,SAAS,CAACI,IAAI,EAAEiL,MAAM,CAACjL,IAAI,CAAC;IACvCJ,SAAS,CAACI,IAAI,CAACkL,UAAU,GAAGD,MAAM,CAACjL,IAAI,CAACkL,UAAU;IAIlD,IAAI/L,UAAU,CAACgM,sBAAsB,IAAIhM,UAAU,CAACiM,oBAAoB,EAAE;MACxEzI,eAAe,CAAC,CAAC;IACnB;IAEA2B,kBAAkB,CAAC,CAAC;EACtB;EAKA,SAASA,kBAAkBA,CAAA,EAAG;IAC5B,IAAI,CAACnF,UAAU,CAACO,SAAS,IAAIP,UAAU,CAACe,cAAc,EAAE;IAExDf,UAAU,CAACe,cAAc,GAAG,IAAI;IAKhCf,UAAU,CAACa,IAAI,CAAC8F,OAAO,CACrBtH,WAAC,CAACkM,mBAAmB,CACnBlM,WAAC,CAACgD,cAAc,CACdrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CACvBtC,UAAU,CAACH,OAAO,GAAG,eAAe,GAAG,UACzC,CAAC,EACD,CAACR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,EAAEI,WAAC,CAACE,SAAS,CAACS,UAAU,CAACK,SAAS,CAAC,CACtE,CACF,CACF,CAAC;EACH;EAEA,SAAS6L,kBAAkBA,CAAA,EAAG;IAC5B,MAAM;MAAEhL,WAAW;MAAE/B,IAAI;MAAEgB;IAAM,CAAC,GAAGH,UAAU;IAE/C,KAAK,MAAMmM,IAAI,IAAIhN,IAAI,CAAC0B,IAAI,CAACA,IAAI,EAAE;MACjC,IAAI,CAACxB,WAAC,CAACqD,aAAa,CAACyJ,IAAI,CAAC,IAAI,CAACA,IAAI,CAACnB,QAAQ,EAAE;MAC9C,IAAI7K,KAAK,CAACiM,MAAM,CAACD,IAAI,CAACxG,GAAG,EAAsB,IAAI,CAAC,EAAE;MAEtD,MAAMwF,EAAE,GAAGhL,KAAK,CAACkM,gCAAgC,CAACF,IAAI,CAACxG,GAAG,CAAC;MAC3DzE,WAAW,CAAC0J,GAAG,CAACO,EAAE,CAACnE,IAAI,EAAEmF,IAAI,CAACxG,GAAG,CAAC;MAElCwG,IAAI,CAACxG,GAAG,GAAGwF,EAAE;IACf;EACF;EAEA,SAASmB,sBAAsBA,CAAA,EAAG;IAChC,MAAM;MAAEjM,SAAS;MAAEa;IAAY,CAAC,GAAGlB,UAAU;IAC7C,MAAMuM,aAAa,GAAG,EAAE;IACxB,MAAMC,WAAW,GAAG,EAAE;IAEtB,IAAIxM,UAAU,CAACO,SAAS,EAAE;MACxB,IAAIkM,GAAG,GAAGpN,WAAC,CAACE,SAAS,CAACc,SAAS,CAAC;MAChC,IAAIL,UAAU,CAACQ,aAAa,EAAE;QAC5BiM,GAAG,GAAGpN,WAAC,CAACgD,cAAc,CAACrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,iBAAiB,CAAC,EAAE,CACnEmK,GAAG,CACJ,CAAC;QACF,IAAAC,6BAAc,EAACD,GAAG,CAAC;MACrB;MAEA,MAAME,KAAK,GACT3M,UAAU,CAACG,KAAK,CAACkM,gCAAgC,CAAChM,SAAS,CAAC;MAE9DkM,aAAa,CAACrK,IAAI,CAACyK,KAAK,CAAC;MACzBH,WAAW,CAACtK,IAAI,CAACuK,GAAG,CAAC;MAErB/K,QAAQ,CAAC;QAAErB,SAAS,EAAEhB,WAAC,CAACE,SAAS,CAACoN,KAAK;MAAE,CAAC,CAAC;IAC7C;IAEA,KAAK,MAAM,CAAC3F,IAAI,EAAEsD,KAAK,CAAC,IAAIpJ,WAAW,EAAE;MACvCqL,aAAa,CAACrK,IAAI,CAAC7C,WAAC,CAAC+D,UAAU,CAAC4D,IAAI,CAAC,CAAC;MACtCwF,WAAW,CAACtK,IAAI,CAACoI,KAAK,CAAC;IACzB;IAEA,OAAO;MAAEiC,aAAa;MAAEC;IAAY,CAAC;EACvC;EAEA,SAASI,gBAAgBA,CACvBlN,IAAuB,EACvBC,IAAU,EACVC,cAAmC,EACnCC,OAAgB,EAChB;IACA6B,QAAQ,CAAC;MACPzB,MAAM,EAAEP,IAAI,CAACO,MAAM;MACnBE,KAAK,EAAET,IAAI,CAACS,KAAK;MACjBhB,IAAI,EAAEO,IAAI,CAACP,IAAI;MACfO,IAAI;MACJC,IAAI;MACJE;IACF,CAAC,CAAC;IAEF6B,QAAQ,CAAC;MACPtB,OAAO,EAAEJ,UAAU,CAACb,IAAI,CAACgM,EAAE;MAE3BlM,QAAQ,EAAEe,UAAU,CAACb,IAAI,CAACgM,EAAE,GACxB9L,WAAC,CAAC+D,UAAU,CAACpD,UAAU,CAACb,IAAI,CAACgM,EAAE,CAACnE,IAAI,CAAC,GACrChH,UAAU,CAACG,KAAK,CAACsL,qBAAqB,CAAC,OAAO,CAAC;MACnDpL,SAAS,EAAEL,UAAU,CAACb,IAAI,CAAC0N,UAAU;MACrCtM,SAAS,EAAE,CAAC,CAACP,UAAU,CAACb,IAAI,CAAC0N,UAAU;MACvC3N,eAAe,EAAEG,WAAC,CAAC4D,cAAc,CAAC,EAAE;IACtC,CAAC,CAAC;IAEFvB,QAAQ,CAAC;MACPlB,aAAa,EACXnB,WAAC,CAACyH,YAAY,CAAC9G,UAAU,CAACK,SAAS,CAAC,IACpCT,cAAc,CAACwJ,GAAG,CAACpJ,UAAU,CAACK,SAAS,CAAC2G,IAAI,CAAC,IAC7C,CAAChH,UAAU,CAACG,KAAK,CAAC2M,UAAU,CAC1B9M,UAAU,CAACK,SAAS,CAAC2G,IAAI,EACT,IAClB;IACJ,CAAC,CAAC;IAEF,MAAM;MAAE/H,QAAQ;MAAEE,IAAI;MAAED;IAAgB,CAAC,GAAGc,UAAU;IAEtD0B,QAAQ,CAAC;MACPjB,SAAS,EAAEzB,gBAAgB,CAACC,QAAQ,EAAEC,eAAe,EAAEC,IAAI;IAC7D,CAAC,CAAC;IAEF+M,kBAAkB,CAAC,CAAC;IAEpB,MAAM;MAAErL;IAAK,CAAC,GAAGb,UAAU;IAC3B,MAAM;MAAEuM,aAAa;MAAEC;IAAY,CAAC,GAAGF,sBAAsB,CAAC,CAAC;IAE/DjJ,SAAS,CAAC,CAAC;IAGX,IAAI,CAACvD,WAAW,CAACiN,YAAY,EAAE;MAC7B7N,eAAe,CAAC2B,IAAI,CAAC8F,OAAO,CAC1BtH,WAAC,CAACkM,mBAAmB,CACnBlM,WAAC,CAACgD,cAAc,CAACrC,UAAU,CAACL,IAAI,CAAC2C,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAC5DjD,WAAC,CAACuH,cAAc,CAAC,CAAC,EAClBvH,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CACjC,CACH,CACF,CAAC;IACH;IAEA,MAAM+N,QAAQ,GAAGtN,IAAI,CAACuN,cAAc,CAAC,CAAC;IACtC,IAAIC,eAAe,GAAGrM,IAAI,CAACyE,MAAM,KAAK,CAAC;IACvC,IAAI4H,eAAe,IAAI,CAACF,QAAQ,EAAE;MAChC,KAAK,MAAML,KAAK,IAAI3M,UAAU,CAACS,SAAS,CAACmC,MAAM,EAAE;QAI/C,IAAI,CAACvD,WAAC,CAACyH,YAAY,CAAC6F,KAAK,CAAC,EAAE;UAC1BO,eAAe,GAAG,KAAK;UACvB;QACF;MACF;IACF;IAEA,MAAMnB,UAAU,GAAGmB,eAAe,GAC9BlN,UAAU,CAACS,SAAS,CAACI,IAAI,CAACkL,UAAU,GACpC,EAAE;IACN,IAAI,CAACiB,QAAQ,EAAE;MACbjB,UAAU,CAAC7J,IAAI,CAAC7C,WAAC,CAAC8N,SAAS,CAAC9N,WAAC,CAAC+N,gBAAgB,CAAC,YAAY,CAAC,CAAC,CAAC;IAChE;IAEA,IAAIF,eAAe,EAAE;MAEnB,MAAM5B,IAAI,GAAGjM,WAAC,CAACqL,YAAY,CAAC1K,UAAU,CAACS,SAAS,CAAC;MACjD,OAAOT,UAAU,CAACH,OAAO,GAAGyL,IAAI,GAAGnJ,iBAAiB,CAAC,CAACmJ,IAAI,CAAC,CAAC;IAC9D;IAEA,IAAI,CAACtL,UAAU,CAACgB,iBAAiB,EAAE;MACjCH,IAAI,CAACqB,IAAI,CACP7C,WAAC,CAAC8G,eAAe,CACfnG,UAAU,CAACH,OAAO,GACdR,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,GAChCkD,iBAAiB,CAAC,CAAC9C,WAAC,CAACE,SAAS,CAACS,UAAU,CAACf,QAAQ,CAAC,CAAC,CAC1D,CACF,CAAC;IACH;IAEA4B,IAAI,CAAC8F,OAAO,CAAC3G,UAAU,CAACS,SAAS,CAAC;IAElC,MAAMgH,SAAS,GAAGpI,WAAC,CAACgO,uBAAuB,CACzCd,aAAa,EACblN,WAAC,CAAC4D,cAAc,CAACpC,IAAI,EAAEkL,UAAU,CACnC,CAAC;IACD,OAAO1M,WAAC,CAACgD,cAAc,CAACoF,SAAS,EAAE+E,WAAW,CAAC;EACjD;EAEA,OAAOI,gBAAgB,CAAClN,IAAI,EAAEC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;AAC9D", "ignoreList": []}