import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/refractor/lang/csv.js
var require_csv = __commonJS({
  "node_modules/refractor/lang/csv.js"(exports, module) {
    module.exports = csv;
    csv.displayName = "csv";
    csv.aliases = [];
    function csv(Prism) {
      Prism.languages.csv = {
        value: /[^\r\n,"]+|"(?:[^"]|"")*"(?!")/,
        punctuation: /,/
      };
    }
  }
});

export {
  require_csv
};
//# sourceMappingURL=chunk-JLI5XFDD.js.map
