{"version": 3, "sources": ["../../highlight.js/lib/languages/csp.js"], "sourcesContent": ["/*\nLanguage: CSP\nDescription: Content Security Policy definition highlighting\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP\n\nvim: ts=2 sw=2 st=2\n*/\n\n/** @type LanguageFn */\nfunction csp(hljs) {\n  return {\n    name: 'CSP',\n    case_insensitive: false,\n    keywords: {\n      $pattern: '[a-zA-Z][a-zA-Z0-9_-]*',\n      keyword: 'base-uri child-src connect-src default-src font-src form-action ' +\n        'frame-ancestors frame-src img-src media-src object-src plugin-types ' +\n        'report-uri sandbox script-src style-src'\n    },\n    contains: [\n      {\n        className: 'string',\n        begin: \"'\",\n        end: \"'\"\n      },\n      {\n        className: 'attribute',\n        begin: '^Content',\n        end: ':',\n        excludeEnd: true\n      }\n    ]\n  };\n}\n\nmodule.exports = csp;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,aAAS,IAAI,MAAM;AACjB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SAAS;AAAA,QAGX;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,UACd;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}