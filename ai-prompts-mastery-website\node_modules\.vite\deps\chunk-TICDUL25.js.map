{"version": 3, "sources": ["../../refractor/lang/mermaid.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = mermaid\nmermaid.displayName = 'mermaid'\nmermaid.aliases = []\nfunction mermaid(Prism) {\n  Prism.languages.mermaid = {\n    comment: {\n      pattern: /%%.*/,\n      greedy: true\n    },\n    style: {\n      pattern:\n        /^([ \\t]*(?:classDef|linkStyle|style)[ \\t]+[\\w$-]+[ \\t]+)\\w.*[^\\s;]/m,\n      lookbehind: true,\n      inside: {\n        property: /\\b\\w[\\w-]*(?=[ \\t]*:)/,\n        operator: /:/,\n        punctuation: /,/\n      }\n    },\n    'inter-arrow-label': {\n      pattern:\n        /([^<>ox.=-])(?:-[-.]|==)(?![<>ox.=-])[ \\t]*(?:\"[^\"\\r\\n]*\"|[^\\s\".=-](?:[^\\r\\n.=-]*[^\\s.=-])?)[ \\t]*(?:\\.+->?|--+[->]|==+[=>])(?![<>ox.=-])/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        arrow: {\n          pattern: /(?:\\.+->?|--+[->]|==+[=>])$/,\n          alias: 'operator'\n        },\n        label: {\n          pattern: /^([\\s\\S]{2}[ \\t]*)\\S(?:[\\s\\S]*\\S)?/,\n          lookbehind: true,\n          alias: 'property'\n        },\n        'arrow-head': {\n          pattern: /^\\S+/,\n          alias: ['arrow', 'operator']\n        }\n      }\n    },\n    arrow: [\n      // This might look complex but it really isn't.\n      // There are many possible arrows (see tests) and it's impossible to fit all of them into one pattern. The\n      // problem is that we only have one lookbehind per pattern. However, we cannot disallow too many arrow\n      // characters in the one lookbehind because that would create too many false negatives. So we have to split the\n      // arrows into different patterns.\n      {\n        // ER diagram\n        pattern: /(^|[^{}|o.-])[|}][|o](?:--|\\.\\.)[|o][|{](?![{}|o.-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // flow chart\n        // (?:==+|--+|-\\.*-)\n        pattern:\n          /(^|[^<>ox.=-])(?:[<ox](?:==+|--+|-\\.*-)[>ox]?|(?:==+|--+|-\\.*-)[>ox]|===+|---+|-\\.+-)(?![<>ox.=-])/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // sequence diagram\n        pattern:\n          /(^|[^<>()x-])(?:--?(?:>>|[x>)])(?![<>()x])|(?:<<|[x<(])--?(?!-))/,\n        lookbehind: true,\n        alias: 'operator'\n      },\n      {\n        // class diagram\n        pattern:\n          /(^|[^<>|*o.-])(?:[*o]--|--[*o]|<\\|?(?:--|\\.\\.)|(?:--|\\.\\.)\\|?>|--|\\.\\.)(?![<>|*o.-])/,\n        lookbehind: true,\n        alias: 'operator'\n      }\n    ],\n    label: {\n      pattern: /(^|[^|<])\\|(?:[^\\r\\n\"|]|\"[^\"\\r\\n]*\")+\\|/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    text: {\n      pattern: /(?:[(\\[{]+|\\b>)(?:[^\\r\\n\"()\\[\\]{}]|\"[^\"\\r\\n]*\")+(?:[)\\]}]+|>)/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"[^\"\\r\\n]*\"/,\n      greedy: true\n    },\n    annotation: {\n      pattern:\n        /<<(?:abstract|choice|enumeration|fork|interface|join|service)>>|\\[\\[(?:choice|fork|join)\\]\\]/i,\n      alias: 'important'\n    },\n    keyword: [\n      // This language has both case-sensitive and case-insensitive keywords\n      {\n        pattern:\n          /(^[ \\t]*)(?:action|callback|class|classDef|classDiagram|click|direction|erDiagram|flowchart|gantt|gitGraph|graph|journey|link|linkStyle|pie|requirementDiagram|sequenceDiagram|stateDiagram|stateDiagram-v2|style|subgraph)(?![\\w$-])/m,\n        lookbehind: true,\n        greedy: true\n      },\n      {\n        pattern:\n          /(^[ \\t]*)(?:activate|alt|and|as|autonumber|deactivate|else|end(?:[ \\t]+note)?|loop|opt|par|participant|rect|state|note[ \\t]+(?:over|(?:left|right)[ \\t]+of))(?![\\w$-])/im,\n        lookbehind: true,\n        greedy: true\n      }\n    ],\n    entity: /#[a-z0-9]+;/,\n    operator: {\n      pattern: /(\\w[ \\t]*)&(?=[ \\t]*\\w)|:::|:/,\n      lookbehind: true\n    },\n    punctuation: /[(){};]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,OAAO;AAAA,UACL,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,UAAU;AAAA,YACV,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,qBAAqB;AAAA,UACnB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,OAAO;AAAA,cACL,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA,OAAO;AAAA,cACL,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,cAAc;AAAA,cACZ,SAAS;AAAA,cACT,OAAO,CAAC,SAAS,UAAU;AAAA,YAC7B;AAAA,UACF;AAAA,QACF;AAAA,QACA,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAML;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA;AAAA,YAGE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,YAAY;AAAA,UACV,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA;AAAA,UAEP;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,QACR,UAAU;AAAA,UACR,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}