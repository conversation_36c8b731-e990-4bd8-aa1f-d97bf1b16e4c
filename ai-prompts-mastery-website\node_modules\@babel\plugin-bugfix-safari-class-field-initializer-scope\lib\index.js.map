{"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\nimport type { NodePath } from \"@babel/core\";\n\nfunction needsWrapping(node: t.Node): boolean {\n  if (t.isLiteral(node) && !t.isTemplateLiteral(node)) {\n    return false;\n  }\n\n  if (\n    t.isCallExpression(node) ||\n    t.isOptionalCallExpression(node) ||\n    t.isNewExpression(node)\n  ) {\n    return needsWrapping(node.callee) || node.arguments.some(needsWrapping);\n  }\n\n  if (t.isTemplateLiteral(node)) {\n    return node.expressions.some(needsWrapping);\n  }\n\n  if (t.isTaggedTemplateExpression(node)) {\n    return needsWrapping(node.tag) || needsWrapping(node.quasi);\n  }\n\n  if (t.isArrayExpression(node)) {\n    return node.elements.some(needsWrapping);\n  }\n\n  if (t.isObjectExpression(node)) {\n    return node.properties.some(prop => {\n      if (t.isObjectProperty(prop)) {\n        return (\n          needsWrapping(prop.value) ||\n          (prop.computed && needsWrapping(prop.key))\n        );\n      }\n      if (t.isObjectMethod(prop)) {\n        return false;\n      }\n      return false;\n    });\n  }\n\n  if (t.isMemberExpression(node) || t.isOptionalMemberExpression(node)) {\n    return (\n      needsWrapping(node.object) ||\n      (node.computed && needsWrapping(node.property))\n    );\n  }\n\n  if (\n    t.isFunctionExpression(node) ||\n    t.isArrowFunctionExpression(node) ||\n    t.isClassExpression(node)\n  ) {\n    return false;\n  }\n\n  if (t.isThisExpression(node)) {\n    return false;\n  }\n\n  if (t.isSequenceExpression(node)) {\n    return node.expressions.some(needsWrapping);\n  }\n\n  // Is an identifier, or anything else not covered above\n  return true;\n}\n\nfunction wrapInitializer(\n  path: NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n) {\n  const { value } = path.node;\n\n  if (value && needsWrapping(value)) {\n    path.node.value = t.callExpression(\n      t.arrowFunctionExpression([], value),\n      [],\n    );\n  }\n}\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(\"^7.16.0\"));\n\n  return {\n    name: \"plugin-bugfix-safari-class-field-initializer-scope\",\n\n    visitor: {\n      ClassProperty(path) {\n        wrapInitializer(path);\n      },\n      ClassPrivateProperty(path) {\n        wrapInitializer(path);\n      },\n    },\n  };\n});\n"], "names": ["needsWrapping", "node", "t", "isLiteral", "isTemplateLiteral", "isCallExpression", "isOptionalCallExpression", "isNewExpression", "callee", "arguments", "some", "expressions", "isTaggedTemplateExpression", "tag", "quasi", "isArrayExpression", "elements", "isObjectExpression", "properties", "prop", "isObjectProperty", "value", "computed", "key", "isObjectMethod", "isMemberExpression", "isOptionalMemberExpression", "object", "property", "isFunctionExpression", "isArrowFunctionExpression", "isClassExpression", "isThisExpression", "isSequenceExpression", "wrapInitializer", "path", "callExpression", "arrowFunctionExpression", "declare", "api", "assertVersion", "name", "visitor", "ClassProperty", "ClassPrivateProperty"], "mappings": ";;;;;;;AAIA,SAASA,aAAaA,CAACC,IAAY,EAAW;AAC5C,EAAA,IAAIC,UAAC,CAACC,SAAS,CAACF,IAAI,CAAC,IAAI,CAACC,UAAC,CAACE,iBAAiB,CAACH,IAAI,CAAC,EAAE;AACnD,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;EAEA,IACEC,UAAC,CAACG,gBAAgB,CAACJ,IAAI,CAAC,IACxBC,UAAC,CAACI,wBAAwB,CAACL,IAAI,CAAC,IAChCC,UAAC,CAACK,eAAe,CAACN,IAAI,CAAC,EACvB;AACA,IAAA,OAAOD,aAAa,CAACC,IAAI,CAACO,MAAM,CAAC,IAAIP,IAAI,CAACQ,SAAS,CAACC,IAAI,CAACV,aAAa,CAAC,CAAA;AACzE,GAAA;AAEA,EAAA,IAAIE,UAAC,CAACE,iBAAiB,CAACH,IAAI,CAAC,EAAE;AAC7B,IAAA,OAAOA,IAAI,CAACU,WAAW,CAACD,IAAI,CAACV,aAAa,CAAC,CAAA;AAC7C,GAAA;AAEA,EAAA,IAAIE,UAAC,CAACU,0BAA0B,CAACX,IAAI,CAAC,EAAE;AACtC,IAAA,OAAOD,aAAa,CAACC,IAAI,CAACY,GAAG,CAAC,IAAIb,aAAa,CAACC,IAAI,CAACa,KAAK,CAAC,CAAA;AAC7D,GAAA;AAEA,EAAA,IAAIZ,UAAC,CAACa,iBAAiB,CAACd,IAAI,CAAC,EAAE;AAC7B,IAAA,OAAOA,IAAI,CAACe,QAAQ,CAACN,IAAI,CAACV,aAAa,CAAC,CAAA;AAC1C,GAAA;AAEA,EAAA,IAAIE,UAAC,CAACe,kBAAkB,CAAChB,IAAI,CAAC,EAAE;AAC9B,IAAA,OAAOA,IAAI,CAACiB,UAAU,CAACR,IAAI,CAACS,IAAI,IAAI;AAClC,MAAA,IAAIjB,UAAC,CAACkB,gBAAgB,CAACD,IAAI,CAAC,EAAE;AAC5B,QAAA,OACEnB,aAAa,CAACmB,IAAI,CAACE,KAAK,CAAC,IACxBF,IAAI,CAACG,QAAQ,IAAItB,aAAa,CAACmB,IAAI,CAACI,GAAG,CAAE,CAAA;AAE9C,OAAA;AACA,MAAA,IAAIrB,UAAC,CAACsB,cAAc,CAACL,IAAI,CAAC,EAAE;AAC1B,QAAA,OAAO,KAAK,CAAA;AACd,OAAA;AACA,MAAA,OAAO,KAAK,CAAA;AACd,KAAC,CAAC,CAAA;AACJ,GAAA;AAEA,EAAA,IAAIjB,UAAC,CAACuB,kBAAkB,CAACxB,IAAI,CAAC,IAAIC,UAAC,CAACwB,0BAA0B,CAACzB,IAAI,CAAC,EAAE;AACpE,IAAA,OACED,aAAa,CAACC,IAAI,CAAC0B,MAAM,CAAC,IACzB1B,IAAI,CAACqB,QAAQ,IAAItB,aAAa,CAACC,IAAI,CAAC2B,QAAQ,CAAE,CAAA;AAEnD,GAAA;EAEA,IACE1B,UAAC,CAAC2B,oBAAoB,CAAC5B,IAAI,CAAC,IAC5BC,UAAC,CAAC4B,yBAAyB,CAAC7B,IAAI,CAAC,IACjCC,UAAC,CAAC6B,iBAAiB,CAAC9B,IAAI,CAAC,EACzB;AACA,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,IAAIC,UAAC,CAAC8B,gBAAgB,CAAC/B,IAAI,CAAC,EAAE;AAC5B,IAAA,OAAO,KAAK,CAAA;AACd,GAAA;AAEA,EAAA,IAAIC,UAAC,CAAC+B,oBAAoB,CAAChC,IAAI,CAAC,EAAE;AAChC,IAAA,OAAOA,IAAI,CAACU,WAAW,CAACD,IAAI,CAACV,aAAa,CAAC,CAAA;AAC7C,GAAA;AAGA,EAAA,OAAO,IAAI,CAAA;AACb,CAAA;AAEA,SAASkC,eAAeA,CACtBC,IAAwD,EACxD;EACA,MAAM;AAAEd,IAAAA,KAAAA;GAAO,GAAGc,IAAI,CAAClC,IAAI,CAAA;AAE3B,EAAA,IAAIoB,KAAK,IAAIrB,aAAa,CAACqB,KAAK,CAAC,EAAE;AACjCc,IAAAA,IAAI,CAAClC,IAAI,CAACoB,KAAK,GAAGnB,UAAC,CAACkC,cAAc,CAChClC,UAAC,CAACmC,uBAAuB,CAAC,EAAE,EAAEhB,KAAK,CAAC,EACpC,EACF,CAAC,CAAA;AACH,GAAA;AACF,CAAA;AAEA,YAAeiB,yBAAO,CAACC,GAAG,IAAI;AAC5BA,EAAAA,GAAG,CAACC,aAAa,CAAkB,SAAU,CAAC,CAAA;EAE9C,OAAO;AACLC,IAAAA,IAAI,EAAE,oDAAoD;AAE1DC,IAAAA,OAAO,EAAE;MACPC,aAAaA,CAACR,IAAI,EAAE;QAClBD,eAAe,CAACC,IAAI,CAAC,CAAA;OACtB;MACDS,oBAAoBA,CAACT,IAAI,EAAE;QACzBD,eAAe,CAACC,IAAI,CAAC,CAAA;AACvB,OAAA;AACF,KAAA;GACD,CAAA;AACH,CAAC,CAAC;;;;"}