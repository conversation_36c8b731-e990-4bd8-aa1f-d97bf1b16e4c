{"version": 3, "file": "accessible-description.mjs", "names": ["computeTextAlternative", "queryIdRefs", "computeAccessibleDescription", "root", "options", "description", "map", "element", "compute", "join", "title", "getAttribute"], "sources": ["../sources/accessible-description.ts"], "sourcesContent": ["import {\n\tcomputeTextAlternative,\n\tComputeTextAlternativeOptions,\n} from \"./accessible-name-and-description\";\nimport { queryIdRefs } from \"./util\";\n\n/**\n * @param root\n * @param options\n * @returns\n */\nexport function computeAccessibleDescription(\n\troot: Element,\n\toptions: ComputeTextAlternativeOptions = {}\n): string {\n\tlet description = queryIdRefs(root, \"aria-describedby\")\n\t\t.map((element) => {\n\t\t\treturn computeTextAlternative(element, {\n\t\t\t\t...options,\n\t\t\t\tcompute: \"description\",\n\t\t\t});\n\t\t})\n\t\t.join(\" \");\n\n\t// TODO: Technically we need to make sure that node wasn't used for the accessible name\n\t//       This causes `description_1.0_combobox-focusable-manual` to fail\n\t//\n\t// https://www.w3.org/TR/html-aam-1.0/#accessible-name-and-description-computation\n\t// says for so many elements to use the `title` that we assume all elements are considered\n\tif (description === \"\") {\n\t\tconst title = root.getAttribute(\"title\");\n\t\tdescription = title === null ? \"\" : title;\n\t}\n\n\treturn description;\n}\n"], "mappings": ";;;;;;AAAA,SACCA,sBAAsB,QAEhB,uCAAmC;AAC1C,SAASC,WAAW,QAAQ,YAAQ;;AAEpC;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4B,CAC3CC,IAAa,EAEJ;EAAA,IADTC,OAAsC,uEAAG,CAAC,CAAC;EAE3C,IAAIC,WAAW,GAAGJ,WAAW,CAACE,IAAI,EAAE,kBAAkB,CAAC,CACrDG,GAAG,CAAC,UAACC,OAAO,EAAK;IACjB,OAAOP,sBAAsB,CAACO,OAAO,kCACjCH,OAAO;MACVI,OAAO,EAAE;IAAa,GACrB;EACH,CAAC,CAAC,CACDC,IAAI,CAAC,GAAG,CAAC;;EAEX;EACA;EACA;EACA;EACA;EACA,IAAIJ,WAAW,KAAK,EAAE,EAAE;IACvB,IAAMK,KAAK,GAAGP,IAAI,CAACQ,YAAY,CAAC,OAAO,CAAC;IACxCN,WAAW,GAAGK,KAAK,KAAK,IAAI,GAAG,EAAE,GAAGA,KAAK;EAC1C;EAEA,OAAOL,WAAW;AACnB"}