{"version": 3, "sources": ["../../highlight.js/lib/languages/twig.js"], "sourcesContent": ["/*\nLanguage: Twig\nRequires: xml.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: Twig is a templating language for PHP\nWebsite: https://twig.symfony.com\nCategory: template\n*/\n\nfunction twig(hljs) {\n  var PARAMS = {\n    className: 'params',\n    begin: '\\\\(', end: '\\\\)'\n  };\n\n  var FUNCTION_NAMES = 'attribute block constant cycle date dump include ' +\n                  'max min parent random range source template_from_string';\n\n  var FUNCTIONS = {\n    beginKeywords: FUNCTION_NAMES,\n    keywords: {name: FUNCTION_NAMES},\n    relevance: 0,\n    contains: [\n      PARAMS\n    ]\n  };\n\n  var FILTER = {\n    begin: /\\|[A-Za-z_]+:?/,\n    keywords:\n      'abs batch capitalize column convert_encoding date date_modify default ' +\n      'escape filter first format inky_to_html inline_css join json_encode keys last ' +\n      'length lower map markdown merge nl2br number_format raw reduce replace ' +\n      'reverse round slice sort spaceless split striptags title trim upper url_encode',\n    contains: [\n      FUNCTIONS\n    ]\n  };\n\n  var TAGS = 'apply autoescape block deprecated do embed extends filter flush for from ' +\n    'if import include macro sandbox set use verbatim with';\n\n  TAGS = TAGS + ' ' + TAGS.split(' ').map(function(t){return 'end' + t}).join(' ');\n\n  return {\n    name: 'Twig',\n    aliases: ['craftcms'],\n    case_insensitive: true,\n    subLanguage: 'xml',\n    contains: [\n      hljs.COMMENT(/\\{#/, /#\\}/),\n      {\n        className: 'template-tag',\n        begin: /\\{%/, end: /%\\}/,\n        contains: [\n          {\n            className: 'name',\n            begin: /\\w+/,\n            keywords: TAGS,\n            starts: {\n              endsWithParent: true,\n              contains: [FILTER, FUNCTIONS],\n              relevance: 0\n            }\n          }\n        ]\n      },\n      {\n        className: 'template-variable',\n        begin: /\\{\\{/, end: /\\}\\}/,\n        contains: ['self', FILTER, FUNCTIONS]\n      }\n    ]\n  };\n}\n\nmodule.exports = twig;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,KAAK,MAAM;AAClB,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,QAAO,KAAK;AAAA,MACrB;AAEA,UAAI,iBAAiB;AAGrB,UAAI,YAAY;AAAA,QACd,eAAe;AAAA,QACf,UAAU,EAAC,MAAM,eAAc;AAAA,QAC/B,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,SAAS;AAAA,QACX,OAAO;AAAA,QACP,UACE;AAAA,QAIF,UAAU;AAAA,UACR;AAAA,QACF;AAAA,MACF;AAEA,UAAI,OAAO;AAGX,aAAO,OAAO,MAAM,KAAK,MAAM,GAAG,EAAE,IAAI,SAAS,GAAE;AAAC,eAAO,QAAQ;AAAA,MAAC,CAAC,EAAE,KAAK,GAAG;AAE/E,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,UAAU;AAAA,QACpB,kBAAkB;AAAA,QAClB,aAAa;AAAA,QACb,UAAU;AAAA,UACR,KAAK,QAAQ,OAAO,KAAK;AAAA,UACzB;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAAO,KAAK;AAAA,YACnB,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,UAAU;AAAA,gBACV,QAAQ;AAAA,kBACN,gBAAgB;AAAA,kBAChB,UAAU,CAAC,QAAQ,SAAS;AAAA,kBAC5B,WAAW;AAAA,gBACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YAAQ,KAAK;AAAA,YACpB,UAAU,CAAC,QAAQ,QAAQ,SAAS;AAAA,UACtC;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}