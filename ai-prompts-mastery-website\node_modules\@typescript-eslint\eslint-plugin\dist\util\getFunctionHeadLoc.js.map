{"version": 3, "file": "getFunctionHeadLoc.js", "sourceRoot": "", "sources": ["../../src/util/getFunctionHeadLoc.ts"], "names": [], "mappings": ";AAAA,uIAAuI;;;AAGvI,oDAAuE;AAEvE,yCAA+D;AAO/D;;;;;GAKG;AACH,SAAS,uBAAuB,CAC9B,IAAkB,EAClB,UAA+B;IAE/B,4GAA4G;IAC5G,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB;QACpD,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EACxB,CAAC;QACD,MAAM,QAAQ,GAAG,mBAAW,CAAC,UAAU,CACrC,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EACxC,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAC1E,CAAC;QACF,MAAM,eAAe,GAAG,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;QAE5D,OAAO,eAAe,IAAI,IAAA,8BAAmB,EAAC,eAAe,CAAC;YAC5D,CAAC,CAAC,eAAe;YACjB,CAAC,CAAC,QAAQ,CAAC;IACf,CAAC;IAED,4BAA4B;IAC5B,OAAO,IAAI,CAAC,EAAE,IAAI,IAAI;QACpB,CAAC,CAAC,mBAAW,CAAC,UAAU,CACpB,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,EAAE,8BAAmB,CAAC,EACtD,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,CAAC,CAC7D;QACH,CAAC,CAAC,mBAAW,CAAC,UAAU,CACpB,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,8BAAmB,CAAC,EACnD,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CACxC,qBAAqB,EACrB,UAAU,CACX,CACF,CAAC;AACR,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAgGG;AACH,SAAgB,kBAAkB,CAChC,IAAkB,EAClB,UAA+B;IAE/B,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAC3B,IAAI,KAAK,GAAG,IAAI,CAAC;IACjB,IAAI,GAAG,GAAG,IAAI,CAAC;IAEf,IACE,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;QAC/C,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,EACjD,CAAC;QACD,sDAAsD;QACtD,0EAA0E;QAC1E,wBAAwB;QACxB,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjC,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YACtE,MAAM,wBAAwB,GAAG,mBAAW,CAAC,UAAU,CACrD,UAAU,CAAC,aAAa,CAAC,aAAa,CAAC,EACvC,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CACxC,yBAAyB,EACzB,cAAc,CACf,CACF,CAAC;YACF,KAAK,GAAG,wBAAwB,CAAC,GAAG,CAAC,KAAK,CAAC;QAC7C,CAAC;aAAM,CAAC;YACN,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;QAC3B,CAAC;QACD,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;IAC5D,CAAC;SAAM,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ,EAAE,CAAC;QACnD,KAAK,GAAG,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC;QACzB,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;IAC5D,CAAC;SAAM,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,uBAAuB,EAAE,CAAC;QAChE,MAAM,UAAU,GAAG,mBAAW,CAAC,UAAU,CACvC,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,uBAAY,CAAC,EAClD,mBAAW,CAAC,iBAAiB,CAAC,YAAY,CACxC,aAAa,EACb,gBAAgB,CACjB,CACF,CAAC;QAEF,KAAK,GAAG,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC;QAC7B,GAAG,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3B,CAAC;SAAM,CAAC;QACN,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;QACvB,GAAG,GAAG,uBAAuB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC;IAC5D,CAAC;IAED,OAAO;QACL,KAAK,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,CAAC;QAC/B,GAAG,EAAE,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC;KAC5B,CAAC;AACJ,CAAC;AApDD,gDAoDC"}