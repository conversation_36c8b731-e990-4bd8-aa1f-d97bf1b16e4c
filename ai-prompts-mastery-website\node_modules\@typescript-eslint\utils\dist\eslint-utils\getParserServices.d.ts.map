{"version": 3, "file": "getParserServices.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/getParserServices.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,KAAK,QAAQ,MAAM,cAAc,CAAC;AAC9C,OAAO,KAAK,EACV,cAAc,EACd,iCAAiC,EAClC,MAAM,cAAc,CAAC;AAUtB;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,SAAS,OAAO,EAAE,EAEnC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,GAC7D,iCAAiC,CAAC;AACrC;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,SAAS,OAAO,EAAE,EAEnC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,EAC9D,+BAA+B,EAAE,KAAK,GACrC,iCAAiC,CAAC;AACrC;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,SAAS,OAAO,EAAE,EAEnC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,EAC9D,+BAA+B,EAAE,IAAI,GACpC,cAAc,CAAC;AAClB;;;GAGG;AACH,iBAAS,iBAAiB,CACxB,WAAW,SAAS,MAAM,EAC1B,QAAQ,SAAS,SAAS,OAAO,EAAE,EAEnC,OAAO,EAAE,QAAQ,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,EAC9D,+BAA+B,EAAE,OAAO,GACvC,cAAc,CAAC;AAmDlB,OAAO,EAAE,iBAAiB,EAAE,CAAC"}