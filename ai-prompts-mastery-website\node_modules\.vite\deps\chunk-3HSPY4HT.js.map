{"version": 3, "sources": ["../../highlight.js/lib/languages/shell.js"], "sourcesContent": ["/*\nLanguage: Shell Session\nRequires: bash.js\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\nCategory: common\nAudit: 2020\n*/\n\n/** @type LanguageFn */\nfunction shell(hljs) {\n  return {\n    name: 'Shell Session',\n    aliases: [ 'console' ],\n    contains: [\n      {\n        className: 'meta',\n        // We cannot add \\s (spaces) in the regular expression otherwise it will be too broad and produce unexpected result.\n        // For instance, in the following example, it would match \"echo /path/to/home >\" as a prompt:\n        // echo /path/to/home > t.exe\n        begin: /^\\s{0,3}[/~\\w\\d[\\]()@-]*[>%$#]/,\n        starts: {\n          end: /[^\\\\](?=\\s*$)/,\n          subLanguage: 'bash'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = shell;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,MAAM,MAAM;AACnB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,SAAU;AAAA,QACrB,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA;AAAA;AAAA;AAAA,YAIX,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}