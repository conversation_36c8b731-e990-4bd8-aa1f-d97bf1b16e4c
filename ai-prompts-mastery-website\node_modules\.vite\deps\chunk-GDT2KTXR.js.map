{"version": 3, "sources": ["../../refractor/lang/actionscript.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = actionscript\nactionscript.displayName = 'actionscript'\nactionscript.aliases = []\nfunction actionscript(Prism) {\n  Prism.languages.actionscript = Prism.languages.extend('javascript', {\n    keyword:\n      /\\b(?:as|break|case|catch|class|const|default|delete|do|dynamic|each|else|extends|final|finally|for|function|get|if|implements|import|in|include|instanceof|interface|internal|is|namespace|native|new|null|override|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|use|var|void|while|with)\\b/,\n    operator: /\\+\\+|--|(?:[+\\-*\\/%^]|&&?|\\|\\|?|<<?|>>?>?|[!=]=?)=?|[~?@]/\n  })\n  Prism.languages.actionscript['class-name'].alias = 'function' // doesn't work with AS because AS is too complex\n  delete Prism.languages.actionscript['parameter']\n  delete Prism.languages.actionscript['literal-property']\n  if (Prism.languages.markup) {\n    Prism.languages.insertBefore('actionscript', 'string', {\n      xml: {\n        pattern:\n          /(^|[^.])<\\/?\\w+(?:\\s+[^\\s>\\/=]+=(\"|')(?:\\\\[\\s\\S]|(?!\\2)[^\\\\])*\\2)*\\s*\\/?>/,\n        lookbehind: true,\n        inside: Prism.languages.markup\n      }\n    })\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,iBAAa,cAAc;AAC3B,iBAAa,UAAU,CAAC;AACxB,aAAS,aAAa,OAAO;AAC3B,YAAM,UAAU,eAAe,MAAM,UAAU,OAAO,cAAc;AAAA,QAClE,SACE;AAAA,QACF,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,YAAY,EAAE,QAAQ;AACnD,aAAO,MAAM,UAAU,aAAa,WAAW;AAC/C,aAAO,MAAM,UAAU,aAAa,kBAAkB;AACtD,UAAI,MAAM,UAAU,QAAQ;AAC1B,cAAM,UAAU,aAAa,gBAAgB,UAAU;AAAA,UACrD,KAAK;AAAA,YACH,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ,MAAM,UAAU;AAAA,UAC1B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAAA;AAAA;", "names": []}