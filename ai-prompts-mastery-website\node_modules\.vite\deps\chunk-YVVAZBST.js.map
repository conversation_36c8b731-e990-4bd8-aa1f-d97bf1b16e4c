{"version": 3, "sources": ["../../highlight.js/lib/languages/prolog.js"], "sourcesContent": ["/*\nLanguage: Prolog\nDescription: Prolog is a general purpose logic programming language associated with artificial intelligence and computational linguistics.\nAuthor: <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/Prolog\n*/\n\nfunction prolog(hljs) {\n  const ATOM = {\n\n    begin: /[a-z][A-Za-z0-9_]*/,\n    relevance: 0\n  };\n\n  const VAR = {\n\n    className: 'symbol',\n    variants: [\n      {\n        begin: /[A-Z][a-zA-Z0-9_]*/\n      },\n      {\n        begin: /_[A-Za-z0-9_]*/\n      }\n    ],\n    relevance: 0\n  };\n\n  const PARENTED = {\n\n    begin: /\\(/,\n    end: /\\)/,\n    relevance: 0\n  };\n\n  const LIST = {\n\n    begin: /\\[/,\n    end: /\\]/\n  };\n\n  const LINE_COMMENT = {\n\n    className: 'comment',\n    begin: /%/,\n    end: /$/,\n    contains: [ hljs.PHRASAL_WORDS_MODE ]\n  };\n\n  const BACKTICK_STRING = {\n\n    className: 'string',\n    begin: /`/,\n    end: /`/,\n    contains: [ hljs.BACKSLASH_ESCAPE ]\n  };\n\n  const CHAR_CODE = {\n    className: 'string', // 0'a etc.\n    begin: /0'(\\\\'|.)/\n  };\n\n  const SPACE_CODE = {\n    className: 'string',\n    begin: /0'\\\\s/ // 0'\\s\n  };\n\n  const PRED_OP = { // relevance booster\n    begin: /:-/\n  };\n\n  const inner = [\n\n    ATOM,\n    VAR,\n    PARENTED,\n    PRED_OP,\n    LIST,\n    LINE_COMMENT,\n    hljs.C_BLOCK_COMMENT_MODE,\n    hljs.QUOTE_STRING_MODE,\n    hljs.APOS_STRING_MODE,\n    BACKTICK_STRING,\n    CHAR_CODE,\n    SPACE_CODE,\n    hljs.C_NUMBER_MODE\n  ];\n\n  PARENTED.contains = inner;\n  LIST.contains = inner;\n\n  return {\n    name: 'Prolog',\n    contains: inner.concat([\n      { // relevance booster\n        begin: /\\.$/\n      }\n    ])\n  };\n}\n\nmodule.exports = prolog;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,OAAO,MAAM;AACpB,YAAM,OAAO;AAAA,QAEX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,MAAM;AAAA,QAEV,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AAEA,YAAM,WAAW;AAAA,QAEf,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAEA,YAAM,OAAO;AAAA,QAEX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AAEA,YAAM,eAAe;AAAA,QAEnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,kBAAmB;AAAA,MACtC;AAEA,YAAM,kBAAkB;AAAA,QAEtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU,CAAE,KAAK,gBAAiB;AAAA,MACpC;AAEA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA;AAAA,MACT;AAEA,YAAM,UAAU;AAAA;AAAA,QACd,OAAO;AAAA,MACT;AAEA,YAAM,QAAQ;AAAA,QAEZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,MACP;AAEA,eAAS,WAAW;AACpB,WAAK,WAAW;AAEhB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU,MAAM,OAAO;AAAA,UACrB;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}