{"version": 3, "sources": ["../../refractor/lang/pascaligo.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = pascaligo\npascaligo.displayName = 'pascaligo'\npascaligo.aliases = []\nfunction pascaligo(Prism) {\n  ;(function (Prism) {\n    // Pascaligo is a layer 2 smart contract language for the tezos blockchain\n    var braces = /\\((?:[^()]|\\((?:[^()]|\\([^()]*\\))*\\))*\\)/.source\n    var type = /(?:\\b\\w+(?:<braces>)?|<braces>)/.source.replace(\n      /<braces>/g,\n      function () {\n        return braces\n      }\n    )\n    var pascaligo = (Prism.languages.pascaligo = {\n      comment: /\\(\\*[\\s\\S]+?\\*\\)|\\/\\/.*/,\n      string: {\n        pattern: /([\"'`])(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|\\^[a-z]/i,\n        greedy: true\n      },\n      'class-name': [\n        {\n          pattern: RegExp(\n            /(\\btype\\s+\\w+\\s+is\\s+)<type>/.source.replace(\n              /<type>/g,\n              function () {\n                return type\n              }\n            ),\n            'i'\n          ),\n          lookbehind: true,\n          inside: null // see below\n        },\n        {\n          pattern: RegExp(\n            /<type>(?=\\s+is\\b)/.source.replace(/<type>/g, function () {\n              return type\n            }),\n            'i'\n          ),\n          inside: null // see below\n        },\n        {\n          pattern: RegExp(\n            /(:\\s*)<type>/.source.replace(/<type>/g, function () {\n              return type\n            })\n          ),\n          lookbehind: true,\n          inside: null // see below\n        }\n      ],\n      keyword: {\n        pattern:\n          /(^|[^&])\\b(?:begin|block|case|const|else|end|fail|for|from|function|if|is|nil|of|remove|return|skip|then|type|var|while|with)\\b/i,\n        lookbehind: true\n      },\n      boolean: {\n        pattern: /(^|[^&])\\b(?:False|True)\\b/i,\n        lookbehind: true\n      },\n      builtin: {\n        pattern: /(^|[^&])\\b(?:bool|int|list|map|nat|record|string|unit)\\b/i,\n        lookbehind: true\n      },\n      function: /\\b\\w+(?=\\s*\\()/,\n      number: [\n        // Hexadecimal, octal and binary\n        /%[01]+|&[0-7]+|\\$[a-f\\d]+/i, // Decimal\n        /\\b\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?(?:mtz|n)?/i\n      ],\n      operator:\n        /->|=\\/=|\\.\\.|\\*\\*|:=|<[<=>]?|>[>=]?|[+\\-*\\/]=?|[@^=|]|\\b(?:and|mod|or)\\b/,\n      punctuation: /\\(\\.|\\.\\)|[()\\[\\]:;,.{}]/\n    })\n    var classNameInside = [\n      'comment',\n      'keyword',\n      'builtin',\n      'operator',\n      'punctuation'\n    ].reduce(function (accum, key) {\n      accum[key] = pascaligo[key]\n      return accum\n    }, {})\n    pascaligo['class-name'].forEach(function (p) {\n      p.inside = classNameInside\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,cAAU,cAAc;AACxB,cAAU,UAAU,CAAC;AACrB,aAAS,UAAU,OAAO;AACxB;AAAC,OAAC,SAAUA,QAAO;AAEjB,YAAI,SAAS,2CAA2C;AACxD,YAAI,OAAO,kCAAkC,OAAO;AAAA,UAClD;AAAA,UACA,WAAY;AACV,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAIC,aAAaD,OAAM,UAAU,YAAY;AAAA,UAC3C,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ;AAAA,cACE,SAAS;AAAA,gBACP,+BAA+B,OAAO;AAAA,kBACpC;AAAA,kBACA,WAAY;AACV,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,gBACA;AAAA,cACF;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,oBAAoB,OAAO,QAAQ,WAAW,WAAY;AACxD,yBAAO;AAAA,gBACT,CAAC;AAAA,gBACD;AAAA,cACF;AAAA,cACA,QAAQ;AAAA;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,eAAe,OAAO,QAAQ,WAAW,WAAY;AACnD,yBAAO;AAAA,gBACT,CAAC;AAAA,cACH;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA;AAAA,YACV;AAAA,UACF;AAAA,UACA,SAAS;AAAA,YACP,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,UACV,QAAQ;AAAA;AAAA,YAEN;AAAA;AAAA,YACA;AAAA,UACF;AAAA,UACA,UACE;AAAA,UACF,aAAa;AAAA,QACf;AACA,YAAI,kBAAkB;AAAA,UACpB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF,EAAE,OAAO,SAAU,OAAO,KAAK;AAC7B,gBAAM,GAAG,IAAIC,WAAU,GAAG;AAC1B,iBAAO;AAAA,QACT,GAAG,CAAC,CAAC;AACL,QAAAA,WAAU,YAAY,EAAE,QAAQ,SAAU,GAAG;AAC3C,YAAE,SAAS;AAAA,QACb,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "pascaligo"]}