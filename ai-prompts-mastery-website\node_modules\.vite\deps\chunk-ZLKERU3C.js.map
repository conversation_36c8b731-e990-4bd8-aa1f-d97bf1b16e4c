{"version": 3, "sources": ["../../refractor/lang/hcl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = hcl\nhcl.displayName = 'hcl'\nhcl.aliases = []\nfunction hcl(Prism) {\n  Prism.languages.hcl = {\n    comment: /(?:\\/\\/|#).*|\\/\\*[\\s\\S]*?(?:\\*\\/|$)/,\n    heredoc: {\n      pattern: /<<-?(\\w+\\b)[\\s\\S]*?^[ \\t]*\\1/m,\n      greedy: true,\n      alias: 'string'\n    },\n    keyword: [\n      {\n        pattern:\n          /(?:data|resource)\\s+(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")(?=\\s+\"[\\w-]+\"\\s+\\{)/i,\n        inside: {\n          type: {\n            pattern: /(resource|data|\\s+)(?:\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")/i,\n            lookbehind: true,\n            alias: 'variable'\n          }\n        }\n      },\n      {\n        pattern:\n          /(?:backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+(?=\\{)/i,\n        inside: {\n          type: {\n            pattern:\n              /(backend|module|output|provider|provisioner|variable)\\s+(?:[\\w-]+|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")\\s+/i,\n            lookbehind: true,\n            alias: 'variable'\n          }\n        }\n      },\n      /[\\w-]+(?=\\s+\\{)/\n    ],\n    property: [/[-\\w\\.]+(?=\\s*=(?!=))/, /\"(?:\\\\[\\s\\S]|[^\\\\\"])+\"(?=\\s*[:=])/],\n    string: {\n      pattern:\n        /\"(?:[^\\\\$\"]|\\\\[\\s\\S]|\\$(?:(?=\")|\\$+(?!\\$)|[^\"${])|\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\})*\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /(^|[^$])\\$\\{(?:[^{}\"]|\"(?:[^\\\\\"]|\\\\[\\s\\S])*\")*\\}/,\n          lookbehind: true,\n          inside: {\n            type: {\n              pattern:\n                /(\\b(?:count|data|local|module|path|self|terraform|var)\\b\\.)[\\w\\*]+/i,\n              lookbehind: true,\n              alias: 'variable'\n            },\n            keyword: /\\b(?:count|data|local|module|path|self|terraform|var)\\b/i,\n            function: /\\w+(?=\\()/,\n            string: {\n              pattern: /\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"/,\n              greedy: true\n            },\n            number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n            punctuation: /[!\\$#%&'()*+,.\\/;<=>@\\[\\\\\\]^`{|}~?:]/\n          }\n        }\n      }\n    },\n    number: /\\b0x[\\da-f]+\\b|\\b\\d+(?:\\.\\d*)?(?:e[+-]?\\d+)?/i,\n    boolean: /\\b(?:false|true)\\b/i,\n    punctuation: /[=\\[\\]{}]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,UAAU,MAAM;AAAA,QACpB,SAAS;AAAA,QACT,SAAS;AAAA,UACP,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,UACP;AAAA,YACE,SACE;AAAA,YACF,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,QAAQ;AAAA,cACN,MAAM;AAAA,gBACJ,SACE;AAAA,gBACF,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU,CAAC,yBAAyB,mCAAmC;AAAA,QACvE,QAAQ;AAAA,UACN,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,MAAM;AAAA,kBACJ,SACE;AAAA,kBACF,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,gBACA,SAAS;AAAA,gBACT,UAAU;AAAA,gBACV,QAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,QAAQ;AAAA,gBACV;AAAA,gBACA,QAAQ;AAAA,gBACR,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,QAAQ;AAAA,QACR,SAAS;AAAA,QACT,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}