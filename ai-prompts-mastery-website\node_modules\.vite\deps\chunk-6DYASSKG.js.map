{"version": 3, "sources": ["../../refractor/lang/io.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = io\nio.displayName = 'io'\nio.aliases = []\nfunction io(Prism) {\n  Prism.languages.io = {\n    comment: {\n      pattern: /(^|[^\\\\])(?:\\/\\*[\\s\\S]*?(?:\\*\\/|$)|\\/\\/.*|#.*)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'triple-quoted-string': {\n      pattern: /\"\"\"(?:\\\\[\\s\\S]|(?!\"\"\")[^\\\\])*\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"(?:\\\\.|[^\\\\\\r\\n\"])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:activate|activeCoroCount|asString|block|break|call|catch|clone|collectGarbage|compileString|continue|do|doFile|doMessage|doString|else|elseif|exit|for|foreach|forward|getEnvironmentVariable|getSlot|hasSlot|if|ifFalse|ifNil|ifNilEval|ifTrue|isActive|isNil|isResumable|list|message|method|parent|pass|pause|perform|performWithArgList|print|println|proto|raise|raiseResumable|removeSlot|resend|resume|schedulerSleepSeconds|self|sender|setSchedulerSleepSeconds|setSlot|shallowCopy|slotNames|super|system|then|thisBlock|thisContext|try|type|uniqueId|updateSlot|wait|while|write|yield)\\b/,\n    builtin:\n      /\\b(?:Array|AudioDevice|AudioMixer|BigNum|Block|Box|Buffer|CFunction|CGI|Color|Curses|DBM|DNSResolver|DOConnection|DOProxy|DOServer|Date|Directory|Duration|DynLib|Error|Exception|FFT|File|Fnmatch|Font|Future|GL|GLE|GLScissor|GLU|GLUCylinder|GLUQuadric|GLUSphere|GLUT|Host|Image|Importer|LinkList|List|Lobby|Locals|MD5|MP3Decoder|MP3Encoder|Map|Message|Movie|Notification|Number|Object|OpenGL|Point|Protos|Random|Regex|SGML|SGMLElement|SGMLParser|SQLite|Sequence|Server|ShowMessage|SleepyCat|SleepyCatCursor|Socket|SocketManager|Sound|Soup|Store|String|Tree|UDPSender|UPDReceiver|URL|User|Warning|WeakLink)\\b/,\n    boolean: /\\b(?:false|nil|true)\\b/,\n    number: /\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e-?\\d+)?/i,\n    operator:\n      /[=!*/%+\\-^&|]=|>>?=?|<<?=?|:?:?=|\\+\\+?|--?|\\*\\*?|\\/\\/?|%|\\|\\|?|&&?|\\b(?:and|not|or|return)\\b|@@?|\\?\\??|\\.\\./,\n    punctuation: /[{}[\\];(),.:]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,OAAG,cAAc;AACjB,OAAG,UAAU,CAAC;AACd,aAAS,GAAG,OAAO;AACjB,YAAM,UAAU,KAAK;AAAA,QACnB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,wBAAwB;AAAA,UACtB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,SACE;AAAA,QACF,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,UACE;AAAA,QACF,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}