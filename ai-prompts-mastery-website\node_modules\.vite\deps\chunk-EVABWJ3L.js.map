{"version": 3, "sources": ["../../refractor/lang/swift.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = swift\nswift.displayName = 'swift'\nswift.aliases = []\nfunction swift(Prism) {\n  Prism.languages.swift = {\n    comment: {\n      // Nested comments are supported up to 2 levels\n      pattern:\n        /(^|[^\\\\:])(?:\\/\\/.*|\\/\\*(?:[^/*]|\\/(?!\\*)|\\*(?!\\/)|\\/\\*(?:[^*]|\\*(?!\\/))*\\*\\/)*\\*\\/)/,\n      lookbehind: true,\n      greedy: true\n    },\n    'string-literal': [\n      // https://docs.swift.org/swift-book/LanguageGuide/StringsAndCharacters.html\n      {\n        pattern: RegExp(\n          /(^|[^\"#])/.source +\n            '(?:' + // single-line string\n            /\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^(])|[^\\\\\\r\\n\"])*\"/\n              .source +\n            '|' + // multi-line string\n            /\"\"\"(?:\\\\(?:\\((?:[^()]|\\([^()]*\\))*\\)|[^(])|[^\\\\\"]|\"(?!\"\"))*\"\"\"/\n              .source +\n            ')' +\n            /(?![\"#])/.source\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /(\\\\\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          'interpolation-punctuation': {\n            pattern: /^\\)|\\\\\\($/,\n            alias: 'punctuation'\n          },\n          punctuation: /\\\\(?=[\\r\\n])/,\n          string: /[\\s\\S]+/\n        }\n      },\n      {\n        pattern: RegExp(\n          /(^|[^\"#])(#+)/.source +\n            '(?:' + // single-line string\n            /\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|\\r\\n|[^#])|[^\\\\\\r\\n])*?\"/\n              .source +\n            '|' + // multi-line string\n            /\"\"\"(?:\\\\(?:#+\\((?:[^()]|\\([^()]*\\))*\\)|[^#])|[^\\\\])*?\"\"\"/.source +\n            ')' +\n            '\\\\2'\n        ),\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          interpolation: {\n            pattern: /(\\\\#+\\()(?:[^()]|\\([^()]*\\))*(?=\\))/,\n            lookbehind: true,\n            inside: null // see below\n          },\n          'interpolation-punctuation': {\n            pattern: /^\\)|\\\\#+\\($/,\n            alias: 'punctuation'\n          },\n          string: /[\\s\\S]+/\n        }\n      }\n    ],\n    directive: {\n      // directives with conditions\n      pattern: RegExp(\n        /#/.source +\n          '(?:' +\n          (/(?:elseif|if)\\b/.source +\n            '(?:[ \\t]*' + // This regex is a little complex. It's equivalent to this:\n            //   (?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*<round>)?|<round>)(?:[ \\t]*(?:&&|\\|\\|))?\n            // where <round> is a general parentheses expression.\n            /(?:![ \\t]*)?(?:\\b\\w+\\b(?:[ \\t]*\\((?:[^()]|\\([^()]*\\))*\\))?|\\((?:[^()]|\\([^()]*\\))*\\))(?:[ \\t]*(?:&&|\\|\\|))?/\n              .source +\n            ')+') +\n          '|' +\n          /(?:else|endif)\\b/.source +\n          ')'\n      ),\n      alias: 'property',\n      inside: {\n        'directive-name': /^#\\w+/,\n        boolean: /\\b(?:false|true)\\b/,\n        number: /\\b\\d+(?:\\.\\d+)*\\b/,\n        operator: /!|&&|\\|\\||[<>]=?/,\n        punctuation: /[(),]/\n      }\n    },\n    literal: {\n      pattern:\n        /#(?:colorLiteral|column|dsohandle|file(?:ID|Literal|Path)?|function|imageLiteral|line)\\b/,\n      alias: 'constant'\n    },\n    'other-directive': {\n      pattern: /#\\w+\\b/,\n      alias: 'property'\n    },\n    attribute: {\n      pattern: /@\\w+/,\n      alias: 'atrule'\n    },\n    'function-definition': {\n      pattern: /(\\bfunc\\s+)\\w+/,\n      lookbehind: true,\n      alias: 'function'\n    },\n    label: {\n      // https://docs.swift.org/swift-book/LanguageGuide/ControlFlow.html#ID141\n      pattern:\n        /\\b(break|continue)\\s+\\w+|\\b[a-zA-Z_]\\w*(?=\\s*:\\s*(?:for|repeat|while)\\b)/,\n      lookbehind: true,\n      alias: 'important'\n    },\n    keyword:\n      /\\b(?:Any|Protocol|Self|Type|actor|as|assignment|associatedtype|associativity|async|await|break|case|catch|class|continue|convenience|default|defer|deinit|didSet|do|dynamic|else|enum|extension|fallthrough|fileprivate|final|for|func|get|guard|higherThan|if|import|in|indirect|infix|init|inout|internal|is|isolated|lazy|left|let|lowerThan|mutating|none|nonisolated|nonmutating|open|operator|optional|override|postfix|precedencegroup|prefix|private|protocol|public|repeat|required|rethrows|return|right|safe|self|set|some|static|struct|subscript|super|switch|throw|throws|try|typealias|unowned|unsafe|var|weak|where|while|willSet)\\b/,\n    boolean: /\\b(?:false|true)\\b/,\n    nil: {\n      pattern: /\\bnil\\b/,\n      alias: 'constant'\n    },\n    'short-argument': /\\$\\d+\\b/,\n    omit: {\n      pattern: /\\b_\\b/,\n      alias: 'keyword'\n    },\n    number:\n      /\\b(?:[\\d_]+(?:\\.[\\de_]+)?|0x[a-f0-9_]+(?:\\.[a-f0-9p_]+)?|0b[01_]+|0o[0-7_]+)\\b/i,\n    // A class name must start with an upper-case letter and be either 1 letter long or contain a lower-case letter.\n    'class-name': /\\b[A-Z](?:[A-Z_\\d]*[a-z]\\w*)?\\b/,\n    function: /\\b[a-z_]\\w*(?=\\s*\\()/i,\n    constant: /\\b(?:[A-Z_]{2,}|k[A-Z][A-Za-z_]+)\\b/,\n    // Operators are generic in Swift. Developers can even create new operators (e.g. +++).\n    // https://docs.swift.org/swift-book/ReferenceManual/zzSummaryOfTheGrammar.html#ID481\n    // This regex only supports ASCII operators.\n    operator: /[-+*/%=!<>&|^~?]+|\\.[.\\-+*/%=!<>&|^~?]+/,\n    punctuation: /[{}[\\]();,.:\\\\]/\n  }\n  Prism.languages.swift['string-literal'].forEach(function (rule) {\n    rule.inside['interpolation'].inside = Prism.languages.swift\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,UAAU,QAAQ;AAAA,QACtB,SAAS;AAAA;AAAA,UAEP,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,kBAAkB;AAAA;AAAA,UAEhB;AAAA,YACE,SAAS;AAAA,cACP,YAAY,SACV;AAAA,cACA,8DACG,SACH;AAAA,cACA,iEACG,SACH,MACA,WAAW;AAAA,YACf;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA,cACV;AAAA,cACA,6BAA6B;AAAA,gBAC3B,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,aAAa;AAAA,cACb,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA;AAAA,YACE,SAAS;AAAA,cACP,gBAAgB,SACd;AAAA,cACA,gEACG,SACH;AAAA,cACA,2DAA2D,SAC3D;AAAA,YAEJ;AAAA,YACA,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,eAAe;AAAA,gBACb,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA,cACV;AAAA,cACA,6BAA6B;AAAA,gBAC3B,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA;AAAA,UAET,SAAS;AAAA,YACP,IAAI,SACF,SACC,kBAAkB,SACjB;AAAA;AAAA;AAAA,YAGA,8GACG,SACH,QACF,MACA,mBAAmB,SACnB;AAAA,UACJ;AAAA,UACA,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,kBAAkB;AAAA,YAClB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,UAAU;AAAA,YACV,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,SAAS;AAAA,UACP,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,uBAAuB;AAAA,UACrB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA;AAAA,UAEL,SACE;AAAA,UACF,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,QACT,KAAK;AAAA,UACH,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,kBAAkB;AAAA,QAClB,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QACE;AAAA;AAAA,QAEF,cAAc;AAAA,QACd,UAAU;AAAA,QACV,UAAU;AAAA;AAAA;AAAA;AAAA,QAIV,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AACA,YAAM,UAAU,MAAM,gBAAgB,EAAE,QAAQ,SAAU,MAAM;AAC9D,aAAK,OAAO,eAAe,EAAE,SAAS,MAAM,UAAU;AAAA,MACxD,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}