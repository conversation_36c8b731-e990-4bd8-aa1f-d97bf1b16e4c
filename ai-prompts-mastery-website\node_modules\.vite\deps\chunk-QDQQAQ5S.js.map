{"version": 3, "sources": ["../../highlight.js/lib/languages/awk.js"], "sourcesContent": ["/*\nLanguage: Awk\nAuthor: <PERSON> <<EMAIL>>\nWebsite: https://www.gnu.org/software/gawk/manual/gawk.html\nDescription: language definition for Awk scripts\n*/\n\n/** @type LanguageFn */\nfunction awk(hljs) {\n  const VARIABLE = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$[\\w\\d#@][\\w\\d_]*/\n      },\n      {\n        begin: /\\$\\{(.*?)\\}/\n      }\n    ]\n  };\n  const KEYWORDS = 'BEGIN END if else while do for in break continue delete next nextfile function func exit|10';\n  const STRING = {\n    className: 'string',\n    contains: [hljs.BACKSLASH_ESCAPE],\n    variants: [\n      {\n        begin: /(u|b)?r?'''/,\n        end: /'''/,\n        relevance: 10\n      },\n      {\n        begin: /(u|b)?r?\"\"\"/,\n        end: /\"\"\"/,\n        relevance: 10\n      },\n      {\n        begin: /(u|r|ur)'/,\n        end: /'/,\n        relevance: 10\n      },\n      {\n        begin: /(u|r|ur)\"/,\n        end: /\"/,\n        relevance: 10\n      },\n      {\n        begin: /(b|br)'/,\n        end: /'/\n      },\n      {\n        begin: /(b|br)\"/,\n        end: /\"/\n      },\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE\n    ]\n  };\n  return {\n    name: 'Awk',\n    keywords: {\n      keyword: KEYWORDS\n    },\n    contains: [\n      VARIABLE,\n      STRING,\n      hljs.REGEXP_MODE,\n      hljs.HASH_COMMENT_MODE,\n      hljs.NUMBER_MODE\n    ]\n  };\n}\n\nmodule.exports = awk;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,IAAI,MAAM;AACjB,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,YAAM,WAAW;AACjB,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU,CAAC,KAAK,gBAAgB;AAAA,QAChC,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}