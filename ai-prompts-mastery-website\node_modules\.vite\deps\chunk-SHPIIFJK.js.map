{"version": 3, "sources": ["../../refractor/lang/rust.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = rust\nrust.displayName = 'rust'\nrust.aliases = []\nfunction rust(Prism) {\n  ;(function (Prism) {\n    var multilineComment = /\\/\\*(?:[^*/]|\\*(?!\\/)|\\/(?!\\*)|<self>)*\\*\\//.source\n    for (var i = 0; i < 2; i++) {\n      // support 4 levels of nested comments\n      multilineComment = multilineComment.replace(/<self>/g, function () {\n        return multilineComment\n      })\n    }\n    multilineComment = multilineComment.replace(/<self>/g, function () {\n      return /[^\\s\\S]/.source\n    })\n    Prism.languages.rust = {\n      comment: [\n        {\n          pattern: RegExp(/(^|[^\\\\])/.source + multilineComment),\n          lookbehind: true,\n          greedy: true\n        },\n        {\n          pattern: /(^|[^\\\\:])\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      string: {\n        pattern: /b?\"(?:\\\\[\\s\\S]|[^\\\\\"])*\"|b?r(#*)\"(?:[^\"]|\"(?!\\1))*\"\\1/,\n        greedy: true\n      },\n      char: {\n        pattern:\n          /b?'(?:\\\\(?:x[0-7][\\da-fA-F]|u\\{(?:[\\da-fA-F]_*){1,6}\\}|.)|[^\\\\\\r\\n\\t'])'/,\n        greedy: true\n      },\n      attribute: {\n        pattern: /#!?\\[(?:[^\\[\\]\"]|\"(?:\\\\[\\s\\S]|[^\\\\\"])*\")*\\]/,\n        greedy: true,\n        alias: 'attr-name',\n        inside: {\n          string: null // see below\n        }\n      },\n      // Closure params should not be confused with bitwise OR |\n      'closure-params': {\n        pattern: /([=(,:]\\s*|\\bmove\\s*)\\|[^|]*\\||\\|[^|]*\\|(?=\\s*(?:\\{|->))/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          'closure-punctuation': {\n            pattern: /^\\||\\|$/,\n            alias: 'punctuation'\n          },\n          rest: null // see below\n        }\n      },\n      'lifetime-annotation': {\n        pattern: /'\\w+/,\n        alias: 'symbol'\n      },\n      'fragment-specifier': {\n        pattern: /(\\$\\w+:)[a-z]+/,\n        lookbehind: true,\n        alias: 'punctuation'\n      },\n      variable: /\\$\\w+/,\n      'function-definition': {\n        pattern: /(\\bfn\\s+)\\w+/,\n        lookbehind: true,\n        alias: 'function'\n      },\n      'type-definition': {\n        pattern: /(\\b(?:enum|struct|trait|type|union)\\s+)\\w+/,\n        lookbehind: true,\n        alias: 'class-name'\n      },\n      'module-declaration': [\n        {\n          pattern: /(\\b(?:crate|mod)\\s+)[a-z][a-z_\\d]*/,\n          lookbehind: true,\n          alias: 'namespace'\n        },\n        {\n          pattern:\n            /(\\b(?:crate|self|super)\\s*)::\\s*[a-z][a-z_\\d]*\\b(?:\\s*::(?:\\s*[a-z][a-z_\\d]*\\s*::)*)?/,\n          lookbehind: true,\n          alias: 'namespace',\n          inside: {\n            punctuation: /::/\n          }\n        }\n      ],\n      keyword: [\n        // https://github.com/rust-lang/reference/blob/master/src/keywords.md\n        /\\b(?:Self|abstract|as|async|await|become|box|break|const|continue|crate|do|dyn|else|enum|extern|final|fn|for|if|impl|in|let|loop|macro|match|mod|move|mut|override|priv|pub|ref|return|self|static|struct|super|trait|try|type|typeof|union|unsafe|unsized|use|virtual|where|while|yield)\\b/, // primitives and str\n        // https://doc.rust-lang.org/stable/rust-by-example/primitives.html\n        /\\b(?:bool|char|f(?:32|64)|[ui](?:8|16|32|64|128|size)|str)\\b/\n      ],\n      // functions can technically start with an upper-case letter, but this will introduce a lot of false positives\n      // and Rust's naming conventions recommend snake_case anyway.\n      // https://doc.rust-lang.org/1.0.0/style/style/naming/README.html\n      function: /\\b[a-z_]\\w*(?=\\s*(?:::\\s*<|\\())/,\n      macro: {\n        pattern: /\\b\\w+!/,\n        alias: 'property'\n      },\n      constant: /\\b[A-Z_][A-Z_\\d]+\\b/,\n      'class-name': /\\b[A-Z]\\w*\\b/,\n      namespace: {\n        pattern: /(?:\\b[a-z][a-z_\\d]*\\s*::\\s*)*\\b[a-z][a-z_\\d]*\\s*::(?!\\s*<)/,\n        inside: {\n          punctuation: /::/\n        }\n      },\n      // Hex, oct, bin, dec numbers with visual separators and type suffix\n      number:\n        /\\b(?:0x[\\dA-Fa-f](?:_?[\\dA-Fa-f])*|0o[0-7](?:_?[0-7])*|0b[01](?:_?[01])*|(?:(?:\\d(?:_?\\d)*)?\\.)?\\d(?:_?\\d)*(?:[Ee][+-]?\\d+)?)(?:_?(?:f32|f64|[iu](?:8|16|32|64|size)?))?\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      punctuation: /->|\\.\\.=|\\.{1,3}|::|[{}[\\];(),:]/,\n      operator: /[-+*\\/%!^]=?|=[=>]?|&[&=]?|\\|[|=]?|<<?=?|>>?=?|[@?]/\n    }\n    Prism.languages.rust['closure-params'].inside.rest = Prism.languages.rust\n    Prism.languages.rust['attribute'].inside['string'] =\n      Prism.languages.rust['string']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,mBAAmB,8CAA8C;AACrE,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAE1B,6BAAmB,iBAAiB,QAAQ,WAAW,WAAY;AACjE,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,2BAAmB,iBAAiB,QAAQ,WAAW,WAAY;AACjE,iBAAO,UAAU;AAAA,QACnB,CAAC;AACD,QAAAA,OAAM,UAAU,OAAO;AAAA,UACrB,SAAS;AAAA,YACP;AAAA,cACE,SAAS,OAAO,YAAY,SAAS,gBAAgB;AAAA,cACrD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,MAAM;AAAA,YACJ,SACE;AAAA,YACF,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,QAAQ;AAAA;AAAA,YACV;AAAA,UACF;AAAA;AAAA,UAEA,kBAAkB;AAAA,YAChB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,uBAAuB;AAAA,gBACrB,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,MAAM;AAAA;AAAA,YACR;AAAA,UACF;AAAA,UACA,uBAAuB;AAAA,YACrB,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,sBAAsB;AAAA,YACpB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,uBAAuB;AAAA,YACrB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,mBAAmB;AAAA,YACjB,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,sBAAsB;AAAA,YACpB;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,UACF;AAAA,UACA,SAAS;AAAA;AAAA,YAEP;AAAA;AAAA;AAAA,YAEA;AAAA,UACF;AAAA;AAAA;AAAA;AAAA,UAIA,UAAU;AAAA,UACV,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,cAAc;AAAA,UACd,WAAW;AAAA,YACT,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA;AAAA,UAEA,QACE;AAAA,UACF,SAAS;AAAA,UACT,aAAa;AAAA,UACb,UAAU;AAAA,QACZ;AACA,QAAAA,OAAM,UAAU,KAAK,gBAAgB,EAAE,OAAO,OAAOA,OAAM,UAAU;AACrE,QAAAA,OAAM,UAAU,KAAK,WAAW,EAAE,OAAO,QAAQ,IAC/CA,OAAM,UAAU,KAAK,QAAQ;AAAA,MACjC,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}