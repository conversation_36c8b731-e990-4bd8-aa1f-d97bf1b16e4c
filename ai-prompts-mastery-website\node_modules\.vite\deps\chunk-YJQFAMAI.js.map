{"version": 3, "sources": ["../../refractor/lang/tt2.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = tt2\ntt2.displayName = 'tt2'\ntt2.aliases = []\nfunction tt2(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    Prism.languages.tt2 = Prism.languages.extend('clike', {\n      comment: /#.*|\\[%#[\\s\\S]*?%\\]/,\n      keyword:\n        /\\b(?:BLOCK|CALL|CASE|CATCH|CLEAR|DEBUG|DEFAULT|ELSE|ELSIF|END|FILTER|FINAL|FOREACH|GET|IF|IN|INCLUDE|INSERT|LAST|MACRO|META|NEXT|PERL|PROCESS|RAWPERL|RETURN|SET|STOP|SWITCH|TAGS|THROW|TRY|UNLESS|USE|WHILE|WRAPPER)\\b/,\n      punctuation: /[[\\]{},()]/\n    })\n    Prism.languages.insertBefore('tt2', 'number', {\n      operator: /=[>=]?|!=?|<=?|>=?|&&|\\|\\|?|\\b(?:and|not|or)\\b/,\n      variable: {\n        pattern: /\\b[a-z]\\w*(?:\\s*\\.\\s*(?:\\d+|\\$?[a-z]\\w*))*\\b/i\n      }\n    })\n    Prism.languages.insertBefore('tt2', 'keyword', {\n      delimiter: {\n        pattern: /^(?:\\[%|%%)-?|-?%\\]$/,\n        alias: 'punctuation'\n      }\n    })\n    Prism.languages.insertBefore('tt2', 'string', {\n      'single-quoted-string': {\n        pattern: /'[^\\\\']*(?:\\\\[\\s\\S][^\\\\']*)*'/,\n        greedy: true,\n        alias: 'string'\n      },\n      'double-quoted-string': {\n        pattern: /\"[^\\\\\"]*(?:\\\\[\\s\\S][^\\\\\"]*)*\"/,\n        greedy: true,\n        alias: 'string',\n        inside: {\n          variable: {\n            pattern: /\\$(?:[a-z]\\w*(?:\\.(?:\\d+|\\$?[a-z]\\w*))*)/i\n          }\n        }\n      }\n    }) // The different types of TT2 strings \"replace\" the C-like standard string\n    delete Prism.languages.tt2.string\n    Prism.hooks.add('before-tokenize', function (env) {\n      var tt2Pattern = /\\[%[\\s\\S]+?%\\]/g\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'tt2',\n        tt2Pattern\n      )\n    })\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'tt2')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,MAAMA,OAAM,UAAU,OAAO,SAAS;AAAA,UACpD,SAAS;AAAA,UACT,SACE;AAAA,UACF,aAAa;AAAA,QACf,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,UAAU;AAAA,UAC5C,UAAU;AAAA,UACV,UAAU;AAAA,YACR,SAAS;AAAA,UACX;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,WAAW;AAAA,UAC7C,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,OAAO,UAAU;AAAA,UAC5C,wBAAwB;AAAA,YACtB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,UACT;AAAA,UACA,wBAAwB;AAAA,YACtB,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,UAAU;AAAA,gBACR,SAAS;AAAA,cACX;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAOA,OAAM,UAAU,IAAI;AAC3B,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,aAAa;AACjB,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,QACtE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}