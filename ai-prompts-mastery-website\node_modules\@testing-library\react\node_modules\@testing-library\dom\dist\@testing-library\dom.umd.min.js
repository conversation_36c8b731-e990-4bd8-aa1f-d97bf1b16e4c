!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).TestingLibraryDom={})}(this,(function(e){"use strict";function t(e,t){return t.forEach((function(t){t&&"string"!=typeof t&&!Array.isArray(t)&&Object.keys(t).forEach((function(r){if("default"!==r&&!(r in e)){var n=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,n.get?n:{enumerable:!0,get:function(){return t[r]}})}}))})),Object.freeze(e)}var r="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function n(e){var t=e.default;if("function"==typeof t){var r=function(){return t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach((function(t){var n=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,n.get?n:{enumerable:!0,get:function(){return e[t]}})})),r}var o={},a={exports:{}};!function(e){const t=function(e){return void 0===e&&(e=0),t=>"["+(38+e)+";5;"+t+"m"},r=function(e){return void 0===e&&(e=0),(t,r,n)=>"["+(38+e)+";2;"+t+";"+r+";"+n+"m"};Object.defineProperty(e,"exports",{enumerable:!0,get:function(){const e=new Map,n={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};n.color.gray=n.color.blackBright,n.bgColor.bgGray=n.bgColor.bgBlackBright,n.color.grey=n.color.blackBright,n.bgColor.bgGrey=n.bgColor.bgBlackBright;for(const[t,r]of Object.entries(n)){for(const[t,o]of Object.entries(r))n[t]={open:"["+o[0]+"m",close:"["+o[1]+"m"},r[t]=n[t],e.set(o[0],o[1]);Object.defineProperty(n,t,{value:r,enumerable:!1})}return Object.defineProperty(n,"codes",{value:e,enumerable:!1}),n.color.close="[39m",n.bgColor.close="[49m",n.color.ansi256=t(),n.color.ansi16m=r(),n.bgColor.ansi256=t(10),n.bgColor.ansi16m=r(10),Object.defineProperties(n,{rgbToAnsi256:{value:(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(t/255*5)+Math.round(r/255*5),enumerable:!1},hexToRgb:{value:e=>{const t=/(?<colorString>[a-f\d]{6}|[a-f\d]{3})/i.exec(e.toString(16));if(!t)return[0,0,0];let{colorString:r}=t.groups;3===r.length&&(r=r.split("").map((e=>e+e)).join(""));const n=Number.parseInt(r,16);return[n>>16&255,n>>8&255,255&n]},enumerable:!1},hexToAnsi256:{value:e=>n.rgbToAnsi256(...n.hexToRgb(e)),enumerable:!1}}),n}})}(a);var i={};Object.defineProperty(i,"__esModule",{value:!0}),i.printIteratorEntries=function(e,t,r,n,o,a,i){void 0===i&&(i=": ");let l="",u=e.next();if(!u.done){l+=t.spacingOuter;const s=r+t.indent;for(;!u.done;){l+=s+a(u.value[0],t,s,n,o)+i+a(u.value[1],t,s,n,o),u=e.next(),u.done?t.min||(l+=","):l+=","+t.spacingInner}l+=t.spacingOuter+r}return l},i.printIteratorValues=function(e,t,r,n,o,a){let i="",l=e.next();if(!l.done){i+=t.spacingOuter;const u=r+t.indent;for(;!l.done;)i+=u+a(l.value,t,u,n,o),l=e.next(),l.done?t.min||(i+=","):i+=","+t.spacingInner;i+=t.spacingOuter+r}return i},i.printListItems=function(e,t,r,n,o,a){let i="";if(e.length){i+=t.spacingOuter;const l=r+t.indent;for(let r=0;r<e.length;r++)i+=l,r in e&&(i+=a(e[r],t,l,n,o)),r<e.length-1?i+=","+t.spacingInner:t.min||(i+=",");i+=t.spacingOuter+r}return i},i.printObjectProperties=function(e,t,r,n,o,a){let i="";const u=l(e,t.compareKeys);if(u.length){i+=t.spacingOuter;const l=r+t.indent;for(let r=0;r<u.length;r++){const s=u[r];i+=l+a(s,t,l,n,o)+": "+a(e[s],t,l,n,o),r<u.length-1?i+=","+t.spacingInner:t.min||(i+=",")}i+=t.spacingOuter+r}return i};const l=(e,t)=>{const r=Object.keys(e).sort(t);return Object.getOwnPropertySymbols&&Object.getOwnPropertySymbols(e).forEach((t=>{Object.getOwnPropertyDescriptor(e,t).enumerable&&r.push(t)})),r};var u={};Object.defineProperty(u,"__esModule",{value:!0}),u.test=u.serialize=u.default=void 0;var s=i,c="undefined"!=typeof globalThis?globalThis:void 0!==c?c:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),d=c["jest-symbol-do-not-touch"]||c.Symbol;const p="function"==typeof d&&d.for?d.for("jest.asymmetricMatcher"):1267621,f=" ",b=(e,t,r,n,o,a)=>{const i=e.toString();return"ArrayContaining"===i||"ArrayNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+f+"["+(0,s.printListItems)(e.sample,t,r,n,o,a)+"]":"ObjectContaining"===i||"ObjectNotContaining"===i?++n>t.maxDepth?"["+i+"]":i+f+"{"+(0,s.printObjectProperties)(e.sample,t,r,n,o,a)+"}":"StringMatching"===i||"StringNotMatching"===i||"StringContaining"===i||"StringNotContaining"===i?i+f+a(e.sample,t,r,n,o):e.toAsymmetricMatcher()};u.serialize=b;const m=e=>e&&e.$$typeof===p;u.test=m;var y={serialize:b,test:m};u.default=y;var v={};Object.defineProperty(v,"__esModule",{value:!0}),v.test=v.serialize=v.default=void 0;var h=P((function(e){let{onlyFirst:t=!1}=void 0===e?{}:e;const r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(r,t?void 0:"g")})),g=P(a.exports);function P(e){return e&&e.__esModule?e:{default:e}}const C=e=>"string"==typeof e&&!!e.match((0,h.default)());v.test=C;const w=(e,t,r,n,o,a)=>a(e.replace((0,h.default)(),(e=>{switch(e){case g.default.red.close:case g.default.green.close:case g.default.cyan.close:case g.default.gray.close:case g.default.white.close:case g.default.yellow.close:case g.default.bgRed.close:case g.default.bgGreen.close:case g.default.bgYellow.close:case g.default.inverse.close:case g.default.dim.close:case g.default.bold.close:case g.default.reset.open:case g.default.reset.close:return"</>";case g.default.red.open:return"<red>";case g.default.green.open:return"<green>";case g.default.cyan.open:return"<cyan>";case g.default.gray.open:return"<gray>";case g.default.white.open:return"<white>";case g.default.yellow.open:return"<yellow>";case g.default.bgRed.open:return"<bgRed>";case g.default.bgGreen.open:return"<bgGreen>";case g.default.bgYellow.open:return"<bgYellow>";case g.default.inverse.open:return"<inverse>";case g.default.dim.open:return"<dim>";case g.default.bold.open:return"<bold>";default:return""}})),t,r,n,o);v.serialize=w;var q={serialize:w,test:C};v.default=q;var E={};Object.defineProperty(E,"__esModule",{value:!0}),E.test=E.serialize=E.default=void 0;var x=i;const O=["DOMStringMap","NamedNodeMap"],j=/^(HTML\w*Collection|NodeList)$/,R=e=>{return e&&e.constructor&&!!e.constructor.name&&(t=e.constructor.name,-1!==O.indexOf(t)||j.test(t));var t};E.test=R;const S=(e,t,r,n,o,a)=>{const i=e.constructor.name;return++n>t.maxDepth?"["+i+"]":(t.min?"":i+" ")+(-1!==O.indexOf(i)?"{"+(0,x.printObjectProperties)((e=>"NamedNodeMap"===e.constructor.name)(e)?Array.from(e).reduce(((e,t)=>(e[t.name]=t.value,e)),{}):{...e},t,r,n,o,a)+"}":"["+(0,x.printListItems)(Array.from(e),t,r,n,o,a)+"]")};E.serialize=S;var A={serialize:S,test:R};E.default=A;var _={},T={},M={};Object.defineProperty(M,"__esModule",{value:!0}),M.default=function(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")},Object.defineProperty(T,"__esModule",{value:!0}),T.printText=T.printProps=T.printElementAsLeaf=T.printElement=T.printComment=T.printChildren=void 0;var I,B=(I=M)&&I.__esModule?I:{default:I};T.printProps=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")};T.printChildren=(e,t,r,n,o,a)=>e.map((e=>t.spacingOuter+r+("string"==typeof e?F(e,t):a(e,t,r,n,o)))).join("");const F=(e,t)=>{const r=t.colors.content;return r.open+(0,B.default)(e)+r.close};T.printText=F;T.printComment=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+(0,B.default)(e)+"--\x3e"+r.close};T.printElement=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close};T.printElementAsLeaf=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},Object.defineProperty(_,"__esModule",{value:!0}),_.test=_.serialize=_.default=void 0;var k=T;const N=/^((HTML|SVG)\w*)?Element$/,L=e=>{var t;return(null==e||null===(t=e.constructor)||void 0===t?void 0:t.name)&&(e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||(e=>{try{return"function"==typeof e.hasAttribute&&e.hasAttribute("is")}catch{return!1}})(e);return 1===r&&(N.test(t)||o)||3===r&&"Text"===t||8===r&&"Comment"===t||11===r&&"DocumentFragment"===t})(e)};function U(e){return 11===e.nodeType}_.test=L;const D=(e,t,r,n,o,a)=>{if(function(e){return 3===e.nodeType}(e))return(0,k.printText)(e.data,t);if(function(e){return 8===e.nodeType}(e))return(0,k.printComment)(e.data,t);const i=U(e)?"DocumentFragment":e.tagName.toLowerCase();return++n>t.maxDepth?(0,k.printElementAsLeaf)(i,t):(0,k.printElement)(i,(0,k.printProps)(U(e)?[]:Array.from(e.attributes).map((e=>e.name)).sort(),U(e)?{}:Array.from(e.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),t,r+t.indent,n,o,a),(0,k.printChildren)(Array.prototype.slice.call(e.childNodes||e.children),t,r+t.indent,n,o,a),t,r)};_.serialize=D;var H={serialize:D,test:L};_.default=H;var W={};Object.defineProperty(W,"__esModule",{value:!0}),W.test=W.serialize=W.default=void 0;var z=i;const $="@@__IMMUTABLE_ORDERED__@@",V=e=>"Immutable."+e,G=e=>"["+e+"]",J=" ";const Q=(e,t,r,n,o,a,i)=>++n>t.maxDepth?G(V(i)):V(i)+J+"["+(0,z.printIteratorValues)(e.values(),t,r,n,o,a)+"]",X=(e,t,r,n,o,a)=>e["@@__IMMUTABLE_MAP__@@"]?((e,t,r,n,o,a,i)=>++n>t.maxDepth?G(V(i)):V(i)+J+"{"+(0,z.printIteratorEntries)(e.entries(),t,r,n,o,a)+"}")(e,t,r,n,o,a,e[$]?"OrderedMap":"Map"):e["@@__IMMUTABLE_LIST__@@"]?Q(e,t,r,n,o,a,"List"):e["@@__IMMUTABLE_SET__@@"]?Q(e,t,r,n,o,a,e[$]?"OrderedSet":"Set"):e["@@__IMMUTABLE_STACK__@@"]?Q(e,t,r,n,o,a,"Stack"):e["@@__IMMUTABLE_SEQ__@@"]?((e,t,r,n,o,a)=>{const i=V("Seq");return++n>t.maxDepth?G(i):e["@@__IMMUTABLE_KEYED__@@"]?i+J+"{"+(e._iter||e._object?(0,z.printIteratorEntries)(e.entries(),t,r,n,o,a):"…")+"}":i+J+"["+(e._iter||e._array||e._collection||e._iterable?(0,z.printIteratorValues)(e.values(),t,r,n,o,a):"…")+"]"})(e,t,r,n,o,a):((e,t,r,n,o,a)=>{const i=V(e._name||"Record");return++n>t.maxDepth?G(i):i+J+"{"+(0,z.printIteratorEntries)(function(e){let t=0;return{next(){if(t<e._keys.length){const r=e._keys[t++];return{done:!1,value:[r,e.get(r)]}}return{done:!0,value:void 0}}}}(e),t,r,n,o,a)+"}"})(e,t,r,n,o,a);W.serialize=X;const K=e=>e&&(!0===e["@@__IMMUTABLE_ITERABLE__@@"]||!0===e["@@__IMMUTABLE_RECORD__@@"]);W.test=K;var Y={serialize:X,test:K};W.default=Y;var Z,ee={},te={exports:{}},re={};!function(e){e.exports=function(){if(Z)return re;Z=1;var e=60103,t=60106,r=60107,n=60108,o=60114,a=60109,i=60110,l=60112,u=60113,s=60120,c=60115,d=60116,p=60121,f=60122,b=60117,m=60129,y=60131;if("function"==typeof Symbol&&Symbol.for){var v=Symbol.for;e=v("react.element"),t=v("react.portal"),r=v("react.fragment"),n=v("react.strict_mode"),o=v("react.profiler"),a=v("react.provider"),i=v("react.context"),l=v("react.forward_ref"),u=v("react.suspense"),s=v("react.suspense_list"),c=v("react.memo"),d=v("react.lazy"),p=v("react.block"),f=v("react.server.block"),b=v("react.fundamental"),m=v("react.debug_trace_mode"),y=v("react.legacy_hidden")}function h(p){if("object"==typeof p&&null!==p){var f=p.$$typeof;switch(f){case e:switch(p=p.type){case r:case o:case n:case u:case s:return p;default:switch(p=p&&p.$$typeof){case i:case l:case d:case c:case a:return p;default:return f}}case t:return f}}}var g=a,P=e,C=l,w=r,q=d,E=c,x=t,O=o,j=n,R=u;return re.ContextConsumer=i,re.ContextProvider=g,re.Element=P,re.ForwardRef=C,re.Fragment=w,re.Lazy=q,re.Memo=E,re.Portal=x,re.Profiler=O,re.StrictMode=j,re.Suspense=R,re.isAsyncMode=function(){return!1},re.isConcurrentMode=function(){return!1},re.isContextConsumer=function(e){return h(e)===i},re.isContextProvider=function(e){return h(e)===a},re.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===e},re.isForwardRef=function(e){return h(e)===l},re.isFragment=function(e){return h(e)===r},re.isLazy=function(e){return h(e)===d},re.isMemo=function(e){return h(e)===c},re.isPortal=function(e){return h(e)===t},re.isProfiler=function(e){return h(e)===o},re.isStrictMode=function(e){return h(e)===n},re.isSuspense=function(e){return h(e)===u},re.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===r||e===o||e===m||e===n||e===u||e===s||e===y||"object"==typeof e&&null!==e&&(e.$$typeof===d||e.$$typeof===c||e.$$typeof===a||e.$$typeof===i||e.$$typeof===l||e.$$typeof===b||e.$$typeof===p||e[0]===f)},re.typeOf=h,re}()}(te),Object.defineProperty(ee,"__esModule",{value:!0}),ee.test=ee.serialize=ee.default=void 0;var ne=function(e,t){if(!t&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=ae(t);if(r&&r.has(e))return r.get(e);var n={},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in e)if("default"!==a&&Object.prototype.hasOwnProperty.call(e,a)){var i=o?Object.getOwnPropertyDescriptor(e,a):null;i&&(i.get||i.set)?Object.defineProperty(n,a,i):n[a]=e[a]}n.default=e,r&&r.set(e,n);return n}(te.exports),oe=T;function ae(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(ae=function(e){return e?r:t})(e)}const ie=function(e,t){return void 0===t&&(t=[]),Array.isArray(e)?e.forEach((e=>{ie(e,t)})):null!=e&&!1!==e&&t.push(e),t},le=e=>{const t=e.type;if("string"==typeof t)return t;if("function"==typeof t)return t.displayName||t.name||"Unknown";if(ne.isFragment(e))return"React.Fragment";if(ne.isSuspense(e))return"React.Suspense";if("object"==typeof t&&null!==t){if(ne.isContextProvider(e))return"Context.Provider";if(ne.isContextConsumer(e))return"Context.Consumer";if(ne.isForwardRef(e)){if(t.displayName)return t.displayName;const e=t.render.displayName||t.render.name||"";return""!==e?"ForwardRef("+e+")":"ForwardRef"}if(ne.isMemo(e)){const e=t.displayName||t.type.displayName||t.type.name||"";return""!==e?"Memo("+e+")":"Memo"}}return"UNDEFINED"},ue=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,oe.printElementAsLeaf)(le(e),t):(0,oe.printElement)(le(e),(0,oe.printProps)((e=>{const{props:t}=e;return Object.keys(t).filter((e=>"children"!==e&&void 0!==t[e])).sort()})(e),e.props,t,r+t.indent,n,o,a),(0,oe.printChildren)(ie(e.props.children),t,r+t.indent,n,o,a),t,r);ee.serialize=ue;const se=e=>null!=e&&ne.isElement(e);ee.test=se;var ce={serialize:ue,test:se};ee.default=ce;var de={};Object.defineProperty(de,"__esModule",{value:!0}),de.test=de.serialize=de.default=void 0;var pe=T,fe="undefined"!=typeof globalThis?globalThis:void 0!==fe?fe:"undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")(),be=fe["jest-symbol-do-not-touch"]||fe.Symbol;const me="function"==typeof be&&be.for?be.for("react.test.json"):245830487,ye=(e,t,r,n,o,a)=>++n>t.maxDepth?(0,pe.printElementAsLeaf)(e.type,t):(0,pe.printElement)(e.type,e.props?(0,pe.printProps)((e=>{const{props:t}=e;return t?Object.keys(t).filter((e=>void 0!==t[e])).sort():[]})(e),e.props,t,r+t.indent,n,o,a):"",e.children?(0,pe.printChildren)(e.children,t,r+t.indent,n,o,a):"",t,r);de.serialize=ye;const ve=e=>e&&e.$$typeof===me;de.test=ve;var he={serialize:ye,test:ve};de.default=he,Object.defineProperty(o,"__esModule",{value:!0});var ge=o.default=o.DEFAULT_OPTIONS=void 0,Pe=o.format=ot,Ce=o.plugins=void 0,we=_e(a.exports),qe=i,Ee=_e(u),xe=_e(v),Oe=_e(E),je=_e(_),Re=_e(W),Se=_e(ee),Ae=_e(de);function _e(e){return e&&e.__esModule?e:{default:e}}const Te=Object.prototype.toString,Me=Date.prototype.toISOString,Ie=Error.prototype.toString,Be=RegExp.prototype.toString,Fe=e=>"function"==typeof e.constructor&&e.constructor.name||"Object",ke=e=>"undefined"!=typeof window&&e===window,Ne=/^Symbol\((.*)\)(.*)$/,Le=/\n/gi;class Ue extends Error{constructor(e,t){super(e),this.stack=t,this.name=this.constructor.name}}function De(e,t){return t?"[Function "+(e.name||"anonymous")+"]":"[Function]"}function He(e){return String(e).replace(Ne,"Symbol($1)")}function We(e){return"["+Ie.call(e)+"]"}function ze(e,t,r,n){if(!0===e||!1===e)return""+e;if(void 0===e)return"undefined";if(null===e)return"null";const o=typeof e;if("number"===o)return function(e){return Object.is(e,-0)?"-0":String(e)}(e);if("bigint"===o)return function(e){return String(e+"n")}(e);if("string"===o)return n?'"'+e.replace(/"|\\/g,"\\$&")+'"':'"'+e+'"';if("function"===o)return De(e,t);if("symbol"===o)return He(e);const a=Te.call(e);return"[object WeakMap]"===a?"WeakMap {}":"[object WeakSet]"===a?"WeakSet {}":"[object Function]"===a||"[object GeneratorFunction]"===a?De(e,t):"[object Symbol]"===a?He(e):"[object Date]"===a?isNaN(+e)?"Date { NaN }":Me.call(e):"[object Error]"===a?We(e):"[object RegExp]"===a?r?Be.call(e).replace(/[\\^$*+?.()|[\]{}]/g,"\\$&"):Be.call(e):e instanceof Error?We(e):null}function $e(e,t,r,n,o,a){if(-1!==o.indexOf(e))return"[Circular]";(o=o.slice()).push(e);const i=++n>t.maxDepth,l=t.min;if(t.callToJSON&&!i&&e.toJSON&&"function"==typeof e.toJSON&&!a)return Je(e.toJSON(),t,r,n,o,!0);const u=Te.call(e);return"[object Arguments]"===u?i?"[Arguments]":(l?"":"Arguments ")+"["+(0,qe.printListItems)(e,t,r,n,o,Je)+"]":function(e){return"[object Array]"===e||"[object ArrayBuffer]"===e||"[object DataView]"===e||"[object Float32Array]"===e||"[object Float64Array]"===e||"[object Int8Array]"===e||"[object Int16Array]"===e||"[object Int32Array]"===e||"[object Uint8Array]"===e||"[object Uint8ClampedArray]"===e||"[object Uint16Array]"===e||"[object Uint32Array]"===e}(u)?i?"["+e.constructor.name+"]":(l?"":t.printBasicPrototype||"Array"!==e.constructor.name?e.constructor.name+" ":"")+"["+(0,qe.printListItems)(e,t,r,n,o,Je)+"]":"[object Map]"===u?i?"[Map]":"Map {"+(0,qe.printIteratorEntries)(e.entries(),t,r,n,o,Je," => ")+"}":"[object Set]"===u?i?"[Set]":"Set {"+(0,qe.printIteratorValues)(e.values(),t,r,n,o,Je)+"}":i||ke(e)?"["+Fe(e)+"]":(l?"":t.printBasicPrototype||"Object"!==Fe(e)?Fe(e)+" ":"")+"{"+(0,qe.printObjectProperties)(e,t,r,n,o,Je)+"}"}function Ve(e,t,r,n,o,a){let i;try{i=function(e){return null!=e.serialize}(e)?e.serialize(t,r,n,o,a,Je):e.print(t,(e=>Je(e,r,n,o,a)),(e=>{const t=n+r.indent;return t+e.replace(Le,"\n"+t)}),{edgeSpacing:r.spacingOuter,min:r.min,spacing:r.spacingInner},r.colors)}catch(e){throw new Ue(e.message,e.stack)}if("string"!=typeof i)throw new Error('pretty-format: Plugin must return type "string" but instead returned "'+typeof i+'".');return i}function Ge(e,t){for(let r=0;r<e.length;r++)try{if(e[r].test(t))return e[r]}catch(e){throw new Ue(e.message,e.stack)}return null}function Je(e,t,r,n,o,a){const i=Ge(t.plugins,e);if(null!==i)return Ve(i,e,t,r,n,o);const l=ze(e,t.printFunctionName,t.escapeRegex,t.escapeString);return null!==l?l:$e(e,t,r,n,o,a)}const Qe={comment:"gray",content:"reset",prop:"yellow",tag:"cyan",value:"green"},Xe=Object.keys(Qe),Ke={callToJSON:!0,compareKeys:void 0,escapeRegex:!1,escapeString:!0,highlight:!1,indent:2,maxDepth:1/0,min:!1,plugins:[],printBasicPrototype:!0,printFunctionName:!0,theme:Qe};var Ye=o.DEFAULT_OPTIONS=Ke;const Ze=e=>Xe.reduce(((t,r)=>{const n=e.theme&&void 0!==e.theme[r]?e.theme[r]:Qe[r],o=n&&we.default[n];if(!o||"string"!=typeof o.close||"string"!=typeof o.open)throw new Error('pretty-format: Option "theme" has a key "'+r+'" whose value "'+n+'" is undefined in ansi-styles.');return t[r]=o,t}),Object.create(null)),et=e=>e&&void 0!==e.printFunctionName?e.printFunctionName:Ke.printFunctionName,tt=e=>e&&void 0!==e.escapeRegex?e.escapeRegex:Ke.escapeRegex,rt=e=>e&&void 0!==e.escapeString?e.escapeString:Ke.escapeString,nt=e=>{var t,r;return{callToJSON:e&&void 0!==e.callToJSON?e.callToJSON:Ke.callToJSON,colors:e&&e.highlight?Ze(e):Xe.reduce(((e,t)=>(e[t]={close:"",open:""},e)),Object.create(null)),compareKeys:e&&"function"==typeof e.compareKeys?e.compareKeys:Ke.compareKeys,escapeRegex:tt(e),escapeString:rt(e),indent:e&&e.min?"":(r=e&&void 0!==e.indent?e.indent:Ke.indent,new Array(r+1).join(" ")),maxDepth:e&&void 0!==e.maxDepth?e.maxDepth:Ke.maxDepth,min:e&&void 0!==e.min?e.min:Ke.min,plugins:e&&void 0!==e.plugins?e.plugins:Ke.plugins,printBasicPrototype:null===(t=null==e?void 0:e.printBasicPrototype)||void 0===t||t,printFunctionName:et(e),spacingInner:e&&e.min?" ":"\n",spacingOuter:e&&e.min?"":"\n"}};function ot(e,t){if(t&&(function(e){if(Object.keys(e).forEach((e=>{if(!Ke.hasOwnProperty(e))throw new Error('pretty-format: Unknown option "'+e+'".')})),e.min&&void 0!==e.indent&&0!==e.indent)throw new Error('pretty-format: Options "min" and "indent" cannot be used together.');if(void 0!==e.theme){if(null===e.theme)throw new Error('pretty-format: Option "theme" must not be null.');if("object"!=typeof e.theme)throw new Error('pretty-format: Option "theme" must be of type "object" but instead received "'+typeof e.theme+'".')}}(t),t.plugins)){const r=Ge(t.plugins,e);if(null!==r)return Ve(r,e,nt(t),"",0,[])}const r=ze(e,et(t),tt(t),rt(t));return null!==r?r:$e(e,nt(t),"",0,[])}const at={AsymmetricMatcher:Ee.default,ConvertAnsi:xe.default,DOMCollection:Oe.default,DOMElement:je.default,Immutable:Re.default,ReactElement:Se.default,ReactTestComponent:Ae.default};Ce=o.plugins=at;var it=ot;ge=o.default=it;var lt=t({__proto__:null,get DEFAULT_OPTIONS(){return Ye},format:Pe,get plugins(){return Ce},get default(){return ge}},[o]);function ut(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;")}const st=(e,t,r,n,o,a,i)=>{const l=n+r.indent,u=r.colors;return e.map((e=>{const s=t[e];let c=i(s,r,l,o,a);return"string"!=typeof s&&(-1!==c.indexOf("\n")&&(c=r.spacingOuter+l+c+r.spacingOuter+n),c="{"+c+"}"),r.spacingInner+n+u.prop.open+e+u.prop.close+"="+u.value.open+c+u.value.close})).join("")},ct=(e,t,r,n,o,a)=>e.map((e=>{const i="string"==typeof e?dt(e,t):a(e,t,r,n,o);return""===i&&"object"==typeof e&&null!==e&&3!==e.nodeType?"":t.spacingOuter+r+i})).join(""),dt=(e,t)=>{const r=t.colors.content;return r.open+ut(e)+r.close},pt=(e,t)=>{const r=t.colors.comment;return r.open+"\x3c!--"+ut(e)+"--\x3e"+r.close},ft=(e,t,r,n,o)=>{const a=n.colors.tag;return a.open+"<"+e+(t&&a.close+t+n.spacingOuter+o+a.open)+(r?">"+a.close+r+n.spacingOuter+o+a.open+"</"+e:(t&&!n.min?"":" ")+"/")+">"+a.close},bt=(e,t)=>{const r=t.colors.tag;return r.open+"<"+e+r.close+" …"+r.open+" />"+r.close},mt=3,yt=8,vt=11,ht=/^((HTML|SVG)\w*)?Element$/,gt=e=>{const t=e.constructor.name,{nodeType:r,tagName:n}=e,o="string"==typeof n&&n.includes("-")||"function"==typeof e.hasAttribute&&e.hasAttribute("is");return 1===r&&(ht.test(t)||o)||r===mt&&"Text"===t||r===yt&&"Comment"===t||r===vt&&"DocumentFragment"===t};function Pt(e){return e.nodeType===vt}function Ct(e){return{test:e=>{var t;return(null==e||null==(t=e.constructor)?void 0:t.name)&&gt(e)},serialize:(t,r,n,o,a,i)=>{if(function(e){return e.nodeType===mt}(t))return dt(t.data,r);if(function(e){return e.nodeType===yt}(t))return pt(t.data,r);const l=Pt(t)?"DocumentFragment":t.tagName.toLowerCase();return++o>r.maxDepth?bt(l,r):ft(l,st(Pt(t)?[]:Array.from(t.attributes).map((e=>e.name)).sort(),Pt(t)?{}:Array.from(t.attributes).reduce(((e,t)=>(e[t.name]=t.value,e)),{}),r,n+r.indent,o,a,i),ct(Array.prototype.slice.call(t.childNodes||t.children).filter(e),r,n+r.indent,o,a,i),r,n)}}}let wt=null,qt=null,Et=null;try{const e=module&&module.require;qt=e.call(module,"fs").readFileSync,Et=e.call(module,"@babel/code-frame").codeFrameColumns,wt=e.call(module,"chalk")}catch{}function xt(){if(!qt||!Et)return"";return function(e){const t=e.indexOf("(")+1,r=e.indexOf(")"),n=e.slice(t,r),o=n.split(":"),[a,i,l]=[o[0],parseInt(o[1],10),parseInt(o[2],10)];let u="";try{u=qt(a,"utf-8")}catch{return""}const s=Et(u,{start:{line:i,column:l}},{highlightCode:!0,linesBelow:0});return wt.dim(n)+"\n"+s+"\n"}((new Error).stack.split("\n").slice(1).find((e=>!e.includes("node_modules/"))))}const Ot=3;function jt(){return"undefined"!=typeof jest&&null!==jest&&(!0===setTimeout._isMockFunction||Object.prototype.hasOwnProperty.call(setTimeout,"clock"))}function Rt(){if("undefined"==typeof window)throw new Error("Could not find default container");return window.document}function St(e){if(e.defaultView)return e.defaultView;if(e.ownerDocument&&e.ownerDocument.defaultView)return e.ownerDocument.defaultView;if(e.window)return e.window;throw e.ownerDocument&&null===e.ownerDocument.defaultView?new Error("It looks like the window object is not available for the provided node."):e.then instanceof Function?new Error("It looks like you passed a Promise object instead of a DOM node. Did you do something like `fireEvent.click(screen.findBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`, or await the findBy query `fireEvent.click(await screen.findBy...`?"):Array.isArray(e)?new Error("It looks like you passed an Array instead of a DOM node. Did you do something like `fireEvent.click(screen.getAllBy...` when you meant to use a `getBy` query `fireEvent.click(screen.getBy...`?"):"function"==typeof e.debug&&"function"==typeof e.logTestingPlaygroundURL?new Error("It looks like you passed a `screen` object. Did you do something like `fireEvent.click(screen, ...` when you meant to use a query, e.g. `fireEvent.click(screen.getBy..., `?"):new Error("The given node is not an Element, the node type is: "+typeof e+".")}function At(e){if(!e||"function"!=typeof e.querySelector||"function"!=typeof e.querySelectorAll)throw new TypeError("Expected container to be an Element, a Document or a DocumentFragment but got "+function(e){if("object"==typeof e)return null===e?"null":e.constructor.name;return typeof e}(e)+".")}const _t=()=>{let e;try{var t;e=JSON.parse(null==(t=process)||null==(t=t.env)?void 0:t.COLORS)}catch(e){}return"boolean"==typeof e?e:"undefined"!=typeof process&&void 0!==process.versions&&void 0!==process.versions.node},{DOMCollection:Tt}=Ce,Mt=1,It=8;function Bt(e){return e.nodeType!==It&&(e.nodeType!==Mt||!e.matches(Lt().defaultIgnore))}function Ft(e,t,r){if(void 0===r&&(r={}),e||(e=Rt().body),"number"!=typeof t&&(t=7e3),0===t)return"";e.documentElement&&(e=e.documentElement);let n=typeof e;if("object"===n?n=e.constructor.name:e={},!("outerHTML"in e))throw new TypeError("Expected an element or document but got "+n);const{filterNode:o=Bt,...a}=r,i=Pe(e,{plugins:[Ct(o),Tt],printFunctionName:!1,highlight:_t(),...a});return void 0!==t&&e.outerHTML.length>t?i.slice(0,t)+"...":i}const kt=function(){const e=xt();e?console.log(Ft(...arguments)+"\n\n"+e):console.log(Ft(...arguments))};let Nt={testIdAttribute:"data-testid",asyncUtilTimeout:1e3,asyncWrapper:e=>e(),unstable_advanceTimersWrapper:e=>e(),eventWrapper:e=>e(),defaultHidden:!1,defaultIgnore:"script, style",showOriginalStackTrace:!1,throwSuggestions:!1,getElementError(e,t){const r=Ft(t),n=new Error([e,"Ignored nodes: comments, "+Nt.defaultIgnore+"\n"+r].filter(Boolean).join("\n\n"));return n.name="TestingLibraryElementError",n},_disableExpensiveErrorDiagnostics:!1,computedStyleSupportsPseudoElements:!1};function Lt(){return Nt}const Ut=["button","meter","output","progress","select","textarea","input"];function Dt(e){return Ut.includes(e.nodeName.toLowerCase())?"":e.nodeType===Ot?e.textContent:Array.from(e.childNodes).map((e=>Dt(e))).join("")}function Ht(e){let t;return t="label"===e.tagName.toLowerCase()?Dt(e):e.value||e.textContent,t}function Wt(e){var t;if(void 0!==e.labels)return null!=(t=e.labels)?t:[];if(!function(e){return/BUTTON|METER|OUTPUT|PROGRESS|SELECT|TEXTAREA/.test(e.tagName)||"INPUT"===e.tagName&&"hidden"!==e.getAttribute("type")}(e))return[];const r=e.ownerDocument.querySelectorAll("label");return Array.from(r).filter((t=>t.control===e))}function zt(e,t,r){let{selector:n="*"}=void 0===r?{}:r;const o=t.getAttribute("aria-labelledby"),a=o?o.split(" "):[];return a.length?a.map((t=>{const r=e.querySelector('[id="'+t+'"]');return r?{content:Ht(r),formControl:null}:{content:"",formControl:null}})):Array.from(Wt(t)).map((e=>({content:Ht(e),formControl:Array.from(e.querySelectorAll("button, input, meter, output, progress, select, textarea")).filter((e=>e.matches(n)))[0]})))}function $t(e){if(null==e)throw new Error("It looks like "+e+" was passed instead of a matcher. Did you do something like getByText("+e+")?")}function Vt(e,t,r,n){if("string"!=typeof e)return!1;$t(r);const o=n(e);return"string"==typeof r||"number"==typeof r?o.toLowerCase().includes(r.toString().toLowerCase()):"function"==typeof r?r(o,t):Xt(r,o)}function Gt(e,t,r,n){if("string"!=typeof e)return!1;$t(r);const o=n(e);return r instanceof Function?r(o,t):r instanceof RegExp?Xt(r,o):o===String(r)}function Jt(e){let{trim:t=!0,collapseWhitespace:r=!0}=void 0===e?{}:e;return e=>{let n=e;return n=t?n.trim():n,n=r?n.replace(/\s+/g," "):n,n}}function Qt(e){let{trim:t,collapseWhitespace:r,normalizer:n}=e;if(!n)return Jt({trim:t,collapseWhitespace:r});if(void 0!==t||void 0!==r)throw new Error('trim and collapseWhitespace are not supported with a normalizer. If you want to use the default trim and collapseWhitespace logic in your normalizer, use "getDefaultNormalizer({trim, collapseWhitespace})" and compose that into your normalizer');return n}function Xt(e,t){const r=e.test(t);return e.global&&0!==e.lastIndex&&(console.warn("To match all elements we had to reset the lastIndex of the RegExp because the global flag is enabled. We encourage to remove the global flag from the RegExp."),e.lastIndex=0),r}function Kt(e){return e.matches("input[type=submit], input[type=button], input[type=reset]")?e.value:Array.from(e.childNodes).filter((e=>e.nodeType===Ot&&Boolean(e.textContent))).map((e=>e.textContent)).join("")}var Yt=Object.prototype.toString;function Zt(e){return"function"==typeof e||"[object Function]"===Yt.call(e)}var er=Math.pow(2,53)-1;function tr(e){var t=function(e){var t=Number(e);return isNaN(t)?0:0!==t&&isFinite(t)?(t>0?1:-1)*Math.floor(Math.abs(t)):t}(e);return Math.min(Math.max(t,0),er)}function rr(e,t){var r=Array,n=Object(e);if(null==e)throw new TypeError("Array.from requires an array-like object - not null or undefined");if(void 0!==t&&!Zt(t))throw new TypeError("Array.from: when provided, the second argument must be a function");for(var o,a=tr(n.length),i=Zt(r)?Object(new r(a)):new Array(a),l=0;l<a;)o=n[l],i[l]=t?t(o,l):o,l+=1;return i.length=a,i}function nr(e){return nr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},nr(e)}function or(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,ar(n.key),n)}}function ar(e){var t=function(e,t){if("object"!==nr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==nr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===nr(t)?t:String(t)}var ir=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),function(e,t,r){(t=ar(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r}(this,"items",void 0),this.items=t}var t,r,n;return t=e,(r=[{key:"add",value:function(e){return!1===this.has(e)&&this.items.push(e),this}},{key:"clear",value:function(){this.items=[]}},{key:"delete",value:function(e){var t=this.items.length;return this.items=this.items.filter((function(t){return t!==e})),t!==this.items.length}},{key:"forEach",value:function(e){var t=this;this.items.forEach((function(r){e(r,r,t)}))}},{key:"has",value:function(e){return-1!==this.items.indexOf(e)}},{key:"size",get:function(){return this.items.length}}])&&or(t.prototype,r),n&&or(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),lr="undefined"==typeof Set?Set:ir;function ur(e){var t;return null!==(t=e.localName)&&void 0!==t?t:e.tagName.toLowerCase()}var sr={article:"article",aside:"complementary",button:"button",datalist:"listbox",dd:"definition",details:"group",dialog:"dialog",dt:"term",fieldset:"group",figure:"figure",form:"form",footer:"contentinfo",h1:"heading",h2:"heading",h3:"heading",h4:"heading",h5:"heading",h6:"heading",header:"banner",hr:"separator",html:"document",legend:"legend",li:"listitem",math:"math",main:"main",menu:"list",nav:"navigation",ol:"list",optgroup:"group",option:"option",output:"status",progress:"progressbar",section:"region",summary:"button",table:"table",tbody:"rowgroup",textarea:"textbox",tfoot:"rowgroup",td:"cell",th:"columnheader",thead:"rowgroup",tr:"row",ul:"list"},cr={caption:new Set(["aria-label","aria-labelledby"]),code:new Set(["aria-label","aria-labelledby"]),deletion:new Set(["aria-label","aria-labelledby"]),emphasis:new Set(["aria-label","aria-labelledby"]),generic:new Set(["aria-label","aria-labelledby","aria-roledescription"]),insertion:new Set(["aria-label","aria-labelledby"]),paragraph:new Set(["aria-label","aria-labelledby"]),presentation:new Set(["aria-label","aria-labelledby"]),strong:new Set(["aria-label","aria-labelledby"]),subscript:new Set(["aria-label","aria-labelledby"]),superscript:new Set(["aria-label","aria-labelledby"])};function dr(e,t){return function(e,t){return["aria-atomic","aria-busy","aria-controls","aria-current","aria-describedby","aria-details","aria-dropeffect","aria-flowto","aria-grabbed","aria-hidden","aria-keyshortcuts","aria-label","aria-labelledby","aria-live","aria-owns","aria-relevant","aria-roledescription"].some((function(r){var n;return e.hasAttribute(r)&&!(null!==(n=cr[t])&&void 0!==n&&n.has(r))}))}(e,t)}function pr(e){var t=function(e){var t=e.getAttribute("role");if(null!==t){var r=t.trim().split(" ")[0];if(r.length>0)return r}return null}(e);if(null===t||"presentation"===t){var r=function(e){var t=sr[ur(e)];if(void 0!==t)return t;switch(ur(e)){case"a":case"area":case"link":if(e.hasAttribute("href"))return"link";break;case"img":return""!==e.getAttribute("alt")||dr(e,"img")?"img":"presentation";case"input":var r=e.type;switch(r){case"button":case"image":case"reset":case"submit":return"button";case"checkbox":case"radio":return r;case"range":return"slider";case"email":case"tel":case"text":case"url":return e.hasAttribute("list")?"combobox":"textbox";case"search":return e.hasAttribute("list")?"combobox":"searchbox";case"number":return"spinbutton";default:return null}case"select":return e.hasAttribute("multiple")||e.size>1?"listbox":"combobox"}return null}(e);if("presentation"!==t||dr(e,r||""))return r}return t}function fr(e){return null!==e&&e.nodeType===e.ELEMENT_NODE}function br(e){return fr(e)&&"caption"===ur(e)}function mr(e){return fr(e)&&"input"===ur(e)}function yr(e){return fr(e)&&"legend"===ur(e)}function vr(e){return function(e){return fr(e)&&void 0!==e.ownerSVGElement}(e)&&"title"===ur(e)}function hr(e,t){if(fr(e)&&e.hasAttribute(t)){var r=e.getAttribute(t).split(" "),n=e.getRootNode?e.getRootNode():e.ownerDocument;return r.map((function(e){return n.getElementById(e)})).filter((function(e){return null!==e}))}return[]}function gr(e,t){return!!fr(e)&&-1!==t.indexOf(pr(e))}function Pr(e,t){if(!fr(e))return!1;if("range"===t)return gr(e,["meter","progressbar","scrollbar","slider","spinbutton"]);throw new TypeError("No knowledge about abstract role '".concat(t,"'. This is likely a bug :("))}function Cr(e,t){var r=rr(e.querySelectorAll(t));return hr(e,"aria-owns").forEach((function(e){r.push.apply(r,rr(e.querySelectorAll(t)))})),r}function wr(e){return fr(t=e)&&"select"===ur(t)?e.selectedOptions||Cr(e,"[selected]"):Cr(e,'[aria-selected="true"]');var t}function qr(e){return mr(e)||fr(t=e)&&"textarea"===ur(t)?e.value:e.textContent||"";var t}function Er(e){var t=e.getPropertyValue("content");return/^["'].*["']$/.test(t)?t.slice(1,-1):""}function xr(e){var t=ur(e);return"button"===t||"input"===t&&"hidden"!==e.getAttribute("type")||"meter"===t||"output"===t||"progress"===t||"select"===t||"textarea"===t}function Or(e){if(xr(e))return e;var t=null;return e.childNodes.forEach((function(e){if(null===t&&fr(e)){var r=Or(e);null!==r&&(t=r)}})),t}function jr(e){if(void 0!==e.control)return e.control;var t=e.getAttribute("for");return null!==t?e.ownerDocument.getElementById(t):Or(e)}function Rr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=new lr,n=function(e){var t=(null===e.ownerDocument?e:e.ownerDocument).defaultView;if(null===t)throw new TypeError("no window available");return t}(e),o=t.compute,a=void 0===o?"name":o,i=t.computedStyleSupportsPseudoElements,l=void 0===i?void 0!==t.getComputedStyle:i,u=t.getComputedStyle,s=void 0===u?n.getComputedStyle.bind(n):u,c=t.hidden,d=void 0!==c&&c;function p(e,t){var r,n,o="";if(fr(e)&&l){var a=Er(s(e,"::before"));o="".concat(a," ").concat(o)}if((function(e){return fr(e)&&"slot"===ur(e)}(e)?0===(n=(r=e).assignedNodes()).length?rr(r.childNodes):n:rr(e.childNodes).concat(hr(e,"aria-owns"))).forEach((function(e){var r=m(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0}),n="inline"!==(fr(e)?s(e).getPropertyValue("display"):"inline")?" ":"";o+="".concat(n).concat(r).concat(n)})),fr(e)&&l){var i=Er(s(e,"::after"));o="".concat(o," ").concat(i)}return o.trim()}function f(e,t){var n=e.getAttributeNode(t);return null===n||r.has(n)||""===n.value.trim()?null:(r.add(n),n.value)}function b(e){if(!fr(e))return null;if(function(e){return fr(e)&&"fieldset"===ur(e)}(e)){r.add(e);for(var t=rr(e.childNodes),n=0;n<t.length;n+=1){var o=t[n];if(yr(o))return m(o,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else if(function(e){return fr(e)&&"table"===ur(e)}(e)){r.add(e);for(var a=rr(e.childNodes),i=0;i<a.length;i+=1){var l=a[i];if(br(l))return m(l,{isEmbeddedInLabel:!1,isReferenced:!1,recursion:!1})}}else{if(function(e){return fr(e)&&"svg"===ur(e)}(e)){r.add(e);for(var u=rr(e.childNodes),s=0;s<u.length;s+=1){var c=u[s];if(vr(c))return c.textContent}return null}if("img"===ur(e)||"area"===ur(e)){var d=f(e,"alt");if(null!==d)return d}else if(function(e){return fr(e)&&"optgroup"===ur(e)}(e)){var b=f(e,"label");if(null!==b)return b}}if(mr(e)&&("button"===e.type||"submit"===e.type||"reset"===e.type)){var y=f(e,"value");if(null!==y)return y;if("submit"===e.type)return"Submit";if("reset"===e.type)return"Reset"}var v,h,g=null===(h=(v=e).labels)?h:void 0!==h?rr(h):xr(v)?rr(v.ownerDocument.querySelectorAll("label")).filter((function(e){return jr(e)===v})):null;if(null!==g&&0!==g.length)return r.add(e),rr(g).map((function(e){return m(e,{isEmbeddedInLabel:!0,isReferenced:!1,recursion:!0})})).filter((function(e){return e.length>0})).join(" ");if(mr(e)&&"image"===e.type){var P=f(e,"alt");if(null!==P)return P;var C=f(e,"title");return null!==C?C:"Submit Query"}if(gr(e,["button"])){var w=p(e,{isEmbeddedInLabel:!1,isReferenced:!1});if(""!==w)return w}return null}function m(e,t){if(r.has(e))return"";if(!d&&function(e,t){if(!fr(e))return!1;if(e.hasAttribute("hidden")||"true"===e.getAttribute("aria-hidden"))return!0;var r=t(e);return"none"===r.getPropertyValue("display")||"hidden"===r.getPropertyValue("visibility")}(e,s)&&!t.isReferenced)return r.add(e),"";var n=fr(e)?e.getAttributeNode("aria-labelledby"):null,o=null===n||r.has(n)?[]:hr(e,"aria-labelledby");if("name"===a&&!t.isReferenced&&o.length>0)return r.add(n),o.map((function(e){return m(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!0,recursion:!1})})).join(" ");var i,l=t.recursion&&(gr(i=e,["button","combobox","listbox","textbox"])||Pr(i,"range"))&&"name"===a;if(!l){var u=(fr(e)&&e.getAttribute("aria-label")||"").trim();if(""!==u&&"name"===a)return r.add(e),u;if(!function(e){return gr(e,["none","presentation"])}(e)){var c=b(e);if(null!==c)return r.add(e),c}}if(gr(e,["menu"]))return r.add(e),"";if(l||t.isEmbeddedInLabel||t.isReferenced){if(gr(e,["combobox","listbox"])){r.add(e);var y=wr(e);return 0===y.length?mr(e)?e.value:"":rr(y).map((function(e){return m(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1,recursion:!0})})).join(" ")}if(Pr(e,"range"))return r.add(e),e.hasAttribute("aria-valuetext")?e.getAttribute("aria-valuetext"):e.hasAttribute("aria-valuenow")?e.getAttribute("aria-valuenow"):e.getAttribute("value")||"";if(gr(e,["textbox"]))return r.add(e),qr(e)}if(function(e){return gr(e,["button","cell","checkbox","columnheader","gridcell","heading","label","legend","link","menuitem","menuitemcheckbox","menuitemradio","option","radio","row","rowheader","switch","tab","tooltip","treeitem"])}(e)||fr(e)&&t.isReferenced||function(e){return br(e)}(e)){var v=p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});if(""!==v)return r.add(e),v}if(e.nodeType===e.TEXT_NODE)return r.add(e),e.textContent||"";if(t.recursion)return r.add(e),p(e,{isEmbeddedInLabel:t.isEmbeddedInLabel,isReferenced:!1});var h=function(e){return fr(e)?f(e,"title"):null}(e);return null!==h?(r.add(e),h):(r.add(e),"")}return m(e,{isEmbeddedInLabel:!1,isReferenced:"description"===a,recursion:!1}).trim().replace(/\s\s+/g," ")}function Sr(e){return Sr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Sr(e)}function Ar(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function _r(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?Ar(Object(r),!0).forEach((function(t){Tr(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):Ar(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Tr(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Sr(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Sr(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Sr(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function Mr(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=hr(e,"aria-describedby").map((function(e){return Rr(e,_r(_r({},t),{},{compute:"description"}))})).join(" ");if(""===r){var n=e.getAttribute("title");r=null===n?"":n}return r}function Ir(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return gr(e,["caption","code","deletion","emphasis","generic","insertion","paragraph","presentation","strong","subscript","superscript"])?"":Rr(e,t)}var Br={},Fr={},kr={},Nr={};Object.defineProperty(Nr,"__esModule",{value:!0}),Nr.default=void 0;var Lr=function(){var e=this,t=0,r={"@@iterator":function(){return r},next:function(){if(t<e.length){var r=e[t];return t+=1,{done:!1,value:r}}return{done:!0}}};return r};Nr.default=Lr,Object.defineProperty(kr,"__esModule",{value:!0}),kr.default=function(e,t){"function"==typeof Symbol&&"symbol"===Dr(Symbol.iterator)&&Object.defineProperty(e,Symbol.iterator,{value:Ur.default.bind(t)});return e};var Ur=function(e){return e&&e.__esModule?e:{default:e}}(Nr);function Dr(e){return Dr="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dr(e)}Object.defineProperty(Fr,"__esModule",{value:!0}),Fr.default=void 0;var Hr=function(e){return e&&e.__esModule?e:{default:e}}(kr);function Wr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||zr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function zr(e,t){if(e){if("string"==typeof e)return $r(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?$r(e,t):void 0}}function $r(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var Vr=[["aria-activedescendant",{type:"id"}],["aria-atomic",{type:"boolean"}],["aria-autocomplete",{type:"token",values:["inline","list","both","none"]}],["aria-busy",{type:"boolean"}],["aria-checked",{type:"tristate"}],["aria-colcount",{type:"integer"}],["aria-colindex",{type:"integer"}],["aria-colspan",{type:"integer"}],["aria-controls",{type:"idlist"}],["aria-current",{type:"token",values:["page","step","location","date","time",!0,!1]}],["aria-describedby",{type:"idlist"}],["aria-details",{type:"id"}],["aria-disabled",{type:"boolean"}],["aria-dropeffect",{type:"tokenlist",values:["copy","execute","link","move","none","popup"]}],["aria-errormessage",{type:"id"}],["aria-expanded",{type:"boolean",allowundefined:!0}],["aria-flowto",{type:"idlist"}],["aria-grabbed",{type:"boolean",allowundefined:!0}],["aria-haspopup",{type:"token",values:[!1,!0,"menu","listbox","tree","grid","dialog"]}],["aria-hidden",{type:"boolean",allowundefined:!0}],["aria-invalid",{type:"token",values:["grammar",!1,"spelling",!0]}],["aria-keyshortcuts",{type:"string"}],["aria-label",{type:"string"}],["aria-labelledby",{type:"idlist"}],["aria-level",{type:"integer"}],["aria-live",{type:"token",values:["assertive","off","polite"]}],["aria-modal",{type:"boolean"}],["aria-multiline",{type:"boolean"}],["aria-multiselectable",{type:"boolean"}],["aria-orientation",{type:"token",values:["vertical","undefined","horizontal"]}],["aria-owns",{type:"idlist"}],["aria-placeholder",{type:"string"}],["aria-posinset",{type:"integer"}],["aria-pressed",{type:"tristate"}],["aria-readonly",{type:"boolean"}],["aria-relevant",{type:"tokenlist",values:["additions","all","removals","text"]}],["aria-required",{type:"boolean"}],["aria-roledescription",{type:"string"}],["aria-rowcount",{type:"integer"}],["aria-rowindex",{type:"integer"}],["aria-rowspan",{type:"integer"}],["aria-selected",{type:"boolean",allowundefined:!0}],["aria-setsize",{type:"integer"}],["aria-sort",{type:"token",values:["ascending","descending","none","other"]}],["aria-valuemax",{type:"number"}],["aria-valuemin",{type:"number"}],["aria-valuenow",{type:"number"}],["aria-valuetext",{type:"string"}]],Gr={entries:function(){return Vr},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=zr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Vr);try{for(n.s();!(t=n.n()).done;){var o=Wr(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Vr)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Vr.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!Gr.get(e)},keys:function(){return Vr.map((function(e){return Wr(e,1)[0]}))},values:function(){return Vr.map((function(e){return Wr(e,2)[1]}))}},Jr=(0,Hr.default)(Gr,Gr.entries());Fr.default=Jr;var Qr={};Object.defineProperty(Qr,"__esModule",{value:!0}),Qr.default=void 0;var Xr=function(e){return e&&e.__esModule?e:{default:e}}(kr);function Kr(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||Yr(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Yr(e,t){if(e){if("string"==typeof e)return Zr(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Zr(e,t):void 0}}function Zr(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var en=[["a",{reserved:!1}],["abbr",{reserved:!1}],["acronym",{reserved:!1}],["address",{reserved:!1}],["applet",{reserved:!1}],["area",{reserved:!1}],["article",{reserved:!1}],["aside",{reserved:!1}],["audio",{reserved:!1}],["b",{reserved:!1}],["base",{reserved:!0}],["bdi",{reserved:!1}],["bdo",{reserved:!1}],["big",{reserved:!1}],["blink",{reserved:!1}],["blockquote",{reserved:!1}],["body",{reserved:!1}],["br",{reserved:!1}],["button",{reserved:!1}],["canvas",{reserved:!1}],["caption",{reserved:!1}],["center",{reserved:!1}],["cite",{reserved:!1}],["code",{reserved:!1}],["col",{reserved:!0}],["colgroup",{reserved:!0}],["content",{reserved:!1}],["data",{reserved:!1}],["datalist",{reserved:!1}],["dd",{reserved:!1}],["del",{reserved:!1}],["details",{reserved:!1}],["dfn",{reserved:!1}],["dialog",{reserved:!1}],["dir",{reserved:!1}],["div",{reserved:!1}],["dl",{reserved:!1}],["dt",{reserved:!1}],["em",{reserved:!1}],["embed",{reserved:!1}],["fieldset",{reserved:!1}],["figcaption",{reserved:!1}],["figure",{reserved:!1}],["font",{reserved:!1}],["footer",{reserved:!1}],["form",{reserved:!1}],["frame",{reserved:!1}],["frameset",{reserved:!1}],["h1",{reserved:!1}],["h2",{reserved:!1}],["h3",{reserved:!1}],["h4",{reserved:!1}],["h5",{reserved:!1}],["h6",{reserved:!1}],["head",{reserved:!0}],["header",{reserved:!1}],["hgroup",{reserved:!1}],["hr",{reserved:!1}],["html",{reserved:!0}],["i",{reserved:!1}],["iframe",{reserved:!1}],["img",{reserved:!1}],["input",{reserved:!1}],["ins",{reserved:!1}],["kbd",{reserved:!1}],["keygen",{reserved:!1}],["label",{reserved:!1}],["legend",{reserved:!1}],["li",{reserved:!1}],["link",{reserved:!0}],["main",{reserved:!1}],["map",{reserved:!1}],["mark",{reserved:!1}],["marquee",{reserved:!1}],["menu",{reserved:!1}],["menuitem",{reserved:!1}],["meta",{reserved:!0}],["meter",{reserved:!1}],["nav",{reserved:!1}],["noembed",{reserved:!0}],["noscript",{reserved:!0}],["object",{reserved:!1}],["ol",{reserved:!1}],["optgroup",{reserved:!1}],["option",{reserved:!1}],["output",{reserved:!1}],["p",{reserved:!1}],["param",{reserved:!0}],["picture",{reserved:!0}],["pre",{reserved:!1}],["progress",{reserved:!1}],["q",{reserved:!1}],["rp",{reserved:!1}],["rt",{reserved:!1}],["rtc",{reserved:!1}],["ruby",{reserved:!1}],["s",{reserved:!1}],["samp",{reserved:!1}],["script",{reserved:!0}],["section",{reserved:!1}],["select",{reserved:!1}],["small",{reserved:!1}],["source",{reserved:!0}],["spacer",{reserved:!1}],["span",{reserved:!1}],["strike",{reserved:!1}],["strong",{reserved:!1}],["style",{reserved:!0}],["sub",{reserved:!1}],["summary",{reserved:!1}],["sup",{reserved:!1}],["table",{reserved:!1}],["tbody",{reserved:!1}],["td",{reserved:!1}],["textarea",{reserved:!1}],["tfoot",{reserved:!1}],["th",{reserved:!1}],["thead",{reserved:!1}],["time",{reserved:!1}],["title",{reserved:!0}],["tr",{reserved:!1}],["track",{reserved:!0}],["tt",{reserved:!1}],["u",{reserved:!1}],["ul",{reserved:!1}],["var",{reserved:!1}],["video",{reserved:!1}],["wbr",{reserved:!1}],["xmp",{reserved:!1}]],tn={entries:function(){return en},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Yr(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(en);try{for(n.s();!(t=n.n()).done;){var o=Kr(t.value,2),a=o[0],i=o[1];e.call(r,i,a,en)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=en.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!tn.get(e)},keys:function(){return en.map((function(e){return Kr(e,1)[0]}))},values:function(){return en.map((function(e){return Kr(e,2)[1]}))}},rn=(0,Xr.default)(tn,tn.entries());Qr.default=rn;var nn={},on={},an={};Object.defineProperty(an,"__esModule",{value:!0}),an.default=void 0;var ln={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};an.default=ln;var un={};Object.defineProperty(un,"__esModule",{value:!0}),un.default=void 0;var sn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};un.default=sn;var cn={};Object.defineProperty(cn,"__esModule",{value:!0}),cn.default=void 0;var dn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null},relatedConcepts:[{concept:{name:"input"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget"]]};cn.default=dn;var pn={};Object.defineProperty(pn,"__esModule",{value:!0}),pn.default=void 0;var fn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};pn.default=fn;var bn={};Object.defineProperty(bn,"__esModule",{value:!0}),bn.default=void 0;var mn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuemax":null,"aria-valuemin":null,"aria-valuenow":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};bn.default=mn;var yn={};Object.defineProperty(yn,"__esModule",{value:!0}),yn.default=void 0;var vn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{"aria-atomic":null,"aria-busy":null,"aria-controls":null,"aria-current":null,"aria-describedby":null,"aria-details":null,"aria-dropeffect":null,"aria-flowto":null,"aria-grabbed":null,"aria-hidden":null,"aria-keyshortcuts":null,"aria-label":null,"aria-labelledby":null,"aria-live":null,"aria-owns":null,"aria-relevant":null,"aria-roledescription":null},relatedConcepts:[{concept:{name:"rel"},module:"HTML"},{concept:{name:"role"},module:"XHTML"},{concept:{name:"type"},module:"Dublin Core"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};yn.default=vn;var hn={};Object.defineProperty(hn,"__esModule",{value:!0}),hn.default=void 0;var gn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"frontmatter"},module:"DTB"},{concept:{name:"level"},module:"DTB"},{concept:{name:"level"},module:"SMIL"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};hn.default=gn;var Pn={};Object.defineProperty(Pn,"__esModule",{value:!0}),Pn.default=void 0;var Cn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Pn.default=Cn;var wn={};Object.defineProperty(wn,"__esModule",{value:!0}),wn.default=void 0;var qn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","group"]]};wn.default=qn;var En={};Object.defineProperty(En,"__esModule",{value:!0}),En.default=void 0;var xn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};En.default=xn;var On={};Object.defineProperty(On,"__esModule",{value:!0}),On.default=void 0;var jn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};On.default=jn;var Rn={};Object.defineProperty(Rn,"__esModule",{value:!0}),Rn.default=void 0;var Sn={abstract:!0,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-modal":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype"]]};Rn.default=Sn,Object.defineProperty(on,"__esModule",{value:!0}),on.default=void 0;var An=Hn(an),_n=Hn(un),Tn=Hn(cn),Mn=Hn(pn),In=Hn(bn),Bn=Hn(yn),Fn=Hn(hn),kn=Hn(Pn),Nn=Hn(wn),Ln=Hn(En),Un=Hn(On),Dn=Hn(Rn);function Hn(e){return e&&e.__esModule?e:{default:e}}var Wn=[["command",An.default],["composite",_n.default],["input",Tn.default],["landmark",Mn.default],["range",In.default],["roletype",Bn.default],["section",Fn.default],["sectionhead",kn.default],["select",Nn.default],["structure",Ln.default],["widget",Un.default],["window",Dn.default]];on.default=Wn;var zn={},$n={};Object.defineProperty($n,"__esModule",{value:!0}),$n.default=void 0;var Vn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"assertive"},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};$n.default=Vn;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0}),Gn.default=void 0;var Jn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"alert"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","alert"],["roletype","window","dialog"]]};Gn.default=Jn;var Qn={};Object.defineProperty(Qn,"__esModule",{value:!0}),Qn.default=void 0;var Xn={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Qn.default=Xn;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0}),Kn.default=void 0;var Yn={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"article"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};Kn.default=Yn;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0}),Zn.default=void 0;var eo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"header"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Zn.default=eo;var to={};Object.defineProperty(to,"__esModule",{value:!0}),to.default=void 0;var ro={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};to.default=ro;var no={};Object.defineProperty(no,"__esModule",{value:!0}),no.default=void 0;var oo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-pressed":null},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-pressed"},{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"false"}],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"aria-expanded",value:"true"}],constraints:["direct descendant of details element with the open attribute defined"],name:"summary"},module:"HTML"},{concept:{attributes:[{name:"type",value:"button"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"image"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"reset"}],name:"input"},module:"HTML"},{concept:{attributes:[{name:"type",value:"submit"}],name:"input"},module:"HTML"},{concept:{name:"button"},module:"HTML"},{concept:{name:"trigger"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};no.default=oo;var ao={};Object.defineProperty(ao,"__esModule",{value:!0}),ao.default=void 0;var io={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:["figure","grid","table"],requiredContextRole:["figure","grid","table"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ao.default=io;var lo={};Object.defineProperty(lo,"__esModule",{value:!0}),lo.default=void 0;var uo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-colspan":null,"aria-rowindex":null,"aria-rowspan":null},relatedConcepts:[{concept:{constraints:["descendant of table"],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};lo.default=uo;var so={};Object.defineProperty(so,"__esModule",{value:!0}),so.default=void 0;var co={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"checkbox"}],name:"input"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};so.default=co;var po={};Object.defineProperty(po,"__esModule",{value:!0}),po.default=void 0;var fo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};po.default=fo;var bo={};Object.defineProperty(bo,"__esModule",{value:!0}),bo.default=void 0;var mo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{attributes:[{name:"scope",value:"col"}],concept:{name:"th"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};bo.default=mo;var yo={};Object.defineProperty(yo,"__esModule",{value:!0}),yo.default=void 0;var vo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-expanded":"false","aria-haspopup":"listbox"},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{constraints:["undefined"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"multiple"},{name:"size",value:1}],name:"select"},module:"HTML"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-expanded":"false"},superClass:[["roletype","widget","input"]]};yo.default=vo;var ho={};Object.defineProperty(ho,"__esModule",{value:!0}),ho.default=void 0;var go={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"aside"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ho.default=go;var Po={};Object.defineProperty(Po,"__esModule",{value:!0}),Po.default=void 0;var Co={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{constraints:["direct descendant of document"],name:"footer"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Po.default=Co;var wo={};Object.defineProperty(wo,"__esModule",{value:!0}),wo.default=void 0;var qo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dd"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};wo.default=qo;var Eo={};Object.defineProperty(Eo,"__esModule",{value:!0}),Eo.default=void 0;var xo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Eo.default=xo;var Oo={};Object.defineProperty(Oo,"__esModule",{value:!0}),Oo.default=void 0;var jo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dialog"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","window"]]};Oo.default=jo;var Ro={};Object.defineProperty(Ro,"__esModule",{value:!0}),Ro.default=void 0;var So={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{module:"DAISY Guide"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Ro.default=So;var Ao={};Object.defineProperty(Ao,"__esModule",{value:!0}),Ao.default=void 0;var _o={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"Device Independence Delivery Unit"}},{concept:{name:"body"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Ao.default=_o;var To={};Object.defineProperty(To,"__esModule",{value:!0}),To.default=void 0;var Mo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};To.default=Mo;var Io={};Object.defineProperty(Io,"__esModule",{value:!0}),Io.default=void 0;var Bo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["article"]],requiredProps:{},superClass:[["roletype","structure","section","list"]]};Io.default=Bo;var Fo={};Object.defineProperty(Fo,"__esModule",{value:!0}),Fo.default=void 0;var ko={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"figure"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fo.default=ko;var No={};Object.defineProperty(No,"__esModule",{value:!0}),No.default=void 0;var Lo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"form"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"name"}],name:"form"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};No.default=Lo;var Uo={};Object.defineProperty(Uo,"__esModule",{value:!0}),Uo.default=void 0;var Do={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[{concept:{name:"span"},module:"HTML"},{concept:{name:"div"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Uo.default=Do;var Ho={};Object.defineProperty(Ho,"__esModule",{value:!0}),Ho.default=void 0;var Wo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-multiselectable":null,"aria-readonly":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"grid"}],name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","structure","section","table"]]};Ho.default=Wo;var zo={};Object.defineProperty(zo,"__esModule",{value:!0}),zo.default=void 0;var $o={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-selected":null},relatedConcepts:[{concept:{attributes:[{name:"role",value:"gridcell"}],name:"td"},module:"HTML"}],requireContextRole:["row"],requiredContextRole:["row"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","widget"]]};zo.default=$o;var Vo={};Object.defineProperty(Vo,"__esModule",{value:!0}),Vo.default=void 0;var Go={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-disabled":null},relatedConcepts:[{concept:{name:"details"},module:"HTML"},{concept:{name:"fieldset"},module:"HTML"},{concept:{name:"optgroup"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Vo.default=Go;var Jo={};Object.defineProperty(Jo,"__esModule",{value:!0}),Jo.default=void 0;var Qo={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-level":"2"},relatedConcepts:[{concept:{name:"h1"},module:"HTML"},{concept:{name:"h2"},module:"HTML"},{concept:{name:"h3"},module:"HTML"},{concept:{name:"h4"},module:"HTML"},{concept:{name:"h5"},module:"HTML"},{concept:{name:"h6"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-level":"2"},superClass:[["roletype","structure","sectionhead"]]};Jo.default=Qo;var Xo={};Object.defineProperty(Xo,"__esModule",{value:!0}),Xo.default=void 0;var Ko={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"alt"}],name:"img"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"alt"}],name:"img"},module:"HTML"},{concept:{name:"imggroup"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Xo.default=Ko;var Yo={};Object.defineProperty(Yo,"__esModule",{value:!0}),Yo.default=void 0;var Zo={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Yo.default=Zo;var ea={};Object.defineProperty(ea,"__esModule",{value:!0}),ea.default=void 0;var ta={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[{concept:{attributes:[{name:"href"}],name:"a"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"area"},module:"HTML"},{concept:{attributes:[{name:"href"}],name:"link"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};ea.default=ta;var ra={};Object.defineProperty(ra,"__esModule",{value:!0}),ra.default=void 0;var na={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menu"},module:"HTML"},{concept:{name:"ol"},module:"HTML"},{concept:{name:"ul"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["listitem"]],requiredProps:{},superClass:[["roletype","structure","section"]]};ra.default=na;var oa={};Object.defineProperty(oa,"__esModule",{value:!0}),oa.default=void 0;var aa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-invalid":null,"aria-multiselectable":null,"aria-readonly":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[{concept:{attributes:[{constraints:[">1"],name:"size"},{name:"multiple"}],name:"select"},module:"HTML"},{concept:{attributes:[{constraints:[">1"],name:"size"}],name:"select"},module:"HTML"},{concept:{attributes:[{name:"multiple"}],name:"select"},module:"HTML"},{concept:{name:"datalist"},module:"HTML"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["option","group"],["option"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};oa.default=aa;var ia={};Object.defineProperty(ia,"__esModule",{value:!0}),ia.default=void 0;var la={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{constraints:["direct descendant of ol, ul or menu"],name:"li"},module:"HTML"},{concept:{name:"item"},module:"XForms"}],requireContextRole:["directory","list"],requiredContextRole:["directory","list"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ia.default=la;var ua={};Object.defineProperty(ua,"__esModule",{value:!0}),ua.default=void 0;var sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-live":"polite"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ua.default=sa;var ca={};Object.defineProperty(ca,"__esModule",{value:!0}),ca.default=void 0;var da={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"main"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ca.default=da;var pa={};Object.defineProperty(pa,"__esModule",{value:!0}),pa.default=void 0;var fa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};pa.default=fa;var ba={};Object.defineProperty(ba,"__esModule",{value:!0}),ba.default=void 0;var ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"math"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ba.default=ma;var ya={};Object.defineProperty(ya,"__esModule",{value:!0}),ya.default=void 0;var va={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"vertical"},relatedConcepts:[{concept:{name:"MENU"},module:"JAPI"},{concept:{name:"list"},module:"ARIA"},{concept:{name:"select"},module:"XForms"},{concept:{name:"sidebar"},module:"DTB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};ya.default=va;var ha={};Object.defineProperty(ha,"__esModule",{value:!0}),ha.default=void 0;var ga={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"toolbar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["menuitem","group"],["menuitemradio","group"],["menuitemcheckbox","group"],["menuitem"],["menuitemcheckbox"],["menuitemradio"]],requiredProps:{},superClass:[["roletype","widget","composite","select","menu"],["roletype","structure","section","group","select","menu"]]};ha.default=ga;var Pa={};Object.defineProperty(Pa,"__esModule",{value:!0}),Pa.default=void 0;var Ca={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"MENU_ITEM"},module:"JAPI"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"menuitem"},module:"HTML"},{concept:{name:"option"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command"]]};Pa.default=Ca;var wa={};Object.defineProperty(wa,"__esModule",{value:!0}),wa.default=void 0;var qa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"],["roletype","widget","command","menuitem"]]};wa.default=qa;var Ea={};Object.defineProperty(Ea,"__esModule",{value:!0}),Ea.default=void 0;var xa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"menuitem"},module:"ARIA"}],requireContextRole:["group","menu","menubar"],requiredContextRole:["group","menu","menubar"],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox","menuitemcheckbox"],["roletype","widget","command","menuitem","menuitemcheckbox"],["roletype","widget","input","radio"]]};Ea.default=xa;var Oa={};Object.defineProperty(Oa,"__esModule",{value:!0}),Oa.default=void 0;var ja={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null,"aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","structure","range"]]};Oa.default=ja;var Ra={};Object.defineProperty(Ra,"__esModule",{value:!0}),Ra.default=void 0;var Sa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"nav"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ra.default=Sa;var Aa={};Object.defineProperty(Aa,"__esModule",{value:!0}),Aa.default=void 0;var _a={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:[],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[]};Aa.default=_a;var Ta={};Object.defineProperty(Ta,"__esModule",{value:!0}),Ta.default=void 0;var Ma={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ta.default=Ma;var Ia={};Object.defineProperty(Ia,"__esModule",{value:!0}),Ia.default=void 0;var Ba={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[{concept:{name:"item"},module:"XForms"},{concept:{name:"listitem"},module:"ARIA"},{concept:{name:"option"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-selected":"false"},superClass:[["roletype","widget","input"]]};Ia.default=Ba;var Fa={};Object.defineProperty(Fa,"__esModule",{value:!0}),Fa.default=void 0;var ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Fa.default=ka;var Na={};Object.defineProperty(Na,"__esModule",{value:!0}),Na.default=void 0;var La={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};Na.default=La;var Ua={};Object.defineProperty(Ua,"__esModule",{value:!0}),Ua.default=void 0;var Da={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-valuetext":null},relatedConcepts:[{concept:{name:"progress"},module:"HTML"},{concept:{name:"status"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","range"],["roletype","widget"]]};Ua.default=Da;var Ha={};Object.defineProperty(Ha,"__esModule",{value:!0}),Ha.default=void 0;var Wa={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-checked":null,"aria-posinset":null,"aria-setsize":null},relatedConcepts:[{concept:{attributes:[{name:"type",value:"radio"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input"]]};Ha.default=Wa;var za={};Object.defineProperty(za,"__esModule",{value:!0}),za.default=void 0;var $a={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{name:"list"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["radio"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};za.default=$a;var Va={};Object.defineProperty(Va,"__esModule",{value:!0}),Va.default=void 0;var Ga={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["set"],name:"aria-label"}],name:"section"},module:"HTML"},{concept:{attributes:[{constraints:["set"],name:"aria-labelledby"}],name:"section"},module:"HTML"},{concept:{name:"Device Independence Glossart perceivable unit"}},{concept:{name:"frame"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Va.default=Ga;var Ja={};Object.defineProperty(Ja,"__esModule",{value:!0}),Ja.default=void 0;var Qa={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-colindex":null,"aria-expanded":null,"aria-level":null,"aria-posinset":null,"aria-rowindex":null,"aria-selected":null,"aria-setsize":null},relatedConcepts:[{concept:{name:"tr"},module:"HTML"}],requireContextRole:["grid","rowgroup","table","treegrid"],requiredContextRole:["grid","rowgroup","table","treegrid"],requiredOwnedElements:[["cell"],["columnheader"],["gridcell"],["rowheader"]],requiredProps:{},superClass:[["roletype","structure","section","group"],["roletype","widget"]]};Ja.default=Qa;var Xa={};Object.defineProperty(Xa,"__esModule",{value:!0}),Xa.default=void 0;var Ka={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"tbody"},module:"HTML"},{concept:{name:"tfoot"},module:"HTML"},{concept:{name:"thead"},module:"HTML"}],requireContextRole:["grid","table","treegrid"],requiredContextRole:["grid","table","treegrid"],requiredOwnedElements:[["row"]],requiredProps:{},superClass:[["roletype","structure"]]};Xa.default=Ka;var Ya={};Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.default=void 0;var Za={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-sort":null},relatedConcepts:[{concept:{attributes:[{name:"scope",value:"row"}],name:"th"},module:"HTML"},{concept:{attributes:[{name:"scope",value:"rowgroup"}],name:"th"},module:"HTML"}],requireContextRole:["row","rowgroup"],requiredContextRole:["row","rowgroup"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","cell"],["roletype","structure","section","cell","gridcell"],["roletype","widget","gridcell"],["roletype","structure","sectionhead"]]};Ya.default=Za;var ei={};Object.defineProperty(ei,"__esModule",{value:!0}),ei.default=void 0;var ti={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-valuetext":null,"aria-orientation":"vertical","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-controls":null,"aria-valuenow":null},superClass:[["roletype","structure","range"],["roletype","widget"]]};ei.default=ti;var ri={};Object.defineProperty(ri,"__esModule",{value:!0}),ri.default=void 0;var ni={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ri.default=ni;var oi={};Object.defineProperty(oi,"__esModule",{value:!0}),oi.default=void 0;var ai={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"search"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input","textbox"]]};oi.default=ai;var ii={};Object.defineProperty(ii,"__esModule",{value:!0}),ii.default=void 0;var li={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0","aria-valuenow":null,"aria-valuetext":null},relatedConcepts:[{concept:{name:"hr"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure"]]};ii.default=li;var ui={};Object.defineProperty(ui,"__esModule",{value:!0}),ui.default=void 0;var si={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-readonly":null,"aria-valuetext":null,"aria-orientation":"horizontal","aria-valuemax":"100","aria-valuemin":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"range"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-valuenow":null},superClass:[["roletype","widget","input"],["roletype","structure","range"]]};ui.default=si;var ci={};Object.defineProperty(ci,"__esModule",{value:!0}),ci.default=void 0;var di={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-readonly":null,"aria-required":null,"aria-valuetext":null,"aria-valuenow":"0"},relatedConcepts:[{concept:{attributes:[{name:"type",value:"number"}],name:"input"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","composite"],["roletype","widget","input"],["roletype","structure","range"]]};ci.default=di;var pi={};Object.defineProperty(pi,"__esModule",{value:!0}),pi.default=void 0;var fi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-atomic":"true","aria-live":"polite"},relatedConcepts:[{concept:{name:"output"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};pi.default=fi;var bi={};Object.defineProperty(bi,"__esModule",{value:!0}),bi.default=void 0;var mi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};bi.default=mi;var yi={};Object.defineProperty(yi,"__esModule",{value:!0}),yi.default=void 0;var vi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};yi.default=vi;var hi={};Object.defineProperty(hi,"__esModule",{value:!0}),hi.default=void 0;var gi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["prohibited"],prohibitedProps:["aria-label","aria-labelledby"],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};hi.default=gi;var Pi={};Object.defineProperty(Pi,"__esModule",{value:!0}),Pi.default=void 0;var Ci={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"button"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{"aria-checked":null},superClass:[["roletype","widget","input","checkbox"]]};Pi.default=Ci;var wi={};Object.defineProperty(wi,"__esModule",{value:!0}),wi.default=void 0;var qi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!0,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-disabled":null,"aria-expanded":null,"aria-haspopup":null,"aria-posinset":null,"aria-setsize":null,"aria-selected":"false"},relatedConcepts:[],requireContextRole:["tablist"],requiredContextRole:["tablist"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"],["roletype","widget"]]};wi.default=qi;var Ei={};Object.defineProperty(Ei,"__esModule",{value:!0}),Ei.default=void 0;var xi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-colcount":null,"aria-rowcount":null},relatedConcepts:[{concept:{name:"table"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","structure","section"]]};Ei.default=xi;var Oi={};Object.defineProperty(Oi,"__esModule",{value:!0}),Oi.default=void 0;var ji={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-level":null,"aria-multiselectable":null,"aria-orientation":"horizontal"},relatedConcepts:[{module:"DAISY",concept:{name:"guide"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["tab"]],requiredProps:{},superClass:[["roletype","widget","composite"]]};Oi.default=ji;var Ri={};Object.defineProperty(Ri,"__esModule",{value:!0}),Ri.default=void 0;var Si={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ri.default=Si;var Ai={};Object.defineProperty(Ai,"__esModule",{value:!0}),Ai.default=void 0;var _i={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"dfn"},module:"HTML"},{concept:{name:"dt"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ai.default=_i;var Ti={};Object.defineProperty(Ti,"__esModule",{value:!0}),Ti.default=void 0;var Mi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-activedescendant":null,"aria-autocomplete":null,"aria-errormessage":null,"aria-haspopup":null,"aria-invalid":null,"aria-multiline":null,"aria-placeholder":null,"aria-readonly":null,"aria-required":null},relatedConcepts:[{concept:{attributes:[{constraints:["undefined"],name:"type"},{constraints:["undefined"],name:"list"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"email"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"tel"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"text"}],name:"input"},module:"HTML"},{concept:{attributes:[{constraints:["undefined"],name:"list"},{name:"type",value:"url"}],name:"input"},module:"HTML"},{concept:{name:"input"},module:"XForms"},{concept:{name:"textarea"},module:"HTML"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","input"]]};Ti.default=Mi;var Ii={};Object.defineProperty(Ii,"__esModule",{value:!0}),Ii.default=void 0;var Bi={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ii.default=Bi;var Fi={};Object.defineProperty(Fi,"__esModule",{value:!0}),Fi.default=void 0;var ki={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","status"]]};Fi.default=ki;var Ni={};Object.defineProperty(Ni,"__esModule",{value:!0}),Ni.default=void 0;var Li={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-orientation":"horizontal"},relatedConcepts:[{concept:{name:"menubar"},module:"ARIA"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};Ni.default=Li;var Ui={};Object.defineProperty(Ui,"__esModule",{value:!0}),Ui.default=void 0;var Di={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ui.default=Di;var Hi={};Object.defineProperty(Hi,"__esModule",{value:!0}),Hi.default=void 0;var Wi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null,"aria-multiselectable":null,"aria-required":null,"aria-orientation":"vertical"},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["treeitem","group"],["treeitem"]],requiredProps:{},superClass:[["roletype","widget","composite","select"],["roletype","structure","section","group","select"]]};Hi.default=Wi;var zi={};Object.defineProperty(zi,"__esModule",{value:!0}),zi.default=void 0;var $i={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["row"],["row","rowgroup"]],requiredProps:{},superClass:[["roletype","widget","composite","grid"],["roletype","structure","section","table","grid"],["roletype","widget","composite","select","tree"],["roletype","structure","section","group","select","tree"]]};zi.default=$i;var Vi={};Object.defineProperty(Vi,"__esModule",{value:!0}),Vi.default=void 0;var Gi={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-expanded":null,"aria-haspopup":null},relatedConcepts:[],requireContextRole:["group","tree"],requiredContextRole:["group","tree"],requiredOwnedElements:[],requiredProps:{"aria-selected":null},superClass:[["roletype","structure","section","listitem"],["roletype","widget","input","option"]]};Vi.default=Gi,Object.defineProperty(zn,"__esModule",{value:!0}),zn.default=void 0;var Ji=wu($n),Qi=wu(Gn),Xi=wu(Qn),Ki=wu(Kn),Yi=wu(Zn),Zi=wu(to),el=wu(no),tl=wu(ao),rl=wu(lo),nl=wu(so),ol=wu(po),al=wu(bo),il=wu(yo),ll=wu(ho),ul=wu(Po),sl=wu(wo),cl=wu(Eo),dl=wu(Oo),pl=wu(Ro),fl=wu(Ao),bl=wu(To),ml=wu(Io),yl=wu(Fo),vl=wu(No),hl=wu(Uo),gl=wu(Ho),Pl=wu(zo),Cl=wu(Vo),wl=wu(Jo),ql=wu(Xo),El=wu(Yo),xl=wu(ea),Ol=wu(ra),jl=wu(oa),Rl=wu(ia),Sl=wu(ua),Al=wu(ca),_l=wu(pa),Tl=wu(ba),Ml=wu(ya),Il=wu(ha),Bl=wu(Pa),Fl=wu(wa),kl=wu(Ea),Nl=wu(Oa),Ll=wu(Ra),Ul=wu(Aa),Dl=wu(Ta),Hl=wu(Ia),Wl=wu(Fa),zl=wu(Na),$l=wu(Ua),Vl=wu(Ha),Gl=wu(za),Jl=wu(Va),Ql=wu(Ja),Xl=wu(Xa),Kl=wu(Ya),Yl=wu(ei),Zl=wu(ri),eu=wu(oi),tu=wu(ii),ru=wu(ui),nu=wu(ci),ou=wu(pi),au=wu(bi),iu=wu(yi),lu=wu(hi),uu=wu(Pi),su=wu(wi),cu=wu(Ei),du=wu(Oi),pu=wu(Ri),fu=wu(Ai),bu=wu(Ti),mu=wu(Ii),yu=wu(Fi),vu=wu(Ni),hu=wu(Ui),gu=wu(Hi),Pu=wu(zi),Cu=wu(Vi);function wu(e){return e&&e.__esModule?e:{default:e}}var qu=[["alert",Ji.default],["alertdialog",Qi.default],["application",Xi.default],["article",Ki.default],["banner",Yi.default],["blockquote",Zi.default],["button",el.default],["caption",tl.default],["cell",rl.default],["checkbox",nl.default],["code",ol.default],["columnheader",al.default],["combobox",il.default],["complementary",ll.default],["contentinfo",ul.default],["definition",sl.default],["deletion",cl.default],["dialog",dl.default],["directory",pl.default],["document",fl.default],["emphasis",bl.default],["feed",ml.default],["figure",yl.default],["form",vl.default],["generic",hl.default],["grid",gl.default],["gridcell",Pl.default],["group",Cl.default],["heading",wl.default],["img",ql.default],["insertion",El.default],["link",xl.default],["list",Ol.default],["listbox",jl.default],["listitem",Rl.default],["log",Sl.default],["main",Al.default],["marquee",_l.default],["math",Tl.default],["menu",Ml.default],["menubar",Il.default],["menuitem",Bl.default],["menuitemcheckbox",Fl.default],["menuitemradio",kl.default],["meter",Nl.default],["navigation",Ll.default],["none",Ul.default],["note",Dl.default],["option",Hl.default],["paragraph",Wl.default],["presentation",zl.default],["progressbar",$l.default],["radio",Vl.default],["radiogroup",Gl.default],["region",Jl.default],["row",Ql.default],["rowgroup",Xl.default],["rowheader",Kl.default],["scrollbar",Yl.default],["search",Zl.default],["searchbox",eu.default],["separator",tu.default],["slider",ru.default],["spinbutton",nu.default],["status",ou.default],["strong",au.default],["subscript",iu.default],["superscript",lu.default],["switch",uu.default],["tab",su.default],["table",cu.default],["tablist",du.default],["tabpanel",pu.default],["term",fu.default],["textbox",bu.default],["time",mu.default],["timer",yu.default],["toolbar",vu.default],["tooltip",hu.default],["tree",gu.default],["treegrid",Pu.default],["treeitem",Cu.default]];zn.default=qu;var Eu={},xu={};Object.defineProperty(xu,"__esModule",{value:!0}),xu.default=void 0;var Ou={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"abstract [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};xu.default=Ou;var ju={};Object.defineProperty(ju,"__esModule",{value:!0}),ju.default=void 0;var Ru={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"acknowledgments [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ju.default=Ru;var Su={};Object.defineProperty(Su,"__esModule",{value:!0}),Su.default=void 0;var Au={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"afterword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Su.default=Au;var _u={};Object.defineProperty(_u,"__esModule",{value:!0}),_u.default=void 0;var Tu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"appendix [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};_u.default=Tu;var Mu={};Object.defineProperty(Mu,"__esModule",{value:!0}),Mu.default=void 0;var Iu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","content"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"referrer [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Mu.default=Iu;var Bu={};Object.defineProperty(Bu,"__esModule",{value:!0}),Bu.default=void 0;var Fu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"EPUB biblioentry [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-bibliography"],requiredContextRole:["doc-bibliography"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};Bu.default=Fu;var ku={};Object.defineProperty(ku,"__esModule",{value:!0}),ku.default=void 0;var Nu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"bibliography [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-biblioentry"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ku.default=Nu;var Lu={};Object.defineProperty(Lu,"__esModule",{value:!0}),Lu.default=void 0;var Uu={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"biblioref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};Lu.default=Uu;var Du={};Object.defineProperty(Du,"__esModule",{value:!0}),Du.default=void 0;var Hu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"chapter [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Du.default=Hu;var Wu={};Object.defineProperty(Wu,"__esModule",{value:!0}),Wu.default=void 0;var zu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"colophon [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Wu.default=zu;var $u={};Object.defineProperty($u,"__esModule",{value:!0}),$u.default=void 0;var Vu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"conclusion [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};$u.default=Vu;var Gu={};Object.defineProperty(Gu,"__esModule",{value:!0}),Gu.default=void 0;var Ju={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"cover [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Gu.default=Ju;var Qu={};Object.defineProperty(Qu,"__esModule",{value:!0}),Qu.default=void 0;var Xu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credit [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Qu.default=Xu;var Ku={};Object.defineProperty(Ku,"__esModule",{value:!0}),Ku.default=void 0;var Yu={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"credits [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ku.default=Yu;var Zu={};Object.defineProperty(Zu,"__esModule",{value:!0}),Zu.default=void 0;var es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"dedication [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Zu.default=es;var ts={};Object.defineProperty(ts,"__esModule",{value:!0}),ts.default=void 0;var rs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:["doc-endnotes"],requiredContextRole:["doc-endnotes"],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","listitem"]]};ts.default=rs;var ns={};Object.defineProperty(ns,"__esModule",{value:!0}),ns.default=void 0;var os={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"rearnotes [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["doc-endnote"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ns.default=os;var as={};Object.defineProperty(as,"__esModule",{value:!0}),as.default=void 0;var is={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epigraph [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};as.default=is;var ls={};Object.defineProperty(ls,"__esModule",{value:!0}),ls.default=void 0;var us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"epilogue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ls.default=us;var ss={};Object.defineProperty(ss,"__esModule",{value:!0}),ss.default=void 0;var cs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"errata [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ss.default=cs;var ds={};Object.defineProperty(ds,"__esModule",{value:!0}),ds.default=void 0;var ps={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};ds.default=ps;var fs={};Object.defineProperty(fs,"__esModule",{value:!0}),fs.default=void 0;var bs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"footnote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};fs.default=bs;var ms={};Object.defineProperty(ms,"__esModule",{value:!0}),ms.default=void 0;var ys={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"foreword [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ms.default=ys;var vs={};Object.defineProperty(vs,"__esModule",{value:!0}),vs.default=void 0;var hs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossary [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[["definition"],["term"]],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};vs.default=hs;var gs={};Object.defineProperty(gs,"__esModule",{value:!0}),gs.default=void 0;var Ps={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"glossref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};gs.default=Ps;var Cs={};Object.defineProperty(Cs,"__esModule",{value:!0}),Cs.default=void 0;var ws={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"index [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Cs.default=ws;var qs={};Object.defineProperty(qs,"__esModule",{value:!0}),qs.default=void 0;var Es={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"introduction [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};qs.default=Es;var xs={};Object.defineProperty(xs,"__esModule",{value:!0}),xs.default=void 0;var Os={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"noteref [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","widget","command","link"]]};xs.default=Os;var js={};Object.defineProperty(js,"__esModule",{value:!0}),js.default=void 0;var Rs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"notice [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};js.default=Rs;var Ss={};Object.defineProperty(Ss,"__esModule",{value:!0}),Ss.default=void 0;var As={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"pagebreak [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","separator"]]};Ss.default=As;var _s={};Object.defineProperty(_s,"__esModule",{value:!0}),_s.default=void 0;var Ts={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"page-list [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};_s.default=Ts;var Ms={};Object.defineProperty(Ms,"__esModule",{value:!0}),Ms.default=void 0;var Is={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"part [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Ms.default=Is;var Bs={};Object.defineProperty(Bs,"__esModule",{value:!0}),Bs.default=void 0;var Fs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"preface [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};Bs.default=Fs;var ks={};Object.defineProperty(ks,"__esModule",{value:!0}),ks.default=void 0;var Ns={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"prologue [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark"]]};ks.default=Ns;var Ls={};Object.defineProperty(Ls,"__esModule",{value:!0}),Ls.default=void 0;var Us={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{},relatedConcepts:[{concept:{name:"pullquote [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["none"]]};Ls.default=Us;var Ds={};Object.defineProperty(Ds,"__esModule",{value:!0}),Ds.default=void 0;var Hs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"qna [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section"]]};Ds.default=Hs;var Ws={};Object.defineProperty(Ws,"__esModule",{value:!0}),Ws.default=void 0;var zs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"subtitle [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","sectionhead"]]};Ws.default=zs;var $s={};Object.defineProperty($s,"__esModule",{value:!0}),$s.default=void 0;var Vs={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"help [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","note"]]};$s.default=Vs;var Gs={};Object.defineProperty(Gs,"__esModule",{value:!0}),Gs.default=void 0;var Js={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{concept:{name:"toc [EPUB-SSV]"},module:"EPUB"}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","landmark","navigation"]]};Gs.default=Js,Object.defineProperty(Eu,"__esModule",{value:!0}),Eu.default=void 0;var Qs=Ic(xu),Xs=Ic(ju),Ks=Ic(Su),Ys=Ic(_u),Zs=Ic(Mu),ec=Ic(Bu),tc=Ic(ku),rc=Ic(Lu),nc=Ic(Du),oc=Ic(Wu),ac=Ic($u),ic=Ic(Gu),lc=Ic(Qu),uc=Ic(Ku),sc=Ic(Zu),cc=Ic(ts),dc=Ic(ns),pc=Ic(as),fc=Ic(ls),bc=Ic(ss),mc=Ic(ds),yc=Ic(fs),vc=Ic(ms),hc=Ic(vs),gc=Ic(gs),Pc=Ic(Cs),Cc=Ic(qs),wc=Ic(xs),qc=Ic(js),Ec=Ic(Ss),xc=Ic(_s),Oc=Ic(Ms),jc=Ic(Bs),Rc=Ic(ks),Sc=Ic(Ls),Ac=Ic(Ds),_c=Ic(Ws),Tc=Ic($s),Mc=Ic(Gs);function Ic(e){return e&&e.__esModule?e:{default:e}}var Bc=[["doc-abstract",Qs.default],["doc-acknowledgments",Xs.default],["doc-afterword",Ks.default],["doc-appendix",Ys.default],["doc-backlink",Zs.default],["doc-biblioentry",ec.default],["doc-bibliography",tc.default],["doc-biblioref",rc.default],["doc-chapter",nc.default],["doc-colophon",oc.default],["doc-conclusion",ac.default],["doc-cover",ic.default],["doc-credit",lc.default],["doc-credits",uc.default],["doc-dedication",sc.default],["doc-endnote",cc.default],["doc-endnotes",dc.default],["doc-epigraph",pc.default],["doc-epilogue",fc.default],["doc-errata",bc.default],["doc-example",mc.default],["doc-footnote",yc.default],["doc-foreword",vc.default],["doc-glossary",hc.default],["doc-glossref",gc.default],["doc-index",Pc.default],["doc-introduction",Cc.default],["doc-noteref",wc.default],["doc-notice",qc.default],["doc-pagebreak",Ec.default],["doc-pagelist",xc.default],["doc-part",Oc.default],["doc-preface",jc.default],["doc-prologue",Rc.default],["doc-pullquote",Sc.default],["doc-qna",Ac.default],["doc-subtitle",_c.default],["doc-tip",Tc.default],["doc-toc",Mc.default]];Eu.default=Bc;var Fc={},kc={};Object.defineProperty(kc,"__esModule",{value:!0}),kc.default=void 0;var Nc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!1,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-object"}},{module:"ARIA",concept:{name:"img"}},{module:"ARIA",concept:{name:"article"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","document"]]};kc.default=Nc;var Lc={};Object.defineProperty(Lc,"__esModule",{value:!0}),Lc.default=void 0;var Uc={abstract:!1,accessibleNameRequired:!1,baseConcepts:[],childrenPresentational:!1,nameFrom:["author","contents"],prohibitedProps:[],props:{"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[{module:"GRAPHICS",concept:{name:"graphics-document"}},{module:"ARIA",concept:{name:"group"}},{module:"ARIA",concept:{name:"img"}},{module:"GRAPHICS",concept:{name:"graphics-symbol"}}],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","group"]]};Lc.default=Uc;var Dc={};Object.defineProperty(Dc,"__esModule",{value:!0}),Dc.default=void 0;var Hc={abstract:!1,accessibleNameRequired:!0,baseConcepts:[],childrenPresentational:!0,nameFrom:["author"],prohibitedProps:[],props:{"aria-disabled":null,"aria-errormessage":null,"aria-expanded":null,"aria-haspopup":null,"aria-invalid":null},relatedConcepts:[],requireContextRole:[],requiredContextRole:[],requiredOwnedElements:[],requiredProps:{},superClass:[["roletype","structure","section","img"]]};Dc.default=Hc,Object.defineProperty(Fc,"__esModule",{value:!0}),Fc.default=void 0;var Wc=Vc(kc),zc=Vc(Lc),$c=Vc(Dc);function Vc(e){return e&&e.__esModule?e:{default:e}}var Gc=[["graphics-document",Wc.default],["graphics-object",zc.default],["graphics-symbol",$c.default]];Fc.default=Gc,Object.defineProperty(nn,"__esModule",{value:!0}),nn.default=void 0;var Jc=Zc(on),Qc=Zc(zn),Xc=Zc(Eu),Kc=Zc(Fc),Yc=Zc(kr);function Zc(e){return e&&e.__esModule?e:{default:e}}function ed(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function td(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=nd(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}function rd(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||nd(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nd(e,t){if(e){if("string"==typeof e)return od(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?od(e,t):void 0}}function od(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ad=[].concat(Jc.default,Qc.default,Xc.default,Kc.default);ad.forEach((function(e){var t,r=rd(e,2)[1],n=td(r.superClass);try{for(n.s();!(t=n.n()).done;){var o,a=td(t.value);try{var i=function(){var e=o.value,t=ad.find((function(t){return rd(t,1)[0]===e}));if(t)for(var n=t[1],a=0,i=Object.keys(n.props);a<i.length;a++){var l=i[a];Object.prototype.hasOwnProperty.call(r.props,l)||Object.assign(r.props,ed({},l,n.props[l]))}};for(a.s();!(o=a.n()).done;)i()}catch(e){a.e(e)}finally{a.f()}}}catch(e){n.e(e)}finally{n.f()}}));var id={entries:function(){return ad},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=td(ad);try{for(n.s();!(t=n.n()).done;){var o=rd(t.value,2),a=o[0],i=o[1];e.call(r,i,a,ad)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=ad.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!id.get(e)},keys:function(){return ad.map((function(e){return rd(e,1)[0]}))},values:function(){return ad.map((function(e){return rd(e,2)[1]}))}},ld=(0,Yc.default)(id,id.entries());nn.default=ld;var ud,sd,cd={},dd=Object.prototype.toString,pd=function(e){var t=dd.call(e),r="[object Arguments]"===t;return r||(r="[object Array]"!==t&&null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Function]"===dd.call(e.callee)),r};var fd=Array.prototype.slice,bd=pd,md=Object.keys,yd=md?function(e){return md(e)}:function(){if(sd)return ud;var e;if(sd=1,!Object.keys){var t=Object.prototype.hasOwnProperty,r=Object.prototype.toString,n=Object.prototype.propertyIsEnumerable,o=!n.call({toString:null},"toString"),a=n.call((function(){}),"prototype"),i=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],l=function(e){var t=e.constructor;return t&&t.prototype===e},u={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},s=function(){if("undefined"==typeof window)return!1;for(var e in window)try{if(!u["$"+e]&&t.call(window,e)&&null!==window[e]&&"object"==typeof window[e])try{l(window[e])}catch(e){return!0}}catch(e){return!0}return!1}();e=function(e){var n=null!==e&&"object"==typeof e,u="[object Function]"===r.call(e),c=pd(e),d=n&&"[object String]"===r.call(e),p=[];if(!n&&!u&&!c)throw new TypeError("Object.keys called on a non-object");if(d&&e.length>0&&!t.call(e,0))for(var f=0;f<e.length;++f)p.push(String(f));if(c&&e.length>0)for(var b=0;b<e.length;++b)p.push(String(b));else for(var m in e)a&&u&&"prototype"===m||!t.call(e,m)||p.push(String(m));if(o)for(var y=function(e){if("undefined"==typeof window||!s)return l(e);try{return l(e)}catch(e){return!1}}(e),v=0;v<i.length;++v)y&&"constructor"===i[v]||!t.call(e,i[v])||p.push(i[v]);return p}}return ud=e}(),vd=Object.keys;yd.shim=function(){if(Object.keys){var e=function(){var e=Object.keys(arguments);return e&&e.length===arguments.length}(1,2);e||(Object.keys=function(e){return bd(e)?vd(fd.call(e)):vd(e)})}else Object.keys=yd;return Object.keys||yd};var hd,gd,Pd=yd;function Cd(){return gd||(gd=1,hd=function(){if("function"!=typeof Symbol||"function"!=typeof Object.getOwnPropertySymbols)return!1;if("symbol"==typeof Symbol.iterator)return!0;var e={},t=Symbol("test"),r=Object(t);if("string"==typeof t)return!1;if("[object Symbol]"!==Object.prototype.toString.call(t))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;for(t in e[t]=42,e)return!1;if("function"==typeof Object.keys&&0!==Object.keys(e).length)return!1;if("function"==typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(e).length)return!1;var n=Object.getOwnPropertySymbols(e);if(1!==n.length||n[0]!==t)return!1;if(!Object.prototype.propertyIsEnumerable.call(e,t))return!1;if("function"==typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(e,t);if(42!==o.value||!0!==o.enumerable)return!1}return!0}),hd}var wd,qd="undefined"!=typeof Symbol&&Symbol,Ed=Cd(),xd=function(){return"function"==typeof qd&&("function"==typeof Symbol&&("symbol"==typeof qd("foo")&&("symbol"==typeof Symbol("bar")&&Ed())))},Od={foo:{}},jd=Object,Rd=Object.prototype.toString,Sd=Math.max,Ad=function(e,t){for(var r=[],n=0;n<e.length;n+=1)r[n]=e[n];for(var o=0;o<t.length;o+=1)r[o+e.length]=t[o];return r},_d=function(e){var t=this;if("function"!=typeof t||"[object Function]"!==Rd.apply(t))throw new TypeError("Function.prototype.bind called on incompatible "+t);for(var r,n=function(e,t){for(var r=[],n=t||0,o=0;n<e.length;n+=1,o+=1)r[o]=e[n];return r}(arguments,1),o=Sd(0,t.length-n.length),a=[],i=0;i<o;i++)a[i]="$"+i;if(r=Function("binder","return function ("+function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r}(a,",")+"){ return binder.apply(this,arguments); }")((function(){if(this instanceof r){var o=t.apply(this,Ad(n,arguments));return Object(o)===o?o:this}return t.apply(e,Ad(n,arguments))})),t.prototype){var l=function(){};l.prototype=t.prototype,r.prototype=new l,l.prototype=null}return r},Td=Function.prototype.bind||_d,Md=Function.prototype.call,Id=Object.prototype.hasOwnProperty,Bd=Td.call(Md,Id),Fd=SyntaxError,kd=Function,Nd=TypeError,Ld=function(e){try{return kd('"use strict"; return ('+e+").constructor;")()}catch(e){}},Ud=Object.getOwnPropertyDescriptor;if(Ud)try{Ud({},"")}catch(e){Ud=null}var Dd=function(){throw new Nd},Hd=Ud?function(){try{return Dd}catch(e){try{return Ud(arguments,"callee").get}catch(e){return Dd}}}():Dd,Wd=xd(),zd={__proto__:Od}.foo===Od.foo&&!({__proto__:null}instanceof jd),$d=Object.getPrototypeOf||(zd?function(e){return e.__proto__}:null),Vd={},Gd="undefined"!=typeof Uint8Array&&$d?$d(Uint8Array):wd,Jd={"%AggregateError%":"undefined"==typeof AggregateError?wd:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"==typeof ArrayBuffer?wd:ArrayBuffer,"%ArrayIteratorPrototype%":Wd&&$d?$d([][Symbol.iterator]()):wd,"%AsyncFromSyncIteratorPrototype%":wd,"%AsyncFunction%":Vd,"%AsyncGenerator%":Vd,"%AsyncGeneratorFunction%":Vd,"%AsyncIteratorPrototype%":Vd,"%Atomics%":"undefined"==typeof Atomics?wd:Atomics,"%BigInt%":"undefined"==typeof BigInt?wd:BigInt,"%BigInt64Array%":"undefined"==typeof BigInt64Array?wd:BigInt64Array,"%BigUint64Array%":"undefined"==typeof BigUint64Array?wd:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"==typeof DataView?wd:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"==typeof Float32Array?wd:Float32Array,"%Float64Array%":"undefined"==typeof Float64Array?wd:Float64Array,"%FinalizationRegistry%":"undefined"==typeof FinalizationRegistry?wd:FinalizationRegistry,"%Function%":kd,"%GeneratorFunction%":Vd,"%Int8Array%":"undefined"==typeof Int8Array?wd:Int8Array,"%Int16Array%":"undefined"==typeof Int16Array?wd:Int16Array,"%Int32Array%":"undefined"==typeof Int32Array?wd:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":Wd&&$d?$d($d([][Symbol.iterator]())):wd,"%JSON%":"object"==typeof JSON?JSON:wd,"%Map%":"undefined"==typeof Map?wd:Map,"%MapIteratorPrototype%":"undefined"!=typeof Map&&Wd&&$d?$d((new Map)[Symbol.iterator]()):wd,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"==typeof Promise?wd:Promise,"%Proxy%":"undefined"==typeof Proxy?wd:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"==typeof Reflect?wd:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"==typeof Set?wd:Set,"%SetIteratorPrototype%":"undefined"!=typeof Set&&Wd&&$d?$d((new Set)[Symbol.iterator]()):wd,"%SharedArrayBuffer%":"undefined"==typeof SharedArrayBuffer?wd:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":Wd&&$d?$d(""[Symbol.iterator]()):wd,"%Symbol%":Wd?Symbol:wd,"%SyntaxError%":Fd,"%ThrowTypeError%":Hd,"%TypedArray%":Gd,"%TypeError%":Nd,"%Uint8Array%":"undefined"==typeof Uint8Array?wd:Uint8Array,"%Uint8ClampedArray%":"undefined"==typeof Uint8ClampedArray?wd:Uint8ClampedArray,"%Uint16Array%":"undefined"==typeof Uint16Array?wd:Uint16Array,"%Uint32Array%":"undefined"==typeof Uint32Array?wd:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"==typeof WeakMap?wd:WeakMap,"%WeakRef%":"undefined"==typeof WeakRef?wd:WeakRef,"%WeakSet%":"undefined"==typeof WeakSet?wd:WeakSet};if($d)try{null.error}catch(e){var Qd=$d($d(e));Jd["%Error.prototype%"]=Qd}var Xd=function e(t){var r;if("%AsyncFunction%"===t)r=Ld("async function () {}");else if("%GeneratorFunction%"===t)r=Ld("function* () {}");else if("%AsyncGeneratorFunction%"===t)r=Ld("async function* () {}");else if("%AsyncGenerator%"===t){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===t){var o=e("%AsyncGenerator%");o&&$d&&(r=$d(o.prototype))}return Jd[t]=r,r},Kd={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},Yd=Td,Zd=Bd,ep=Yd.call(Function.call,Array.prototype.concat),tp=Yd.call(Function.apply,Array.prototype.splice),rp=Yd.call(Function.call,String.prototype.replace),np=Yd.call(Function.call,String.prototype.slice),op=Yd.call(Function.call,RegExp.prototype.exec),ap=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,ip=/\\(\\)?/g,lp=function(e,t){var r,n=e;if(Zd(Kd,n)&&(n="%"+(r=Kd[n])[0]+"%"),Zd(Jd,n)){var o=Jd[n];if(o===Vd&&(o=Xd(n)),void 0===o&&!t)throw new Nd("intrinsic "+e+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:o}}throw new Fd("intrinsic "+e+" does not exist!")},up=function(e,t){if("string"!=typeof e||0===e.length)throw new Nd("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!=typeof t)throw new Nd('"allowMissing" argument must be a boolean');if(null===op(/^%?[^%]*%?$/,e))throw new Fd("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=function(e){var t=np(e,0,1),r=np(e,-1);if("%"===t&&"%"!==r)throw new Fd("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==t)throw new Fd("invalid intrinsic syntax, expected opening `%`");var n=[];return rp(e,ap,(function(e,t,r,o){n[n.length]=r?rp(o,ip,"$1"):t||e})),n}(e),n=r.length>0?r[0]:"",o=lp("%"+n+"%",t),a=o.name,i=o.value,l=!1,u=o.alias;u&&(n=u[0],tp(r,ep([0,1],u)));for(var s=1,c=!0;s<r.length;s+=1){var d=r[s],p=np(d,0,1),f=np(d,-1);if(('"'===p||"'"===p||"`"===p||'"'===f||"'"===f||"`"===f)&&p!==f)throw new Fd("property names with quotes must have matching quotes");if("constructor"!==d&&c||(l=!0),Zd(Jd,a="%"+(n+="."+d)+"%"))i=Jd[a];else if(null!=i){if(!(d in i)){if(!t)throw new Nd("base intrinsic for "+e+" exists, but the property is not available.");return}if(Ud&&s+1>=r.length){var b=Ud(i,d);i=(c=!!b)&&"get"in b&&!("originalValue"in b.get)?b.get:i[d]}else c=Zd(i,d),i=i[d];c&&!l&&(Jd[a]=i)}}return i},sp=up("%Object.defineProperty%",!0),cp=function(){if(sp)try{return sp({},"a",{value:1}),!0}catch(e){return!1}return!1};cp.hasArrayLengthDefineBug=function(){if(!cp())return null;try{return 1!==sp([],"length",{value:1}).length}catch(e){return!0}};var dp=cp,pp=up("%Object.getOwnPropertyDescriptor%",!0);if(pp)try{pp([],"length")}catch(e){pp=null}var fp=pp,bp=dp(),mp=up,yp=bp&&mp("%Object.defineProperty%",!0);if(yp)try{yp({},"a",{value:1})}catch(e){yp=!1}var vp=mp("%SyntaxError%"),hp=mp("%TypeError%"),gp=fp,Pp=function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new hp("`obj` must be an object or a function`");if("string"!=typeof t&&"symbol"!=typeof t)throw new hp("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!=typeof arguments[3]&&null!==arguments[3])throw new hp("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!=typeof arguments[4]&&null!==arguments[4])throw new hp("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!=typeof arguments[5]&&null!==arguments[5])throw new hp("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!=typeof arguments[6])throw new hp("`loose`, if provided, must be a boolean");var n=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,a=arguments.length>5?arguments[5]:null,i=arguments.length>6&&arguments[6],l=!!gp&&gp(e,t);if(yp)yp(e,t,{configurable:null===a&&l?l.configurable:!a,enumerable:null===n&&l?l.enumerable:!n,value:r,writable:null===o&&l?l.writable:!o});else{if(!i&&(n||o||a))throw new vp("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");e[t]=r}},Cp=Pd,wp="function"==typeof Symbol&&"symbol"==typeof Symbol("foo"),qp=Object.prototype.toString,Ep=Array.prototype.concat,xp=Pp,Op=dp(),jp=function(e,t,r,n){if(t in e)if(!0===n){if(e[t]===r)return}else if("function"!=typeof(o=n)||"[object Function]"!==qp.call(o)||!n())return;var o;Op?xp(e,t,r,!0):xp(e,t,r)},Rp=function(e,t){var r=arguments.length>2?arguments[2]:{},n=Cp(t);wp&&(n=Ep.call(n,Object.getOwnPropertySymbols(t)));for(var o=0;o<n.length;o+=1)jp(e,n[o],t[n[o]],r[n[o]])};Rp.supportsDescriptors=!!Op;var Sp=Rp,Ap={exports:{}},_p=up,Tp=Pp,Mp=dp(),Ip=fp,Bp=_p("%TypeError%"),Fp=_p("%Math.floor%");!function(e){var t=Td,r=up,n=r("%TypeError%"),o=r("%Function.prototype.apply%"),a=r("%Function.prototype.call%"),i=r("%Reflect.apply%",!0)||t.call(a,o),l=r("%Object.defineProperty%",!0),u=r("%Math.max%");if(l)try{l({},"a",{value:1})}catch(e){l=null}e.exports=function(e){if("function"!=typeof e)throw new n("a function is required");return function(e,t){if("function"!=typeof e)throw new Bp("`fn` is not a function");if("number"!=typeof t||t<0||t>4294967295||Fp(t)!==t)throw new Bp("`length` must be a positive 32-bit integer");var r=arguments.length>2&&!!arguments[2],n=!0,o=!0;if("length"in e&&Ip){var a=Ip(e,"length");a&&!a.configurable&&(n=!1),a&&!a.writable&&(o=!1)}return(n||o||!r)&&(Mp?Tp(e,"length",t,!0,!0):Tp(e,"length",t)),e}(i(t,a,arguments),1+u(0,e.length-(arguments.length-1)),!0)};var s=function(){return i(t,o,arguments)};l?l(e.exports,"apply",{value:s}):e.exports.apply=s}(Ap);var kp=up,Np=Ap.exports,Lp=Np(kp("String.prototype.indexOf")),Up=function(e,t){var r=kp(e,!!t);return"function"==typeof r&&Lp(e,".prototype.")>-1?Np(r):r},Dp=Pd,Hp=Cd()(),Wp=Up,zp=Object,$p=Wp("Array.prototype.push"),Vp=Wp("Object.prototype.propertyIsEnumerable"),Gp=Hp?Object.getOwnPropertySymbols:null,Jp=function(e){if(null==e)throw new TypeError("target must be an object");var t=zp(e);if(1===arguments.length)return t;for(var r=1;r<arguments.length;++r){var n=zp(arguments[r]),o=Dp(n),a=Hp&&(Object.getOwnPropertySymbols||Gp);if(a)for(var i=a(n),l=0;l<i.length;++l){var u=i[l];Vp(n,u)&&$p(o,u)}for(var s=0;s<o.length;++s){var c=o[s];if(Vp(n,c)){var d=n[c];t[c]=d}}}return t},Qp=Jp,Xp=function(){return Object.assign?function(){if(!Object.assign)return!1;for(var e="abcdefghijklmnopqrst",t=e.split(""),r={},n=0;n<t.length;++n)r[t[n]]=t[n];var o=Object.assign({},r),a="";for(var i in o)a+=i;return e!==a}()||function(){if(!Object.assign||!Object.preventExtensions)return!1;var e=Object.preventExtensions({1:2});try{Object.assign(e,"xy")}catch(t){return"y"===e[1]}return!1}()?Qp:Object.assign:Qp},Kp=Sp,Yp=Xp,Zp=Sp,ef=Jp,tf=Xp,rf=function(){var e=Yp();return Kp(Object,{assign:e},{assign:function(){return Object.assign!==e}}),e},nf=Ap.exports.apply(tf()),of=function(){return nf(Object,arguments)};Zp(of,{getPolyfill:tf,implementation:ef,shim:rf});var af=of,lf=function(){return"string"==typeof function(){}.name},uf=Object.getOwnPropertyDescriptor;if(uf)try{uf([],"length")}catch(e){uf=null}lf.functionsHaveConfigurableNames=function(){if(!lf()||!uf)return!1;var e=uf((function(){}),"name");return!!e&&!!e.configurable};var sf=Function.prototype.bind;lf.boundFunctionsHaveNames=function(){return lf()&&"function"==typeof sf&&""!==function(){}.bind().name};var cf=lf,df=Pp,pf=dp(),ff=cf.functionsHaveConfigurableNames(),bf=TypeError,mf=function(e,t){if("function"!=typeof e)throw new bf("`fn` is not a function");return arguments.length>2&&!!arguments[2]&&!ff||(pf?df(e,"name",t,!0,!0):df(e,"name",t)),e},yf=Object,vf=TypeError,hf=mf((function(){if(null!=this&&this!==yf(this))throw new vf("RegExp.prototype.flags getter called on non-object");var e="";return this.hasIndices&&(e+="d"),this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),this.dotAll&&(e+="s"),this.unicode&&(e+="u"),this.unicodeSets&&(e+="v"),this.sticky&&(e+="y"),e}),"get flags",!0),gf=hf,Pf=Sp.supportsDescriptors,Cf=Object.getOwnPropertyDescriptor,wf=function(){if(Pf&&"gim"===/a/gim.flags){var e=Cf(RegExp.prototype,"flags");if(e&&"function"==typeof e.get&&"boolean"==typeof RegExp.prototype.dotAll&&"boolean"==typeof RegExp.prototype.hasIndices){var t="",r={};if(Object.defineProperty(r,"hasIndices",{get:function(){t+="d"}}),Object.defineProperty(r,"sticky",{get:function(){t+="y"}}),"dy"===t)return e.get}}return gf},qf=Sp.supportsDescriptors,Ef=wf,xf=Object.getOwnPropertyDescriptor,Of=Object.defineProperty,jf=TypeError,Rf=Object.getPrototypeOf,Sf=/a/,Af=Sp,_f=hf,Tf=wf,Mf=function(){if(!qf||!Rf)throw new jf("RegExp.prototype.flags requires a true ES5 environment that supports property descriptors");var e=Ef(),t=Rf(Sf),r=xf(t,"flags");return r&&r.get===e||Of(t,"flags",{configurable:!0,enumerable:!1,get:e}),e},If=(0,Ap.exports)(Tf());Af(If,{getPolyfill:Tf,implementation:_f,shim:Mf});var Bf=If,Ff={exports:{}},kf=Cd(),Nf=function(){return kf()&&!!Symbol.toStringTag},Lf=Nf(),Uf=Up("Object.prototype.toString"),Df=function(e){return!(Lf&&e&&"object"==typeof e&&Symbol.toStringTag in e)&&"[object Arguments]"===Uf(e)},Hf=function(e){return!!Df(e)||null!==e&&"object"==typeof e&&"number"==typeof e.length&&e.length>=0&&"[object Array]"!==Uf(e)&&"[object Function]"===Uf(e.callee)},Wf=function(){return Df(arguments)}();Df.isLegacyArguments=Hf;var zf=Wf?Df:Hf,$f=n(Object.freeze({__proto__:null,default:{}})),Vf="function"==typeof Map&&Map.prototype,Gf=Object.getOwnPropertyDescriptor&&Vf?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Jf=Vf&&Gf&&"function"==typeof Gf.get?Gf.get:null,Qf=Vf&&Map.prototype.forEach,Xf="function"==typeof Set&&Set.prototype,Kf=Object.getOwnPropertyDescriptor&&Xf?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Yf=Xf&&Kf&&"function"==typeof Kf.get?Kf.get:null,Zf=Xf&&Set.prototype.forEach,eb="function"==typeof WeakMap&&WeakMap.prototype?WeakMap.prototype.has:null,tb="function"==typeof WeakSet&&WeakSet.prototype?WeakSet.prototype.has:null,rb="function"==typeof WeakRef&&WeakRef.prototype?WeakRef.prototype.deref:null,nb=Boolean.prototype.valueOf,ob=Object.prototype.toString,ab=Function.prototype.toString,ib=String.prototype.match,lb=String.prototype.slice,ub=String.prototype.replace,sb=String.prototype.toUpperCase,cb=String.prototype.toLowerCase,db=RegExp.prototype.test,pb=Array.prototype.concat,fb=Array.prototype.join,bb=Array.prototype.slice,mb=Math.floor,yb="function"==typeof BigInt?BigInt.prototype.valueOf:null,vb=Object.getOwnPropertySymbols,hb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol.prototype.toString:null,gb="function"==typeof Symbol&&"object"==typeof Symbol.iterator,Pb="function"==typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===gb||"symbol")?Symbol.toStringTag:null,Cb=Object.prototype.propertyIsEnumerable,wb=("function"==typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function qb(e,t){if(e===1/0||e===-1/0||e!=e||e&&e>-1e3&&e<1e3||db.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"==typeof e){var n=e<0?-mb(-e):mb(e);if(n!==e){var o=String(n),a=lb.call(t,o.length+1);return ub.call(o,r,"$&_")+"."+ub.call(ub.call(a,/([0-9]{3})/g,"$&_"),/_$/,"")}}return ub.call(t,r,"$&_")}var Eb=$f,xb=Eb.custom,Ob=_b(xb)?xb:null;function jb(e,t,r){var n="double"===(r.quoteStyle||t)?'"':"'";return n+e+n}function Rb(e){return ub.call(String(e),/"/g,"&quot;")}function Sb(e){return!("[object Array]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}function Ab(e){return!("[object RegExp]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}function _b(e){if(gb)return e&&"object"==typeof e&&e instanceof Symbol;if("symbol"==typeof e)return!0;if(!e||"object"!=typeof e||!hb)return!1;try{return hb.call(e),!0}catch(e){}return!1}var Tb=Object.prototype.hasOwnProperty||function(e){return e in this};function Mb(e,t){return Tb.call(e,t)}function Ib(e){return ob.call(e)}function Bb(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function Fb(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return Fb(lb.call(e,0,t.maxStringLength),t)+n}return jb(ub.call(ub.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,kb),"single",t)}function kb(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+sb.call(t.toString(16))}function Nb(e){return"Object("+e+")"}function Lb(e){return e+" { ? }"}function Ub(e,t,r,n){return e+" ("+t+") {"+(n?Db(r,n):fb.call(r,", "))+"}"}function Db(e,t){if(0===e.length)return"";var r="\n"+t.prev+t.base;return r+fb.call(e,","+r)+"\n"+t.prev}function Hb(e,t){var r=Sb(e),n=[];if(r){n.length=e.length;for(var o=0;o<e.length;o++)n[o]=Mb(e,o)?t(e[o],e):""}var a,i="function"==typeof vb?vb(e):[];if(gb){a={};for(var l=0;l<i.length;l++)a["$"+i[l]]=i[l]}for(var u in e)Mb(e,u)&&(r&&String(Number(u))===u&&u<e.length||gb&&a["$"+u]instanceof Symbol||(db.call(/[^\w$]/,u)?n.push(t(u,e)+": "+t(e[u],e)):n.push(u+": "+t(e[u],e))));if("function"==typeof vb)for(var s=0;s<i.length;s++)Cb.call(e,i[s])&&n.push("["+t(i[s])+"]: "+t(e[i[s]],e));return n}var Wb=up,zb=Up,$b=function e(t,n,o,a){var i=n||{};if(Mb(i,"quoteStyle")&&"single"!==i.quoteStyle&&"double"!==i.quoteStyle)throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Mb(i,"maxStringLength")&&("number"==typeof i.maxStringLength?i.maxStringLength<0&&i.maxStringLength!==1/0:null!==i.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var l=!Mb(i,"customInspect")||i.customInspect;if("boolean"!=typeof l&&"symbol"!==l)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Mb(i,"indent")&&null!==i.indent&&"\t"!==i.indent&&!(parseInt(i.indent,10)===i.indent&&i.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Mb(i,"numericSeparator")&&"boolean"!=typeof i.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var u=i.numericSeparator;if(void 0===t)return"undefined";if(null===t)return"null";if("boolean"==typeof t)return t?"true":"false";if("string"==typeof t)return Fb(t,i);if("number"==typeof t){if(0===t)return 1/0/t>0?"0":"-0";var s=String(t);return u?qb(t,s):s}if("bigint"==typeof t){var c=String(t)+"n";return u?qb(t,c):c}var d=void 0===i.depth?5:i.depth;if(void 0===o&&(o=0),o>=d&&d>0&&"object"==typeof t)return Sb(t)?"[Array]":"[Object]";var p=function(e,t){var r;if("\t"===e.indent)r="\t";else{if(!("number"==typeof e.indent&&e.indent>0))return null;r=fb.call(Array(e.indent+1)," ")}return{base:r,prev:fb.call(Array(t+1),r)}}(i,o);if(void 0===a)a=[];else if(Bb(a,t)>=0)return"[Circular]";function f(t,r,n){if(r&&(a=bb.call(a)).push(r),n){var l={depth:i.depth};return Mb(i,"quoteStyle")&&(l.quoteStyle=i.quoteStyle),e(t,l,o+1,a)}return e(t,i,o+1,a)}if("function"==typeof t&&!Ab(t)){var b=function(e){if(e.name)return e.name;var t=ib.call(ab.call(e),/^function\s*([\w$]+)/);if(t)return t[1];return null}(t),m=Hb(t,f);return"[Function"+(b?": "+b:" (anonymous)")+"]"+(m.length>0?" { "+fb.call(m,", ")+" }":"")}if(_b(t)){var y=gb?ub.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):hb.call(t);return"object"!=typeof t||gb?y:Nb(y)}if(function(e){if(!e||"object"!=typeof e)return!1;if("undefined"!=typeof HTMLElement&&e instanceof HTMLElement)return!0;return"string"==typeof e.nodeName&&"function"==typeof e.getAttribute}(t)){for(var v="<"+cb.call(String(t.nodeName)),h=t.attributes||[],g=0;g<h.length;g++)v+=" "+h[g].name+"="+jb(Rb(h[g].value),"double",i);return v+=">",t.childNodes&&t.childNodes.length&&(v+="..."),v+="</"+cb.call(String(t.nodeName))+">"}if(Sb(t)){if(0===t.length)return"[]";var P=Hb(t,f);return p&&!function(e){for(var t=0;t<e.length;t++)if(Bb(e[t],"\n")>=0)return!1;return!0}(P)?"["+Db(P,p)+"]":"[ "+fb.call(P,", ")+" ]"}if(function(e){return!("[object Error]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}(t)){var C=Hb(t,f);return"cause"in Error.prototype||!("cause"in t)||Cb.call(t,"cause")?0===C.length?"["+String(t)+"]":"{ ["+String(t)+"] "+fb.call(C,", ")+" }":"{ ["+String(t)+"] "+fb.call(pb.call("[cause]: "+f(t.cause),C),", ")+" }"}if("object"==typeof t&&l){if(Ob&&"function"==typeof t[Ob]&&Eb)return Eb(t,{depth:d-o});if("symbol"!==l&&"function"==typeof t.inspect)return t.inspect()}if(function(e){if(!Jf||!e||"object"!=typeof e)return!1;try{Jf.call(e);try{Yf.call(e)}catch(e){return!0}return e instanceof Map}catch(e){}return!1}(t)){var w=[];return Qf&&Qf.call(t,(function(e,r){w.push(f(r,t,!0)+" => "+f(e,t))})),Ub("Map",Jf.call(t),w,p)}if(function(e){if(!Yf||!e||"object"!=typeof e)return!1;try{Yf.call(e);try{Jf.call(e)}catch(e){return!0}return e instanceof Set}catch(e){}return!1}(t)){var q=[];return Zf&&Zf.call(t,(function(e){q.push(f(e,t))})),Ub("Set",Yf.call(t),q,p)}if(function(e){if(!eb||!e||"object"!=typeof e)return!1;try{eb.call(e,eb);try{tb.call(e,tb)}catch(e){return!0}return e instanceof WeakMap}catch(e){}return!1}(t))return Lb("WeakMap");if(function(e){if(!tb||!e||"object"!=typeof e)return!1;try{tb.call(e,tb);try{eb.call(e,eb)}catch(e){return!0}return e instanceof WeakSet}catch(e){}return!1}(t))return Lb("WeakSet");if(function(e){if(!rb||!e||"object"!=typeof e)return!1;try{return rb.call(e),!0}catch(e){}return!1}(t))return Lb("WeakRef");if(function(e){return!("[object Number]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}(t))return Nb(f(Number(t)));if(function(e){if(!e||"object"!=typeof e||!yb)return!1;try{return yb.call(e),!0}catch(e){}return!1}(t))return Nb(f(yb.call(t)));if(function(e){return!("[object Boolean]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}(t))return Nb(nb.call(t));if(function(e){return!("[object String]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}(t))return Nb(f(String(t)));if("undefined"!=typeof window&&t===window)return"{ [object Window] }";if(t===r)return"{ [object globalThis] }";if(!function(e){return!("[object Date]"!==Ib(e)||Pb&&"object"==typeof e&&Pb in e)}(t)&&!Ab(t)){var E=Hb(t,f),x=wb?wb(t)===Object.prototype:t instanceof Object||t.constructor===Object,O=t instanceof Object?"":"null prototype",j=!x&&Pb&&Object(t)===t&&Pb in t?lb.call(Ib(t),8,-1):O?"Object":"",R=(x||"function"!=typeof t.constructor?"":t.constructor.name?t.constructor.name+" ":"")+(j||O?"["+fb.call(pb.call([],j||[],O||[]),": ")+"] ":"");return 0===E.length?R+"{}":p?R+"{"+Db(E,p)+"}":R+"{ "+fb.call(E,", ")+" }"}return String(t)},Vb=Wb("%TypeError%"),Gb=Wb("%WeakMap%",!0),Jb=Wb("%Map%",!0),Qb=zb("WeakMap.prototype.get",!0),Xb=zb("WeakMap.prototype.set",!0),Kb=zb("WeakMap.prototype.has",!0),Yb=zb("Map.prototype.get",!0),Zb=zb("Map.prototype.set",!0),em=zb("Map.prototype.has",!0),tm=function(e,t){for(var r,n=e;null!==(r=n.next);n=r)if(r.key===t)return n.next=r.next,r.next=e.next,e.next=r,r},rm=function(){var e,t,r,n={assert:function(e){if(!n.has(e))throw new Vb("Side channel does not contain "+$b(e))},get:function(n){if(Gb&&n&&("object"==typeof n||"function"==typeof n)){if(e)return Qb(e,n)}else if(Jb){if(t)return Yb(t,n)}else if(r)return function(e,t){var r=tm(e,t);return r&&r.value}(r,n)},has:function(n){if(Gb&&n&&("object"==typeof n||"function"==typeof n)){if(e)return Kb(e,n)}else if(Jb){if(t)return em(t,n)}else if(r)return function(e,t){return!!tm(e,t)}(r,n);return!1},set:function(n,o){Gb&&n&&("object"==typeof n||"function"==typeof n)?(e||(e=new Gb),Xb(e,n,o)):Jb?(t||(t=new Jb),Zb(t,n,o)):(r||(r={key:{},next:null}),function(e,t,r){var n=tm(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}}(r,n,o))}};return n},nm=up,om=Bd,am=rm(),im=nm("%TypeError%"),lm={assert:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new im("`O` is not an object");if("string"!=typeof t)throw new im("`slot` must be a string");if(am.assert(e),!lm.has(e,t))throw new im("`"+t+"` is not present on `O`")},get:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new im("`O` is not an object");if("string"!=typeof t)throw new im("`slot` must be a string");var r=am.get(e);return r&&r["$"+t]},has:function(e,t){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new im("`O` is not an object");if("string"!=typeof t)throw new im("`slot` must be a string");var r=am.get(e);return!!r&&om(r,"$"+t)},set:function(e,t,r){if(!e||"object"!=typeof e&&"function"!=typeof e)throw new im("`O` is not an object");if("string"!=typeof t)throw new im("`slot` must be a string");var n=am.get(e);n||(n={},am.set(e,n)),n["$"+t]=r}};Object.freeze&&Object.freeze(lm);var um,sm=lm,cm=SyntaxError,dm="object"==typeof StopIteration?StopIteration:null,pm={}.toString,fm=Array.isArray||function(e){return"[object Array]"==pm.call(e)},bm=String.prototype.valueOf,mm=Object.prototype.toString,ym=Nf(),vm=function(e){return"string"==typeof e||"object"==typeof e&&(ym?function(e){try{return bm.call(e),!0}catch(e){return!1}}(e):"[object String]"===mm.call(e))},hm="function"==typeof Map&&Map.prototype?Map:null,gm="function"==typeof Set&&Set.prototype?Set:null;hm||(um=function(){return!1});var Pm=hm?Map.prototype.has:null,Cm=gm?Set.prototype.has:null;um||Pm||(um=function(){return!1});var wm,qm=um||function(e){if(!e||"object"!=typeof e)return!1;try{if(Pm.call(e),Cm)try{Cm.call(e)}catch(e){return!0}return e instanceof hm}catch(e){}return!1},Em="function"==typeof Map&&Map.prototype?Map:null,xm="function"==typeof Set&&Set.prototype?Set:null;xm||(wm=function(){return!1});var Om=Em?Map.prototype.has:null,jm=xm?Set.prototype.has:null;wm||jm||(wm=function(){return!1});var Rm=wm||function(e){if(!e||"object"!=typeof e)return!1;try{if(jm.call(e),Om)try{Om.call(e)}catch(e){return!0}return e instanceof xm}catch(e){}return!1},Sm=zf,Am=function(e){if(!dm)throw new cm("this environment lacks StopIteration");sm.set(e,"[[Done]]",!1);var t={next:function(){var e=sm.get(this,"[[Iterator]]"),t=sm.get(e,"[[Done]]");try{return{done:t,value:t?void 0:e.next()}}catch(t){if(sm.set(e,"[[Done]]",!0),t!==dm)throw t;return{done:!0,value:void 0}}}};return sm.set(t,"[[Iterator]]",e),t};if(xd()||Cd()()){var _m=Symbol.iterator;Ff.exports=function(e){return null!=e&&void 0!==e[_m]?e[_m]():Sm(e)?Array.prototype[_m].call(e):void 0}}else{var Tm=fm,Mm=vm,Im=up,Bm=Im("%Map%",!0),Fm=Im("%Set%",!0),km=Up,Nm=km("Array.prototype.push"),Lm=km("String.prototype.charCodeAt"),Um=km("String.prototype.slice"),Dm=function(e){var t=0;return{next:function(){var r,n=t>=e.length;return n||(r=e[t],t+=1),{done:n,value:r}}}},Hm=function(e,t){if(Tm(e)||Sm(e))return Dm(e);if(Mm(e)){var r=0;return{next:function(){var t=function(e,t){if(t+1>=e.length)return t+1;var r=Lm(e,t);if(r<55296||r>56319)return t+1;var n=Lm(e,t+1);return n<56320||n>57343?t+1:t+2}(e,r),n=Um(e,r,t);return r=t,{done:t>e.length,value:n}}}}return t&&void 0!==e["_es6-shim iterator_"]?e["_es6-shim iterator_"]():void 0};if(Bm||Fm){var Wm=qm,zm=Rm,$m=km("Map.prototype.forEach",!0),Vm=km("Set.prototype.forEach",!0);if("undefined"==typeof process||!process.versions||!process.versions.node)var Gm=km("Map.prototype.iterator",!0),Jm=km("Set.prototype.iterator",!0);var Qm=km("Map.prototype.@@iterator",!0)||km("Map.prototype._es6-shim iterator_",!0),Xm=km("Set.prototype.@@iterator",!0)||km("Set.prototype._es6-shim iterator_",!0);Ff.exports=function(e){return function(e){if(Wm(e)){if(Gm)return Am(Gm(e));if(Qm)return Qm(e);if($m){var t=[];return $m(e,(function(e,r){Nm(t,[r,e])})),Dm(t)}}if(zm(e)){if(Jm)return Am(Jm(e));if(Xm)return Xm(e);if(Vm){var r=[];return Vm(e,(function(e){Nm(r,e)})),Dm(r)}}}(e)||Hm(e)}}else Ff.exports=function(e){if(null!=e)return Hm(e,!0)}}var Km=function(e){return e!=e},Ym=function(e,t){return 0===e&&0===t?1/e==1/t:e===t||!(!Km(e)||!Km(t))},Zm=Ym,ey=function(){return"function"==typeof Object.is?Object.is:Zm},ty=ey,ry=Sp,ny=Sp,oy=Ym,ay=ey,iy=function(){var e=ty();return ry(Object,{is:e},{is:function(){return Object.is!==e}}),e},ly=(0,Ap.exports)(ay(),Object);ny(ly,{getPolyfill:ay,implementation:oy,shim:iy});var uy,sy,cy=ly,dy=Function.prototype.toString,py="object"==typeof Reflect&&null!==Reflect&&Reflect.apply;if("function"==typeof py&&"function"==typeof Object.defineProperty)try{uy=Object.defineProperty({},"length",{get:function(){throw sy}}),sy={},py((function(){throw 42}),null,uy)}catch(e){e!==sy&&(py=null)}else py=null;var fy=/^\s*class\b/,by=function(e){try{var t=dy.call(e);return fy.test(t)}catch(e){return!1}},my=function(e){try{return!by(e)&&(dy.call(e),!0)}catch(e){return!1}},yy=Object.prototype.toString,vy="function"==typeof Symbol&&!!Symbol.toStringTag,hy=!(0 in[,]),gy=function(){return!1};if("object"==typeof document){var Py=document.all;yy.call(Py)===yy.call(document.all)&&(gy=function(e){if((hy||!e)&&(void 0===e||"object"==typeof e))try{var t=yy.call(e);return("[object HTMLAllCollection]"===t||"[object HTML document.all class]"===t||"[object HTMLCollection]"===t||"[object Object]"===t)&&null==e("")}catch(e){}return!1})}var Cy=py?function(e){if(gy(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;try{py(e,null,uy)}catch(e){if(e!==sy)return!1}return!by(e)&&my(e)}:function(e){if(gy(e))return!0;if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if(vy)return my(e);if(by(e))return!1;var t=yy.call(e);return!("[object Function]"!==t&&"[object GeneratorFunction]"!==t&&!/^\[object HTML/.test(t))&&my(e)},wy=Cy,qy=Object.prototype.toString,Ey=Object.prototype.hasOwnProperty,xy=function(e,t,r){if(!wy(t))throw new TypeError("iterator must be a function");var n;arguments.length>=3&&(n=r),"[object Array]"===qy.call(e)?function(e,t,r){for(var n=0,o=e.length;n<o;n++)Ey.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n):"string"==typeof e?function(e,t,r){for(var n=0,o=e.length;n<o;n++)null==r?t(e.charAt(n),n,e):t.call(r,e.charAt(n),n,e)}(e,t,n):function(e,t,r){for(var n in e)Ey.call(e,n)&&(null==r?t(e[n],n,e):t.call(r,e[n],n,e))}(e,t,n)},Oy=["BigInt64Array","BigUint64Array","Float32Array","Float64Array","Int16Array","Int32Array","Int8Array","Uint16Array","Uint32Array","Uint8Array","Uint8ClampedArray"],jy="undefined"==typeof globalThis?r:globalThis,Ry=xy,Sy=function(){for(var e=[],t=0;t<Oy.length;t++)"function"==typeof jy[Oy[t]]&&(e[e.length]=Oy[t]);return e},Ay=Ap.exports,_y=Up,Ty=fp,My=_y("Object.prototype.toString"),Iy=Nf(),By="undefined"==typeof globalThis?r:globalThis,Fy=Sy(),ky=_y("String.prototype.slice"),Ny=Object.getPrototypeOf,Ly=_y("Array.prototype.indexOf",!0)||function(e,t){for(var r=0;r<e.length;r+=1)if(e[r]===t)return r;return-1},Uy={__proto__:null};Ry(Fy,Iy&&Ty&&Ny?function(e){var t=new By[e];if(Symbol.toStringTag in t){var r=Ny(t),n=Ty(r,Symbol.toStringTag);if(!n){var o=Ny(r);n=Ty(o,Symbol.toStringTag)}Uy["$"+e]=Ay(n.get)}}:function(e){var t=new By[e],r=t.slice||t.set;r&&(Uy["$"+e]=Ay(r))});var Dy,Hy,Wy,zy,$y=function(e){if(!e||"object"!=typeof e)return!1;if(!Iy){var t=ky(My(e),8,-1);return Ly(Fy,t)>-1?t:"Object"===t&&function(e){var t=!1;return Ry(Uy,(function(r,n){if(!t)try{r(e),t=ky(n,1)}catch(e){}})),t}(e)}return Ty?function(e){var t=!1;return Ry(Uy,(function(r,n){if(!t)try{"$"+r(e)===n&&(t=ky(n,1))}catch(e){}})),t}(e):null},Vy=$y,Gy=Ap.exports,Jy=Up,Qy=up,Xy=function(e){return!!Vy(e)},Ky=Qy("ArrayBuffer",!0),Yy=Qy("Float32Array",!0),Zy=Jy("ArrayBuffer.prototype.byteLength",!0),ev=Ky&&!Zy&&(new Ky).slice,tv=ev&&Gy(ev),rv=Zy||tv?function(e){if(!e||"object"!=typeof e)return!1;try{return Zy?Zy(e):tv(e,0),!0}catch(e){return!1}}:Yy?function(e){try{return new Yy(e).buffer===e&&!Xy(e)}catch(t){return"object"==typeof e&&"RangeError"===t.name}}:function(){return!1},nv=Date.prototype.getDay,ov=Object.prototype.toString,av=Nf(),iv=Up,lv=Nf();if(lv){Dy=iv("Object.prototype.hasOwnProperty"),Hy=iv("RegExp.prototype.exec"),Wy={};var uv=function(){throw Wy};zy={toString:uv,valueOf:uv},"symbol"==typeof Symbol.toPrimitive&&(zy[Symbol.toPrimitive]=uv)}var sv=iv("Object.prototype.toString"),cv=Object.getOwnPropertyDescriptor,dv=lv?function(e){if(!e||"object"!=typeof e)return!1;var t=cv(e,"lastIndex");if(!(t&&Dy(t,"value")))return!1;try{Hy(e,zy)}catch(e){return e===Wy}}:function(e){return!(!e||"object"!=typeof e&&"function"!=typeof e)&&"[object RegExp]"===sv(e)},pv=Up("SharedArrayBuffer.prototype.byteLength",!0),fv=pv?function(e){if(!e||"object"!=typeof e)return!1;try{return pv(e),!0}catch(e){return!1}}:function(){return!1},bv=Number.prototype.toString,mv=Object.prototype.toString,yv=Nf(),vv=Up,hv=vv("Boolean.prototype.toString"),gv=vv("Object.prototype.toString"),Pv=Nf(),Cv={exports:{}},wv=Object.prototype.toString;if(xd()){var qv=Symbol.prototype.toString,Ev=/^Symbol\(.*\)$/;Cv.exports=function(e){if("symbol"==typeof e)return!0;if("[object Symbol]"!==wv.call(e))return!1;try{return function(e){return"symbol"==typeof e.valueOf()&&Ev.test(qv.call(e))}(e)}catch(e){return!1}}}else Cv.exports=function(e){return!1};var xv={exports:{}},Ov="undefined"!=typeof BigInt&&BigInt;if("function"==typeof Ov&&"function"==typeof BigInt&&"bigint"==typeof Ov(42)&&"bigint"==typeof BigInt(42)){var jv=BigInt.prototype.valueOf;xv.exports=function(e){return null!=e&&"boolean"!=typeof e&&"string"!=typeof e&&"number"!=typeof e&&"symbol"!=typeof e&&"function"!=typeof e&&("bigint"==typeof e||function(e){try{return jv.call(e),!0}catch(e){}return!1}(e))}}else xv.exports=function(e){return!1};var Rv,Sv=vm,Av=function(e){return"number"==typeof e||"object"==typeof e&&(yv?function(e){try{return bv.call(e),!0}catch(e){return!1}}(e):"[object Number]"===mv.call(e))},_v=function(e){return"boolean"==typeof e||null!==e&&"object"==typeof e&&(Pv&&Symbol.toStringTag in e?function(e){try{return hv(e),!0}catch(e){return!1}}(e):"[object Boolean]"===gv(e))},Tv=Cv.exports,Mv=xv.exports,Iv="function"==typeof WeakMap&&WeakMap.prototype?WeakMap:null,Bv="function"==typeof WeakSet&&WeakSet.prototype?WeakSet:null;Iv||(Rv=function(){return!1});var Fv=Iv?Iv.prototype.has:null,kv=Bv?Bv.prototype.has:null;Rv||Fv||(Rv=function(){return!1});var Nv=Rv||function(e){if(!e||"object"!=typeof e)return!1;try{if(Fv.call(e,Fv),kv)try{kv.call(e,kv)}catch(e){return!0}return e instanceof Iv}catch(e){}return!1},Lv={exports:{}},Uv=Up,Dv=up("%WeakSet%",!0),Hv=Uv("WeakSet.prototype.has",!0);if(Hv){var Wv=Uv("WeakMap.prototype.has",!0);Lv.exports=function(e){if(!e||"object"!=typeof e)return!1;try{if(Hv(e,Hv),Wv)try{Wv(e,Wv)}catch(e){return!0}return e instanceof Dv}catch(e){}return!1}}else Lv.exports=function(){return!1};var zv=qm,$v=Rm,Vv=Nv,Gv=Lv.exports,Jv=Up("ArrayBuffer.prototype.byteLength",!0),Qv=rv,Xv=af,Kv=Up,Yv=Bf,Zv=up,eh=Ff.exports,th=rm,rh=cy,nh=zf,oh=fm,ah=rv,ih=function(e){return"object"==typeof e&&null!==e&&(av?function(e){try{return nv.call(e),!0}catch(e){return!1}}(e):"[object Date]"===ov.call(e))},lh=dv,uh=fv,sh=Pd,ch=function(e){return null==e||"object"!=typeof e&&"function"!=typeof e?null:Sv(e)?"String":Av(e)?"Number":_v(e)?"Boolean":Tv(e)?"Symbol":Mv(e)?"BigInt":void 0},dh=function(e){if(e&&"object"==typeof e){if(zv(e))return"Map";if($v(e))return"Set";if(Vv(e))return"WeakMap";if(Gv(e))return"WeakSet"}return!1},ph=$y,fh=function(e){return Qv(e)?Jv?Jv(e):e.byteLength:NaN},bh=Kv("SharedArrayBuffer.prototype.byteLength",!0),mh=Kv("Date.prototype.getTime"),yh=Object.getPrototypeOf,vh=Kv("Object.prototype.toString"),hh=Zv("%Set%",!0),gh=Kv("Map.prototype.has",!0),Ph=Kv("Map.prototype.get",!0),Ch=Kv("Map.prototype.size",!0),wh=Kv("Set.prototype.add",!0),qh=Kv("Set.prototype.delete",!0),Eh=Kv("Set.prototype.has",!0),xh=Kv("Set.prototype.size",!0);function Oh(e,t,r,n){for(var o,a=eh(e);(o=a.next())&&!o.done;)if(_h(t,o.value,r,n))return qh(e,o.value),!0;return!1}function jh(e){return void 0===e?null:"object"!=typeof e?"symbol"!=typeof e&&("string"!=typeof e&&"number"!=typeof e||+e==+e):void 0}function Rh(e,t,r,n,o,a){var i=jh(r);if(null!=i)return i;var l=Ph(t,i),u=Xv({},o,{strict:!1});return!(void 0===l&&!gh(t,i)||!_h(n,l,u,a))&&(!gh(e,i)&&_h(n,l,u,a))}function Sh(e,t,r){var n=jh(r);return null!=n?n:Eh(t,n)&&!Eh(e,n)}function Ah(e,t,r,n,o,a){for(var i,l,u=eh(e);(i=u.next())&&!i.done;)if(_h(r,l=i.value,o,a)&&_h(n,Ph(t,l),o,a))return qh(e,l),!0;return!1}function _h(e,t,r,n){var o=r||{};if(o.strict?rh(e,t):e===t)return!0;if(ch(e)!==ch(t))return!1;if(!e||!t||"object"!=typeof e&&"object"!=typeof t)return o.strict?rh(e,t):e==t;var a,i=n.has(e),l=n.has(t);if(i&&l){if(n.get(e)===n.get(t))return!0}else a={};return i||n.set(e,a),l||n.set(t,a),function(e,t,r,n){var o,a;if(typeof e!=typeof t)return!1;if(null==e||null==t)return!1;if(vh(e)!==vh(t))return!1;if(nh(e)!==nh(t))return!1;var i=oh(e),l=oh(t);if(i!==l)return!1;var u=e instanceof Error,s=t instanceof Error;if(u!==s)return!1;if((u||s)&&(e.name!==t.name||e.message!==t.message))return!1;var c=lh(e),d=lh(t);if(c!==d)return!1;if((c||d)&&(e.source!==t.source||Yv(e)!==Yv(t)))return!1;var p=ih(e),f=ih(t);if(p!==f)return!1;if((p||f)&&mh(e)!==mh(t))return!1;if(r.strict&&yh&&yh(e)!==yh(t))return!1;var b=ph(e),m=ph(t);if(b!==m)return!1;if(b||m){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var y=Th(e),v=Th(t);if(y!==v)return!1;if(y||v){if(e.length!==t.length)return!1;for(o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}var h=ah(e),g=ah(t);if(h!==g)return!1;if(h||g)return fh(e)===fh(t)&&("function"==typeof Uint8Array&&_h(new Uint8Array(e),new Uint8Array(t),r,n));var P=uh(e),C=uh(t);if(P!==C)return!1;if(P||C)return bh(e)===bh(t)&&("function"==typeof Uint8Array&&_h(new Uint8Array(e),new Uint8Array(t),r,n));if(typeof e!=typeof t)return!1;var w=sh(e),q=sh(t);if(w.length!==q.length)return!1;for(w.sort(),q.sort(),o=w.length-1;o>=0;o--)if(w[o]!=q[o])return!1;for(o=w.length-1;o>=0;o--)if(!_h(e[a=w[o]],t[a],r,n))return!1;var E=dh(e),x=dh(t);if(E!==x)return!1;if("Set"===E||"Set"===x)return function(e,t,r,n){if(xh(e)!==xh(t))return!1;var o,a,i,l=eh(e),u=eh(t);for(;(o=l.next())&&!o.done;)if(o.value&&"object"==typeof o.value)i||(i=new hh),wh(i,o.value);else if(!Eh(t,o.value)){if(r.strict)return!1;if(!Sh(e,t,o.value))return!1;i||(i=new hh),wh(i,o.value)}if(i){for(;(a=u.next())&&!a.done;)if(a.value&&"object"==typeof a.value){if(!Oh(i,a.value,r.strict,n))return!1}else if(!r.strict&&!Eh(e,a.value)&&!Oh(i,a.value,r.strict,n))return!1;return 0===xh(i)}return!0}(e,t,r,n);if("Map"===E)return function(e,t,r,n){if(Ch(e)!==Ch(t))return!1;var o,a,i,l,u,s,c=eh(e),d=eh(t);for(;(o=c.next())&&!o.done;)if(l=o.value[0],u=o.value[1],l&&"object"==typeof l)i||(i=new hh),wh(i,l);else if(void 0===(s=Ph(t,l))&&!gh(t,l)||!_h(u,s,r,n)){if(r.strict)return!1;if(!Rh(e,t,l,u,r,n))return!1;i||(i=new hh),wh(i,l)}if(i){for(;(a=d.next())&&!a.done;)if(l=a.value[0],s=a.value[1],l&&"object"==typeof l){if(!Ah(i,e,l,s,r,n))return!1}else if(!(r.strict||e.has(l)&&_h(Ph(e,l),s,r,n)||Ah(i,e,l,s,Xv({},r,{strict:!1}),n)))return!1;return 0===xh(i)}return!0}(e,t,r,n);return!0}(e,t,o,n)}function Th(e){return!(!e||"object"!=typeof e||"number"!=typeof e.length)&&("function"==typeof e.copy&&"function"==typeof e.slice&&(!(e.length>0&&"number"!=typeof e[0])&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))))}Object.defineProperty(cd,"__esModule",{value:!0}),cd.default=void 0;var Mh=Fh((function(e,t,r){return _h(e,t,r,th())})),Ih=Fh(kr),Bh=Fh(nn);function Fh(e){return e&&e.__esModule?e:{default:e}}function kh(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||Nh(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Nh(e,t){if(e){if("string"==typeof e)return Lh(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Lh(e,t):void 0}}function Lh(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var Uh=[],Dh=Bh.default.keys(),Hh=0;Hh<Dh.length;Hh++){var Wh=Dh[Hh],zh=Bh.default.get(Wh);if(zh)for(var $h=[].concat(zh.baseConcepts,zh.relatedConcepts),Vh=0;Vh<$h.length;Vh++){var Gh=$h[Vh];if("HTML"===Gh.module){var Jh=Gh.concept;Jh&&function(){var e=JSON.stringify(Jh),t=Uh.find((function(t){return JSON.stringify(t[0])===e})),r=void 0;r=t?t[1]:[];for(var n=!0,o=0;o<r.length;o++)if(r[o]===Wh){n=!1;break}n&&r.push(Wh),Uh.push([Jh,r])}()}}}var Qh={entries:function(){return Uh},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=Nh(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(Uh);try{for(n.s();!(t=n.n()).done;){var o=kh(t.value,2),a=o[0],i=o[1];e.call(r,i,a,Uh)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=Uh.find((function(t){return(0,Mh.default)(e,t[0])}));return t&&t[1]},has:function(e){return!!Qh.get(e)},keys:function(){return Uh.map((function(e){return kh(e,1)[0]}))},values:function(){return Uh.map((function(e){return kh(e,2)[1]}))}},Xh=(0,Ih.default)(Qh,Qh.entries());cd.default=Xh;var Kh={};Object.defineProperty(Kh,"__esModule",{value:!0}),Kh.default=void 0;var Yh=eg(kr),Zh=eg(nn);function eg(e){return e&&e.__esModule?e:{default:e}}function tg(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null==r)return;var n,o,a=[],i=!0,l=!1;try{for(r=r.call(e);!(i=(n=r.next()).done)&&(a.push(n.value),!t||a.length!==t);i=!0);}catch(e){l=!0,o=e}finally{try{i||null==r.return||r.return()}finally{if(l)throw o}}return a}(e,t)||rg(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rg(e,t){if(e){if("string"==typeof e)return ng(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);return"Object"===r&&e.constructor&&(r=e.constructor.name),"Map"===r||"Set"===r?Array.from(e):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ng(e,t):void 0}}function ng(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}for(var og=[],ag=Zh.default.keys(),ig=function(e){var t=ag[e],r=Zh.default.get(t);if(r)for(var n=[].concat(r.baseConcepts,r.relatedConcepts),o=0;o<n.length;o++){var a=n[o];if("HTML"===a.module){var i=a.concept;if(i){var l=og.find((function(e){return e[0]===t})),u=void 0;(u=l?l[1]:[]).push(i),og.push([t,u])}}}},lg=0;lg<ag.length;lg++)ig(lg);var ug={entries:function(){return og},forEach:function(e){var t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=function(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=rg(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}(og);try{for(n.s();!(t=n.n()).done;){var o=tg(t.value,2),a=o[0],i=o[1];e.call(r,i,a,og)}}catch(e){n.e(e)}finally{n.f()}},get:function(e){var t=og.find((function(t){return t[0]===e}));return t&&t[1]},has:function(e){return!!ug.get(e)},keys:function(){return og.map((function(e){return tg(e,1)[0]}))},values:function(){return og.map((function(e){return tg(e,2)[1]}))}},sg=(0,Yh.default)(ug,ug.entries());Kh.default=sg,Object.defineProperty(Br,"__esModule",{value:!0});var cg=Br.roles=qg=Br.roleElements=Br.elementRoles=Br.dom=Br.aria=void 0,dg=yg(Fr),pg=yg(Qr),fg=yg(nn),bg=yg(cd),mg=yg(Kh);function yg(e){return e&&e.__esModule?e:{default:e}}var vg=dg.default;Br.aria=vg;var hg=pg.default;Br.dom=hg;var gg=fg.default;cg=Br.roles=gg;var Pg=bg.default,Cg=Br.elementRoles=Pg,wg=mg.default,qg=Br.roleElements=wg;const Eg=function(e){function t(e){let{attributes:t=[]}=e;return t.length}function r(e){let{attributes:t=[]}=e;const r=t.findIndex((e=>e.value&&"type"===e.name&&"text"===e.value));r>=0&&(t=[...t.slice(0,r),...t.slice(r+1)]);const n=function(e){let{name:t,attributes:r}=e;return""+t+r.map((e=>{let{name:t,value:r,constraints:n=[]}=e;return-1!==n.indexOf("undefined")?":not(["+t+"])":r?"["+t+'="'+r+'"]':"["+t+"]"})).join("")}({...e,attributes:t});return e=>!(r>=0&&"text"!==e.type)&&e.matches(n)}let n=[];for(const[o,a]of e.entries())n=[...n,{match:r(o),roles:Array.from(a),specificity:t(o)}];return n.sort((function(e,t){let{specificity:r}=e,{specificity:n}=t;return n-r}))}(Cg);function xg(e){if(!0===e.hidden)return!0;if("true"===e.getAttribute("aria-hidden"))return!0;return"none"===e.ownerDocument.defaultView.getComputedStyle(e).display}function Og(e,t){void 0===t&&(t={});const{isSubtreeInaccessible:r=xg}=t;if("hidden"===e.ownerDocument.defaultView.getComputedStyle(e).visibility)return!0;let n=e;for(;n;){if(r(n))return!0;n=n.parentElement}return!1}function jg(e){for(const{match:t,roles:r}of Eg)if(t(e))return[...r];return[]}function Rg(e,t){let{hidden:r=!1}=void 0===t?{}:t;return function e(t){return[t,...Array.from(t.children).reduce(((t,r)=>[...t,...e(r)]),[])]}(e).filter((e=>!1!==r||!1===Og(e))).reduce(((e,t)=>{let r=[];return r=t.hasAttribute("role")?t.getAttribute("role").split(" ").slice(0,1):jg(t),r.reduce(((e,r)=>Array.isArray(e[r])?{...e,[r]:[...e[r],t]}:{...e,[r]:[t]}),e)}),{})}function Sg(e,t){let{hidden:r,includeDescription:n}=t;const o=Rg(e,{hidden:r});return Object.entries(o).filter((e=>{let[t]=e;return"generic"!==t})).map((e=>{let[t,r]=e;const o="-".repeat(50);return t+":\n\n"+r.map((e=>{const t='Name "'+Ir(e,{computedStyleSupportsPseudoElements:Lt().computedStyleSupportsPseudoElements})+'":\n',r=Ft(e.cloneNode(!1));if(n){return""+t+('Description "'+Mr(e,{computedStyleSupportsPseudoElements:Lt().computedStyleSupportsPseudoElements})+'":\n')+r}return""+t+r})).join("\n\n")+"\n\n"+o})).join("\n")}function Ag(e,t){const r=e.getAttribute(t);return"true"===r||"false"!==r&&void 0}const _g=Jt();function Tg(e){return new RegExp(function(e){return e.replace(/[.*+\-?^${}()|[\]\\]/g,"\\$&")}(e.toLowerCase()),"i")}function Mg(e,t,r,n){let{variant:o,name:a}=n,i="";const l={},u=[["Role","TestId"].includes(e)?r:Tg(r)];a&&(l.name=Tg(a)),"Role"===e&&Og(t)&&(l.hidden=!0,i="Element is inaccessible. This means that the element and all its children are invisible to screen readers.\n    If you are using the aria-hidden prop, make sure this is the right choice for your case.\n    "),Object.keys(l).length>0&&u.push(l);const s=o+"By"+e;return{queryName:e,queryMethod:s,queryArgs:u,variant:o,warning:i,toString(){i&&console.warn(i);let[e,t]=u;return e="string"==typeof e?"'"+e+"'":e,t=t?", { "+Object.entries(t).map((e=>{let[t,r]=e;return t+": "+r})).join(", ")+" }":"",s+"("+e+t+")"}}}function Ig(e,t,r){return r&&(!t||t.toLowerCase()===e.toLowerCase())}function Bg(e,t,r){var n,o;if(void 0===t&&(t="get"),e.matches(Lt().defaultIgnore))return;const a=null!=(n=e.getAttribute("role"))?n:null==(o=jg(e))?void 0:o[0];if("generic"!==a&&Ig("Role",r,a))return Mg("Role",e,a,{variant:t,name:Ir(e,{computedStyleSupportsPseudoElements:Lt().computedStyleSupportsPseudoElements})});const i=zt(document,e).map((e=>e.content)).join(" ");if(Ig("LabelText",r,i))return Mg("LabelText",e,i,{variant:t});const l=e.getAttribute("placeholder");if(Ig("PlaceholderText",r,l))return Mg("PlaceholderText",e,l,{variant:t});const u=_g(Kt(e));if(Ig("Text",r,u))return Mg("Text",e,u,{variant:t});if(Ig("DisplayValue",r,e.value))return Mg("DisplayValue",e,_g(e.value),{variant:t});const s=e.getAttribute("alt");if(Ig("AltText",r,s))return Mg("AltText",e,s,{variant:t});const c=e.getAttribute("title");if(Ig("Title",r,c))return Mg("Title",e,c,{variant:t});const d=e.getAttribute(Lt().testIdAttribute);return Ig("TestId",r,d)?Mg("TestId",e,d,{variant:t}):void 0}function Fg(e,t){e.stack=t.stack.replace(t.message,e.message)}function kg(e,t){let{container:r=Rt(),timeout:n=Lt().asyncUtilTimeout,showOriginalStackTrace:o=Lt().showOriginalStackTrace,stackTraceError:a,interval:i=50,onTimeout:l=(e=>(Object.defineProperty(e,"message",{value:Lt().getElementError(e.message,r).message}),e)),mutationObserverOptions:u={subtree:!0,childList:!0,attributes:!0,characterData:!0}}=t;if("function"!=typeof e)throw new TypeError("Received `callback` arg must be a function");return new Promise((async(t,s)=>{let c,d,p,f=!1,b="idle";const m=setTimeout((function(){let e;c?(e=c,o||"TestingLibraryElementError"!==e.name||Fg(e,a)):(e=new Error("Timed out in waitFor."),o||Fg(e,a)),v(l(e),null)}),n),y=jt();if(y){const{unstable_advanceTimersWrapper:e}=Lt();for(g();!f;){if(!jt()){const e=new Error("Changed from using fake timers to real timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to real timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||Fg(e,a),void s(e)}if(await e((async()=>{jest.advanceTimersByTime(i)})),f)break;g()}}else{try{At(r)}catch(e){return void s(e)}d=setInterval(h,i);const{MutationObserver:e}=St(r);p=new e(h),p.observe(r,u),g()}function v(e,r){f=!0,clearTimeout(m),y||(clearInterval(d),p.disconnect()),e?s(e):t(r)}function h(){if(jt()){const e=new Error("Changed from using real timers to fake timers while using waitFor. This is not allowed and will result in very strange behavior. Please ensure you're awaiting all async things your test is doing before changing to fake timers. For more info, please go to https://github.com/testing-library/dom-testing-library/issues/830");return o||Fg(e,a),s(e)}return g()}function g(){if("pending"!==b)try{const t=function(e){try{return Nt._disableExpensiveErrorDiagnostics=!0,e()}finally{Nt._disableExpensiveErrorDiagnostics=!1}}(e);"function"==typeof(null==t?void 0:t.then)?(b="pending",t.then((e=>{b="resolved",v(null,e)}),(e=>{b="rejected",c=e}))):v(null,t)}catch(e){c=e}}}))}function Ng(e,t){const r=new Error("STACK_TRACE_MESSAGE");return Lt().asyncWrapper((()=>kg(e,{stackTraceError:r,...t})))}function Lg(e,t){return Lt().getElementError(e,t)}function Ug(e,t){return Lg(e+"\n\n(If this is intentional, then use the `*AllBy*` variant of the query (like `queryAllByText`, `getAllByText`, or `findAllByText`)).",t)}function Dg(e,t,r,n){let{exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===n?{}:n;const u=o?Gt:Vt,s=Qt({collapseWhitespace:a,trim:i,normalizer:l});return Array.from(t.querySelectorAll("["+e+"]")).filter((t=>u(t.getAttribute(e),t,r,s)))}function Hg(e,t,r,n){const o=Dg(e,t,r,n);if(o.length>1)throw Ug("Found multiple elements by ["+e+"="+r+"]",t);return o[0]||null}function Wg(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(i.length>1){const e=i.map((e=>Lg(null,e).message)).join("\n\n");throw Ug(t(r,...o)+"\n\nHere are the matching elements:\n\n"+e,r)}return i[0]||null}}function zg(e,t){return Lt().getElementError("A better query is available, try this:\n"+e.toString()+"\n",t)}function $g(e,t){return function(r){for(var n=arguments.length,o=new Array(n>1?n-1:0),a=1;a<n;a++)o[a-1]=arguments[a];const i=e(r,...o);if(!i.length)throw Lt().getElementError(t(r,...o),r);return i}}function Vg(e){return(t,r,n,o)=>Ng((()=>e(t,r,n)),{container:t,...o})}const Gg=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=Lt().throwSuggestions}={}]=a.slice(-1);if(l&&u){const e=Bg(l,r);if(e&&!t.endsWith(e.queryName))throw zg(e.toString(),n)}return l},Jg=(e,t,r)=>function(n){for(var o=arguments.length,a=new Array(o>1?o-1:0),i=1;i<o;i++)a[i-1]=arguments[i];const l=e(n,...a),[{suggest:u=Lt().throwSuggestions}={}]=a.slice(-1);if(l.length&&u){const e=[...new Set(l.map((e=>{var t;return null==(t=Bg(e,r))?void 0:t.toString()})))];if(1===e.length&&!t.endsWith(Bg(l[0],r).queryName))throw zg(e[0],n)}return l};function Qg(e,t,r){const n=Gg(Wg(e,t),e.name,"query"),o=$g(e,r),a=Wg(o,t),i=Gg(a,e.name,"get");return[n,Jg(o,e.name.replace("query","get"),"getAll"),i,Vg(Jg(o,e.name,"findAll")),Vg(Gg(a,e.name,"find"))]}var Xg=Object.freeze({__proto__:null,getElementError:Lg,wrapAllByQueryWithSuggestion:Jg,wrapSingleQueryWithSuggestion:Gg,getMultipleElementsFoundError:Ug,queryAllByAttribute:Dg,queryByAttribute:Hg,makeSingleQuery:Wg,makeGetAllQuery:$g,makeFindQuery:Vg,buildQueries:Qg});const Kg=function(e,t,r){let{exact:n=!0,trim:o,collapseWhitespace:a,normalizer:i}=void 0===r?{}:r;const l=n?Gt:Vt,u=Qt({collapseWhitespace:a,trim:o,normalizer:i}),s=function(e){return Array.from(e.querySelectorAll("label,input")).map((e=>({node:e,textToMatch:Ht(e)}))).filter((e=>{let{textToMatch:t}=e;return null!==t}))}(e);return s.filter((e=>{let{node:r,textToMatch:n}=e;return l(n,r,t,u)})).map((e=>{let{node:t}=e;return t}))},Yg=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,normalizer:l}=void 0===r?{}:r;At(e);const u=o?Gt:Vt,s=Qt({collapseWhitespace:a,trim:i,normalizer:l}),c=Array.from(e.querySelectorAll("*")).filter((e=>Wt(e).length||e.hasAttribute("aria-labelledby"))).reduce(((r,o)=>{const a=zt(e,o,{selector:n});a.filter((e=>Boolean(e.formControl))).forEach((e=>{u(e.content,e.formControl,t,s)&&e.formControl&&r.push(e.formControl)}));const i=a.filter((e=>Boolean(e.content))).map((e=>e.content));return u(i.join(" "),o,t,s)&&r.push(o),i.length>1&&i.forEach(((e,n)=>{u(e,o,t,s)&&r.push(o);const a=[...i];a.splice(n,1),a.length>1&&u(a.join(" "),o,t,s)&&r.push(o)})),r}),[]).concat(Dg("aria-label",e,t,{exact:o,normalizer:s}));return Array.from(new Set(c)).filter((e=>e.matches(n)))},Zg=function(e,t){for(var r=arguments.length,n=new Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o];const a=Yg(e,t,...n);if(!a.length){const r=Kg(e,t,...n);if(r.length){const n=r.map((t=>function(e,t){const r=t.getAttribute("for");if(!r)return null;const n=e.querySelector('[id="'+r+'"]');return n?n.tagName.toLowerCase():null}(e,t))).filter((e=>!!e));throw n.length?Lt().getElementError(n.map((e=>"Found a label with the text of: "+t+", however the element associated with this label (<"+e+" />) is non-labellable [https://html.spec.whatwg.org/multipage/forms.html#category-label]. If you really need to label a <"+e+" />, you can use aria-label or aria-labelledby instead.")).join("\n\n"),e):Lt().getElementError("Found a label with the text of: "+t+', however no form control was found associated to that label. Make sure you\'re using the "for" attribute or "aria-labelledby" attribute correctly.',e)}throw Lt().getElementError("Unable to find a label with the text of: "+t,e)}return a};const eP=(e,t)=>"Found multiple elements with the text of: "+t,tP=Gg(Wg(Yg,eP),Yg.name,"query"),rP=Wg(Zg,eP),nP=Vg(Jg(Zg,Zg.name,"findAll")),oP=Vg(Gg(rP,Zg.name,"find")),aP=Jg(Zg,Zg.name,"getAll"),iP=Gg(rP,Zg.name,"get"),lP=Jg(Yg,Yg.name,"queryAll"),uP=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return At(t[0]),Dg("placeholder",...t)},sP=Jg(uP,uP.name,"queryAll"),[cP,dP,pP,fP,bP]=Qg(uP,((e,t)=>"Found multiple elements with the placeholder text of: "+t),((e,t)=>"Unable to find an element with the placeholder text of: "+t)),mP=function(e,t,r){let{selector:n="*",exact:o=!0,collapseWhitespace:a,trim:i,ignore:l=Lt().defaultIgnore,normalizer:u}=void 0===r?{}:r;At(e);const s=o?Gt:Vt,c=Qt({collapseWhitespace:a,trim:i,normalizer:u});let d=[];return"function"==typeof e.matches&&e.matches(n)&&(d=[e]),[...d,...Array.from(e.querySelectorAll(n))].filter((e=>!l||!e.matches(l))).filter((e=>s(Kt(e),e,t,c)))},yP=Jg(mP,mP.name,"queryAll"),[vP,hP,gP,PP,CP]=Qg(mP,((e,t)=>"Found multiple elements with the text: "+t),(function(e,t,r){void 0===r&&(r={});const{collapseWhitespace:n,trim:o,normalizer:a,selector:i}=r,l=Qt({collapseWhitespace:n,trim:o,normalizer:a})(t.toString());return"Unable to find an element with the text: "+(l!==t.toString()?l+" (normalized from '"+t+"')":t)+("*"!==(null!=i?i:"*")?", which matches selector '"+i+"'":"")+". This could be because the text is broken up by multiple elements. In this case, you can provide a function for your text matcher to make your matcher more flexible."})),wP=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;At(e);const l=n?Gt:Vt,u=Qt({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("input,textarea,select")).filter((e=>{if("SELECT"===e.tagName){return Array.from(e.options).filter((e=>e.selected)).some((e=>l(Kt(e),e,t,u)))}return l(e.value,e,t,u)}))},qP=Jg(wP,wP.name,"queryAll"),[EP,xP,OP,jP,RP]=Qg(wP,((e,t)=>"Found multiple elements with the display value: "+t+"."),((e,t)=>"Unable to find an element with the display value: "+t+".")),SP=/^(img|input|area|.+-.+)$/i,AP=function(e,t,r){return void 0===r&&(r={}),At(e),Dg("alt",e,t,r).filter((e=>SP.test(e.tagName)))},_P=Jg(AP,AP.name,"queryAll"),[TP,MP,IP,BP,FP]=Qg(AP,((e,t)=>"Found multiple elements with the alt text: "+t),((e,t)=>"Unable to find an element with the alt text: "+t)),kP=function(e,t,r){let{exact:n=!0,collapseWhitespace:o,trim:a,normalizer:i}=void 0===r?{}:r;At(e);const l=n?Gt:Vt,u=Qt({collapseWhitespace:o,trim:a,normalizer:i});return Array.from(e.querySelectorAll("[title], svg > title")).filter((e=>l(e.getAttribute("title"),e,t,u)||(e=>{var t;return"title"===e.tagName.toLowerCase()&&"svg"===(null==(t=e.parentElement)?void 0:t.tagName.toLowerCase())})(e)&&l(Kt(e),e,t,u)))},NP=Jg(kP,kP.name,"queryAll"),[LP,UP,DP,HP,WP]=Qg(kP,((e,t)=>"Found multiple elements with the title: "+t+"."),((e,t)=>"Unable to find an element with the title: "+t+".")),zP=function(e,t,r){let{hidden:n=Lt().defaultHidden,name:o,description:a,queryFallbacks:i=!1,selected:l,busy:u,checked:s,pressed:c,current:d,level:p,expanded:f,value:{now:b,min:m,max:y,text:v}={}}=void 0===r?{}:r;var h,g,P,C,w,q,E,x,O,j;if((At(e),void 0!==l)&&void 0===(null==(h=cg.get(t))?void 0:h.props["aria-selected"]))throw new Error('"aria-selected" is not supported on role "'+t+'".');if(void 0!==u&&void 0===(null==(g=cg.get(t))?void 0:g.props["aria-busy"]))throw new Error('"aria-busy" is not supported on role "'+t+'".');if(void 0!==s&&void 0===(null==(P=cg.get(t))?void 0:P.props["aria-checked"]))throw new Error('"aria-checked" is not supported on role "'+t+'".');if(void 0!==c&&void 0===(null==(C=cg.get(t))?void 0:C.props["aria-pressed"]))throw new Error('"aria-pressed" is not supported on role "'+t+'".');if(void 0!==d&&void 0===(null==(w=cg.get(t))?void 0:w.props["aria-current"]))throw new Error('"aria-current" is not supported on role "'+t+'".');if(void 0!==p&&"heading"!==t)throw new Error('Role "'+t+'" cannot have "level" property.');if(void 0!==b&&void 0===(null==(q=cg.get(t))?void 0:q.props["aria-valuenow"]))throw new Error('"aria-valuenow" is not supported on role "'+t+'".');if(void 0!==y&&void 0===(null==(E=cg.get(t))?void 0:E.props["aria-valuemax"]))throw new Error('"aria-valuemax" is not supported on role "'+t+'".');if(void 0!==m&&void 0===(null==(x=cg.get(t))?void 0:x.props["aria-valuemin"]))throw new Error('"aria-valuemin" is not supported on role "'+t+'".');if(void 0!==v&&void 0===(null==(O=cg.get(t))?void 0:O.props["aria-valuetext"]))throw new Error('"aria-valuetext" is not supported on role "'+t+'".');if(void 0!==f&&void 0===(null==(j=cg.get(t))?void 0:j.props["aria-expanded"]))throw new Error('"aria-expanded" is not supported on role "'+t+'".');const R=new WeakMap;function S(e){return R.has(e)||R.set(e,xg(e)),R.get(e)}return Array.from(e.querySelectorAll(function(e){var t;const r=null!=(t=qg.get(e))?t:new Set,n=new Set(Array.from(r).map((e=>{let{name:t}=e;return t})));return['*[role~="'+e+'"]'].concat(Array.from(n)).join(",")}(t))).filter((e=>{if(e.hasAttribute("role")){const r=e.getAttribute("role");if(i)return r.split(" ").filter(Boolean).some((e=>e===t));const[n]=r.split(" ");return n===t}return jg(e).some((e=>e===t))})).filter((e=>{if(void 0!==l)return l===function(e){return"OPTION"===e.tagName?e.selected:Ag(e,"aria-selected")}(e);if(void 0!==u)return u===function(e){return"true"===e.getAttribute("aria-busy")}(e);if(void 0!==s)return s===function(e){if(!("indeterminate"in e)||!e.indeterminate)return"checked"in e?e.checked:Ag(e,"aria-checked")}(e);if(void 0!==c)return c===function(e){return Ag(e,"aria-pressed")}(e);if(void 0!==d)return d===function(e){var t,r;return null!=(t=null!=(r=Ag(e,"aria-current"))?r:e.getAttribute("aria-current"))&&t}(e);if(void 0!==f)return f===function(e){return Ag(e,"aria-expanded")}(e);if(void 0!==p)return p===function(e){return e.getAttribute("aria-level")&&Number(e.getAttribute("aria-level"))||{H1:1,H2:2,H3:3,H4:4,H5:5,H6:6}[e.tagName]}(e);if(void 0!==b||void 0!==y||void 0!==m||void 0!==v){let r=!0;var t;if(void 0!==b&&r&&(r=b===function(e){const t=e.getAttribute("aria-valuenow");return null===t?void 0:+t}(e)),void 0!==y&&r&&(r=y===function(e){const t=e.getAttribute("aria-valuemax");return null===t?void 0:+t}(e)),void 0!==m&&r&&(r=m===function(e){const t=e.getAttribute("aria-valuemin");return null===t?void 0:+t}(e)),void 0!==v)r&&(r=Gt(null!=(t=function(e){const t=e.getAttribute("aria-valuetext");return null===t?void 0:t}(e))?t:null,e,v,(e=>e)));return r}return!0})).filter((e=>void 0===o||Gt(Ir(e,{computedStyleSupportsPseudoElements:Lt().computedStyleSupportsPseudoElements}),e,o,(e=>e)))).filter((e=>void 0===a||Gt(Mr(e,{computedStyleSupportsPseudoElements:Lt().computedStyleSupportsPseudoElements}),e,a,(e=>e)))).filter((e=>!1!==n||!1===Og(e,{isSubtreeInaccessible:S})))};const $P=e=>{let t="";return t=void 0===e?"":"string"==typeof e?' and name "'+e+'"':" and name `"+e+"`",t},VP=Jg(zP,zP.name,"queryAll"),[GP,JP,QP,XP,KP]=Qg(zP,(function(e,t,r){let{name:n}=void 0===r?{}:r;return'Found multiple elements with the role "'+t+'"'+$P(n)}),(function(e,t,r){let{hidden:n=Lt().defaultHidden,name:o,description:a}=void 0===r?{}:r;if(Lt()._disableExpensiveErrorDiagnostics)return'Unable to find role="'+t+'"'+$P(o);let i,l="";Array.from(e.children).forEach((e=>{l+=Sg(e,{hidden:n,includeDescription:void 0!==a})})),i=0===l.length?!1===n?"There are no accessible roles. But there might be some inaccessible roles. If you wish to access them, then set the `hidden` option to `true`. Learn more about this here: https://testing-library.com/docs/dom-testing-library/api-queries#byrole":"There are no available roles.":("\nHere are the "+(!1===n?"accessible":"available")+" roles:\n\n  "+l.replace(/\n/g,"\n  ").replace(/\n\s\s\n/g,"\n\n")+"\n").trim();let u="";u=void 0===o?"":"string"==typeof o?' and name "'+o+'"':" and name `"+o+"`";let s="";return s=void 0===a?"":"string"==typeof a?' and description "'+a+'"':" and description `"+a+"`",("\nUnable to find an "+(!1===n?"accessible ":"")+'element with the role "'+t+'"'+u+s+"\n\n"+i).trim()})),YP=()=>Lt().testIdAttribute,ZP=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];return At(t[0]),Dg(YP(),...t)},eC=Jg(ZP,ZP.name,"queryAll"),[tC,rC,nC,oC,aC]=Qg(ZP,((e,t)=>"Found multiple elements by: ["+YP()+'="'+t+'"]'),((e,t)=>"Unable to find an element by: ["+YP()+'="'+t+'"]'));var iC=Object.freeze({__proto__:null,queryAllByLabelText:lP,queryByLabelText:tP,getAllByLabelText:aP,getByLabelText:iP,findAllByLabelText:nP,findByLabelText:oP,queryByPlaceholderText:cP,queryAllByPlaceholderText:sP,getByPlaceholderText:pP,getAllByPlaceholderText:dP,findAllByPlaceholderText:fP,findByPlaceholderText:bP,queryByText:vP,queryAllByText:yP,getByText:gP,getAllByText:hP,findAllByText:PP,findByText:CP,queryByDisplayValue:EP,queryAllByDisplayValue:qP,getByDisplayValue:OP,getAllByDisplayValue:xP,findAllByDisplayValue:jP,findByDisplayValue:RP,queryByAltText:TP,queryAllByAltText:_P,getByAltText:IP,getAllByAltText:MP,findAllByAltText:BP,findByAltText:FP,queryByTitle:LP,queryAllByTitle:NP,getByTitle:DP,getAllByTitle:UP,findAllByTitle:HP,findByTitle:WP,queryByRole:GP,queryAllByRole:VP,getAllByRole:JP,getByRole:QP,findAllByRole:XP,findByRole:KP,queryByTestId:tC,queryAllByTestId:eC,getByTestId:nC,getAllByTestId:rC,findAllByTestId:oC,findByTestId:aC});function lC(e,t,r){return void 0===t&&(t=iC),void 0===r&&(r={}),Object.keys(t).reduce(((r,n)=>{const o=t[n];return r[n]=o.bind(null,e),r}),r)}const uC=e=>!e||Array.isArray(e)&&!e.length;function sC(e){if(uC(e))throw new Error("The element(s) given to waitForElementToBeRemoved are already removed. waitForElementToBeRemoved requires that the element(s) exist(s) before waiting for removal.")}const cC={copy:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},cut:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},paste:{EventType:"ClipboardEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionEnd:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionStart:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},compositionUpdate:{EventType:"CompositionEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},keyDown:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyPress:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},keyUp:{EventType:"KeyboardEvent",defaultInit:{bubbles:!0,cancelable:!0,charCode:0,composed:!0}},focus:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},blur:{EventType:"FocusEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},focusIn:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},focusOut:{EventType:"FocusEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},change:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},input:{EventType:"InputEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},invalid:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!0}},submit:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},reset:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!0}},click:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,button:0,composed:!0}},contextMenu:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dblClick:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drag:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragEnd:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragEnter:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragExit:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragLeave:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},dragOver:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},dragStart:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},drop:{EventType:"DragEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseDown:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseEnter:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseLeave:{EventType:"MouseEvent",defaultInit:{bubbles:!1,cancelable:!1,composed:!0}},mouseMove:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOut:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseOver:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},mouseUp:{EventType:"MouseEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},select:{EventType:"Event",defaultInit:{bubbles:!0,cancelable:!1}},touchCancel:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},touchEnd:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchMove:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},touchStart:{EventType:"TouchEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},resize:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},scroll:{EventType:"UIEvent",defaultInit:{bubbles:!1,cancelable:!1}},wheel:{EventType:"WheelEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},abort:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlay:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},canPlayThrough:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},durationChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},emptied:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},encrypted:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},ended:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedData:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadedMetadata:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},loadStart:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},pause:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},play:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},playing:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},progress:{EventType:"ProgressEvent",defaultInit:{bubbles:!1,cancelable:!1}},rateChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeked:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},seeking:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},stalled:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},suspend:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},timeUpdate:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},volumeChange:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},waiting:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},load:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},error:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},animationStart:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationEnd:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},animationIteration:{EventType:"AnimationEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionCancel:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionEnd:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!0}},transitionRun:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},transitionStart:{EventType:"TransitionEvent",defaultInit:{bubbles:!0,cancelable:!1}},pointerOver:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerEnter:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},pointerDown:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerMove:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerUp:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerCancel:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},pointerOut:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!0,composed:!0}},pointerLeave:{EventType:"PointerEvent",defaultInit:{bubbles:!1,cancelable:!1}},gotPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},lostPointerCapture:{EventType:"PointerEvent",defaultInit:{bubbles:!0,cancelable:!1,composed:!0}},popState:{EventType:"PopStateEvent",defaultInit:{bubbles:!0,cancelable:!1}},offline:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}},online:{EventType:"Event",defaultInit:{bubbles:!1,cancelable:!1}}},dC={doubleClick:"dblClick"};function pC(e,t){return Lt().eventWrapper((()=>{if(!t)throw new Error("Unable to fire an event - please provide an event object.");if(!e)throw new Error('Unable to fire a "'+t.type+'" event - please provide a DOM element.');return e.dispatchEvent(t)}))}function fC(e,t,r,n){let{EventType:o="Event",defaultInit:a={}}=void 0===n?{}:n;if(!t)throw new Error('Unable to fire a "'+e+'" event - please provide a DOM element.');const i={...a,...r},{target:{value:l,files:u,...s}={}}=i;void 0!==l&&function(e,t){const{set:r}=Object.getOwnPropertyDescriptor(e,"value")||{},n=Object.getPrototypeOf(e),{set:o}=Object.getOwnPropertyDescriptor(n,"value")||{};if(o&&r!==o)o.call(e,t);else{if(!r)throw new Error("The given element does not have a value setter");r.call(e,t)}}(t,l),void 0!==u&&Object.defineProperty(t,"files",{configurable:!0,enumerable:!0,writable:!0,value:u}),Object.assign(t,s);const c=St(t),d=c[o]||c.Event;let p;if("function"==typeof d)p=new d(e,i);else{p=c.document.createEvent(o);const{bubbles:t,cancelable:r,detail:n,...a}=i;p.initEvent(e,t,r,n),Object.keys(a).forEach((e=>{p[e]=a[e]}))}return["dataTransfer","clipboardData"].forEach((e=>{const t=i[e];"object"==typeof t&&("function"==typeof c.DataTransfer?Object.defineProperty(p,e,{value:Object.getOwnPropertyNames(t).reduce(((e,r)=>(Object.defineProperty(e,r,{value:t[r]}),e)),new c.DataTransfer)}):Object.defineProperty(p,e,{value:t}))})),p}Object.keys(cC).forEach((e=>{const{EventType:t,defaultInit:r}=cC[e],n=e.toLowerCase();fC[e]=(e,o)=>fC(n,e,o,{EventType:t,defaultInit:r}),pC[e]=(t,r)=>pC(t,fC[e](t,r))})),Object.keys(dC).forEach((e=>{const t=dC[e];pC[e]=function(){return pC[t](...arguments)}}));var bC={exports:{}};!function(e){var t=function(){var e=String.fromCharCode,t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+-$",n={};function o(e,t){if(!n[e]){n[e]={};for(var r=0;r<e.length;r++)n[e][e.charAt(r)]=r}return n[e][t]}var a={compressToBase64:function(e){if(null==e)return"";var r=a._compress(e,6,(function(e){return t.charAt(e)}));switch(r.length%4){default:case 0:return r;case 1:return r+"===";case 2:return r+"==";case 3:return r+"="}},decompressFromBase64:function(e){return null==e?"":""==e?null:a._decompress(e.length,32,(function(r){return o(t,e.charAt(r))}))},compressToUTF16:function(t){return null==t?"":a._compress(t,15,(function(t){return e(t+32)}))+" "},decompressFromUTF16:function(e){return null==e?"":""==e?null:a._decompress(e.length,16384,(function(t){return e.charCodeAt(t)-32}))},compressToUint8Array:function(e){for(var t=a.compress(e),r=new Uint8Array(2*t.length),n=0,o=t.length;n<o;n++){var i=t.charCodeAt(n);r[2*n]=i>>>8,r[2*n+1]=i%256}return r},decompressFromUint8Array:function(t){if(null==t)return a.decompress(t);for(var r=new Array(t.length/2),n=0,o=r.length;n<o;n++)r[n]=256*t[2*n]+t[2*n+1];var i=[];return r.forEach((function(t){i.push(e(t))})),a.decompress(i.join(""))},compressToEncodedURIComponent:function(e){return null==e?"":a._compress(e,6,(function(e){return r.charAt(e)}))},decompressFromEncodedURIComponent:function(e){return null==e?"":""==e?null:(e=e.replace(/ /g,"+"),a._decompress(e.length,32,(function(t){return o(r,e.charAt(t))})))},compress:function(t){return a._compress(t,16,(function(t){return e(t)}))},_compress:function(e,t,r){if(null==e)return"";var n,o,a,i={},l={},u="",s="",c="",d=2,p=3,f=2,b=[],m=0,y=0;for(a=0;a<e.length;a+=1)if(u=e.charAt(a),Object.prototype.hasOwnProperty.call(i,u)||(i[u]=p++,l[u]=!0),s=c+u,Object.prototype.hasOwnProperty.call(i,s))c=s;else{if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,y==t-1?(y=0,b.push(r(m)),m=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)m=m<<1|o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++),i[s]=p++,c=String(u)}if(""!==c){if(Object.prototype.hasOwnProperty.call(l,c)){if(c.charCodeAt(0)<256){for(n=0;n<f;n++)m<<=1,y==t-1?(y=0,b.push(r(m)),m=0):y++;for(o=c.charCodeAt(0),n=0;n<8;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}else{for(o=1,n=0;n<f;n++)m=m<<1|o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o=0;for(o=c.charCodeAt(0),n=0;n<16;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1}0==--d&&(d=Math.pow(2,f),f++),delete l[c]}else for(o=i[c],n=0;n<f;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1;0==--d&&(d=Math.pow(2,f),f++)}for(o=2,n=0;n<f;n++)m=m<<1|1&o,y==t-1?(y=0,b.push(r(m)),m=0):y++,o>>=1;for(;;){if(m<<=1,y==t-1){b.push(r(m));break}y++}return b.join("")},decompress:function(e){return null==e?"":""==e?null:a._decompress(e.length,32768,(function(t){return e.charCodeAt(t)}))},_decompress:function(t,r,n){var o,a,i,l,u,s,c,d=[],p=4,f=4,b=3,m="",y=[],v={val:n(0),position:r,index:1};for(o=0;o<3;o+=1)d[o]=o;for(i=0,u=Math.pow(2,2),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;c=e(i);break;case 2:return""}for(d[3]=c,a=c,y.push(c);;){if(v.index>t)return"";for(i=0,u=Math.pow(2,b),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;switch(c=i){case 0:for(i=0,u=Math.pow(2,8),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 1:for(i=0,u=Math.pow(2,16),s=1;s!=u;)l=v.val&v.position,v.position>>=1,0==v.position&&(v.position=r,v.val=n(v.index++)),i|=(l>0?1:0)*s,s<<=1;d[f++]=e(i),c=f-1,p--;break;case 2:return y.join("")}if(0==p&&(p=Math.pow(2,b),b++),d[c])m=d[c];else{if(c!==f)return null;m=a+a.charAt(0)}y.push(m),d[f++]=a+m.charAt(0),a=m,0==--p&&(p=Math.pow(2,b),b++)}}};return a}();null!=e?e.exports=t:"undefined"!=typeof angular&&null!=angular&&angular.module("LZString",[]).factory("LZString",(function(){return t}))}(bC);var mC=bC.exports;function yC(e){return"https://testing-playground.com/#markup="+(t=e,mC.compressToEncodedURIComponent(t.replace(/[ \t]*[\n][ \t]*/g,"\n")));var t}const vC={debug:(e,t,r)=>Array.isArray(e)?e.forEach((e=>kt(e,t,r))):kt(e,t,r),logTestingPlaygroundURL:function(e){if(void 0===e&&(e=Rt().body),!e||!("innerHTML"in e))return void console.log("The element you're providing isn't a valid DOM element.");if(!e.innerHTML)return void console.log("The provided element doesn't have any children.");const t=yC(e.innerHTML);return console.log("Open this URL in your browser\n\n"+t),t}},hC="undefined"!=typeof document&&document.body?lC(document.body,iC,vC):Object.keys(iC).reduce(((e,t)=>(e[t]=()=>{throw new TypeError("For queries bound to document.body a global document has to be available... Learn more: https://testing-library.com/s/screen-global-error")},e)),vC);e.buildQueries=Qg,e.configure=function(e){"function"==typeof e&&(e=e(Nt)),Nt={...Nt,...e}},e.createEvent=fC,e.findAllByAltText=BP,e.findAllByDisplayValue=jP,e.findAllByLabelText=nP,e.findAllByPlaceholderText=fP,e.findAllByRole=XP,e.findAllByTestId=oC,e.findAllByText=PP,e.findAllByTitle=HP,e.findByAltText=FP,e.findByDisplayValue=RP,e.findByLabelText=oP,e.findByPlaceholderText=bP,e.findByRole=KP,e.findByTestId=aC,e.findByText=CP,e.findByTitle=WP,e.fireEvent=pC,e.getAllByAltText=MP,e.getAllByDisplayValue=xP,e.getAllByLabelText=aP,e.getAllByPlaceholderText=dP,e.getAllByRole=JP,e.getAllByTestId=rC,e.getAllByText=hP,e.getAllByTitle=UP,e.getByAltText=IP,e.getByDisplayValue=OP,e.getByLabelText=iP,e.getByPlaceholderText=pP,e.getByRole=QP,e.getByTestId=nC,e.getByText=gP,e.getByTitle=DP,e.getConfig=Lt,e.getDefaultNormalizer=Jt,e.getElementError=Lg,e.getMultipleElementsFoundError=Ug,e.getNodeText=Kt,e.getQueriesForElement=lC,e.getRoles=Rg,e.getSuggestedQuery=Bg,e.isInaccessible=Og,e.logDOM=kt,e.logRoles=function(e,t){let{hidden:r=!1}=void 0===t?{}:t;return console.log(Sg(e,{hidden:r}))},e.makeFindQuery=Vg,e.makeGetAllQuery=$g,e.makeSingleQuery=Wg,e.prettyDOM=Ft,e.prettyFormat=lt,e.queries=iC,e.queryAllByAltText=_P,e.queryAllByAttribute=Dg,e.queryAllByDisplayValue=qP,e.queryAllByLabelText=lP,e.queryAllByPlaceholderText=sP,e.queryAllByRole=VP,e.queryAllByTestId=eC,e.queryAllByText=yP,e.queryAllByTitle=NP,e.queryByAltText=TP,e.queryByAttribute=Hg,e.queryByDisplayValue=EP,e.queryByLabelText=tP,e.queryByPlaceholderText=cP,e.queryByRole=GP,e.queryByTestId=tC,e.queryByText=vP,e.queryByTitle=LP,e.queryHelpers=Xg,e.screen=hC,e.waitFor=Ng,e.waitForElementToBeRemoved=async function(e,t){const r=new Error("Timed out in waitForElementToBeRemoved.");if("function"!=typeof e){sC(e);const t=(Array.isArray(e)?e:[e]).map((e=>{let t=e.parentElement;if(null===t)return()=>null;for(;t.parentElement;)t=t.parentElement;return()=>t.contains(e)?e:null}));e=()=>t.map((e=>e())).filter(Boolean)}return sC(e()),Ng((()=>{let t;try{t=e()}catch(e){if("TestingLibraryElementError"===e.name)return;throw e}if(!uC(t))throw r}),t)},e.within=lC,e.wrapAllByQueryWithSuggestion=Jg,e.wrapSingleQueryWithSuggestion=Gg,Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=dom.umd.min.js.map
