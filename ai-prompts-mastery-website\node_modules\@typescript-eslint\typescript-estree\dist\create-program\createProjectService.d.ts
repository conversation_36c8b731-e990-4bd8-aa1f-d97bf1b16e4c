import type * as ts from 'typescript/lib/tsserverlibrary';
import type { ProjectServiceOptions } from '../parser-options';
export type TypeScriptProjectService = ts.server.ProjectService;
export interface ProjectServiceSettings {
    allowDefaultProjectForFiles: string[] | undefined;
    service: TypeScriptProjectService;
}
export declare function createProjectService(options: boolean | ProjectServiceOptions | undefined, jsDocParsingMode: ts.JSDocParsingMode | undefined): ProjectServiceSettings;
//# sourceMappingURL=createProjectService.d.ts.map