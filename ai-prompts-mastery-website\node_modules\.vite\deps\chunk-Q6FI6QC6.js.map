{"version": 3, "sources": ["../../refractor/lang/wolfram.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = wolfram\nwolfram.displayName = 'wolfram'\nwolfram.aliases = ['mathematica', 'wl', 'nb']\nfunction wolfram(Prism) {\n  Prism.languages.wolfram = {\n    // Allow one level of nesting - note: regex taken from applescipt\n    comment: /\\(\\*(?:\\(\\*(?:[^*]|\\*(?!\\)))*\\*\\)|(?!\\(\\*)[\\s\\S])*?\\*\\)/,\n    string: {\n      pattern: /\"(?:\\\\.|[^\"\\\\\\r\\n])*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:Abs|AbsArg|Accuracy|Block|Do|For|Function|If|Manipulate|Module|Nest|NestList|None|Return|Switch|Table|Which|While)\\b/,\n    context: {\n      pattern: /\\b\\w+`+\\w*/,\n      alias: 'class-name'\n    },\n    blank: {\n      pattern: /\\b\\w+_\\b/,\n      alias: 'regex'\n    },\n    'global-variable': {\n      pattern: /\\$\\w+/,\n      alias: 'variable'\n    },\n    boolean: /\\b(?:False|True)\\b/,\n    number:\n      /(?:\\b(?=\\d)|\\B(?=\\.))(?:0[bo])?(?:(?:\\d|0x[\\da-f])[\\da-f]*(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?j?\\b/i,\n    operator:\n      /\\/\\.|;|=\\.|\\^=|\\^:=|:=|<<|>>|<\\||\\|>|:>|\\|->|->|<-|@@@|@@|@|\\/@|=!=|===|==|=|\\+|-|\\^|\\[\\/-+%=\\]=?|!=|\\*\\*?=?|\\/\\/?=?|<[<=>]?|>[=>]?|[&|^~]/,\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.mathematica = Prism.languages.wolfram\n  Prism.languages.wl = Prism.languages.wolfram\n  Prism.languages.nb = Prism.languages.wolfram\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC,eAAe,MAAM,IAAI;AAC5C,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA;AAAA,QAExB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,UACP,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,SAAS;AAAA,QACT,QACE;AAAA,QACF,UACE;AAAA,QACF,aAAa;AAAA,MACf;AACA,YAAM,UAAU,cAAc,MAAM,UAAU;AAC9C,YAAM,UAAU,KAAK,MAAM,UAAU;AACrC,YAAM,UAAU,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA;AAAA;", "names": []}