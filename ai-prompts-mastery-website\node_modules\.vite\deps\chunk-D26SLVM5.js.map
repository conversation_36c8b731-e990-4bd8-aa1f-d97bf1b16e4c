{"version": 3, "sources": ["../../refractor/lang/sml.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = sml\nsml.displayName = 'sml'\nsml.aliases = ['smlnj']\nfunction sml(Prism) {\n  // https://smlfamily.github.io/sml97-defn.pdf\n  // https://people.mpi-sws.org/~rossberg/sml.html\n  ;(function (Prism) {\n    var keywords =\n      /\\b(?:abstype|and|andalso|as|case|datatype|do|else|end|eqtype|exception|fn|fun|functor|handle|if|in|include|infix|infixr|let|local|nonfix|of|op|open|orelse|raise|rec|sharing|sig|signature|struct|structure|then|type|val|where|while|with|withtype)\\b/i\n    Prism.languages.sml = {\n      // allow one level of nesting\n      comment:\n        /\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*)|\\(\\*(?:[^*(]|\\*(?!\\))|\\((?!\\*))*\\*\\))*\\*\\)/,\n      string: {\n        pattern: /#?\"(?:[^\"\\\\]|\\\\.)*\"/,\n        greedy: true\n      },\n      'class-name': [\n        {\n          // This is only an approximation since the real grammar is context-free\n          //\n          // Why the main loop so complex?\n          // The main loop is approximately the same as /(?:\\s*(?:[*,]|->)\\s*<TERMINAL>)*/ which is, obviously, a lot\n          // simpler. The difference is that if a comma is the last iteration of the loop, then the terminal must be\n          // followed by a long identifier.\n          pattern: RegExp(\n            /((?:^|[^:]):\\s*)<TERMINAL>(?:\\s*(?:(?:\\*|->)\\s*<TERMINAL>|,\\s*<TERMINAL>(?:(?=<NOT-LAST>)|(?!<NOT-LAST>)\\s+<LONG-ID>)))*/.source\n              .replace(/<NOT-LAST>/g, function () {\n                return /\\s*(?:[*,]|->)/.source\n              })\n              .replace(/<TERMINAL>/g, function () {\n                return /(?:'[\\w']*|<LONG-ID>|\\((?:[^()]|\\([^()]*\\))*\\)|\\{(?:[^{}]|\\{[^{}]*\\})*\\})(?:\\s+<LONG-ID>)*/\n                  .source\n              })\n              .replace(/<LONG-ID>/g, function () {\n                return /(?!<KEYWORD>)[a-z\\d_][\\w'.]*/.source\n              })\n              .replace(/<KEYWORD>/g, function () {\n                return keywords.source\n              }),\n            'i'\n          ),\n          lookbehind: true,\n          greedy: true,\n          inside: null // see below\n        },\n        {\n          pattern:\n            /((?:^|[^\\w'])(?:datatype|exception|functor|signature|structure|type)\\s+)[a-z_][\\w'.]*/i,\n          lookbehind: true\n        }\n      ],\n      function: {\n        pattern: /((?:^|[^\\w'])fun\\s+)[a-z_][\\w'.]*/i,\n        lookbehind: true\n      },\n      keyword: keywords,\n      variable: {\n        pattern: /(^|[^\\w'])'[\\w']*/,\n        lookbehind: true\n      },\n      number: /~?\\b(?:\\d+(?:\\.\\d+)?(?:e~?\\d+)?|0x[\\da-f]+)\\b/i,\n      word: {\n        pattern: /\\b0w(?:\\d+|x[\\da-f]+)\\b/i,\n        alias: 'constant'\n      },\n      boolean: /\\b(?:false|true)\\b/i,\n      operator: /\\.\\.\\.|:[>=:]|=>?|->|[<>]=?|[!+\\-*/^#|@~]/,\n      punctuation: /[(){}\\[\\].:,;]/\n    }\n    Prism.languages.sml['class-name'][0].inside = Prism.languages.sml\n    Prism.languages.smlnj = Prism.languages.sml\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC,OAAO;AACtB,aAAS,IAAI,OAAO;AAGlB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,WACF;AACF,QAAAA,OAAM,UAAU,MAAM;AAAA;AAAA,UAEpB,SACE;AAAA,UACF,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,cAAc;AAAA,YACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAOE,SAAS;AAAA,gBACP,2HAA2H,OACxH,QAAQ,eAAe,WAAY;AAClC,yBAAO,iBAAiB;AAAA,gBAC1B,CAAC,EACA,QAAQ,eAAe,WAAY;AAClC,yBAAO,6FACJ;AAAA,gBACL,CAAC,EACA,QAAQ,cAAc,WAAY;AACjC,yBAAO,+BAA+B;AAAA,gBACxC,CAAC,EACA,QAAQ,cAAc,WAAY;AACjC,yBAAO,SAAS;AAAA,gBAClB,CAAC;AAAA,gBACH;AAAA,cACF;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,QAAQ;AAAA;AAAA,YACV;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,QAAQ;AAAA,UACR,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,UACT,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,UAAU,IAAI,YAAY,EAAE,CAAC,EAAE,SAASA,OAAM,UAAU;AAC9D,QAAAA,OAAM,UAAU,QAAQA,OAAM,UAAU;AAAA,MAC1C,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}