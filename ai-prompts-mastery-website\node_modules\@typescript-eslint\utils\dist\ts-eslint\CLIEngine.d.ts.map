{"version": 3, "file": "CLIEngine.d.ts", "sourceRoot": "", "sources": ["../../src/ts-eslint/CLIEngine.ts"], "names": [], "mappings": "AAKA,OAAO,KAAK,EAAE,MAAM,EAAE,MAAM,UAAU,CAAC;AACvC,OAAO,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AAmGvD,kBAAU,SAAS,CAAC;IAClB,UAAiB,OAAO;QACtB,iBAAiB,CAAC,EAAE,OAAO,CAAC;QAC5B,UAAU,CAAC,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC7C,KAAK,CAAC,EAAE,OAAO,CAAC;QAChB,SAAS,CAAC,EAAE,MAAM,CAAC;QACnB,aAAa,CAAC,EAAE,MAAM,CAAC;QACvB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,GAAG,CAAC,EAAE,MAAM,CAAC;QACb,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC;QAChB,uBAAuB,CAAC,EAAE,OAAO,CAAC;QAClC,UAAU,CAAC,EAAE,MAAM,EAAE,CAAC;QACtB,GAAG,CAAC,EAAE,OAAO,CAAC;QACd,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB,MAAM,CAAC,EAAE,OAAO,CAAC;QACjB,UAAU,CAAC,EAAE,MAAM,CAAC;QACpB,aAAa,CAAC,EAAE,MAAM,EAAE,GAAG,MAAM,CAAC;QAClC,WAAW,CAAC,EAAE,OAAO,CAAC;QACtB,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,aAAa,CAAC,EAAE,MAAM,CAAC,aAAa,CAAC;QACrC,OAAO,CAAC,EAAE,MAAM,EAAE,CAAC;QACnB,wBAAwB,CAAC,EAAE,MAAM,CAAC;QAClC,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;QACtE,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC;QACrB,6BAA6B,CAAC,EAAE,OAAO,CAAC;KACzC;IAED,UAAiB,UAAU;QACzB,QAAQ,EAAE,MAAM,CAAC;QACjB,QAAQ,EAAE,MAAM,CAAC,WAAW,EAAE,CAAC;QAC/B,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,MAAM,CAAC,EAAE,MAAM,CAAC;QAChB,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,UAAU;QACzB,OAAO,EAAE,UAAU,EAAE,CAAC;QACtB,UAAU,EAAE,MAAM,CAAC;QACnB,YAAY,EAAE,MAAM,CAAC;QACrB,iBAAiB,EAAE,MAAM,CAAC;QAC1B,mBAAmB,EAAE,MAAM,CAAC;QAC5B,mBAAmB,EAAE,iBAAiB,EAAE,CAAC;KAC1C;IAED,UAAiB,iBAAiB;QAChC,MAAM,EAAE,MAAM,CAAC;QACf,UAAU,EAAE,MAAM,EAAE,CAAC;KACtB;IAED,UAAiB,cAAc,CAAC,WAAW,SAAS,MAAM;QACxD,SAAS,EAAE,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC,CAAC;KACtD;IAED,KAAY,SAAS,GAAG,CAAC,WAAW,SAAS,MAAM,EACjD,OAAO,EAAE,UAAU,EAAE,EACrB,IAAI,CAAC,EAAE,cAAc,CAAC,WAAW,CAAC,KAC/B,MAAM,CAAC;CACb;AAED;;;;;GAKG;AACH,QAAA,MAAM,SAAS;kBAhKQ,UAAU,OAAO;QAEtC;;;;WAIG;wBACa,MAAM,gBAAgB,OAAO,MAAM,GAAG,IAAI;QAE1D;;;;WAIG;iCACsB,MAAM,EAAE,GAAG,UAAU,UAAU;QAExD;;;;;;WAMG;4BAEK,MAAM,aACD,MAAM,gBACH,OAAO,GACpB,UAAU,UAAU;QAEvB;;;;;WAKG;mCACwB,MAAM,GAAG,OAAO,MAAM;QAEjD;;;;WAIG;8BACmB,MAAM,GAAG,UAAU,SAAS;QAElD;;;;WAIG;gCACqB,MAAM,GAAG,OAAO;QAExC;;;;WAIG;0CAC+B,MAAM,EAAE,GAAG,MAAM,EAAE;;;IAWrD;;;;OAIG;6BAEQ,UAAU,UAAU,EAAE,GAC9B,UAAU,UAAU,EAAE;IAEzB;;;;OAIG;0BAC0B,MAAM,GAAG,UAAU,SAAS;IAEzD;;;OAGG;wBACwB,UAAU,UAAU,GAAG,IAAI;aAEtC,MAAM;aAyEX,CAAC;AAEd,OAAO,EAAE,SAAS,EAAE,CAAC"}