{"name": "babel-plugin-polyfill-regenerator", "version": "0.6.4", "description": "A Babel plugin to inject imports to regenerator-runtime", "repository": {"type": "git", "url": "https://github.com/babel/babel-polyfills.git", "directory": "packages/babel-plugin-polyfill-regenerator"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "lib/index.js", "exports": {".": [{"import": "./esm/index.mjs", "default": "./lib/index.js"}, "./lib/index.js"], "./package.json": "./package.json"}, "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.4"}, "devDependencies": {"@babel/core": "^7.17.8", "@babel/helper-plugin-test-runner": "^7.16.7", "@babel/plugin-transform-regenerator": "~7.14.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}, "gitHead": "d87c29c909148920ad18690b63d450c561842298"}