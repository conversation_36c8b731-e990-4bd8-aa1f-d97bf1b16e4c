class ParseError extends Error{sourceStart;sourceEnd;parserState;constructor(e,n,t,o){super(e),this.name="ParseError",this.sourceStart=n,this.sourceEnd=t,this.parserState=o}}class ParseErrorWithToken extends ParseError{token;constructor(e,n,t,o,r){super(e,n,t,o),this.token=r}}const e={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'},n="undefined"!=typeof globalThis&&"structuredClone"in globalThis;function cloneTokens(e){return n?structuredClone(e):JSON.parse(JSON.stringify(e))}function stringify(...e){let n="";for(let t=0;t<e.length;t++)n+=e[t][1];return n}const t=13,o=45,r=10,i=43,s=65533;function checkIfFourCodePointsWouldStartCDO(e){return 60===e.source.codePointAt(e.cursor)&&33===e.source.codePointAt(e.cursor+1)&&e.source.codePointAt(e.cursor+2)===o&&e.source.codePointAt(e.cursor+3)===o}function isDigitCodePoint(e){return e>=48&&e<=57}function isUppercaseLetterCodePoint(e){return e>=65&&e<=90}function isLowercaseLetterCodePoint(e){return e>=97&&e<=122}function isHexDigitCodePoint(e){return e>=48&&e<=57||e>=97&&e<=102||e>=65&&e<=70}function isLetterCodePoint(e){return isLowercaseLetterCodePoint(e)||isUppercaseLetterCodePoint(e)}function isIdentStartCodePoint(e){return isLetterCodePoint(e)||isNonASCII_IdentCodePoint(e)||95===e}function isIdentCodePoint(e){return isIdentStartCodePoint(e)||isDigitCodePoint(e)||e===o}function isNonASCII_IdentCodePoint(e){return 183===e||8204===e||8205===e||8255===e||8256===e||8204===e||(192<=e&&e<=214||216<=e&&e<=246||248<=e&&e<=893||895<=e&&e<=8191||8304<=e&&e<=8591||11264<=e&&e<=12271||12289<=e&&e<=55295||63744<=e&&e<=64975||65008<=e&&e<=65533||(0===e||(!!isSurrogate(e)||e>=65536)))}function isNewLine(e){return e===r||e===t||12===e}function isWhitespace(e){return 32===e||e===r||9===e||e===t||12===e}function isSurrogate(e){return e>=55296&&e<=57343}function checkIfTwoCodePointsAreAValidEscape(e){return 92===e.source.codePointAt(e.cursor)&&!isNewLine(e.source.codePointAt(e.cursor+1)??-1)}function checkIfThreeCodePointsWouldStartAnIdentSequence(e,n){return n.source.codePointAt(n.cursor)===o?n.source.codePointAt(n.cursor+1)===o||(!!isIdentStartCodePoint(n.source.codePointAt(n.cursor+1)??-1)||92===n.source.codePointAt(n.cursor+1)&&!isNewLine(n.source.codePointAt(n.cursor+2)??-1)):!!isIdentStartCodePoint(n.source.codePointAt(n.cursor)??-1)||checkIfTwoCodePointsAreAValidEscape(n)}function checkIfThreeCodePointsWouldStartANumber(e){return e.source.codePointAt(e.cursor)===i||e.source.codePointAt(e.cursor)===o?!!isDigitCodePoint(e.source.codePointAt(e.cursor+1)??-1)||46===e.source.codePointAt(e.cursor+1)&&isDigitCodePoint(e.source.codePointAt(e.cursor+2)??-1):46===e.source.codePointAt(e.cursor)?isDigitCodePoint(e.source.codePointAt(e.cursor+1)??-1):isDigitCodePoint(e.source.codePointAt(e.cursor)??-1)}function checkIfTwoCodePointsStartAComment(e){return 47===e.source.codePointAt(e.cursor)&&42===e.source.codePointAt(e.cursor+1)}function checkIfThreeCodePointsWouldStartCDC(e){return e.source.codePointAt(e.cursor)===o&&e.source.codePointAt(e.cursor+1)===o&&62===e.source.codePointAt(e.cursor+2)}var c,a,u;function mirrorVariantType(e){switch(e){case c.OpenParen:return c.CloseParen;case c.CloseParen:return c.OpenParen;case c.OpenCurly:return c.CloseCurly;case c.CloseCurly:return c.OpenCurly;case c.OpenSquare:return c.CloseSquare;case c.CloseSquare:return c.OpenSquare;default:return null}}function mirrorVariant(e){switch(e[0]){case c.OpenParen:return[c.CloseParen,")",-1,-1,void 0];case c.CloseParen:return[c.OpenParen,"(",-1,-1,void 0];case c.OpenCurly:return[c.CloseCurly,"}",-1,-1,void 0];case c.CloseCurly:return[c.OpenCurly,"{",-1,-1,void 0];case c.OpenSquare:return[c.CloseSquare,"]",-1,-1,void 0];case c.CloseSquare:return[c.OpenSquare,"[",-1,-1,void 0];default:return null}}function consumeComment(n,t){for(t.advanceCodePoint(2);;){const o=t.readCodePoint();if(void 0===o){const o=[c.Comment,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInComment,t.representationStart,t.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],o)),o}if(42===o&&(void 0!==t.source.codePointAt(t.cursor)&&47===t.source.codePointAt(t.cursor))){t.advanceCodePoint();break}}return[c.Comment,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0]}function consumeEscapedCodePoint(n,o){const i=o.readCodePoint();if(void 0===i)return n.onParseError(new ParseError(e.UnexpectedEOFInEscapedCodePoint,o.representationStart,o.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),s;if(isHexDigitCodePoint(i)){const e=[i];let n;for(;void 0!==(n=o.source.codePointAt(o.cursor))&&isHexDigitCodePoint(n)&&e.length<6;)e.push(n),o.advanceCodePoint();isWhitespace(o.source.codePointAt(o.cursor)??-1)&&(o.source.codePointAt(o.cursor)===t&&o.source.codePointAt(o.cursor+1)===r&&o.advanceCodePoint(),o.advanceCodePoint());const c=parseInt(String.fromCodePoint(...e),16);return 0===c||isSurrogate(c)||c>1114111?s:c}return 0===i||isSurrogate(i)?s:i}function consumeIdentSequence(e,n){const t=[];for(;;){const o=n.source.codePointAt(n.cursor)??-1;if(0===o||isSurrogate(o))t.push(s),n.advanceCodePoint(+(o>65535)+1);else if(isIdentCodePoint(o))t.push(o),n.advanceCodePoint(+(o>65535)+1);else{if(!checkIfTwoCodePointsAreAValidEscape(n))return t;n.advanceCodePoint(),t.push(consumeEscapedCodePoint(e,n))}}}function consumeHashToken(e,n){n.advanceCodePoint();const t=n.source.codePointAt(n.cursor);if(void 0!==t&&(isIdentCodePoint(t)||checkIfTwoCodePointsAreAValidEscape(n))){let t=u.Unrestricted;checkIfThreeCodePointsWouldStartAnIdentSequence(0,n)&&(t=u.ID);const o=consumeIdentSequence(e,n);return[c.Hash,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...o),type:t}]}return[c.Delim,"#",n.representationStart,n.representationEnd,{value:"#"}]}function consumeNumber(e,n){let t=a.Integer;for(n.source.codePointAt(n.cursor)!==i&&n.source.codePointAt(n.cursor)!==o||n.advanceCodePoint();isDigitCodePoint(n.source.codePointAt(n.cursor)??-1);)n.advanceCodePoint();if(46===n.source.codePointAt(n.cursor)&&isDigitCodePoint(n.source.codePointAt(n.cursor+1)??-1))for(n.advanceCodePoint(2),t=a.Number;isDigitCodePoint(n.source.codePointAt(n.cursor)??-1);)n.advanceCodePoint();if(101===n.source.codePointAt(n.cursor)||69===n.source.codePointAt(n.cursor)){if(isDigitCodePoint(n.source.codePointAt(n.cursor+1)??-1))n.advanceCodePoint(2);else{if(n.source.codePointAt(n.cursor+1)!==o&&n.source.codePointAt(n.cursor+1)!==i||!isDigitCodePoint(n.source.codePointAt(n.cursor+2)??-1))return t;n.advanceCodePoint(3)}for(t=a.Number;isDigitCodePoint(n.source.codePointAt(n.cursor)??-1);)n.advanceCodePoint()}return t}function consumeNumericToken(e,n){let t;{const e=n.source.codePointAt(n.cursor);e===o?t="-":e===i&&(t="+")}const r=consumeNumber(0,n),s=parseFloat(n.source.slice(n.representationStart,n.representationEnd+1));if(checkIfThreeCodePointsWouldStartAnIdentSequence(0,n)){const o=consumeIdentSequence(e,n);return[c.Dimension,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:s,signCharacter:t,type:r,unit:String.fromCodePoint(...o)}]}return 37===n.source.codePointAt(n.cursor)?(n.advanceCodePoint(),[c.Percentage,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:s,signCharacter:t}]):[c.Number,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:s,signCharacter:t,type:r}]}function consumeWhiteSpace(e){for(;isWhitespace(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();return[c.Whitespace,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}!function(e){e.Comment="comment",e.AtKeyword="at-keyword-token",e.BadString="bad-string-token",e.BadURL="bad-url-token",e.CDC="CDC-token",e.CDO="CDO-token",e.Colon="colon-token",e.Comma="comma-token",e.Delim="delim-token",e.Dimension="dimension-token",e.EOF="EOF-token",e.Function="function-token",e.Hash="hash-token",e.Ident="ident-token",e.Number="number-token",e.Percentage="percentage-token",e.Semicolon="semicolon-token",e.String="string-token",e.URL="url-token",e.Whitespace="whitespace-token",e.OpenParen="(-token",e.CloseParen=")-token",e.OpenSquare="[-token",e.CloseSquare="]-token",e.OpenCurly="{-token",e.CloseCurly="}-token",e.UnicodeRange="unicode-range-token"}(c||(c={})),function(e){e.Integer="integer",e.Number="number"}(a||(a={})),function(e){e.Unrestricted="unrestricted",e.ID="id"}(u||(u={}));class Reader{cursor=0;source="";representationStart=0;representationEnd=-1;constructor(e){this.source=e}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.cursor-1}readCodePoint(){const e=this.source.codePointAt(this.cursor);if(void 0!==e)return this.cursor=this.cursor+1,this.representationEnd=this.cursor-1,e}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.cursor-1}resetRepresentation(){this.representationStart=this.cursor,this.representationEnd=-1}}function consumeStringToken(n,o){let i="";const a=o.readCodePoint();for(;;){const u=o.readCodePoint();if(void 0===u){const t=[c.String,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:i}];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInString,o.representationStart,o.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],t)),t}if(isNewLine(u)){o.unreadCodePoint();const i=[c.BadString,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.UnexpectedNewLineInString,o.representationStart,o.source.codePointAt(o.cursor)===t&&o.source.codePointAt(o.cursor+1)===r?o.representationEnd+2:o.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],i)),i}if(u===a)return[c.String,o.source.slice(o.representationStart,o.representationEnd+1),o.representationStart,o.representationEnd,{value:i}];if(92!==u)0===u||isSurrogate(u)?i+=String.fromCodePoint(s):i+=String.fromCodePoint(u);else{if(void 0===o.source.codePointAt(o.cursor))continue;if(isNewLine(o.source.codePointAt(o.cursor)??-1)){o.source.codePointAt(o.cursor)===t&&o.source.codePointAt(o.cursor+1)===r&&o.advanceCodePoint(),o.advanceCodePoint();continue}i+=String.fromCodePoint(consumeEscapedCodePoint(n,o))}}}function checkIfCodePointsMatchURLIdent(e){return!(3!==e.length||117!==e[0]&&85!==e[0]||114!==e[1]&&82!==e[1]||108!==e[2]&&76!==e[2])}function consumeBadURL(e,n){for(;;){const t=n.source.codePointAt(n.cursor);if(void 0===t)return;if(41===t)return void n.advanceCodePoint();checkIfTwoCodePointsAreAValidEscape(n)?(n.advanceCodePoint(),consumeEscapedCodePoint(e,n)):n.advanceCodePoint()}}function consumeUrlToken(n,t){for(;isWhitespace(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();let o="";for(;;){if(void 0===t.source.codePointAt(t.cursor)){const r=[c.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:o}];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],r)),r}if(41===t.source.codePointAt(t.cursor))return t.advanceCodePoint(),[c.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:o}];if(isWhitespace(t.source.codePointAt(t.cursor)??-1)){for(t.advanceCodePoint();isWhitespace(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();if(void 0===t.source.codePointAt(t.cursor)){const r=[c.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:o}];return n.onParseError(new ParseErrorWithToken(e.UnexpectedEOFInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],r)),r}return 41===t.source.codePointAt(t.cursor)?(t.advanceCodePoint(),[c.URL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,{value:o}]):(consumeBadURL(n,t),[c.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0])}const i=t.source.codePointAt(t.cursor);if(34===i||39===i||40===i||(11===(r=i??-1)||127===r||0<=r&&r<=8||14<=r&&r<=31)){consumeBadURL(n,t);const o=[c.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.UnexpectedCharacterInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","Unexpected U+0022 QUOTATION MARK (\"), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point"],o)),o}if(92===i){if(checkIfTwoCodePointsAreAValidEscape(t)){t.advanceCodePoint(),o+=String.fromCodePoint(consumeEscapedCodePoint(n,t));continue}consumeBadURL(n,t);const r=[c.BadURL,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0];return n.onParseError(new ParseErrorWithToken(e.InvalidEscapeSequenceInURL,t.representationStart,t.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],r)),r}0===t.source.codePointAt(t.cursor)||isSurrogate(t.source.codePointAt(t.cursor)??-1)?(o+=String.fromCodePoint(s),t.advanceCodePoint()):(o+=t.source[t.cursor],t.advanceCodePoint())}var r}function consumeIdentLikeToken(e,n){const t=consumeIdentSequence(e,n);if(40!==n.source.codePointAt(n.cursor))return[c.Ident,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t)}];if(checkIfCodePointsMatchURLIdent(t)){n.advanceCodePoint();let o=0;for(;;){const e=isWhitespace(n.source.codePointAt(n.cursor)??-1),r=isWhitespace(n.source.codePointAt(n.cursor+1)??-1);if(e&&r){o+=1,n.advanceCodePoint(1);continue}const i=e?n.source.codePointAt(n.cursor+1):n.source.codePointAt(n.cursor);if(34===i||39===i)return o>0&&n.unreadCodePoint(o),[c.Function,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t)}];break}return consumeUrlToken(e,n)}return n.advanceCodePoint(),[c.Function,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{value:String.fromCodePoint(...t)}]}function checkIfThreeCodePointsWouldStartAUnicodeRange(e){return!(117!==e.source.codePointAt(e.cursor)&&85!==e.source.codePointAt(e.cursor)||e.source.codePointAt(e.cursor+1)!==i||63!==e.source.codePointAt(e.cursor+2)&&!isHexDigitCodePoint(e.source.codePointAt(e.cursor+2)??-1))}function consumeUnicodeRangeToken(e,n){n.advanceCodePoint(2);const t=[],r=[];let i;for(;void 0!==(i=n.source.codePointAt(n.cursor))&&t.length<6&&isHexDigitCodePoint(i);)t.push(i),n.advanceCodePoint();for(;void 0!==(i=n.source.codePointAt(n.cursor))&&t.length<6&&63===i;)0===r.length&&r.push(...t),t.push(48),r.push(70),n.advanceCodePoint();if(!r.length&&n.source.codePointAt(n.cursor)===o&&isHexDigitCodePoint(n.source.codePointAt(n.cursor+1)??-1))for(n.advanceCodePoint();void 0!==(i=n.source.codePointAt(n.cursor))&&r.length<6&&isHexDigitCodePoint(i);)r.push(i),n.advanceCodePoint();if(!r.length){const e=parseInt(String.fromCodePoint(...t),16);return[c.UnicodeRange,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{startOfRange:e,endOfRange:e}]}const s=parseInt(String.fromCodePoint(...t),16),a=parseInt(String.fromCodePoint(...r),16);return[c.UnicodeRange,n.source.slice(n.representationStart,n.representationEnd+1),n.representationStart,n.representationEnd,{startOfRange:s,endOfRange:a}]}function tokenize(e,n){const t=tokenizer(e,n),o=[];for(;!t.endOfFile();)o.push(t.nextToken());return o.push(t.nextToken()),o}function tokenizer(n,s){const a=n.css.valueOf(),u=n.unicodeRangesAllowed??!1,d=new Reader(a),p={onParseError:s?.onParseError??noop};return{nextToken:function nextToken(){d.resetRepresentation();const n=d.source.codePointAt(d.cursor);if(void 0===n)return[c.EOF,"",-1,-1,void 0];if(47===n&&checkIfTwoCodePointsStartAComment(d))return consumeComment(p,d);if(u&&(117===n||85===n)&&checkIfThreeCodePointsWouldStartAUnicodeRange(d))return consumeUnicodeRangeToken(0,d);if(isIdentStartCodePoint(n))return consumeIdentLikeToken(p,d);if(isDigitCodePoint(n))return consumeNumericToken(p,d);switch(n){case 44:return d.advanceCodePoint(),[c.Comma,",",d.representationStart,d.representationEnd,void 0];case 58:return d.advanceCodePoint(),[c.Colon,":",d.representationStart,d.representationEnd,void 0];case 59:return d.advanceCodePoint(),[c.Semicolon,";",d.representationStart,d.representationEnd,void 0];case 40:return d.advanceCodePoint(),[c.OpenParen,"(",d.representationStart,d.representationEnd,void 0];case 41:return d.advanceCodePoint(),[c.CloseParen,")",d.representationStart,d.representationEnd,void 0];case 91:return d.advanceCodePoint(),[c.OpenSquare,"[",d.representationStart,d.representationEnd,void 0];case 93:return d.advanceCodePoint(),[c.CloseSquare,"]",d.representationStart,d.representationEnd,void 0];case 123:return d.advanceCodePoint(),[c.OpenCurly,"{",d.representationStart,d.representationEnd,void 0];case 125:return d.advanceCodePoint(),[c.CloseCurly,"}",d.representationStart,d.representationEnd,void 0];case 39:case 34:return consumeStringToken(p,d);case 35:return consumeHashToken(p,d);case i:case 46:return checkIfThreeCodePointsWouldStartANumber(d)?consumeNumericToken(p,d):(d.advanceCodePoint(),[c.Delim,d.source[d.representationStart],d.representationStart,d.representationEnd,{value:d.source[d.representationStart]}]);case r:case t:case 12:case 9:case 32:return consumeWhiteSpace(d);case o:return checkIfThreeCodePointsWouldStartANumber(d)?consumeNumericToken(p,d):checkIfThreeCodePointsWouldStartCDC(d)?(d.advanceCodePoint(3),[c.CDC,"--\x3e",d.representationStart,d.representationEnd,void 0]):checkIfThreeCodePointsWouldStartAnIdentSequence(0,d)?consumeIdentLikeToken(p,d):(d.advanceCodePoint(),[c.Delim,"-",d.representationStart,d.representationEnd,{value:"-"}]);case 60:return checkIfFourCodePointsWouldStartCDO(d)?(d.advanceCodePoint(4),[c.CDO,"\x3c!--",d.representationStart,d.representationEnd,void 0]):(d.advanceCodePoint(),[c.Delim,"<",d.representationStart,d.representationEnd,{value:"<"}]);case 64:if(d.advanceCodePoint(),checkIfThreeCodePointsWouldStartAnIdentSequence(0,d)){const e=consumeIdentSequence(p,d);return[c.AtKeyword,d.source.slice(d.representationStart,d.representationEnd+1),d.representationStart,d.representationEnd,{value:String.fromCodePoint(...e)}]}return[c.Delim,"@",d.representationStart,d.representationEnd,{value:"@"}];case 92:{if(checkIfTwoCodePointsAreAValidEscape(d))return consumeIdentLikeToken(p,d);d.advanceCodePoint();const n=[c.Delim,"\\",d.representationStart,d.representationEnd,{value:"\\"}];return p.onParseError(new ParseErrorWithToken(e.InvalidEscapeSequenceAfterBackslash,d.representationStart,d.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],n)),n}}return d.advanceCodePoint(),[c.Delim,d.source[d.representationStart],d.representationStart,d.representationEnd,{value:d.source[d.representationStart]}]},endOfFile:function endOfFile(){return void 0===d.source.codePointAt(d.cursor)}}}function noop(){}function mutateIdent(e,n){const t=[];for(const e of n)t.push(e.codePointAt(0));const o=String.fromCodePoint(...serializeIdent(t));e[1]=o,e[4].value=n}function mutateUnit(e,n){const t=[];for(const e of n)t.push(e.codePointAt(0));const o=serializeIdent(t);101===o[0]&&insertEscapedCodePoint(o,0,o[0]);const r=String.fromCodePoint(...o),i="+"===e[4].signCharacter?e[4].signCharacter:"",s=e[4].value.toString();e[1]=`${i}${s}${r}`,e[4].unit=n}function serializeIdent(e){let n=0;if(0===e[0])e.splice(0,1,s),n=1;else if(e[0]===o&&e[1]===o)n=2;else if(e[0]===o&&e[1])n=2,isIdentStartCodePoint(e[1])||(n+=insertEscapedCodePoint(e,1,e[1]));else{if(e[0]===o&&!e[1])return[92,e[0]];isIdentStartCodePoint(e[0])?n=1:(n=1,n+=insertEscapedCodePoint(e,0,e[0]))}for(let t=n;t<e.length;t++)0!==e[t]?isIdentCodePoint(e[t])||(t+=insertEscapedCharacter(e,t,e[t])):(e.splice(t,1,s),t++);return e}function insertEscapedCharacter(e,n,t){return e.splice(n,1,92,t),1}function insertEscapedCodePoint(e,n,t){const o=t.toString(16),r=[];for(const e of o)r.push(e.codePointAt(0));return e.splice(n,1,92,...r,32),1+r.length}const d=Object.values(c);function isToken(e){return!!Array.isArray(e)&&(!(e.length<4)&&(!!d.includes(e[0])&&("string"==typeof e[1]&&("number"==typeof e[2]&&"number"==typeof e[3]))))}function isTokenNumeric(e){if(!e)return!1;switch(e[0]){case c.Dimension:case c.Number:case c.Percentage:return!0;default:return!1}}function isTokenWhiteSpaceOrComment(e){if(!e)return!1;switch(e[0]){case c.Whitespace:case c.Comment:return!0;default:return!1}}function isTokenAtKeyword(e){return!!e&&e[0]===c.AtKeyword}function isTokenBadString(e){return!!e&&e[0]===c.BadString}function isTokenBadURL(e){return!!e&&e[0]===c.BadURL}function isTokenCDC(e){return!!e&&e[0]===c.CDC}function isTokenCDO(e){return!!e&&e[0]===c.CDO}function isTokenColon(e){return!!e&&e[0]===c.Colon}function isTokenComma(e){return!!e&&e[0]===c.Comma}function isTokenComment(e){return!!e&&e[0]===c.Comment}function isTokenDelim(e){return!!e&&e[0]===c.Delim}function isTokenDimension(e){return!!e&&e[0]===c.Dimension}function isTokenEOF(e){return!!e&&e[0]===c.EOF}function isTokenFunction(e){return!!e&&e[0]===c.Function}function isTokenHash(e){return!!e&&e[0]===c.Hash}function isTokenIdent(e){return!!e&&e[0]===c.Ident}function isTokenNumber(e){return!!e&&e[0]===c.Number}function isTokenPercentage(e){return!!e&&e[0]===c.Percentage}function isTokenSemicolon(e){return!!e&&e[0]===c.Semicolon}function isTokenString(e){return!!e&&e[0]===c.String}function isTokenURL(e){return!!e&&e[0]===c.URL}function isTokenWhitespace(e){return!!e&&e[0]===c.Whitespace}function isTokenOpenParen(e){return!!e&&e[0]===c.OpenParen}function isTokenCloseParen(e){return!!e&&e[0]===c.CloseParen}function isTokenOpenSquare(e){return!!e&&e[0]===c.OpenSquare}function isTokenCloseSquare(e){return!!e&&e[0]===c.CloseSquare}function isTokenOpenCurly(e){return!!e&&e[0]===c.OpenCurly}function isTokenCloseCurly(e){return!!e&&e[0]===c.CloseCurly}function isTokenUnicodeRange(e){return!!e&&e[0]===c.UnicodeRange}export{u as HashType,a as NumberType,ParseError,e as ParseErrorMessage,ParseErrorWithToken,c as TokenType,cloneTokens,isToken,isTokenAtKeyword,isTokenBadString,isTokenBadURL,isTokenCDC,isTokenCDO,isTokenCloseCurly,isTokenCloseParen,isTokenCloseSquare,isTokenColon,isTokenComma,isTokenComment,isTokenDelim,isTokenDimension,isTokenEOF,isTokenFunction,isTokenHash,isTokenIdent,isTokenNumber,isTokenNumeric,isTokenOpenCurly,isTokenOpenParen,isTokenOpenSquare,isTokenPercentage,isTokenSemicolon,isTokenString,isTokenURL,isTokenUnicodeRange,isTokenWhiteSpaceOrComment,isTokenWhitespace,mirrorVariant,mirrorVariantType,mutateIdent,mutateUnit,stringify,tokenize,tokenizer};
