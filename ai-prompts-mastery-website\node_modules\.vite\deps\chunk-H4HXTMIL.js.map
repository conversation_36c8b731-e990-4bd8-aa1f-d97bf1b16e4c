{"version": 3, "sources": ["../../highlight.js/lib/languages/dockerfile.js"], "sourcesContent": ["/*\nLanguage: Dockerfile\nRequires: bash.js\nAuthor: <PERSON> <<EMAIL>>\nDescription: language definition for Dockerfile files\nWebsite: https://docs.docker.com/engine/reference/builder/\nCategory: config\n*/\n\n/** @type LanguageFn */\nfunction dockerfile(hljs) {\n  return {\n    name: 'Dockerfile',\n    aliases: ['docker'],\n    case_insensitive: true,\n    keywords: 'from maintainer expose env arg user onbuild stopsignal',\n    contains: [\n      hljs.HASH_COMMENT_MODE,\n      hljs.APOS_STRING_MODE,\n      hljs.QUOTE_STRING_MODE,\n      hljs.NUMBER_MODE,\n      {\n        beginKeywords: 'run cmd entrypoint volume add copy workdir label healthcheck shell',\n        starts: {\n          end: /[^\\\\]$/,\n          subLanguage: 'bash'\n        }\n      }\n    ],\n    illegal: '</'\n  };\n}\n\nmodule.exports = dockerfile;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,aAAS,WAAW,MAAM;AACxB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,QAAQ;AAAA,QAClB,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AAAA,UACL;AAAA,YACE,eAAe;AAAA,YACf,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,QACA,SAAS;AAAA,MACX;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}