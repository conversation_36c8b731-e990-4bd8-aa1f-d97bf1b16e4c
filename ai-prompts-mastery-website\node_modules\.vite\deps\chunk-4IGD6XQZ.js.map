{"version": 3, "sources": ["../../refractor/lang/javastacktrace.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = javastacktrace\njavastacktrace.displayName = 'javastacktrace'\njavastacktrace.aliases = []\nfunction javastacktrace(Prism) {\n  // Specification:\n  // https://docs.oracle.com/en/java/javase/13/docs/api/java.base/java/lang/Throwable.html#printStackTrace()\n  Prism.languages.javastacktrace = {\n    // java.sql.SQLException: Violation of unique constraint MY_ENTITY_UK_1: duplicate value(s) for column(s) MY_COLUMN in statement [...]\n    // Caused by: java.sql.SQLException: Violation of unique constraint MY_ENTITY_UK_1: duplicate value(s) for column(s) MY_COLUMN in statement [...]\n    // Caused by: com.example.myproject.MyProjectServletException\n    // Caused by: MidLevelException: LowLevelException\n    // Suppressed: Resource$CloseFailException: Resource ID = 0\n    summary: {\n      pattern:\n        /^([\\t ]*)(?:(?:Caused by:|Suppressed:|Exception in thread \"[^\"]*\")[\\t ]+)?[\\w$.]+(?::.*)?$/m,\n      lookbehind: true,\n      inside: {\n        keyword: {\n          pattern:\n            /^([\\t ]*)(?:(?:Caused by|Suppressed)(?=:)|Exception in thread)/m,\n          lookbehind: true\n        },\n        // the current thread if the summary starts with 'Exception in thread'\n        string: {\n          pattern: /^(\\s*)\"[^\"]*\"/,\n          lookbehind: true\n        },\n        exceptions: {\n          pattern: /^(:?\\s*)[\\w$.]+(?=:|$)/,\n          lookbehind: true,\n          inside: {\n            'class-name': /[\\w$]+$/,\n            namespace: /\\b[a-z]\\w*\\b/,\n            punctuation: /\\./\n          }\n        },\n        message: {\n          pattern: /(:\\s*)\\S.*/,\n          lookbehind: true,\n          alias: 'string'\n        },\n        punctuation: /:/\n      }\n    },\n    // at org.mortbay.jetty.servlet.ServletHandler$CachedChain.doFilter(ServletHandler.java:1166)\n    // at org.hsqldb.jdbc.Util.throwError(Unknown Source) here could be some notes\n    // at java.base/java.lang.Class.forName0(Native Method)\n    // at Util.<init>(Unknown Source)\n    // at com.foo.loader/foo@9.0/com.foo.Main.run(Main.java:101)\n    // at com.foo.loader//com.foo.bar.App.run(App.java:12)\n    // at acme@2.1/org.acme.Lib.test(Lib.java:80)\n    // at MyClass.mash(MyClass.java:9)\n    //\n    // More information:\n    // https://docs.oracle.com/en/java/javase/13/docs/api/java.base/java/lang/StackTraceElement.html#toString()\n    //\n    // A valid Java module name is defined as:\n    //   \"A module name consists of one or more Java identifiers (§3.8) separated by \".\" tokens.\"\n    // https://docs.oracle.com/javase/specs/jls/se9/html/jls-6.html#jls-ModuleName\n    //\n    // A Java module version is defined by this class:\n    // https://docs.oracle.com/javase/9/docs/api/java/lang/module/ModuleDescriptor.Version.html\n    // This is the implementation of the `parse` method in JDK13:\n    // https://github.com/matcdac/jdk/blob/2305df71d1b7710266ae0956d73927a225132c0f/src/java.base/share/classes/java/lang/module/ModuleDescriptor.java#L1108\n    // However, to keep this simple, a version will be matched by the pattern /@[\\w$.+-]*/.\n    'stack-frame': {\n      pattern: /^([\\t ]*)at (?:[\\w$./]|@[\\w$.+-]*\\/)+(?:<init>)?\\([^()]*\\)/m,\n      lookbehind: true,\n      inside: {\n        keyword: {\n          pattern: /^(\\s*)at(?= )/,\n          lookbehind: true\n        },\n        source: [\n          // (Main.java:15)\n          // (Main.scala:15)\n          {\n            pattern: /(\\()\\w+\\.\\w+:\\d+(?=\\))/,\n            lookbehind: true,\n            inside: {\n              file: /^\\w+\\.\\w+/,\n              punctuation: /:/,\n              'line-number': {\n                pattern: /\\b\\d+\\b/,\n                alias: 'number'\n              }\n            }\n          }, // (Unknown Source)\n          // (Native Method)\n          // (...something...)\n          {\n            pattern: /(\\()[^()]*(?=\\))/,\n            lookbehind: true,\n            inside: {\n              keyword: /^(?:Native Method|Unknown Source)$/\n            }\n          }\n        ],\n        'class-name': /[\\w$]+(?=\\.(?:<init>|[\\w$]+)\\()/,\n        function: /(?:<init>|[\\w$]+)(?=\\()/,\n        'class-loader': {\n          pattern: /(\\s)[a-z]\\w*(?:\\.[a-z]\\w*)*(?=\\/[\\w@$.]*\\/)/,\n          lookbehind: true,\n          alias: 'namespace',\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        module: {\n          pattern: /([\\s/])[a-z]\\w*(?:\\.[a-z]\\w*)*(?:@[\\w$.+-]*)?(?=\\/)/,\n          lookbehind: true,\n          inside: {\n            version: {\n              pattern: /(@)[\\s\\S]+/,\n              lookbehind: true,\n              alias: 'number'\n            },\n            punctuation: /[@.]/\n          }\n        },\n        namespace: {\n          pattern: /(?:\\b[a-z]\\w*\\.)+/,\n          inside: {\n            punctuation: /\\./\n          }\n        },\n        punctuation: /[()/.]/\n      }\n    },\n    // ... 32 more\n    // ... 32 common frames omitted\n    more: {\n      pattern: /^([\\t ]*)\\.{3} \\d+ [a-z]+(?: [a-z]+)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\.{3}/,\n        number: /\\d+/,\n        keyword: /\\b[a-z]+(?: [a-z]+)*\\b/\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,mBAAe,cAAc;AAC7B,mBAAe,UAAU,CAAC;AAC1B,aAAS,eAAe,OAAO;AAG7B,YAAM,UAAU,iBAAiB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAM/B,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA;AAAA,YAEA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,YAAY;AAAA,cACV,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,cAAc;AAAA,gBACd,WAAW;AAAA,gBACX,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,YACT;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAsBA,eAAe;AAAA,UACb,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,QAAQ;AAAA;AAAA;AAAA,cAGN;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,MAAM;AAAA,kBACN,aAAa;AAAA,kBACb,eAAe;AAAA,oBACb,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA;AAAA;AAAA;AAAA,cAGA;AAAA,gBACE,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,SAAS;AAAA,gBACX;AAAA,cACF;AAAA,YACF;AAAA,YACA,cAAc;AAAA,YACd,UAAU;AAAA,YACV,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,gBACN,SAAS;AAAA,kBACP,SAAS;AAAA,kBACT,YAAY;AAAA,kBACZ,OAAO;AAAA,gBACT;AAAA,gBACA,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,WAAW;AAAA,cACT,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA;AAAA;AAAA,QAGA,MAAM;AAAA,UACJ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,YACb,QAAQ;AAAA,YACR,SAAS;AAAA,UACX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA;AAAA;", "names": []}