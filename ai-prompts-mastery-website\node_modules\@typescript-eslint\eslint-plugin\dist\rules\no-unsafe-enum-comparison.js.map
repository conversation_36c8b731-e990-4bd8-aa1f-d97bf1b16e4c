{"version": 3, "file": "no-unsafe-enum-comparison.js", "sourceRoot": "", "sources": ["../../src/rules/no-unsafe-enum-comparison.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAAwC;AACxC,+CAAiC;AAEjC,kCAAwE;AACxE,gDAI6B;AAE7B;;GAEG;AACH,SAAS,YAAY,CAAC,aAAwB,EAAE,KAAc;IAC5D,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;IAEpE,OAAO,CACL,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;QACtC,OAAO,CAAC,aAAa,CACnB,KAAK,EACL,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAC9C,CAAC;QACJ,CAAC,cAAc,CAAC,GAAG,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;YACtC,OAAO,CAAC,aAAa,CACnB,KAAK,EACL,EAAE,CAAC,SAAS,CAAC,MAAM,GAAG,EAAE,CAAC,SAAS,CAAC,UAAU,CAC9C,CAAC,CACL,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,IAAa;IACrC,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC;QACvD,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,aAAa,CAAC;YACvD,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;YACrB,CAAC,CAAC,EAAE,CAAC,SAAS,CAAC,MAAM;QACvB,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC;AAED,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,2BAA2B;IACjC,IAAI,EAAE;QACJ,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,wDAAwD;YACrE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EACZ,gFAAgF;YAClF,mBAAmB,EACjB,mEAAmE;YACrE,oBAAoB,EAAE,wCAAwC;SAC/D;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAE5D,SAAS,sBAAsB,CAC7B,QAAiB,EACjB,SAAkB;YAElB,+DAA+D;YAC/D,EAAE;YACF,QAAQ;YACR,WAAW;YACX,MAAM;YACN,MAAM,aAAa,GAAG,IAAA,qBAAY,EAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;YAC1D,MAAM,cAAc,GAAG,IAAI,GAAG,CAAC,IAAA,qBAAY,EAAC,WAAW,EAAE,SAAS,CAAC,CAAC,CAAC;YACrE,IAAI,aAAa,CAAC,MAAM,KAAK,CAAC,IAAI,cAAc,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;gBAC5D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,6CAA6C;YAC7C,EAAE;YACF,QAAQ;YACR,gCAAgC;YAChC,MAAM;YACN,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,cAAc,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,CAAC;oBACrC,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,MAAM,aAAa,GAAG,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACvD,MAAM,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YAEzD,oEAAoE;YACpE,EAAE;YACF,QAAQ;YACR,wCAAwC;YACxC,eAAe;YACf,MAAM;YACN,KAAK,MAAM,YAAY,IAAI,aAAa,EAAE,CAAC;gBACzC,IAAI,cAAc,CAAC,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAC1C,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,OAAO,CACL,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC;gBACtC,YAAY,CAAC,cAAc,EAAE,QAAQ,CAAC,CACvC,CAAC;QACJ,CAAC;QAED,OAAO;YACL,8CAA8C,CAC5C,IAA+B;gBAE/B,MAAM,QAAQ,GAAG,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC7D,MAAM,SAAS,GAAG,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE/D,IAAI,sBAAsB,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;oBAChD,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,qBAAqB;wBAChC,IAAI;wBACJ,OAAO,EAAE;4BACP;gCACE,SAAS,EAAE,sBAAsB;gCACjC,GAAG,CAAC,KAAK;oCACP,uDAAuD;oCACvD,EAAE;oCACF,QAAQ;oCACR,0DAA0D;oCAC1D,MAAM;oCACN,MAAM,WAAW,GAAG,IAAA,6BAAoB,EACtC,IAAA,wBAAe,EAAC,QAAQ,CAAC,EACzB,IAAA,qBAAc,EAAC,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAClC,CAAC;oCAEF,IAAI,WAAW,EAAE,CAAC;wCAChB,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;oCACpD,CAAC;oCAED,sDAAsD;oCACtD,EAAE;oCACF,QAAQ;oCACR,8BAA8B;oCAC9B,0DAA0D;oCAC1D,MAAM;oCACN,MAAM,YAAY,GAAG,IAAA,6BAAoB,EACvC,IAAA,wBAAe,EAAC,SAAS,CAAC,EAC1B,IAAA,qBAAc,EAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,CACjC,CAAC;oCAEF,IAAI,YAAY,EAAE,CAAC;wCACjB,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;oCACpD,CAAC;oCAED,OAAO,IAAI,CAAC;gCACd,CAAC;6BACF;yBACF;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAED,UAAU,CAAC,IAAI;gBACb,0BAA0B;gBAC1B,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,EAAE,CAAC;oBACtB,OAAO;gBACT,CAAC;gBAED,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;gBAExB;;mBAEG;gBACH,MAAM,eAAe,GAAG,MAAkC,CAAC;gBAE3D,MAAM,QAAQ,GAAG,cAAc,CAAC,iBAAiB,CAC/C,eAAe,CAAC,YAAY,CAC7B,CAAC;gBACF,MAAM,SAAS,GAAG,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE9D,IAAI,sBAAsB,CAAC,QAAQ,EAAE,SAAS,CAAC,EAAE,CAAC;oBAChD,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,gBAAgB;wBAC3B,IAAI;qBACL,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}