{"version": 3, "sources": ["../../refractor/lang/cypher.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = cypher\ncypher.displayName = 'cypher'\ncypher.aliases = []\nfunction cypher(Prism) {\n  Prism.languages.cypher = {\n    // https://neo4j.com/docs/cypher-manual/current/syntax/comments/\n    comment: /\\/\\/.*/,\n    string: {\n      pattern: /\"(?:[^\"\\\\\\r\\n]|\\\\.)*\"|'(?:[^'\\\\\\r\\n]|\\\\.)*'/,\n      greedy: true\n    },\n    'class-name': {\n      pattern: /(:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)(?=\\s*[{):])/,\n      lookbehind: true,\n      greedy: true\n    },\n    relationship: {\n      pattern:\n        /(-\\[\\s*(?:\\w+\\s*|`(?:[^`\\\\\\r\\n])*`\\s*)?:\\s*|\\|\\s*:\\s*)(?:\\w+|`(?:[^`\\\\\\r\\n])*`)/,\n      lookbehind: true,\n      greedy: true,\n      alias: 'property'\n    },\n    identifier: {\n      pattern: /`(?:[^`\\\\\\r\\n])*`/,\n      greedy: true\n    },\n    variable: /\\$\\w+/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/reserved/\n    keyword:\n      /\\b(?:ADD|ALL|AND|AS|ASC|ASCENDING|ASSERT|BY|CALL|CASE|COMMIT|CONSTRAINT|CONTAINS|CREATE|CSV|DELETE|DESC|DESCENDING|DETACH|DISTINCT|DO|DROP|ELSE|END|ENDS|EXISTS|FOR|FOREACH|IN|INDEX|IS|JOIN|KEY|LIMIT|LOAD|MANDATORY|MATCH|MERGE|NODE|NOT|OF|ON|OPTIONAL|OR|ORDER(?=\\s+BY)|PERIODIC|REMOVE|REQUIRE|RETURN|SCALAR|SCAN|SET|SKIP|START|STARTS|THEN|UNION|UNIQUE|UNWIND|USING|WHEN|WHERE|WITH|XOR|YIELD)\\b/i,\n    function: /\\b\\w+\\b(?=\\s*\\()/,\n    boolean: /\\b(?:false|null|true)\\b/i,\n    number: /\\b(?:0x[\\da-fA-F]+|\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?)\\b/,\n    // https://neo4j.com/docs/cypher-manual/current/syntax/operators/\n    operator: /:|<--?|--?>?|<>|=~?|[<>]=?|[+*/%^|]|\\.\\.\\.?/,\n    punctuation: /[()[\\]{},;.]/\n  }\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,UAAU,SAAS;AAAA;AAAA,QAEvB,SAAS;AAAA,QACT,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,QACV;AAAA,QACA,cAAc;AAAA,UACZ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,YAAY;AAAA,UACV,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,UAAU;AAAA;AAAA,QAEV,SACE;AAAA,QACF,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA;AAAA,QAER,UAAU;AAAA,QACV,aAAa;AAAA,MACf;AAAA,IACF;AAAA;AAAA;", "names": []}