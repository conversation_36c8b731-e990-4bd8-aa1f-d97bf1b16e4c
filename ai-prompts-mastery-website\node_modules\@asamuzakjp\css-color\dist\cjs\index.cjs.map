{"version": 3, "sources": ["../../src/index.ts", "../../src/js/css-calc.ts", "../../src/js/cache.ts", "../../src/js/util.ts", "../../src/js/common.ts", "../../src/js/constant.ts", "../../src/js/color.ts", "../../src/js/css-var.ts", "../../src/js/relative-color.ts", "../../src/js/resolve.ts", "../../src/js/css-gradient.ts", "../../src/js/convert.ts"], "sourcesContent": ["/*!\n * CSS color - Resolve, parse, convert CSS color.\n * @license MIT\n * @copyright asamuzaK (Kazz)\n * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}\n */\n\nimport { cssCalc as csscalc } from './js/css-calc';\nimport { isGradient } from './js/css-gradient';\nimport { cssVar } from './js/css-var';\nimport { extractDashedIdent, isColor as iscolor, splitValue } from './js/util';\n\nexport { convert } from './js/convert';\nexport { resolve } from './js/resolve';\n/* utils */\nexport const utils = {\n  cssCalc: csscalc,\n  cssVar,\n  extractDashedIdent,\n  isColor: iscolor,\n  isGradient,\n  splitValue\n};\n/* TODO: remove later */\n/* alias */\nexport const isColor = utils.isColor;\nexport const cssCalc = utils.cssCalc;\n", "/**\n * css-calc\n */\n\nimport { calc } from '@csstools/css-calc';\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString, isStringOrNumber } from './common';\nimport { resolveVar } from './css-var';\nimport { roundToPrecision } from './util';\nimport { MatchedRegExp, Options } from './typedef';\n\n/* constants */\nimport {\n  ANGLE,\n  LENGTH,\n  NUM,\n  SYN_FN_CALC,\n  SYN_FN_MATH_START,\n  SYN_FN_VAR,\n  SYN_FN_VAR_START,\n  VAL_SPEC\n} from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  Dimension: DIM,\n  EOF,\n  Function: FUNC,\n  OpenParen: PAREN_OPEN,\n  Whitespace: W_SPACE\n} = TokenType;\nconst NAMESPACE = 'css-calc';\n\n/* numeric constants */\nconst TRIA = 3;\nconst HEX = 16;\nconst MAX_PCT = 100;\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_CALC_NUM = new RegExp(`^calc\\\\((${NUM})\\\\)$`);\nconst REG_FN_MATH_START = new RegExp(SYN_FN_MATH_START);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\nconst REG_FN_VAR_START = new RegExp(SYN_FN_VAR_START);\nconst REG_OPERATOR = /\\s[*+/-]\\s/;\nconst REG_TYPE_DIM = new RegExp(`^(${NUM})(${ANGLE}|${LENGTH})$`);\nconst REG_TYPE_DIM_PCT = new RegExp(`^(${NUM})(${ANGLE}|${LENGTH}|%)$`);\nconst REG_TYPE_PCT = new RegExp(`^(${NUM})%$`);\n\n/**\n * Calclator\n */\nexport class Calculator {\n  /* private */\n  // number\n  #hasNum: boolean;\n  #numSum: number[];\n  #numMul: number[];\n  // percentage\n  #hasPct: boolean;\n  #pctSum: number[];\n  #pctMul: number[];\n  // dimension\n  #hasDim: boolean;\n  #dimSum: string[];\n  #dimSub: string[];\n  #dimMul: string[];\n  #dimDiv: string[];\n  // et cetra\n  #hasEtc: boolean;\n  #etcSum: string[];\n  #etcSub: string[];\n  #etcMul: string[];\n  #etcDiv: string[];\n\n  /**\n   * constructor\n   */\n  constructor() {\n    // number\n    this.#hasNum = false;\n    this.#numSum = [];\n    this.#numMul = [];\n    // percentage\n    this.#hasPct = false;\n    this.#pctSum = [];\n    this.#pctMul = [];\n    // dimension\n    this.#hasDim = false;\n    this.#dimSum = [];\n    this.#dimSub = [];\n    this.#dimMul = [];\n    this.#dimDiv = [];\n    // et cetra\n    this.#hasEtc = false;\n    this.#etcSum = [];\n    this.#etcSub = [];\n    this.#etcMul = [];\n    this.#etcDiv = [];\n  }\n\n  get hasNum() {\n    return this.#hasNum;\n  }\n\n  set hasNum(value: boolean) {\n    this.#hasNum = !!value;\n  }\n\n  get numSum() {\n    return this.#numSum;\n  }\n\n  get numMul() {\n    return this.#numMul;\n  }\n\n  get hasPct() {\n    return this.#hasPct;\n  }\n\n  set hasPct(value: boolean) {\n    this.#hasPct = !!value;\n  }\n\n  get pctSum() {\n    return this.#pctSum;\n  }\n\n  get pctMul() {\n    return this.#pctMul;\n  }\n\n  get hasDim() {\n    return this.#hasDim;\n  }\n\n  set hasDim(value: boolean) {\n    this.#hasDim = !!value;\n  }\n\n  get dimSum() {\n    return this.#dimSum;\n  }\n\n  get dimSub() {\n    return this.#dimSub;\n  }\n\n  get dimMul() {\n    return this.#dimMul;\n  }\n\n  get dimDiv() {\n    return this.#dimDiv;\n  }\n\n  get hasEtc() {\n    return this.#hasEtc;\n  }\n\n  set hasEtc(value: boolean) {\n    this.#hasEtc = !!value;\n  }\n\n  get etcSum() {\n    return this.#etcSum;\n  }\n\n  get etcSub() {\n    return this.#etcSub;\n  }\n\n  get etcMul() {\n    return this.#etcMul;\n  }\n\n  get etcDiv() {\n    return this.#etcDiv;\n  }\n\n  /**\n   * clear values\n   * @returns void\n   */\n  clear() {\n    // number\n    this.#hasNum = false;\n    this.#numSum = [];\n    this.#numMul = [];\n    // percentage\n    this.#hasPct = false;\n    this.#pctSum = [];\n    this.#pctMul = [];\n    // dimension\n    this.#hasDim = false;\n    this.#dimSum = [];\n    this.#dimSub = [];\n    this.#dimMul = [];\n    this.#dimDiv = [];\n    // et cetra\n    this.#hasEtc = false;\n    this.#etcSum = [];\n    this.#etcSub = [];\n    this.#etcMul = [];\n    this.#etcDiv = [];\n  }\n\n  /**\n   * sort values\n   * @param values - values\n   * @returns sorted values\n   */\n  sort(values: string[] = []): string[] {\n    const arr = [...values];\n    if (arr.length > 1) {\n      arr.sort((a, b) => {\n        let res;\n        if (REG_TYPE_DIM_PCT.test(a) && REG_TYPE_DIM_PCT.test(b)) {\n          const [, valA, unitA] = a.match(REG_TYPE_DIM_PCT) as MatchedRegExp;\n          const [, valB, unitB] = b.match(REG_TYPE_DIM_PCT) as MatchedRegExp;\n          if (unitA === unitB) {\n            if (Number(valA) === Number(valB)) {\n              res = 0;\n            } else if (Number(valA) > Number(valB)) {\n              res = 1;\n            } else {\n              res = -1;\n            }\n          } else if (unitA > unitB) {\n            res = 1;\n          } else {\n            res = -1;\n          }\n        } else {\n          if (a === b) {\n            res = 0;\n          } else if (a > b) {\n            res = 1;\n          } else {\n            res = -1;\n          }\n        }\n        return res;\n      });\n    }\n    return arr;\n  }\n\n  /**\n   * multiply values\n   * @returns resolved value\n   */\n  multiply(): string {\n    const value = [];\n    let num;\n    if (this.#hasNum) {\n      num = 1;\n      for (const i of this.#numMul) {\n        num *= i;\n        if (num === 0 || !Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      if (!this.#hasPct && !this.#hasDim && !this.hasEtc) {\n        if (Number.isFinite(num)) {\n          num = roundToPrecision(num, HEX);\n        }\n        value.push(num);\n      }\n    }\n    if (this.#hasPct) {\n      if (typeof num !== 'number') {\n        num = 1;\n      }\n      for (const i of this.#pctMul) {\n        num *= i;\n        if (num === 0 || !Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      if (Number.isFinite(num)) {\n        num = `${roundToPrecision(num, HEX)}%`;\n      }\n      if (!this.#hasDim && !this.hasEtc) {\n        value.push(num);\n      }\n    }\n    if (this.#hasDim) {\n      let dim = '';\n      let mul = '';\n      let div = '';\n      if (this.#dimMul.length) {\n        if (this.#dimMul.length === 1) {\n          [mul] = this.#dimMul as [string];\n        } else {\n          mul = `${this.sort(this.#dimMul).join(' * ')}`;\n        }\n      }\n      if (this.#dimDiv.length) {\n        if (this.#dimDiv.length === 1) {\n          [div] = this.#dimDiv as [string];\n        } else {\n          div = `${this.sort(this.#dimDiv).join(' * ')}`;\n        }\n      }\n      if (Number.isFinite(num)) {\n        if (mul) {\n          if (div) {\n            if (div.includes('*')) {\n              dim = calc(`calc(${num} * ${mul} / (${div}))`, {\n                toCanonicalUnits: true\n              });\n            } else {\n              dim = calc(`calc(${num} * ${mul} / ${div})`, {\n                toCanonicalUnits: true\n              });\n            }\n          } else {\n            dim = calc(`calc(${num} * ${mul})`, {\n              toCanonicalUnits: true\n            });\n          }\n        } else if (div.includes('*')) {\n          dim = calc(`calc(${num} / (${div}))`, {\n            toCanonicalUnits: true\n          });\n        } else {\n          dim = calc(`calc(${num} / ${div})`, {\n            toCanonicalUnits: true\n          });\n        }\n        value.push(dim.replace(/^calc/, ''));\n      } else {\n        if (!value.length && num !== undefined) {\n          value.push(num);\n        }\n        if (mul) {\n          if (div) {\n            if (div.includes('*')) {\n              dim = calc(`calc(${mul} / (${div}))`, {\n                toCanonicalUnits: true\n              });\n            } else {\n              dim = calc(`calc(${mul} / ${div})`, {\n                toCanonicalUnits: true\n              });\n            }\n          } else {\n            dim = calc(`calc(${mul})`, {\n              toCanonicalUnits: true\n            });\n          }\n          if (value.length) {\n            value.push('*', dim.replace(/^calc/, ''));\n          } else {\n            value.push(dim.replace(/^calc/, ''));\n          }\n        } else {\n          dim = calc(`calc(${div})`, {\n            toCanonicalUnits: true\n          });\n          if (value.length) {\n            value.push('/', dim.replace(/^calc/, ''));\n          } else {\n            value.push('1', '/', dim.replace(/^calc/, ''));\n          }\n        }\n      }\n    }\n    if (this.#hasEtc) {\n      if (this.#etcMul.length) {\n        if (!value.length && num !== undefined) {\n          value.push(num);\n        }\n        const mul = this.sort(this.#etcMul).join(' * ');\n        if (value.length) {\n          value.push(`* ${mul}`);\n        } else {\n          value.push(`${mul}`);\n        }\n      }\n      if (this.#etcDiv.length) {\n        const div = this.sort(this.#etcDiv).join(' * ');\n        if (div.includes('*')) {\n          if (value.length) {\n            value.push(`/ (${div})`);\n          } else {\n            value.push(`1 / (${div})`);\n          }\n        } else if (value.length) {\n          value.push(`/ ${div}`);\n        } else {\n          value.push(`1 / ${div}`);\n        }\n      }\n    }\n    if (value.length) {\n      return value.join(' ');\n    }\n    return '';\n  }\n\n  /**\n   * sum values\n   * @returns resolved value\n   */\n  sum(): string {\n    const value = [];\n    if (this.#hasNum) {\n      let num = 0;\n      for (const i of this.#numSum) {\n        num += i;\n        if (!Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      value.push(num);\n    }\n    if (this.#hasPct) {\n      let num: number | string = 0;\n      for (const i of this.#pctSum) {\n        num += i;\n        if (!Number.isFinite(num)) {\n          break;\n        }\n      }\n      if (Number.isFinite(num)) {\n        num = `${num}%`;\n      }\n      if (value.length) {\n        value.push(`+ ${num}`);\n      } else {\n        value.push(num);\n      }\n    }\n    if (this.#hasDim) {\n      let dim, sum, sub;\n      if (this.#dimSum.length) {\n        sum = this.sort(this.#dimSum).join(' + ');\n      }\n      if (this.#dimSub.length) {\n        sub = this.sort(this.#dimSub).join(' + ');\n      }\n      if (sum) {\n        if (sub) {\n          if (sub.includes('-')) {\n            dim = calc(`calc(${sum} - (${sub}))`, {\n              toCanonicalUnits: true\n            });\n          } else {\n            dim = calc(`calc(${sum} - ${sub})`, {\n              toCanonicalUnits: true\n            });\n          }\n        } else {\n          dim = calc(`calc(${sum})`, {\n            toCanonicalUnits: true\n          });\n        }\n      } else {\n        dim = calc(`calc(-1 * (${sub}))`, {\n          toCanonicalUnits: true\n        });\n      }\n      if (value.length) {\n        value.push('+', dim.replace(/^calc/, ''));\n      } else {\n        value.push(dim.replace(/^calc/, ''));\n      }\n    }\n    if (this.#hasEtc) {\n      if (this.#etcSum.length) {\n        const sum = this.sort(this.#etcSum)\n          .map(item => {\n            let res;\n            if (\n              REG_OPERATOR.test(item) &&\n              !item.startsWith('(') &&\n              !item.endsWith(')')\n            ) {\n              res = `(${item})`;\n            } else {\n              res = item;\n            }\n            return res;\n          })\n          .join(' + ');\n        if (value.length) {\n          if (this.#etcSum.length > 1) {\n            value.push(`+ (${sum})`);\n          } else {\n            value.push(`+ ${sum}`);\n          }\n        } else {\n          value.push(`${sum}`);\n        }\n      }\n      if (this.#etcSub.length) {\n        const sub = this.sort(this.#etcSub)\n          .map(item => {\n            let res;\n            if (\n              REG_OPERATOR.test(item) &&\n              !item.startsWith('(') &&\n              !item.endsWith(')')\n            ) {\n              res = `(${item})`;\n            } else {\n              res = item;\n            }\n            return res;\n          })\n          .join(' + ');\n        if (value.length) {\n          if (this.#etcSub.length > 1) {\n            value.push(`- (${sub})`);\n          } else {\n            value.push(`- ${sub}`);\n          }\n        } else if (this.#etcSub.length > 1) {\n          value.push(`-1 * (${sub})`);\n        } else {\n          value.push(`-1 * ${sub}`);\n        }\n      }\n    }\n    if (value.length) {\n      return value.join(' ');\n    }\n    return '';\n  }\n}\n\n/**\n * sort calc values\n * @param values - values to sort\n * @param [finalize] - finalize values\n * @returns sorted values\n */\nexport const sortCalcValues = (\n  values: (number | string)[] = [],\n  finalize: boolean = false\n): string => {\n  if (values.length < TRIA) {\n    throw new Error(`Unexpected array length ${values.length}.`);\n  }\n  const start = values.shift();\n  if (!isString(start) || !start.endsWith('(')) {\n    throw new Error(`Unexpected token ${start}.`);\n  }\n  const end = values.pop();\n  if (end !== ')') {\n    throw new Error(`Unexpected token ${end}.`);\n  }\n  if (values.length === 1) {\n    const [value] = values;\n    if (!isStringOrNumber(value)) {\n      throw new Error(`Unexpected token ${value}.`);\n    }\n    return `${start}${value}${end}`;\n  }\n  const sortedValues = [];\n  const cal = new Calculator();\n  let operator: string = '';\n  const l = values.length;\n  for (let i = 0; i < l; i++) {\n    const value = values[i];\n    if (!isStringOrNumber(value)) {\n      throw new Error(`Unexpected token ${value}.`);\n    }\n    if (value === '*' || value === '/') {\n      operator = value;\n    } else if (value === '+' || value === '-') {\n      const sortedValue = cal.multiply();\n      if (sortedValue) {\n        sortedValues.push(sortedValue, value);\n      }\n      cal.clear();\n      operator = '';\n    } else {\n      const numValue = Number(value);\n      const strValue = `${value}`;\n      switch (operator) {\n        case '/': {\n          if (Number.isFinite(numValue)) {\n            cal.hasNum = true;\n            cal.numMul.push(1 / numValue);\n          } else if (REG_TYPE_PCT.test(strValue)) {\n            const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n            cal.hasPct = true;\n            cal.pctMul.push((MAX_PCT * MAX_PCT) / Number(val));\n          } else if (REG_TYPE_DIM.test(strValue)) {\n            cal.hasDim = true;\n            cal.dimDiv.push(strValue);\n          } else {\n            cal.hasEtc = true;\n            cal.etcDiv.push(strValue);\n          }\n          break;\n        }\n        case '*':\n        default: {\n          if (Number.isFinite(numValue)) {\n            cal.hasNum = true;\n            cal.numMul.push(numValue);\n          } else if (REG_TYPE_PCT.test(strValue)) {\n            const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n            cal.hasPct = true;\n            cal.pctMul.push(Number(val));\n          } else if (REG_TYPE_DIM.test(strValue)) {\n            cal.hasDim = true;\n            cal.dimMul.push(strValue);\n          } else {\n            cal.hasEtc = true;\n            cal.etcMul.push(strValue);\n          }\n        }\n      }\n    }\n    if (i === l - 1) {\n      const sortedValue = cal.multiply();\n      if (sortedValue) {\n        sortedValues.push(sortedValue);\n      }\n      cal.clear();\n      operator = '';\n    }\n  }\n  let resolvedValue = '';\n  if (finalize && (sortedValues.includes('+') || sortedValues.includes('-'))) {\n    const finalizedValues = [];\n    cal.clear();\n    operator = '';\n    const l = sortedValues.length;\n    for (let i = 0; i < l; i++) {\n      const value = sortedValues[i];\n      if (isStringOrNumber(value)) {\n        if (value === '+' || value === '-') {\n          operator = value;\n        } else {\n          const numValue = Number(value);\n          const strValue = `${value}`;\n          switch (operator) {\n            case '-': {\n              if (Number.isFinite(numValue)) {\n                cal.hasNum = true;\n                cal.numSum.push(-1 * numValue);\n              } else if (REG_TYPE_PCT.test(strValue)) {\n                const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n                cal.hasPct = true;\n                cal.pctSum.push(-1 * Number(val));\n              } else if (REG_TYPE_DIM.test(strValue)) {\n                cal.hasDim = true;\n                cal.dimSub.push(strValue);\n              } else {\n                cal.hasEtc = true;\n                cal.etcSub.push(strValue);\n              }\n              break;\n            }\n            case '+':\n            default: {\n              if (Number.isFinite(numValue)) {\n                cal.hasNum = true;\n                cal.numSum.push(numValue);\n              } else if (REG_TYPE_PCT.test(strValue)) {\n                const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n                cal.hasPct = true;\n                cal.pctSum.push(Number(val));\n              } else if (REG_TYPE_DIM.test(strValue)) {\n                cal.hasDim = true;\n                cal.dimSum.push(strValue);\n              } else {\n                cal.hasEtc = true;\n                cal.etcSum.push(strValue);\n              }\n            }\n          }\n        }\n      }\n      if (i === l - 1) {\n        const sortedValue = cal.sum();\n        if (sortedValue) {\n          finalizedValues.push(sortedValue);\n        }\n        cal.clear();\n        operator = '';\n      }\n    }\n    resolvedValue = finalizedValues.join(' ').replace(/\\+\\s-/g, '- ');\n  } else {\n    resolvedValue = sortedValues.join(' ').replace(/\\+\\s-/g, '- ');\n  }\n  if (\n    resolvedValue.startsWith('(') &&\n    resolvedValue.endsWith(')') &&\n    resolvedValue.lastIndexOf('(') === 0 &&\n    resolvedValue.indexOf(')') === resolvedValue.length - 1\n  ) {\n    resolvedValue = resolvedValue.replace(/^\\(/, '').replace(/\\)$/, '');\n  }\n  return `${start}${resolvedValue}${end}`;\n};\n\n/**\n * serialize calc\n * @param value - CSS value\n * @param [opt] - options\n * @returns serialized value\n */\nexport const serializeCalc = (value: string, opt: Options = {}): string => {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (!REG_FN_VAR_START.test(value) || format !== VAL_SPEC) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'serializeCalc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string;\n  }\n  const items: string[] = tokenize({ css: value })\n    .map((token: CSSToken): string => {\n      const [type, value] = token as [TokenType, string];\n      let res = '';\n      if (type !== W_SPACE && type !== COMMENT) {\n        res = value;\n      }\n      return res;\n    })\n    .filter(v => v);\n  let startIndex = items.findLastIndex((item: string) => /\\($/.test(item));\n  while (startIndex) {\n    const endIndex = items.findIndex((item: unknown, index: number) => {\n      return item === ')' && index > startIndex;\n    });\n    const slicedValues: string[] = items.slice(startIndex, endIndex + 1);\n    let serializedValue: string = sortCalcValues(slicedValues);\n    if (REG_FN_VAR_START.test(serializedValue)) {\n      serializedValue = calc(serializedValue, {\n        toCanonicalUnits: true\n      });\n    }\n    items.splice(startIndex, endIndex - startIndex + 1, serializedValue);\n    startIndex = items.findLastIndex((item: string) => /\\($/.test(item));\n  }\n  const serializedCalc = sortCalcValues(items, true);\n  setCache(cacheKey, serializedCalc);\n  return serializedCalc;\n};\n\n/**\n * resolve dimension\n * @param token - CSS token\n * @param [opt] - options\n * @returns resolved value\n */\nexport const resolveDimension = (\n  token: CSSToken,\n  opt: Options = {}\n): string | NullObject => {\n  if (!Array.isArray(token)) {\n    throw new TypeError(`${token} is not an array.`);\n  }\n  const [, , , , detail = {}] = token;\n  const { unit, value } = detail as {\n    unit: string;\n    value: number;\n  };\n  const { dimension = {} } = opt;\n  if (unit === 'px') {\n    return `${value}${unit}`;\n  }\n  const relativeValue = Number(value);\n  if (unit && Number.isFinite(relativeValue)) {\n    let pixelValue;\n    if (Object.hasOwnProperty.call(dimension, unit)) {\n      pixelValue = dimension[unit];\n    } else if (typeof dimension.callback === 'function') {\n      pixelValue = dimension.callback(unit);\n    }\n    pixelValue = Number(pixelValue);\n    if (Number.isFinite(pixelValue)) {\n      return `${relativeValue * pixelValue}px`;\n    }\n  }\n  return new NullObject();\n};\n\n/**\n * parse tokens\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns parsed tokens\n */\nexport const parseTokens = (\n  tokens: CSSToken[],\n  opt: Options = {}\n): string[] => {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { format = '' } = opt;\n  const mathFunc = new Set();\n  let nest = 0;\n  const res: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type = '', value = ''] = token as [TokenType, string];\n    switch (type) {\n      case DIM: {\n        if (format === VAL_SPEC && !mathFunc.has(nest)) {\n          res.push(value);\n        } else {\n          const resolvedValue = resolveDimension(token, opt);\n          if (isString(resolvedValue)) {\n            res.push(resolvedValue);\n          } else {\n            res.push(value);\n          }\n        }\n        break;\n      }\n      case FUNC:\n      case PAREN_OPEN: {\n        res.push(value);\n        nest++;\n        if (REG_FN_MATH_START.test(value)) {\n          mathFunc.add(nest);\n        }\n        break;\n      }\n      case PAREN_CLOSE: {\n        if (res.length) {\n          const lastValue = res[res.length - 1];\n          if (lastValue === ' ') {\n            res.splice(-1, 1, value);\n          } else {\n            res.push(value);\n          }\n        } else {\n          res.push(value);\n        }\n        if (mathFunc.has(nest)) {\n          mathFunc.delete(nest);\n        }\n        nest--;\n        break;\n      }\n      case W_SPACE: {\n        if (res.length) {\n          const lastValue = res[res.length - 1];\n          if (\n            isString(lastValue) &&\n            !lastValue.endsWith('(') &&\n            lastValue !== ' '\n          ) {\n            res.push(value);\n          }\n        }\n        break;\n      }\n      default: {\n        if (type !== COMMENT && type !== EOF) {\n          res.push(value);\n        }\n      }\n    }\n  }\n  return res;\n};\n\n/**\n * CSS calc()\n * @param value - CSS value including calc()\n * @param [opt] - options\n * @returns resolved value\n */\nexport const cssCalc = (value: string, opt: Options = {}): string => {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (REG_FN_VAR.test(value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      } else {\n        const resolvedValue = resolveVar(value, opt);\n        if (isString(resolvedValue)) {\n          return resolvedValue;\n        } else {\n          return '';\n        }\n      }\n    } else if (!REG_FN_CALC.test(value)) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'cssCalc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string;\n  }\n  const tokens = tokenize({ css: value });\n  const values = parseTokens(tokens, opt);\n  let resolvedValue: string = calc(values.join(''), {\n    toCanonicalUnits: true\n  });\n  if (REG_FN_VAR_START.test(value)) {\n    if (REG_TYPE_DIM_PCT.test(resolvedValue)) {\n      const [, val, unit] = resolvedValue.match(\n        REG_TYPE_DIM_PCT\n      ) as MatchedRegExp;\n      resolvedValue = `${roundToPrecision(Number(val), HEX)}${unit}`;\n    }\n    // wrap with `calc()`\n    if (\n      resolvedValue &&\n      !REG_FN_VAR_START.test(resolvedValue) &&\n      format === VAL_SPEC\n    ) {\n      resolvedValue = `calc(${resolvedValue})`;\n    }\n  }\n  if (format === VAL_SPEC) {\n    if (/\\s[-+*/]\\s/.test(resolvedValue) && !resolvedValue.includes('NaN')) {\n      resolvedValue = serializeCalc(resolvedValue, opt);\n    } else if (REG_FN_CALC_NUM.test(resolvedValue)) {\n      const [, val] = resolvedValue.match(REG_FN_CALC_NUM) as MatchedRegExp;\n      resolvedValue = `calc(${roundToPrecision(Number(val), HEX)})`;\n    }\n  }\n  setCache(cacheKey, resolvedValue);\n  return resolvedValue;\n};\n", "/**\n * cache\n */\n\nimport { LRUCache } from 'lru-cache';\nimport { Options } from './typedef';\nimport { valueToJsonString } from './util';\n\n/* numeric constants */\nconst MAX_CACHE = 4096;\n\n/**\n * CacheItem\n */\nexport class CacheItem {\n  /* private */\n  #isNull: boolean;\n  #item: unknown;\n\n  /**\n   * constructor\n   */\n  constructor(item: unknown, isNull: boolean = false) {\n    this.#item = item;\n    this.#isNull = !!isNull;\n  }\n\n  get item() {\n    return this.#item;\n  }\n\n  get isNull() {\n    return this.#isNull;\n  }\n}\n\n/**\n * NullObject\n */\nexport class NullObject extends CacheItem {\n  /**\n   * constructor\n   */\n  constructor() {\n    super(Symbol('null'), true);\n  }\n}\n\n/*\n * lru cache\n */\nexport const lruCache = new LRUCache({\n  max: MAX_CACHE\n});\n\n/**\n * set cache\n * @param key - cache key\n * @param value - value to cache\n * @returns void\n */\nexport const setCache = (key: string, value: unknown): void => {\n  if (key) {\n    if (value === null) {\n      lruCache.set(key, new NullObject());\n    } else if (value instanceof CacheItem) {\n      lruCache.set(key, value);\n    } else {\n      lruCache.set(key, new CacheItem(value));\n    }\n  }\n};\n\n/**\n * get cache\n * @param key - cache key\n * @returns cached item or false otherwise\n */\nexport const getCache = (key: string): CacheItem | boolean => {\n  if (key && lruCache.has(key)) {\n    const item = lruCache.get(key);\n    if (item instanceof CacheItem) {\n      return item;\n    }\n    // delete unexpected cached item\n    lruCache.delete(key);\n    return false;\n  }\n  return false;\n};\n\n/**\n * create cache key\n * @param keyData - key data\n * @param [opt] - options\n * @returns cache key\n */\nexport const createCacheKey = (\n  keyData: Record<string, string>,\n  opt: Options = {}\n): string => {\n  const { customProperty = {}, dimension = {} } = opt;\n  let cacheKey = '';\n  if (\n    keyData &&\n    Object.keys(keyData).length &&\n    typeof customProperty.callback !== 'function' &&\n    typeof dimension.callback !== 'function'\n  ) {\n    keyData.opt = valueToJsonString(opt);\n    cacheKey = valueToJsonString(keyData);\n  }\n  return cacheKey;\n};\n", "/**\n * util\n */\n\nimport { TokenType, tokenize } from '@csstools/css-tokenizer';\nimport { CacheItem, createCache<PERSON><PERSON>, getCache, setCache } from './cache';\nimport { isString } from './common';\nimport { resolveColor } from './resolve';\nimport { Options } from './typedef';\n\n/* constants */\nimport { NAMED_COLORS } from './color';\nimport { SYN_COLOR_TYPE, SYN_MIX, VAL_SPEC } from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comma: COMMA,\n  Comment: COMMENT,\n  Delim: DELIM,\n  EOF,\n  Function: FUNC,\n  Ident: IDENT,\n  OpenParen: PAREN_OPEN,\n  Whitespace: W_SPACE\n} = TokenType;\nconst NAMESPACE = 'util';\n\n/* numeric constants */\nconst DEC = 10;\nconst HEX = 16;\nconst DEG = 360;\nconst DEG_HALF = 180;\n\n/* regexp */\nconst REG_COLOR = new RegExp(`^(?:${SYN_COLOR_TYPE})$`);\nconst REG_FN_COLOR =\n  /^(?:(?:ok)?l(?:ab|ch)|color(?:-mix)?|hsla?|hwb|rgba?|var)\\(/;\nconst REG_MIX = new RegExp(SYN_MIX);\n\n/**\n * split value\n * NOTE: comments are stripped, it can be preserved if, in the options param,\n * `delimiter` is either ',' or '/' and with `preserveComment` set to `true`\n * @param value - CSS value\n * @param [opt] - options\n * @returns array of values\n */\nexport const splitValue = (value: string, opt: Options = {}): string[] => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { delimiter = ' ', preserveComment = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'splitValue',\n      value\n    },\n    {\n      delimiter,\n      preserveComment\n    }\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string[];\n  }\n  let regDelimiter;\n  if (delimiter === ',') {\n    regDelimiter = /^,$/;\n  } else if (delimiter === '/') {\n    regDelimiter = /^\\/$/;\n  } else {\n    regDelimiter = /^\\s+$/;\n  }\n  const tokens = tokenize({ css: value });\n  let nest = 0;\n  let str = '';\n  const res: string[] = [];\n  while (tokens.length) {\n    const [type, value] = tokens.shift() as [TokenType, string];\n    switch (type) {\n      case COMMA: {\n        if (regDelimiter.test(value)) {\n          if (nest === 0) {\n            res.push(str.trim());\n            str = '';\n          } else {\n            str += value;\n          }\n        } else {\n          str += value;\n        }\n        break;\n      }\n      case DELIM: {\n        if (regDelimiter.test(value)) {\n          if (nest === 0) {\n            res.push(str.trim());\n            str = '';\n          } else {\n            str += value;\n          }\n        } else {\n          str += value;\n        }\n        break;\n      }\n      case COMMENT: {\n        if (preserveComment && (delimiter === ',' || delimiter === '/')) {\n          str += value;\n        }\n        break;\n      }\n      case FUNC:\n      case PAREN_OPEN: {\n        str += value;\n        nest++;\n        break;\n      }\n      case PAREN_CLOSE: {\n        str += value;\n        nest--;\n        break;\n      }\n      case W_SPACE: {\n        if (regDelimiter.test(value)) {\n          if (nest === 0) {\n            if (str) {\n              res.push(str.trim());\n              str = '';\n            }\n          } else {\n            str += ' ';\n          }\n        } else if (!str.endsWith(' ')) {\n          str += ' ';\n        }\n        break;\n      }\n      default: {\n        if (type === EOF) {\n          res.push(str.trim());\n          str = '';\n        } else {\n          str += value;\n        }\n      }\n    }\n  }\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * extract dashed-ident tokens\n * @param value - CSS value\n * @returns array of dashed-ident tokens\n */\nexport const extractDashedIdent = (value: string): string[] => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey({\n    namespace: NAMESPACE,\n    name: 'extractDashedIdent',\n    value\n  });\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string[];\n  }\n  const tokens = tokenize({ css: value });\n  const items = new Set();\n  while (tokens.length) {\n    const [type, value] = tokens.shift() as [TokenType, string];\n    if (type === IDENT && value.startsWith('--')) {\n      items.add(value);\n    }\n  }\n  const res = [...items] as string[];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * is color\n * @param value - CSS value\n * @param [opt] - options\n * @returns result\n */\nexport const isColor = (value: unknown, opt: Options = {}): boolean => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n    if (value && isString(value)) {\n      if (/^[a-z]+$/.test(value)) {\n        if (\n          /^(?:currentcolor|transparent)$/.test(value) ||\n          Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)\n        ) {\n          return true;\n        }\n      } else if (REG_COLOR.test(value) || REG_MIX.test(value)) {\n        return true;\n      } else if (REG_FN_COLOR.test(value)) {\n        opt.nullable = true;\n        if (!opt.format) {\n          opt.format = VAL_SPEC;\n        }\n        const resolvedValue = resolveColor(value, opt);\n        if (resolvedValue) {\n          return true;\n        }\n      }\n    }\n  }\n  return false;\n};\n\n/**\n * value to JSON string\n * @param value - CSS value\n * @param [func] - stringify function\n * @returns stringified value in JSON notation\n */\nexport const valueToJsonString = (\n  value: unknown,\n  func: boolean = false\n): string => {\n  if (typeof value === 'undefined') {\n    return '';\n  }\n  const res = JSON.stringify(value, (_key, val) => {\n    let replacedValue;\n    if (typeof val === 'undefined') {\n      replacedValue = null;\n    } else if (typeof val === 'function') {\n      if (func) {\n        replacedValue = val.toString().replace(/\\s/g, '').substring(0, HEX);\n      } else {\n        replacedValue = val.name;\n      }\n    } else if (val instanceof Map || val instanceof Set) {\n      replacedValue = [...val];\n    } else if (typeof val === 'bigint') {\n      replacedValue = val.toString();\n    } else {\n      replacedValue = val;\n    }\n    return replacedValue;\n  });\n  return res;\n};\n\n/**\n * round to specified precision\n * @param value - numeric value\n * @param bit - minimum bits\n * @returns rounded value\n */\nexport const roundToPrecision = (value: number, bit: number = 0): number => {\n  if (!Number.isFinite(value)) {\n    throw new TypeError(`${value} is not a finite number.`);\n  }\n  if (!Number.isFinite(bit)) {\n    throw new TypeError(`${bit} is not a finite number.`);\n  } else if (bit < 0 || bit > HEX) {\n    throw new RangeError(`${bit} is not between 0 and ${HEX}.`);\n  }\n  if (bit === 0) {\n    return Math.round(value);\n  }\n  let val;\n  if (bit === HEX) {\n    val = value.toPrecision(6);\n  } else if (bit < DEC) {\n    val = value.toPrecision(4);\n  } else {\n    val = value.toPrecision(5);\n  }\n  return parseFloat(val);\n};\n\n/**\n * interpolate hue\n * @param hueA - hue value\n * @param hueB - hue value\n * @param arc - shorter | longer | increasing | decreasing\n * @returns result - [hueA, hueB]\n */\nexport const interpolateHue = (\n  hueA: number,\n  hueB: number,\n  arc: string = 'shorter'\n): [number, number] => {\n  if (!Number.isFinite(hueA)) {\n    throw new TypeError(`${hueA} is not a finite number.`);\n  }\n  if (!Number.isFinite(hueB)) {\n    throw new TypeError(`${hueB} is not a finite number.`);\n  }\n  switch (arc) {\n    case 'decreasing': {\n      if (hueB > hueA) {\n        hueA += DEG;\n      }\n      break;\n    }\n    case 'increasing': {\n      if (hueB < hueA) {\n        hueB += DEG;\n      }\n      break;\n    }\n    case 'longer': {\n      if (hueB > hueA && hueB < hueA + DEG_HALF) {\n        hueA += DEG;\n      } else if (hueB > hueA + DEG_HALF * -1 && hueB <= hueA) {\n        hueB += DEG;\n      }\n      break;\n    }\n    case 'shorter':\n    default: {\n      if (hueB > hueA + DEG_HALF) {\n        hueA += DEG;\n      } else if (hueB < hueA + DEG_HALF * -1) {\n        hueB += DEG;\n      }\n    }\n  }\n  return [hueA, hueB];\n};\n", "/**\n * common\n */\n\n/* numeric constants */\nconst TYPE_FROM = 8;\nconst TYPE_TO = -1;\n\n/**\n * get type\n * @param o - object to check\n * @returns type of object\n */\nexport const getType = (o: unknown): string =>\n  Object.prototype.toString.call(o).slice(TYPE_FROM, TYPE_TO);\n\n/**\n * is string\n * @param o - object to check\n * @returns result\n */\nexport const isString = (o: unknown): o is string =>\n  typeof o === 'string' || o instanceof String;\n\n/**\n * is string or number\n * @param o - object to check\n * @returns result\n */\nexport const isStringOrNumber = (o: unknown): boolean =>\n  isString(o) || typeof o === 'number';\n", "/**\n * constant\n */\n\n/* values and units */\nconst _DIGIT = '(?:0|[1-9]\\\\d*)';\nconst _COMPARE = 'clamp|max|min';\nconst _EXPO = 'exp|hypot|log|pow|sqrt';\nconst _SIGN = 'abs|sign';\nconst _STEP = 'mod|rem|round';\nconst _TRIG = 'a?(?:cos|sin|tan)|atan2';\nconst _MATH = `${_COMPARE}|${_EXPO}|${_SIGN}|${_STEP}|${_TRIG}`;\nconst _CALC = `calc|${_MATH}`;\nconst _VAR = `var|${_CALC}`;\nexport const ANGLE = 'deg|g?rad|turn';\nexport const LENGTH =\n  '[cm]m|[dls]?v(?:[bhiw]|max|min)|in|p[ctx]|q|r?(?:[cl]h|cap|e[mx]|ic)';\nexport const NUM = `[+-]?(?:${_DIGIT}(?:\\\\.\\\\d*)?|\\\\.\\\\d+)(?:e-?${_DIGIT})?`;\nexport const NUM_POSITIVE = `\\\\+?(?:${_DIGIT}(?:\\\\.\\\\d*)?|\\\\.\\\\d+)(?:e-?${_DIGIT})?`;\nexport const NONE = 'none';\nexport const PCT = `${NUM}%`;\nexport const SYN_FN_CALC = `^(?:${_CALC})\\\\(|(?<=[*\\\\/\\\\s\\\\(])(?:${_CALC})\\\\(`;\nexport const SYN_FN_MATH_START = `^(?:${_MATH})\\\\($`;\nexport const SYN_FN_VAR = '^var\\\\(|(?<=[*\\\\/\\\\s\\\\(])var\\\\(';\nexport const SYN_FN_VAR_START = `^(?:${_VAR})\\\\(`;\n\n/* colors */\nconst _ALPHA = `(?:\\\\s*\\\\/\\\\s*(?:${NUM}|${PCT}|${NONE}))?`;\nconst _ALPHA_LV3 = `(?:\\\\s*,\\\\s*(?:${NUM}|${PCT}))?`;\nconst _COLOR_FUNC = '(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?';\nconst _COLOR_KEY = '[a-z]+|#[\\\\da-f]{3}|#[\\\\da-f]{4}|#[\\\\da-f]{6}|#[\\\\da-f]{8}';\nconst _CS_HUE = '(?:ok)?lch|hsl|hwb';\nconst _CS_HUE_ARC = '(?:de|in)creasing|longer|shorter';\nconst _NUM_ANGLE = `${NUM}(?:${ANGLE})?`;\nconst _NUM_ANGLE_NONE = `(?:${NUM}(?:${ANGLE})?|${NONE})`;\nconst _NUM_PCT_NONE = `(?:${NUM}|${PCT}|${NONE})`;\nexport const CS_HUE = `(?:${_CS_HUE})(?:\\\\s(?:${_CS_HUE_ARC})\\\\shue)?`;\nexport const CS_HUE_CAPT = `(${_CS_HUE})(?:\\\\s(${_CS_HUE_ARC})\\\\shue)?`;\nexport const CS_LAB = '(?:ok)?lab';\nexport const CS_LCH = '(?:ok)?lch';\nexport const CS_SRGB = 'srgb(?:-linear)?';\nexport const CS_RGB = `(?:a98|prophoto)-rgb|display-p3|rec2020|${CS_SRGB}`;\nexport const CS_XYZ = 'xyz(?:-d(?:50|65))?';\nexport const CS_RECT = `${CS_LAB}|${CS_RGB}|${CS_XYZ}`;\nexport const CS_MIX = `${CS_HUE}|${CS_RECT}`;\nexport const FN_COLOR = 'color(';\nexport const FN_MIX = 'color-mix(';\nexport const FN_REL = `(?:${_COLOR_FUNC})\\\\(\\\\s*from\\\\s+`;\nexport const FN_REL_CAPT = `(${_COLOR_FUNC})\\\\(\\\\s*from\\\\s+`;\nexport const FN_VAR = 'var(';\nexport const SYN_FN_COLOR = `(?:${CS_RGB}|${CS_XYZ})(?:\\\\s+${_NUM_PCT_NONE}){3}${_ALPHA}`;\nexport const SYN_FN_REL = `^${FN_REL}|(?<=[\\\\s])${FN_REL}`;\nexport const SYN_HSL = `${_NUM_ANGLE_NONE}(?:\\\\s+${_NUM_PCT_NONE}){2}${_ALPHA}`;\nexport const SYN_HSL_LV3 = `${_NUM_ANGLE}(?:\\\\s*,\\\\s*${PCT}){2}${_ALPHA_LV3}`;\nexport const SYN_LCH = `(?:${_NUM_PCT_NONE}\\\\s+){2}${_NUM_ANGLE_NONE}${_ALPHA}`;\nexport const SYN_MOD = `${_NUM_PCT_NONE}(?:\\\\s+${_NUM_PCT_NONE}){2}${_ALPHA}`;\nexport const SYN_RGB_LV3 = `(?:${NUM}(?:\\\\s*,\\\\s*${NUM}){2}|${PCT}(?:\\\\s*,\\\\s*${PCT}){2})${_ALPHA_LV3}`;\nexport const SYN_COLOR_TYPE = `${_COLOR_KEY}|hsla?\\\\(\\\\s*${SYN_HSL_LV3}\\\\s*\\\\)|rgba?\\\\(\\\\s*${SYN_RGB_LV3}\\\\s*\\\\)|(?:hsla?|hwb)\\\\(\\\\s*${SYN_HSL}\\\\s*\\\\)|(?:(?:ok)?lab|rgba?)\\\\(\\\\s*${SYN_MOD}\\\\s*\\\\)|(?:ok)?lch\\\\(\\\\s*${SYN_LCH}\\\\s*\\\\)|color\\\\(\\\\s*${SYN_FN_COLOR}\\\\s*\\\\)`;\nexport const SYN_MIX_PART = `(?:${SYN_COLOR_TYPE})(?:\\\\s+${PCT})?`;\nexport const SYN_MIX = `color-mix\\\\(\\\\s*in\\\\s+(?:${CS_MIX})\\\\s*,\\\\s*${SYN_MIX_PART}\\\\s*,\\\\s*${SYN_MIX_PART}\\\\s*\\\\)`;\nexport const SYN_MIX_CAPT = `color-mix\\\\(\\\\s*in\\\\s+(${CS_MIX})\\\\s*,\\\\s*(${SYN_MIX_PART})\\\\s*,\\\\s*(${SYN_MIX_PART})\\\\s*\\\\)`;\n\n/* formats */\nexport const VAL_COMP = 'computedValue';\nexport const VAL_MIX = 'mixValue';\nexport const VAL_SPEC = 'specifiedValue';\n", "/**\n * color\n *\n * Ref: CSS Color Module Level 4\n *      Sample code for Color Conversions\n *      https://w3c.github.io/csswg-drafts/css-color-4/#color-conversion-code\n */\n\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString } from './common';\nimport { interpolateHue, roundToPrecision } from './util';\nimport {\n  ColorChannels,\n  ComputedColorChannels,\n  Options,\n  MatchedRegExp,\n  SpecifiedColorChannels,\n  StringColorChannels,\n  StringColorSpacedChannels\n} from './typedef';\n\n/* constants */\nimport {\n  ANGLE,\n  CS_HUE_CAPT,\n  CS_MIX,\n  CS_RGB,\n  CS_XYZ,\n  FN_COLOR,\n  FN_MIX,\n  NONE,\n  NUM,\n  PCT,\n  SYN_COLOR_TYPE,\n  SYN_FN_COLOR,\n  SYN_HSL,\n  SYN_HSL_LV3,\n  SYN_LCH,\n  SYN_MIX,\n  SYN_MIX_CAPT,\n  SYN_MIX_PART,\n  SYN_MOD,\n  SYN_RGB_LV3,\n  VAL_COMP,\n  VAL_MIX,\n  VAL_SPEC\n} from './constant';\nconst NAMESPACE = 'color';\n\n/* numeric constants */\nconst PPTH = 0.001;\nconst HALF = 0.5;\nconst DUO = 2;\nconst TRIA = 3;\nconst QUAD = 4;\nconst OCT = 8;\nconst DEC = 10;\nconst DOZ = 12;\nconst HEX = 16;\nconst SEXA = 60;\nconst DEG_HALF = 180;\nconst DEG = 360;\nconst MAX_PCT = 100;\nconst MAX_RGB = 255;\nconst POW_SQR = 2;\nconst POW_CUBE = 3;\nconst POW_LINEAR = 2.4;\nconst LINEAR_COEF = 12.92;\nconst LINEAR_OFFSET = 0.055;\nconst LAB_L = 116;\nconst LAB_A = 500;\nconst LAB_B = 200;\nconst LAB_EPSILON = 216 / 24389;\nconst LAB_KAPPA = 24389 / 27;\n\n/* type definitions */\n/**\n * @type NumStrColorChannels - string or numeric color channels\n */\ntype NumStrColorChannels = [\n  x: number | string,\n  y: number | string,\n  z: number | string,\n  alpha: number | string\n];\n\n/**\n * @type TriColorChannels - color channels without alpha\n */\ntype TriColorChannels = [x: number, y: number, z: number];\n\n/**\n * @type ColorMatrix - color matrix\n */\ntype ColorMatrix = [\n  r1: TriColorChannels,\n  r2: TriColorChannels,\n  r3: TriColorChannels\n];\n\n/* white point */\nconst D50: TriColorChannels = [\n  0.3457 / 0.3585,\n  1.0,\n  (1.0 - 0.3457 - 0.3585) / 0.3585\n];\nconst MATRIX_D50_TO_D65: ColorMatrix = [\n  [0.955473421488075, -0.02309845494876471, 0.06325924320057072],\n  [-0.0283697093338637, 1.0099953980813041, 0.021041441191917323],\n  [0.012314014864481998, -0.020507649298898964, 1.330365926242124]\n];\nconst MATRIX_D65_TO_D50: ColorMatrix = [\n  [1.0479297925449969, 0.022946870601609652, -0.05019226628920524],\n  [0.02962780877005599, 0.9904344267538799, -0.017073799063418826],\n  [-0.009243040646204504, 0.015055191490298152, 0.7518742814281371]\n];\n\n/* color space */\nconst MATRIX_L_RGB_TO_XYZ: ColorMatrix = [\n  [506752 / 1228815, 87881 / 245763, 12673 / 70218],\n  [87098 / 409605, 175762 / 245763, 12673 / 175545],\n  [7918 / 409605, 87881 / 737289, 1001167 / 1053270]\n];\nconst MATRIX_XYZ_TO_L_RGB: ColorMatrix = [\n  [12831 / 3959, -329 / 214, -1974 / 3959],\n  [-851781 / 878810, 1648619 / 878810, 36519 / 878810],\n  [705 / 12673, -2585 / 12673, 705 / 667]\n];\nconst MATRIX_XYZ_TO_LMS: ColorMatrix = [\n  [0.819022437996703, 0.3619062600528904, -0.1288737815209879],\n  [0.0329836539323885, 0.9292868615863434, 0.0361446663506424],\n  [0.0481771893596242, 0.2642395317527308, 0.6335478284694309]\n];\nconst MATRIX_LMS_TO_XYZ: ColorMatrix = [\n  [1.2268798758459243, -0.5578149944602171, 0.2813910456659647],\n  [-0.0405757452148008, 1.112286803280317, -0.0717110580655164],\n  [-0.0763729366746601, -0.4214933324022432, 1.5869240198367816]\n];\nconst MATRIX_OKLAB_TO_LMS: ColorMatrix = [\n  [1.0, 0.3963377773761749, 0.2158037573099136],\n  [1.0, -0.1055613458156586, -0.0638541728258133],\n  [1.0, -0.0894841775298119, -1.2914855480194092]\n];\nconst MATRIX_LMS_TO_OKLAB: ColorMatrix = [\n  [0.210454268309314, 0.7936177747023054, -0.0040720430116193],\n  [1.9779985324311684, -2.4285922420485799, 0.450593709617411],\n  [0.0259040424655478, 0.7827717124575296, -0.8086757549230774]\n];\nconst MATRIX_P3_TO_XYZ: ColorMatrix = [\n  [608311 / 1250200, 189793 / 714400, 198249 / 1000160],\n  [35783 / 156275, 247089 / 357200, 198249 / 2500400],\n  [0 / 1, 32229 / 714400, 5220557 / 5000800]\n];\nconst MATRIX_REC2020_TO_XYZ: ColorMatrix = [\n  [63426534 / 99577255, 20160776 / 139408157, 47086771 / 278816314],\n  [26158966 / 99577255, 472592308 / 697040785, 8267143 / 139408157],\n  [0 / 1, 19567812 / 697040785, 295819943 / 278816314]\n];\nconst MATRIX_A98_TO_XYZ: ColorMatrix = [\n  [573536 / 994567, 263643 / 1420810, 187206 / 994567],\n  [591459 / 1989134, 6239551 / 9945670, 374412 / 4972835],\n  [53769 / 1989134, 351524 / 4972835, 4929758 / 4972835]\n];\nconst MATRIX_PROPHOTO_TO_XYZ_D50: ColorMatrix = [\n  [0.7977666449006423, 0.13518129740053308, 0.0313477341283922],\n  [0.2880748288194013, 0.711835234241873, 0.00008993693872564],\n  [0.0, 0.0, 0.8251046025104602]\n];\n\n/* regexp */\nconst REG_COLOR = new RegExp(`^(?:${SYN_COLOR_TYPE})$`);\nconst REG_CS_HUE = new RegExp(`^${CS_HUE_CAPT}$`);\nconst REG_CS_XYZ = /^xyz(?:-d(?:50|65))?$/;\nconst REG_CURRENT = /^currentColor$/i;\nconst REG_FN_COLOR = new RegExp(`^color\\\\(\\\\s*(${SYN_FN_COLOR})\\\\s*\\\\)$`);\nconst REG_HSL = new RegExp(`^hsla?\\\\(\\\\s*(${SYN_HSL}|${SYN_HSL_LV3})\\\\s*\\\\)$`);\nconst REG_HWB = new RegExp(`^hwb\\\\(\\\\s*(${SYN_HSL})\\\\s*\\\\)$`);\nconst REG_LAB = new RegExp(`^lab\\\\(\\\\s*(${SYN_MOD})\\\\s*\\\\)$`);\nconst REG_LCH = new RegExp(`^lch\\\\(\\\\s*(${SYN_LCH})\\\\s*\\\\)$`);\nconst REG_MIX = new RegExp(`^${SYN_MIX}$`);\nconst REG_MIX_CAPT = new RegExp(`^${SYN_MIX_CAPT}$`);\nconst REG_MIX_NEST = new RegExp(`${SYN_MIX}`, 'g');\nconst REG_OKLAB = new RegExp(`^oklab\\\\(\\\\s*(${SYN_MOD})\\\\s*\\\\)$`);\nconst REG_OKLCH = new RegExp(`^oklch\\\\(\\\\s*(${SYN_LCH})\\\\s*\\\\)$`);\nconst REG_SPEC = /^(?:specifi|comput)edValue$/;\n\n/**\n * named colors\n */\nexport const NAMED_COLORS = {\n  aliceblue: [0xf0, 0xf8, 0xff],\n  antiquewhite: [0xfa, 0xeb, 0xd7],\n  aqua: [0x00, 0xff, 0xff],\n  aquamarine: [0x7f, 0xff, 0xd4],\n  azure: [0xf0, 0xff, 0xff],\n  beige: [0xf5, 0xf5, 0xdc],\n  bisque: [0xff, 0xe4, 0xc4],\n  black: [0x00, 0x00, 0x00],\n  blanchedalmond: [0xff, 0xeb, 0xcd],\n  blue: [0x00, 0x00, 0xff],\n  blueviolet: [0x8a, 0x2b, 0xe2],\n  brown: [0xa5, 0x2a, 0x2a],\n  burlywood: [0xde, 0xb8, 0x87],\n  cadetblue: [0x5f, 0x9e, 0xa0],\n  chartreuse: [0x7f, 0xff, 0x00],\n  chocolate: [0xd2, 0x69, 0x1e],\n  coral: [0xff, 0x7f, 0x50],\n  cornflowerblue: [0x64, 0x95, 0xed],\n  cornsilk: [0xff, 0xf8, 0xdc],\n  crimson: [0xdc, 0x14, 0x3c],\n  cyan: [0x00, 0xff, 0xff],\n  darkblue: [0x00, 0x00, 0x8b],\n  darkcyan: [0x00, 0x8b, 0x8b],\n  darkgoldenrod: [0xb8, 0x86, 0x0b],\n  darkgray: [0xa9, 0xa9, 0xa9],\n  darkgreen: [0x00, 0x64, 0x00],\n  darkgrey: [0xa9, 0xa9, 0xa9],\n  darkkhaki: [0xbd, 0xb7, 0x6b],\n  darkmagenta: [0x8b, 0x00, 0x8b],\n  darkolivegreen: [0x55, 0x6b, 0x2f],\n  darkorange: [0xff, 0x8c, 0x00],\n  darkorchid: [0x99, 0x32, 0xcc],\n  darkred: [0x8b, 0x00, 0x00],\n  darksalmon: [0xe9, 0x96, 0x7a],\n  darkseagreen: [0x8f, 0xbc, 0x8f],\n  darkslateblue: [0x48, 0x3d, 0x8b],\n  darkslategray: [0x2f, 0x4f, 0x4f],\n  darkslategrey: [0x2f, 0x4f, 0x4f],\n  darkturquoise: [0x00, 0xce, 0xd1],\n  darkviolet: [0x94, 0x00, 0xd3],\n  deeppink: [0xff, 0x14, 0x93],\n  deepskyblue: [0x00, 0xbf, 0xff],\n  dimgray: [0x69, 0x69, 0x69],\n  dimgrey: [0x69, 0x69, 0x69],\n  dodgerblue: [0x1e, 0x90, 0xff],\n  firebrick: [0xb2, 0x22, 0x22],\n  floralwhite: [0xff, 0xfa, 0xf0],\n  forestgreen: [0x22, 0x8b, 0x22],\n  fuchsia: [0xff, 0x00, 0xff],\n  gainsboro: [0xdc, 0xdc, 0xdc],\n  ghostwhite: [0xf8, 0xf8, 0xff],\n  gold: [0xff, 0xd7, 0x00],\n  goldenrod: [0xda, 0xa5, 0x20],\n  gray: [0x80, 0x80, 0x80],\n  green: [0x00, 0x80, 0x00],\n  greenyellow: [0xad, 0xff, 0x2f],\n  grey: [0x80, 0x80, 0x80],\n  honeydew: [0xf0, 0xff, 0xf0],\n  hotpink: [0xff, 0x69, 0xb4],\n  indianred: [0xcd, 0x5c, 0x5c],\n  indigo: [0x4b, 0x00, 0x82],\n  ivory: [0xff, 0xff, 0xf0],\n  khaki: [0xf0, 0xe6, 0x8c],\n  lavender: [0xe6, 0xe6, 0xfa],\n  lavenderblush: [0xff, 0xf0, 0xf5],\n  lawngreen: [0x7c, 0xfc, 0x00],\n  lemonchiffon: [0xff, 0xfa, 0xcd],\n  lightblue: [0xad, 0xd8, 0xe6],\n  lightcoral: [0xf0, 0x80, 0x80],\n  lightcyan: [0xe0, 0xff, 0xff],\n  lightgoldenrodyellow: [0xfa, 0xfa, 0xd2],\n  lightgray: [0xd3, 0xd3, 0xd3],\n  lightgreen: [0x90, 0xee, 0x90],\n  lightgrey: [0xd3, 0xd3, 0xd3],\n  lightpink: [0xff, 0xb6, 0xc1],\n  lightsalmon: [0xff, 0xa0, 0x7a],\n  lightseagreen: [0x20, 0xb2, 0xaa],\n  lightskyblue: [0x87, 0xce, 0xfa],\n  lightslategray: [0x77, 0x88, 0x99],\n  lightslategrey: [0x77, 0x88, 0x99],\n  lightsteelblue: [0xb0, 0xc4, 0xde],\n  lightyellow: [0xff, 0xff, 0xe0],\n  lime: [0x00, 0xff, 0x00],\n  limegreen: [0x32, 0xcd, 0x32],\n  linen: [0xfa, 0xf0, 0xe6],\n  magenta: [0xff, 0x00, 0xff],\n  maroon: [0x80, 0x00, 0x00],\n  mediumaquamarine: [0x66, 0xcd, 0xaa],\n  mediumblue: [0x00, 0x00, 0xcd],\n  mediumorchid: [0xba, 0x55, 0xd3],\n  mediumpurple: [0x93, 0x70, 0xdb],\n  mediumseagreen: [0x3c, 0xb3, 0x71],\n  mediumslateblue: [0x7b, 0x68, 0xee],\n  mediumspringgreen: [0x00, 0xfa, 0x9a],\n  mediumturquoise: [0x48, 0xd1, 0xcc],\n  mediumvioletred: [0xc7, 0x15, 0x85],\n  midnightblue: [0x19, 0x19, 0x70],\n  mintcream: [0xf5, 0xff, 0xfa],\n  mistyrose: [0xff, 0xe4, 0xe1],\n  moccasin: [0xff, 0xe4, 0xb5],\n  navajowhite: [0xff, 0xde, 0xad],\n  navy: [0x00, 0x00, 0x80],\n  oldlace: [0xfd, 0xf5, 0xe6],\n  olive: [0x80, 0x80, 0x00],\n  olivedrab: [0x6b, 0x8e, 0x23],\n  orange: [0xff, 0xa5, 0x00],\n  orangered: [0xff, 0x45, 0x00],\n  orchid: [0xda, 0x70, 0xd6],\n  palegoldenrod: [0xee, 0xe8, 0xaa],\n  palegreen: [0x98, 0xfb, 0x98],\n  paleturquoise: [0xaf, 0xee, 0xee],\n  palevioletred: [0xdb, 0x70, 0x93],\n  papayawhip: [0xff, 0xef, 0xd5],\n  peachpuff: [0xff, 0xda, 0xb9],\n  peru: [0xcd, 0x85, 0x3f],\n  pink: [0xff, 0xc0, 0xcb],\n  plum: [0xdd, 0xa0, 0xdd],\n  powderblue: [0xb0, 0xe0, 0xe6],\n  purple: [0x80, 0x00, 0x80],\n  rebeccapurple: [0x66, 0x33, 0x99],\n  red: [0xff, 0x00, 0x00],\n  rosybrown: [0xbc, 0x8f, 0x8f],\n  royalblue: [0x41, 0x69, 0xe1],\n  saddlebrown: [0x8b, 0x45, 0x13],\n  salmon: [0xfa, 0x80, 0x72],\n  sandybrown: [0xf4, 0xa4, 0x60],\n  seagreen: [0x2e, 0x8b, 0x57],\n  seashell: [0xff, 0xf5, 0xee],\n  sienna: [0xa0, 0x52, 0x2d],\n  silver: [0xc0, 0xc0, 0xc0],\n  skyblue: [0x87, 0xce, 0xeb],\n  slateblue: [0x6a, 0x5a, 0xcd],\n  slategray: [0x70, 0x80, 0x90],\n  slategrey: [0x70, 0x80, 0x90],\n  snow: [0xff, 0xfa, 0xfa],\n  springgreen: [0x00, 0xff, 0x7f],\n  steelblue: [0x46, 0x82, 0xb4],\n  tan: [0xd2, 0xb4, 0x8c],\n  teal: [0x00, 0x80, 0x80],\n  thistle: [0xd8, 0xbf, 0xd8],\n  tomato: [0xff, 0x63, 0x47],\n  turquoise: [0x40, 0xe0, 0xd0],\n  violet: [0xee, 0x82, 0xee],\n  wheat: [0xf5, 0xde, 0xb3],\n  white: [0xff, 0xff, 0xff],\n  whitesmoke: [0xf5, 0xf5, 0xf5],\n  yellow: [0xff, 0xff, 0x00],\n  yellowgreen: [0x9a, 0xcd, 0x32]\n} as const satisfies {\n  [key: string]: TriColorChannels;\n};\n\n/**\n * cache invalid color value\n * @param key - cache key\n * @param nullable - is nullable\n * @returns cached value\n */\nexport const cacheInvalidColorValue = (\n  cacheKey: string,\n  format: string,\n  nullable: boolean = false\n): SpecifiedColorChannels | string | NullObject => {\n  if (format === VAL_SPEC) {\n    const res = '';\n    setCache(cacheKey, res);\n    return res;\n  }\n  if (nullable) {\n    setCache(cacheKey, null);\n    return new NullObject();\n  }\n  const res: SpecifiedColorChannels = ['rgb', 0, 0, 0, 0];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve invalid color value\n * @param format - output format\n * @param nullable - is nullable\n * @returns resolved value\n */\nexport const resolveInvalidColorValue = (\n  format: string,\n  nullable: boolean = false\n): SpecifiedColorChannels | string | NullObject => {\n  switch (format) {\n    case 'hsl':\n    case 'hwb':\n    case VAL_MIX: {\n      return new NullObject();\n    }\n    case VAL_SPEC: {\n      return '';\n    }\n    default: {\n      if (nullable) {\n        return new NullObject();\n      }\n      return ['rgb', 0, 0, 0, 0] as SpecifiedColorChannels;\n    }\n  }\n};\n\n/**\n * validate color components\n * @param arr - color components\n * @param [opt] - options\n * @param [opt.alpha] - alpha channel\n * @param [opt.minLength] - min length\n * @param [opt.maxLength] - max length\n * @param [opt.minRange] - min range\n * @param [opt.maxRange] - max range\n * @param [opt.validateRange] - validate range\n * @returns result - validated color components\n */\nexport const validateColorComponents = (\n  arr: ColorChannels | TriColorChannels,\n  opt: {\n    alpha?: boolean;\n    minLength?: number;\n    maxLength?: number;\n    minRange?: number;\n    maxRange?: number;\n    validateRange?: boolean;\n  } = {}\n): ColorChannels | TriColorChannels => {\n  if (!Array.isArray(arr)) {\n    throw new TypeError(`${arr} is not an array.`);\n  }\n  const {\n    alpha = false,\n    minLength = TRIA,\n    maxLength = QUAD,\n    minRange = 0,\n    maxRange = 1,\n    validateRange = true\n  } = opt;\n  if (!Number.isFinite(minLength)) {\n    throw new TypeError(`${minLength} is not a number.`);\n  }\n  if (!Number.isFinite(maxLength)) {\n    throw new TypeError(`${maxLength} is not a number.`);\n  }\n  if (!Number.isFinite(minRange)) {\n    throw new TypeError(`${minRange} is not a number.`);\n  }\n  if (!Number.isFinite(maxRange)) {\n    throw new TypeError(`${maxRange} is not a number.`);\n  }\n  const l = arr.length;\n  if (l < minLength || l > maxLength) {\n    throw new Error(`Unexpected array length ${l}.`);\n  }\n  let i = 0;\n  while (i < l) {\n    const v = arr[i] as number;\n    if (!Number.isFinite(v)) {\n      throw new TypeError(`${v} is not a number.`);\n    } else if (i < TRIA && validateRange && (v < minRange || v > maxRange)) {\n      throw new RangeError(`${v} is not between ${minRange} and ${maxRange}.`);\n    } else if (i === TRIA && (v < 0 || v > 1)) {\n      throw new RangeError(`${v} is not between 0 and 1.`);\n    }\n    i++;\n  }\n  if (alpha && l === TRIA) {\n    arr.push(1);\n  }\n  return arr;\n};\n\n/**\n * transform matrix\n * @param mtx - 3 * 3 matrix\n * @param vct - vector\n * @param [skip] - skip validate\n * @returns TriColorChannels - [p1, p2, p3]\n */\nexport const transformMatrix = (\n  mtx: ColorMatrix,\n  vct: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!Array.isArray(mtx)) {\n    throw new TypeError(`${mtx} is not an array.`);\n  } else if (mtx.length !== TRIA) {\n    throw new Error(`Unexpected array length ${mtx.length}.`);\n  } else if (!skip) {\n    for (let i of mtx) {\n      i = validateColorComponents(i as TriColorChannels, {\n        maxLength: TRIA,\n        validateRange: false\n      }) as TriColorChannels;\n    }\n  }\n  const [[r1c1, r1c2, r1c3], [r2c1, r2c2, r2c3], [r3c1, r3c2, r3c3]] = mtx;\n  let v1, v2, v3;\n  if (skip) {\n    [v1, v2, v3] = vct;\n  } else {\n    [v1, v2, v3] = validateColorComponents(vct, {\n      maxLength: TRIA,\n      validateRange: false\n    });\n  }\n  const p1 = r1c1 * v1 + r1c2 * v2 + r1c3 * v3;\n  const p2 = r2c1 * v1 + r2c2 * v2 + r2c3 * v3;\n  const p3 = r3c1 * v1 + r3c2 * v2 + r3c3 * v3;\n  return [p1, p2, p3];\n};\n\n/**\n * normalize color components\n * @param colorA - color components [v1, v2, v3, v4]\n * @param colorB - color components [v1, v2, v3, v4]\n * @param [skip] - skip validate\n * @returns result - [colorA, colorB]\n */\nexport const normalizeColorComponents = (\n  colorA: [number | string, number | string, number | string, number | string],\n  colorB: [number | string, number | string, number | string, number | string],\n  skip: boolean = false\n): [ColorChannels, ColorChannels] => {\n  if (!Array.isArray(colorA)) {\n    throw new TypeError(`${colorA} is not an array.`);\n  } else if (colorA.length !== QUAD) {\n    throw new Error(`Unexpected array length ${colorA.length}.`);\n  }\n  if (!Array.isArray(colorB)) {\n    throw new TypeError(`${colorB} is not an array.`);\n  } else if (colorB.length !== QUAD) {\n    throw new Error(`Unexpected array length ${colorB.length}.`);\n  }\n  let i = 0;\n  while (i < QUAD) {\n    if (colorA[i] === NONE && colorB[i] === NONE) {\n      colorA[i] = 0;\n      colorB[i] = 0;\n    } else if (colorA[i] === NONE) {\n      colorA[i] = colorB[i] as number;\n    } else if (colorB[i] === NONE) {\n      colorB[i] = colorA[i] as number;\n    }\n    i++;\n  }\n  if (skip) {\n    return [colorA as ColorChannels, colorB as ColorChannels];\n  }\n  const validatedColorA = validateColorComponents(colorA as ColorChannels, {\n    minLength: QUAD,\n    validateRange: false\n  });\n  const validatedColorB = validateColorComponents(colorB as ColorChannels, {\n    minLength: QUAD,\n    validateRange: false\n  });\n  return [validatedColorA as ColorChannels, validatedColorB as ColorChannels];\n};\n\n/**\n * number to hex string\n * @param value - numeric value\n * @returns hex string\n */\nexport const numberToHexString = (value: number): string => {\n  if (!Number.isFinite(value)) {\n    throw new TypeError(`${value} is not a number.`);\n  } else {\n    value = Math.round(value);\n    if (value < 0 || value > MAX_RGB) {\n      throw new RangeError(`${value} is not between 0 and ${MAX_RGB}.`);\n    }\n  }\n  let hex = value.toString(HEX);\n  if (hex.length === 1) {\n    hex = `0${hex}`;\n  }\n  return hex;\n};\n\n/**\n * angle to deg\n * @param angle\n * @returns deg: 0..360\n */\nexport const angleToDeg = (angle: string): number => {\n  if (isString(angle)) {\n    angle = angle.trim();\n  } else {\n    throw new TypeError(`${angle} is not a string.`);\n  }\n  const GRAD = DEG / 400;\n  const RAD = DEG / (Math.PI * DUO);\n  const reg = new RegExp(`^(${NUM})(${ANGLE})?$`);\n  if (!reg.test(angle)) {\n    throw new SyntaxError(`Invalid property value: ${angle}`);\n  }\n  const [, value, unit] = angle.match(reg) as MatchedRegExp;\n  let deg;\n  switch (unit) {\n    case 'grad':\n      deg = parseFloat(value) * GRAD;\n      break;\n    case 'rad':\n      deg = parseFloat(value) * RAD;\n      break;\n    case 'turn':\n      deg = parseFloat(value) * DEG;\n      break;\n    default:\n      deg = parseFloat(value);\n  }\n  deg %= DEG;\n  if (deg < 0) {\n    deg += DEG;\n  } else if (Object.is(deg, -0)) {\n    deg = 0;\n  }\n  return deg;\n};\n\n/**\n * parse alpha\n * @param [alpha] - alpha value\n * @returns alpha: 0..1\n */\nexport const parseAlpha = (alpha: string = ''): number => {\n  if (isString(alpha)) {\n    alpha = alpha.trim();\n    if (!alpha) {\n      alpha = '1';\n    } else if (alpha === NONE) {\n      alpha = '0';\n    } else {\n      let a;\n      if (alpha.endsWith('%')) {\n        a = parseFloat(alpha) / MAX_PCT;\n      } else {\n        a = parseFloat(alpha);\n      }\n      if (!Number.isFinite(a)) {\n        throw new TypeError(`${a} is not a finite number.`);\n      }\n      if (a < PPTH) {\n        alpha = '0';\n      } else if (a > 1) {\n        alpha = '1';\n      } else {\n        alpha = a.toFixed(TRIA);\n      }\n    }\n  } else {\n    alpha = '1';\n  }\n  return parseFloat(alpha);\n};\n\n/**\n * parse hex alpha\n * @param value - alpha value in hex string\n * @returns alpha: 0..1\n */\nexport const parseHexAlpha = (value: string): number => {\n  if (isString(value)) {\n    if (value === '') {\n      throw new SyntaxError('Invalid property value: (empty string)');\n    }\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  let alpha = parseInt(value, HEX);\n  if (alpha <= 0) {\n    return 0;\n  }\n  if (alpha >= MAX_RGB) {\n    return 1;\n  }\n  const alphaMap = new Map();\n  for (let i = 1; i < MAX_PCT; i++) {\n    alphaMap.set(Math.round((i * MAX_RGB) / MAX_PCT), i);\n  }\n  if (alphaMap.has(alpha)) {\n    alpha = alphaMap.get(alpha) / MAX_PCT;\n  } else {\n    alpha = Math.round(alpha / MAX_RGB / PPTH) * PPTH;\n  }\n  return parseFloat(alpha.toFixed(TRIA));\n};\n\n/**\n * transform rgb to linear rgb\n * @param rgb - [r, g, b] r|g|b: 0..255\n * @param [skip] - skip validate\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..1\n */\nexport const transformRgbToLinearRgb = (\n  rgb: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  let rr, gg, bb;\n  if (skip) {\n    [rr, gg, bb] = rgb;\n  } else {\n    [rr, gg, bb] = validateColorComponents(rgb, {\n      maxLength: TRIA,\n      maxRange: MAX_RGB\n    });\n  }\n  let r = rr / MAX_RGB;\n  let g = gg / MAX_RGB;\n  let b = bb / MAX_RGB;\n  const COND_POW = 0.04045;\n  if (r > COND_POW) {\n    r = Math.pow((r + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    r /= LINEAR_COEF;\n  }\n  if (g > COND_POW) {\n    g = Math.pow((g + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    g /= LINEAR_COEF;\n  }\n  if (b > COND_POW) {\n    b = Math.pow((b + LINEAR_OFFSET) / (1 + LINEAR_OFFSET), POW_LINEAR);\n  } else {\n    b /= LINEAR_COEF;\n  }\n  return [r, g, b];\n};\n\n/**\n * transform rgb to xyz\n * @param rgb - [r, g, b] r|g|b: 0..255\n * @param [skip] - skip validate\n * @returns TriColorChannels - [x, y, z]\n */\nexport const transformRgbToXyz = (\n  rgb: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    rgb = validateColorComponents(rgb, {\n      maxLength: TRIA,\n      maxRange: MAX_RGB\n    }) as TriColorChannels;\n  }\n  rgb = transformRgbToLinearRgb(rgb, true);\n  const xyz = transformMatrix(MATRIX_L_RGB_TO_XYZ, rgb, true);\n  return xyz;\n};\n\n/**\n * transform rgb to xyz-d50\n * @param rgb - [r, g, b] r|g|b: 0..255 alpha: 0..1\n * @returns TriColorChannels - [x, y, z]\n */\nexport const transformRgbToXyzD50 = (\n  rgb: TriColorChannels\n): TriColorChannels => {\n  let xyz = transformRgbToXyz(rgb);\n  xyz = transformMatrix(MATRIX_D65_TO_D50, xyz, true);\n  return xyz;\n};\n\n/**\n * transform linear rgb to rgb\n * @param rgb - [r, g, b] r|g|b: 0..1\n * @param [round] - round result\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..255\n */\nexport const transformLinearRgbToRgb = (\n  rgb: TriColorChannels,\n  round: boolean = false\n): TriColorChannels => {\n  let [r, g, b] = validateColorComponents(rgb, {\n    maxLength: TRIA\n  });\n  const COND_POW = 809 / 258400;\n  if (r > COND_POW) {\n    r = Math.pow(r, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    r *= LINEAR_COEF;\n  }\n  r *= MAX_RGB;\n  if (g > COND_POW) {\n    g = Math.pow(g, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    g *= LINEAR_COEF;\n  }\n  g *= MAX_RGB;\n  if (b > COND_POW) {\n    b = Math.pow(b, 1 / POW_LINEAR) * (1 + LINEAR_OFFSET) - LINEAR_OFFSET;\n  } else {\n    b *= LINEAR_COEF;\n  }\n  b *= MAX_RGB;\n  return [\n    round ? Math.round(r) : r,\n    round ? Math.round(g) : g,\n    round ? Math.round(b) : b\n  ];\n};\n\n/**\n * transform xyz to rgb\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..255\n */\nexport const transformXyzToRgb = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  let [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, xyz, true);\n  [r, g, b] = transformLinearRgbToRgb(\n    [\n      Math.min(Math.max(r, 0), 1),\n      Math.min(Math.max(g, 0), 1),\n      Math.min(Math.max(b, 0), 1)\n    ],\n    true\n  );\n  return [r, g, b];\n};\n\n/**\n * transform xyz to xyz-d50\n * @param xyz - [x, y, z]\n * @returns TriColorChannels - [x, y, z]\n */\nexport const transformXyzToXyzD50 = (\n  xyz: TriColorChannels\n): TriColorChannels => {\n  xyz = validateColorComponents(xyz, {\n    maxLength: TRIA,\n    validateRange: false\n  }) as TriColorChannels;\n  xyz = transformMatrix(MATRIX_D65_TO_D50, xyz, true);\n  return xyz;\n};\n\n/**\n * transform xyz to hsl\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [h, s, l]\n */\nexport const transformXyzToHsl = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [rr, gg, bb] = transformXyzToRgb(xyz, skip);\n  const r = rr / MAX_RGB;\n  const g = gg / MAX_RGB;\n  const b = bb / MAX_RGB;\n  const max = Math.max(r, g, b);\n  const min = Math.min(r, g, b);\n  const d = max - min;\n  const l = (max + min) * HALF * MAX_PCT;\n  let h, s;\n  if (Math.round(l) === 0 || Math.round(l) === MAX_PCT) {\n    h = 0;\n    s = 0;\n  } else {\n    s = (d / (1 - Math.abs(max + min - 1))) * MAX_PCT;\n    if (s === 0) {\n      h = 0;\n    } else {\n      switch (max) {\n        case r:\n          h = (g - b) / d;\n          break;\n        case g:\n          h = (b - r) / d + DUO;\n          break;\n        case b:\n        default:\n          h = (r - g) / d + QUAD;\n          break;\n      }\n      h = (h * SEXA) % DEG;\n      if (h < 0) {\n        h += DEG;\n      }\n    }\n  }\n  return [h, s, l];\n};\n\n/**\n * transform xyz to hwb\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [h, w, b]\n */\nexport const transformXyzToHwb = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [r, g, b] = transformXyzToRgb(xyz, skip);\n  const wh = Math.min(r, g, b) / MAX_RGB;\n  const bk = 1 - Math.max(r, g, b) / MAX_RGB;\n  let h;\n  if (wh + bk === 1) {\n    h = 0;\n  } else {\n    [h] = transformXyzToHsl(xyz);\n  }\n  return [h, wh * MAX_PCT, bk * MAX_PCT];\n};\n\n/**\n * transform xyz to oklab\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, a, b]\n */\nexport const transformXyzToOklab = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  const lms = transformMatrix(MATRIX_XYZ_TO_LMS, xyz, true);\n  const xyzLms = lms.map(c => Math.cbrt(c)) as TriColorChannels;\n  let [l, a, b] = transformMatrix(MATRIX_LMS_TO_OKLAB, xyzLms, true);\n  l = Math.min(Math.max(l, 0), 1);\n  const lPct = Math.round(parseFloat(l.toFixed(QUAD)) * MAX_PCT);\n  if (lPct === 0 || lPct === MAX_PCT) {\n    a = 0;\n    b = 0;\n  }\n  return [l, a, b];\n};\n\n/**\n * transform xyz to oklch\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, c, h]\n */\nexport const transformXyzToOklch = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [l, a, b] = transformXyzToOklab(xyz, skip);\n  let c, h;\n  const lPct = Math.round(parseFloat(l.toFixed(QUAD)) * MAX_PCT);\n  if (lPct === 0 || lPct === MAX_PCT) {\n    c = 0;\n    h = 0;\n  } else {\n    c = Math.max(Math.sqrt(Math.pow(a, POW_SQR) + Math.pow(b, POW_SQR)), 0);\n    if (parseFloat(c.toFixed(QUAD)) === 0) {\n      h = 0;\n    } else {\n      h = (Math.atan2(b, a) * DEG_HALF) / Math.PI;\n      if (h < 0) {\n        h += DEG;\n      }\n    }\n  }\n  return [l, c, h];\n};\n\n/**\n * transform xyz D50 to rgb\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [r, g, b] r|g|b: 0..255\n */\nexport const transformXyzD50ToRgb = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  const xyzD65 = transformMatrix(MATRIX_D50_TO_D65, xyz, true);\n  const rgb = transformXyzToRgb(xyzD65, true);\n  return rgb;\n};\n\n/**\n * transform xyz-d50 to lab\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, a, b]\n */\nexport const transformXyzD50ToLab = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  if (!skip) {\n    xyz = validateColorComponents(xyz, {\n      maxLength: TRIA,\n      validateRange: false\n    }) as TriColorChannels;\n  }\n  const xyzD50 = xyz.map((val, i) => val / (D50[i] as number));\n  const [f0, f1, f2] = xyzD50.map(val =>\n    val > LAB_EPSILON ? Math.cbrt(val) : (val * LAB_KAPPA + HEX) / LAB_L\n  ) as TriColorChannels;\n  const l = Math.min(Math.max(LAB_L * f1 - HEX, 0), MAX_PCT);\n  let a, b;\n  if (l === 0 || l === MAX_PCT) {\n    a = 0;\n    b = 0;\n  } else {\n    a = (f0 - f1) * LAB_A;\n    b = (f1 - f2) * LAB_B;\n  }\n  return [l, a, b];\n};\n\n/**\n * transform xyz-d50 to lch\n * @param xyz - [x, y, z]\n * @param [skip] - skip validate\n * @returns TriColorChannels - [l, c, h]\n */\nexport const transformXyzD50ToLch = (\n  xyz: TriColorChannels,\n  skip: boolean = false\n): TriColorChannels => {\n  const [l, a, b] = transformXyzD50ToLab(xyz, skip);\n  let c, h;\n  if (l === 0 || l === MAX_PCT) {\n    c = 0;\n    h = 0;\n  } else {\n    c = Math.max(Math.sqrt(Math.pow(a, POW_SQR) + Math.pow(b, POW_SQR)), 0);\n    h = (Math.atan2(b, a) * DEG_HALF) / Math.PI;\n    if (h < 0) {\n      h += DEG;\n    }\n  }\n  return [l, c, h];\n};\n\n/**\n * convert rgb to hex color\n * @param rgb - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n * @returns hex color\n */\nexport const convertRgbToHex = (rgb: ColorChannels): string => {\n  const [r, g, b, alpha] = validateColorComponents(rgb, {\n    alpha: true,\n    maxRange: MAX_RGB\n  }) as ColorChannels;\n  const rr = numberToHexString(r);\n  const gg = numberToHexString(g);\n  const bb = numberToHexString(b);\n  const aa = numberToHexString(alpha * MAX_RGB);\n  let hex;\n  if (aa === 'ff') {\n    hex = `#${rr}${gg}${bb}`;\n  } else {\n    hex = `#${rr}${gg}${bb}${aa}`;\n  }\n  return hex;\n};\n\n/**\n * convert linear rgb to hex color\n * @param rgb - [r, g, b, alpha] r|g|b|alpha: 0..1\n * @param [skip] - skip validate\n * @returns hex color\n */\nexport const convertLinearRgbToHex = (\n  rgb: ColorChannels,\n  skip: boolean = false\n): string => {\n  let r, g, b, alpha;\n  if (skip) {\n    [r, g, b, alpha] = rgb;\n  } else {\n    [r, g, b, alpha] = validateColorComponents(rgb, {\n      minLength: QUAD\n    }) as ColorChannels;\n  }\n  [r, g, b] = transformLinearRgbToRgb([r, g, b], true);\n  const rr = numberToHexString(r);\n  const gg = numberToHexString(g);\n  const bb = numberToHexString(b);\n  const aa = numberToHexString(alpha * MAX_RGB);\n  let hex;\n  if (aa === 'ff') {\n    hex = `#${rr}${gg}${bb}`;\n  } else {\n    hex = `#${rr}${gg}${bb}${aa}`;\n  }\n  return hex;\n};\n\n/**\n * convert xyz to hex color\n * @param xyz - [x, y, z, alpha]\n * @returns hex color\n */\nexport const convertXyzToHex = (xyz: ColorChannels): string => {\n  const [x, y, z, alpha] = validateColorComponents(xyz, {\n    minLength: QUAD,\n    validateRange: false\n  }) as ColorChannels;\n  const [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  const hex = convertLinearRgbToHex(\n    [\n      Math.min(Math.max(r, 0), 1),\n      Math.min(Math.max(g, 0), 1),\n      Math.min(Math.max(b, 0), 1),\n      alpha\n    ],\n    true\n  );\n  return hex;\n};\n\n/**\n * convert xyz D50 to hex color\n * @param xyz - [x, y, z, alpha]\n * @returns hex color\n */\nexport const convertXyzD50ToHex = (xyz: ColorChannels): string => {\n  const [x, y, z, alpha] = validateColorComponents(xyz, {\n    minLength: QUAD,\n    validateRange: false\n  }) as ColorChannels;\n  const xyzD65 = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n  const [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, xyzD65, true);\n  const hex = convertLinearRgbToHex([\n    Math.min(Math.max(r, 0), 1),\n    Math.min(Math.max(g, 0), 1),\n    Math.min(Math.max(b, 0), 1),\n    alpha\n  ]);\n  return hex;\n};\n\n/**\n * convert hex color to rgb\n * @param value - hex color value\n * @returns ColorChannels - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertHexToRgb = (value: string): ColorChannels => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  if (\n    !(\n      /^#[\\da-f]{6}$/.test(value) ||\n      /^#[\\da-f]{3}$/.test(value) ||\n      /^#[\\da-f]{8}$/.test(value) ||\n      /^#[\\da-f]{4}$/.test(value)\n    )\n  ) {\n    throw new SyntaxError(`Invalid property value: ${value}`);\n  }\n  const arr: number[] = [];\n  if (/^#[\\da-f]{3}$/.test(value)) {\n    const [, r, g, b] = value.match(\n      /^#([\\da-f])([\\da-f])([\\da-f])$/\n    ) as MatchedRegExp;\n    arr.push(\n      parseInt(`${r}${r}`, HEX),\n      parseInt(`${g}${g}`, HEX),\n      parseInt(`${b}${b}`, HEX),\n      1\n    );\n  } else if (/^#[\\da-f]{4}$/.test(value)) {\n    const [, r, g, b, alpha] = value.match(\n      /^#([\\da-f])([\\da-f])([\\da-f])([\\da-f])$/\n    ) as MatchedRegExp;\n    arr.push(\n      parseInt(`${r}${r}`, HEX),\n      parseInt(`${g}${g}`, HEX),\n      parseInt(`${b}${b}`, HEX),\n      parseHexAlpha(`${alpha}${alpha}`)\n    );\n  } else if (/^#[\\da-f]{8}$/.test(value)) {\n    const [, r, g, b, alpha] = value.match(\n      /^#([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/\n    ) as MatchedRegExp;\n    arr.push(\n      parseInt(r, HEX),\n      parseInt(g, HEX),\n      parseInt(b, HEX),\n      parseHexAlpha(alpha)\n    );\n  } else {\n    const [, r, g, b] = value.match(\n      /^#([\\da-f]{2})([\\da-f]{2})([\\da-f]{2})$/\n    ) as MatchedRegExp;\n    arr.push(parseInt(r, HEX), parseInt(g, HEX), parseInt(b, HEX), 1);\n  }\n  return arr as ColorChannels;\n};\n\n/**\n * convert hex color to linear rgb\n * @param value - hex color value\n * @returns ColorChannels - [r, g, b, alpha] r|g|b|alpha: 0..1\n */\nexport const convertHexToLinearRgb = (value: string): ColorChannels => {\n  const [rr, gg, bb, alpha] = convertHexToRgb(value);\n  const [r, g, b] = transformRgbToLinearRgb([rr, gg, bb], true);\n  return [r, g, b, alpha];\n};\n\n/**\n * convert hex color to xyz\n * @param value - hex color value\n * @returns ColorChannels - [x, y, z, alpha]\n */\nexport const convertHexToXyz = (value: string): ColorChannels => {\n  const [r, g, b, alpha] = convertHexToLinearRgb(value);\n  const [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [r, g, b], true);\n  return [x, y, z, alpha];\n};\n\n/**\n * parse rgb()\n * @param value - rgb color value\n * @param [opt] - options\n * @returns parsed color - ['rgb', r, g, b, alpha], '(empty)', NullObject\n */\nexport const parseRgb = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  const reg = new RegExp(`^rgba?\\\\(\\\\s*(${SYN_MOD}|${SYN_RGB_LV3})\\\\s*\\\\)$`);\n  if (!reg.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(reg) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace(/[,/]/g, ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let r, g, b;\n  if (v1 === NONE) {\n    r = 0;\n  } else {\n    if (v1.endsWith('%')) {\n      r = (parseFloat(v1) * MAX_RGB) / MAX_PCT;\n    } else {\n      r = parseFloat(v1);\n    }\n    r = Math.min(Math.max(roundToPrecision(r, OCT), 0), MAX_RGB);\n  }\n  if (v2 === NONE) {\n    g = 0;\n  } else {\n    if (v2.endsWith('%')) {\n      g = (parseFloat(v2) * MAX_RGB) / MAX_PCT;\n    } else {\n      g = parseFloat(v2);\n    }\n    g = Math.min(Math.max(roundToPrecision(g, OCT), 0), MAX_RGB);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    if (v3.endsWith('%')) {\n      b = (parseFloat(v3) * MAX_RGB) / MAX_PCT;\n    } else {\n      b = parseFloat(v3);\n    }\n    b = Math.min(Math.max(roundToPrecision(b, OCT), 0), MAX_RGB);\n  }\n  const alpha = parseAlpha(v4);\n  return ['rgb', r, g, b, format === VAL_MIX && v4 === NONE ? NONE : alpha];\n};\n\n/**\n * parse hsl()\n * @param value - hsl color value\n * @param [opt] - options\n * @returns parsed color - ['rgb', r, g, b, alpha], '(empty)', NullObject\n */\nexport const parseHsl = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_HSL.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(REG_HSL) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace(/[,/]/g, ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let h, s, l;\n  if (v1 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v1);\n  }\n  if (v2 === NONE) {\n    s = 0;\n  } else {\n    s = Math.min(Math.max(parseFloat(v2), 0), MAX_PCT);\n  }\n  if (v3 === NONE) {\n    l = 0;\n  } else {\n    l = Math.min(Math.max(parseFloat(v3), 0), MAX_PCT);\n  }\n  const alpha = parseAlpha(v4);\n  if (format === 'hsl') {\n    return [\n      format,\n      v1 === NONE ? v1 : h,\n      v2 === NONE ? v2 : s,\n      v3 === NONE ? v3 : l,\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  h = (h / DEG) * DOZ;\n  l /= MAX_PCT;\n  const sa = (s / MAX_PCT) * Math.min(l, 1 - l);\n  const rk = h % DOZ;\n  const gk = (8 + h) % DOZ;\n  const bk = (4 + h) % DOZ;\n  const r = l - sa * Math.max(-1, Math.min(rk - TRIA, TRIA ** POW_SQR - rk, 1));\n  const g = l - sa * Math.max(-1, Math.min(gk - TRIA, TRIA ** POW_SQR - gk, 1));\n  const b = l - sa * Math.max(-1, Math.min(bk - TRIA, TRIA ** POW_SQR - bk, 1));\n  return [\n    'rgb',\n    Math.min(Math.max(roundToPrecision(r * MAX_RGB, OCT), 0), MAX_RGB),\n    Math.min(Math.max(roundToPrecision(g * MAX_RGB, OCT), 0), MAX_RGB),\n    Math.min(Math.max(roundToPrecision(b * MAX_RGB, OCT), 0), MAX_RGB),\n    alpha\n  ];\n};\n\n/**\n * parse hwb()\n * @param value - hwb color value\n * @param [opt] - options\n * @returns parsed color - ['rgb', r, g, b, alpha], '(empty)', NullObject\n */\nexport const parseHwb = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_HWB.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(REG_HWB) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let h, wh, bk;\n  if (v1 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v1);\n  }\n  if (v2 === NONE) {\n    wh = 0;\n  } else {\n    wh = Math.min(Math.max(parseFloat(v2), 0), MAX_PCT) / MAX_PCT;\n  }\n  if (v3 === NONE) {\n    bk = 0;\n  } else {\n    bk = Math.min(Math.max(parseFloat(v3), 0), MAX_PCT) / MAX_PCT;\n  }\n  const alpha = parseAlpha(v4);\n  if (format === 'hwb') {\n    return [\n      format,\n      v1 === NONE ? v1 : h,\n      v2 === NONE ? v2 : wh * MAX_PCT,\n      v3 === NONE ? v3 : bk * MAX_PCT,\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  if (wh + bk >= 1) {\n    const v = roundToPrecision((wh / (wh + bk)) * MAX_RGB, OCT);\n    return ['rgb', v, v, v, alpha];\n  }\n  const factor = (1 - wh - bk) / MAX_RGB;\n  let [, r, g, b] = parseHsl(`hsl(${h} 100 50)`) as ComputedColorChannels;\n  r = roundToPrecision((r * factor + wh) * MAX_RGB, OCT);\n  g = roundToPrecision((g * factor + wh) * MAX_RGB, OCT);\n  b = roundToPrecision((b * factor + wh) * MAX_RGB, OCT);\n  return [\n    'rgb',\n    Math.min(Math.max(r, 0), MAX_RGB),\n    Math.min(Math.max(g, 0), MAX_RGB),\n    Math.min(Math.max(b, 0), MAX_RGB),\n    alpha\n  ];\n};\n\n/**\n * parse lab()\n * @param value - lab color value\n * @param [opt] - options\n * @returns parsed color\n *   - [xyz-d50, x, y, z, alpha], ['lab', l, a, b, alpha], '(empty)', NullObject\n */\nexport const parseLab = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_LAB.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 1.25;\n  const COND_POW = 8;\n  const [, val] = value.match(REG_LAB) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, a, b;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    if (v1.endsWith('%')) {\n      l = parseFloat(v1);\n      if (l > MAX_PCT) {\n        l = MAX_PCT;\n      }\n    } else {\n      l = parseFloat(v1);\n    }\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    a = 0;\n  } else {\n    a = v2.endsWith('%') ? parseFloat(v2) * COEF_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    b = v3.endsWith('%') ? parseFloat(v3) * COEF_PCT : parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'lab',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(a, HEX),\n      v3 === NONE ? v3 : roundToPrecision(b, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const fl = (l + HEX) / LAB_L;\n  const fa = a / LAB_A + fl;\n  const fb = fl - b / LAB_B;\n  const powFl = Math.pow(fl, POW_CUBE);\n  const powFa = Math.pow(fa, POW_CUBE);\n  const powFb = Math.pow(fb, POW_CUBE);\n  const xyz = [\n    powFa > LAB_EPSILON ? powFa : (fa * LAB_L - HEX) / LAB_KAPPA,\n    l > COND_POW ? powFl : l / LAB_KAPPA,\n    powFb > LAB_EPSILON ? powFb : (fb * LAB_L - HEX) / LAB_KAPPA\n  ];\n  const [x, y, z] = xyz.map(\n    (val, i) => val * (D50[i] as number)\n  ) as TriColorChannels;\n  return [\n    'xyz-d50',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse lch()\n * @param value - lch color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-d50', x, y, z, alpha], ['lch', l, c, h, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseLch = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_LCH.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 1.5;\n  const [, val] = value.match(REG_LCH) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, c, h;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    l = parseFloat(v1);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    c = 0;\n  } else {\n    c = v2.endsWith('%') ? parseFloat(v2) * COEF_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'lch',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(c, HEX),\n      v3 === NONE ? v3 : roundToPrecision(h, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const a = c * Math.cos((h * Math.PI) / DEG_HALF);\n  const b = c * Math.sin((h * Math.PI) / DEG_HALF);\n  const [, x, y, z] = parseLab(`lab(${l} ${a} ${b})`) as ComputedColorChannels;\n  return [\n    'xyz-d50',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha as number\n  ];\n};\n\n/**\n * parse oklab()\n * @param value - oklab color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-d65', x, y, z, alpha], ['oklab', l, a, b, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseOklab = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_OKLAB.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 0.4;\n  const [, val] = value.match(REG_OKLAB) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, a, b;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    l = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    a = 0;\n  } else if (v2.endsWith('%')) {\n    a = (parseFloat(v2) * COEF_PCT) / MAX_PCT;\n  } else {\n    a = parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else if (v3.endsWith('%')) {\n    b = (parseFloat(v3) * COEF_PCT) / MAX_PCT;\n  } else {\n    b = parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'oklab',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(a, HEX),\n      v3 === NONE ? v3 : roundToPrecision(b, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const lms = transformMatrix(MATRIX_OKLAB_TO_LMS, [l, a, b]);\n  const xyzLms = lms.map(c => Math.pow(c, POW_CUBE)) as TriColorChannels;\n  const [x, y, z] = transformMatrix(MATRIX_LMS_TO_XYZ, xyzLms, true);\n  return [\n    'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha as number\n  ];\n};\n\n/**\n * parse oklch()\n * @param value - oklch color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-d65', x, y, z, alpha], ['oklch', l, c, h, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseOklch = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  if (!REG_OKLCH.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const COEF_PCT = 0.4;\n  const [, val] = value.match(REG_OKLCH) as MatchedRegExp;\n  const [v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorChannels;\n  let l, c, h;\n  if (v1 === NONE) {\n    l = 0;\n  } else {\n    l = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n    if (l < 0) {\n      l = 0;\n    }\n  }\n  if (v2 === NONE) {\n    c = 0;\n  } else {\n    if (v2.endsWith('%')) {\n      c = (parseFloat(v2) * COEF_PCT) / MAX_PCT;\n    } else {\n      c = parseFloat(v2);\n    }\n    if (c < 0) {\n      c = 0;\n    }\n  }\n  if (v3 === NONE) {\n    h = 0;\n  } else {\n    h = angleToDeg(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format)) {\n    return [\n      'oklch',\n      v1 === NONE ? v1 : roundToPrecision(l, HEX),\n      v2 === NONE ? v2 : roundToPrecision(c, HEX),\n      v3 === NONE ? v3 : roundToPrecision(h, HEX),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  const a = c * Math.cos((h * Math.PI) / DEG_HALF);\n  const b = c * Math.sin((h * Math.PI) / DEG_HALF);\n  const lms = transformMatrix(MATRIX_OKLAB_TO_LMS, [l, a, b]);\n  const xyzLms = lms.map(cc => Math.pow(cc, POW_CUBE)) as TriColorChannels;\n  const [x, y, z] = transformMatrix(MATRIX_LMS_TO_XYZ, xyzLms, true);\n  return [\n    'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha\n  ];\n};\n\n/**\n * parse color()\n * @param value - color function value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-(d50|d65)', x, y, z, alpha], [cs, r, g, b, alpha]\n *   - '(empty)', NullObject\n */\nexport const parseColorFunc = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', d50 = false, format = '', nullable = false } = opt;\n  if (!REG_FN_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n  let [cs, v1, v2, v3, v4 = ''] = val\n    .replace('/', ' ')\n    .split(/\\s+/) as StringColorSpacedChannels;\n  let r, g, b;\n  if (cs === 'xyz') {\n    cs = 'xyz-d65';\n  }\n  if (v1 === NONE) {\n    r = 0;\n  } else {\n    r = v1.endsWith('%') ? parseFloat(v1) / MAX_PCT : parseFloat(v1);\n  }\n  if (v2 === NONE) {\n    g = 0;\n  } else {\n    g = v2.endsWith('%') ? parseFloat(v2) / MAX_PCT : parseFloat(v2);\n  }\n  if (v3 === NONE) {\n    b = 0;\n  } else {\n    b = v3.endsWith('%') ? parseFloat(v3) / MAX_PCT : parseFloat(v3);\n  }\n  const alpha = parseAlpha(v4);\n  if (REG_SPEC.test(format) || (format === VAL_MIX && cs === colorSpace)) {\n    return [\n      cs,\n      v1 === NONE ? v1 : roundToPrecision(r, DEC),\n      v2 === NONE ? v2 : roundToPrecision(g, DEC),\n      v3 === NONE ? v3 : roundToPrecision(b, DEC),\n      v4 === NONE ? v4 : alpha\n    ];\n  }\n  let x = 0;\n  let y = 0;\n  let z = 0;\n  // srgb-linear\n  if (cs === 'srgb-linear') {\n    [x, y, z] = transformMatrix(MATRIX_L_RGB_TO_XYZ, [r, g, b]);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // display-p3\n  } else if (cs === 'display-p3') {\n    const linearRgb = transformRgbToLinearRgb([\n      r * MAX_RGB,\n      g * MAX_RGB,\n      b * MAX_RGB\n    ]);\n    [x, y, z] = transformMatrix(MATRIX_P3_TO_XYZ, linearRgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // rec2020\n  } else if (cs === 'rec2020') {\n    const ALPHA = 1.09929682680944;\n    const BETA = 0.018053968510807;\n    const REC_COEF = 0.45;\n    const rgb = [r, g, b].map(c => {\n      let cl;\n      if (c < BETA * REC_COEF * DEC) {\n        cl = c / (REC_COEF * DEC);\n      } else {\n        cl = Math.pow((c + ALPHA - 1) / ALPHA, 1 / REC_COEF);\n      }\n      return cl;\n    }) as TriColorChannels;\n    [x, y, z] = transformMatrix(MATRIX_REC2020_TO_XYZ, rgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // a98-rgb\n  } else if (cs === 'a98-rgb') {\n    const POW_A98 = 563 / 256;\n    const rgb = [r, g, b].map(c => {\n      const cl = Math.pow(c, POW_A98);\n      return cl;\n    }) as TriColorChannels;\n    [x, y, z] = transformMatrix(MATRIX_A98_TO_XYZ, rgb);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // prophoto-rgb\n  } else if (cs === 'prophoto-rgb') {\n    const POW_PROPHOTO = 1.8;\n    const rgb = [r, g, b].map(c => {\n      let cl;\n      if (c > 1 / (HEX * DUO)) {\n        cl = Math.pow(c, POW_PROPHOTO);\n      } else {\n        cl = c / HEX;\n      }\n      return cl;\n    }) as TriColorChannels;\n    [x, y, z] = transformMatrix(MATRIX_PROPHOTO_TO_XYZ_D50, rgb);\n    if (!d50) {\n      [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n    }\n    // xyz, xyz-d50, xyz-d65\n  } else if (/^xyz(?:-d(?:50|65))?$/.test(cs)) {\n    [x, y, z] = [r, g, b];\n    if (cs === 'xyz-d50') {\n      if (!d50) {\n        [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z]);\n      }\n    } else if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // srgb\n  } else {\n    [x, y, z] = transformRgbToXyz([r * MAX_RGB, g * MAX_RGB, b * MAX_RGB]);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  }\n  return [\n    d50 ? 'xyz-d50' : 'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    format === VAL_MIX && v4 === NONE ? v4 : alpha\n  ];\n};\n\n/**\n * parse color value\n * @param value - CSS color value\n * @param [opt] - options\n * @returns parsed color\n *   - ['xyz-(d50|d65)', x, y, z, alpha], ['rgb', r, g, b, alpha]\n *   - value, '(empty)', NullObject\n */\nexport const parseColorValue = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { d50 = false, format = '', nullable = false } = opt;\n  if (!REG_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      return res;\n    }\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  let x = 0;\n  let y = 0;\n  let z = 0;\n  let alpha = 0;\n  // complement currentcolor as a missing color\n  if (REG_CURRENT.test(value)) {\n    if (format === VAL_COMP) {\n      return ['rgb', 0, 0, 0, 0];\n    }\n    if (format === VAL_SPEC) {\n      return value;\n    }\n    // named-color\n  } else if (/^[a-z]+$/.test(value)) {\n    if (Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      }\n      const [r, g, b] = NAMED_COLORS[\n        value as keyof typeof NAMED_COLORS\n      ] as TriColorChannels;\n      alpha = 1;\n      if (format === VAL_COMP) {\n        return ['rgb', r, g, b, alpha];\n      }\n      [x, y, z] = transformRgbToXyz([r, g, b], true);\n      if (d50) {\n        [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n      }\n    } else {\n      switch (format) {\n        case VAL_COMP: {\n          if (nullable && value !== 'transparent') {\n            return new NullObject();\n          }\n          return ['rgb', 0, 0, 0, 0];\n        }\n        case VAL_SPEC: {\n          if (value === 'transparent') {\n            return value;\n          }\n          return '';\n        }\n        case VAL_MIX: {\n          if (value === 'transparent') {\n            return ['rgb', 0, 0, 0, 0];\n          }\n          return new NullObject();\n        }\n        default:\n      }\n    }\n    // hex-color\n  } else if (value[0] === '#') {\n    if (REG_SPEC.test(format)) {\n      const rgb = convertHexToRgb(value);\n      return ['rgb', ...rgb];\n    }\n    [x, y, z, alpha] = convertHexToXyz(value);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // lab()\n  } else if (value.startsWith('lab')) {\n    if (REG_SPEC.test(format)) {\n      return parseLab(value, opt);\n    }\n    [, x, y, z, alpha] = parseLab(value) as ComputedColorChannels;\n    if (!d50) {\n      [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n    }\n    // lch()\n  } else if (value.startsWith('lch')) {\n    if (REG_SPEC.test(format)) {\n      return parseLch(value, opt);\n    }\n    [, x, y, z, alpha] = parseLch(value) as ComputedColorChannels;\n    if (!d50) {\n      [x, y, z] = transformMatrix(MATRIX_D50_TO_D65, [x, y, z], true);\n    }\n    // oklab()\n  } else if (value.startsWith('oklab')) {\n    if (REG_SPEC.test(format)) {\n      return parseOklab(value, opt);\n    }\n    [, x, y, z, alpha] = parseOklab(value) as ComputedColorChannels;\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n    // oklch()\n  } else if (value.startsWith('oklch')) {\n    if (REG_SPEC.test(format)) {\n      return parseOklch(value, opt);\n    }\n    [, x, y, z, alpha] = parseOklch(value) as ComputedColorChannels;\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  } else {\n    let r, g, b;\n    // hsl()\n    if (value.startsWith('hsl')) {\n      [, r, g, b, alpha] = parseHsl(value) as ComputedColorChannels;\n      // hwb()\n    } else if (value.startsWith('hwb')) {\n      [, r, g, b, alpha] = parseHwb(value) as ComputedColorChannels;\n      // rgb()\n    } else {\n      [, r, g, b, alpha] = parseRgb(value, opt) as ComputedColorChannels;\n    }\n    if (REG_SPEC.test(format)) {\n      return ['rgb', Math.round(r), Math.round(g), Math.round(b), alpha];\n    }\n    [x, y, z] = transformRgbToXyz([r, g, b]);\n    if (d50) {\n      [x, y, z] = transformMatrix(MATRIX_D65_TO_D50, [x, y, z], true);\n    }\n  }\n  return [\n    d50 ? 'xyz-d50' : 'xyz-d65',\n    roundToPrecision(x, HEX),\n    roundToPrecision(y, HEX),\n    roundToPrecision(z, HEX),\n    alpha\n  ];\n};\n\n/**\n * resolve color value\n * @param value - CSS color value\n * @param [opt] - options\n * @returns resolved color\n *   - [cs, v1, v2, v3, alpha], value, '(empty)', NullObject\n */\nexport const resolveColorValue = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', format = '', nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveColorValue',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    const cachedItem = cachedResult.item;\n    if (isString(cachedItem)) {\n      return cachedItem as string;\n    }\n    return cachedItem as SpecifiedColorChannels;\n  }\n  if (!REG_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      setCache(cacheKey, null);\n      return res;\n    }\n    setCache(cacheKey, res);\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  let cs = '';\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let alpha = 0;\n  // complement currentcolor as a missing color\n  if (REG_CURRENT.test(value)) {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    // named-color\n  } else if (/^[a-z]+$/.test(value)) {\n    if (Object.prototype.hasOwnProperty.call(NAMED_COLORS, value)) {\n      if (format === VAL_SPEC) {\n        setCache(cacheKey, value);\n        return value;\n      }\n      [r, g, b] = NAMED_COLORS[\n        value as keyof typeof NAMED_COLORS\n      ] as TriColorChannels;\n      alpha = 1;\n    } else {\n      switch (format) {\n        case VAL_SPEC: {\n          if (value === 'transparent') {\n            setCache(cacheKey, value);\n            return value;\n          }\n          const res = '';\n          setCache(cacheKey, res);\n          return res;\n        }\n        case VAL_MIX: {\n          if (value === 'transparent') {\n            const res: SpecifiedColorChannels = ['rgb', 0, 0, 0, 0];\n            setCache(cacheKey, res);\n            return res;\n          }\n          setCache(cacheKey, null);\n          return new NullObject();\n        }\n        case VAL_COMP:\n        default: {\n          if (nullable && value !== 'transparent') {\n            setCache(cacheKey, null);\n            return new NullObject();\n          }\n          const res: SpecifiedColorChannels = ['rgb', 0, 0, 0, 0];\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    }\n    // hex-color\n  } else if (value[0] === '#') {\n    [r, g, b, alpha] = convertHexToRgb(value);\n    // hsl()\n  } else if (value.startsWith('hsl')) {\n    [, r, g, b, alpha] = parseHsl(value, opt) as ComputedColorChannels;\n    // hwb()\n  } else if (value.startsWith('hwb')) {\n    [, r, g, b, alpha] = parseHwb(value, opt) as ComputedColorChannels;\n    // lab(), lch()\n  } else if (/^l(?:ab|ch)/.test(value)) {\n    let x, y, z;\n    if (value.startsWith('lab')) {\n      [cs, x, y, z, alpha] = parseLab(value, opt) as ComputedColorChannels;\n    } else {\n      [cs, x, y, z, alpha] = parseLch(value, opt) as ComputedColorChannels;\n    }\n    if (REG_SPEC.test(format)) {\n      const res: SpecifiedColorChannels = [cs, x, y, z, alpha];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [r, g, b] = transformXyzD50ToRgb([x, y, z]);\n    // oklab(), oklch()\n  } else if (/^okl(?:ab|ch)/.test(value)) {\n    let x, y, z;\n    if (value.startsWith('oklab')) {\n      [cs, x, y, z, alpha] = parseOklab(value, opt) as ComputedColorChannels;\n    } else {\n      [cs, x, y, z, alpha] = parseOklch(value, opt) as ComputedColorChannels;\n    }\n    if (REG_SPEC.test(format)) {\n      const res: SpecifiedColorChannels = [cs, x, y, z, alpha];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [r, g, b] = transformXyzToRgb([x, y, z]);\n    // rgb()\n  } else {\n    [, r, g, b, alpha] = parseRgb(value, opt) as ComputedColorChannels;\n  }\n  if (format === VAL_MIX && colorSpace === 'srgb') {\n    const res: SpecifiedColorChannels = [\n      'srgb',\n      r / MAX_RGB,\n      g / MAX_RGB,\n      b / MAX_RGB,\n      alpha\n    ];\n    setCache(cacheKey, res);\n    return res;\n  }\n  const res: SpecifiedColorChannels = [\n    'rgb',\n    Math.round(r),\n    Math.round(g),\n    Math.round(b),\n    alpha\n  ];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve color()\n * @param value - color function value\n * @param [opt] - options\n * @returns resolved color - [cs, v1, v2, v3, alpha], '(empty)', NullObject\n */\nexport const resolveColorFunc = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', format = '', nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveColorFunc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    const cachedItem = cachedResult.item;\n    if (isString(cachedItem)) {\n      return cachedItem as string;\n    }\n    return cachedItem as SpecifiedColorChannels;\n  }\n  if (!REG_FN_COLOR.test(value)) {\n    const res = resolveInvalidColorValue(format, nullable);\n    if (res instanceof NullObject) {\n      setCache(cacheKey, null);\n      return res;\n    }\n    setCache(cacheKey, res);\n    if (isString(res)) {\n      return res as string;\n    }\n    return res as SpecifiedColorChannels;\n  }\n  const [cs, v1, v2, v3, v4] = parseColorFunc(\n    value,\n    opt\n  ) as SpecifiedColorChannels;\n  if (REG_SPEC.test(format) || (format === VAL_MIX && cs === colorSpace)) {\n    const res: SpecifiedColorChannels = [cs, v1, v2, v3, v4];\n    setCache(cacheKey, res);\n    return res;\n  }\n  const x = parseFloat(`${v1}`);\n  const y = parseFloat(`${v2}`);\n  const z = parseFloat(`${v3}`);\n  const alpha = parseAlpha(`${v4}`);\n  const [r, g, b] = transformXyzToRgb([x, y, z], true);\n  const res: SpecifiedColorChannels = ['rgb', r, g, b, alpha];\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * convert color value to linear rgb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [r, g, b, alpha] r|g|b|alpha: 0..1\n */\nexport const convertColorToLinearRgb = (\n  value: string,\n  opt: {\n    colorSpace?: string;\n    format?: string;\n  } = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { colorSpace = '', format = '' } = opt;\n  let cs = '';\n  let r, g, b, alpha, x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [cs, x, y, z, alpha] = xyz as ComputedColorChannels;\n    if (cs === colorSpace) {\n      return [x, y, z, alpha];\n    }\n    [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n    const [cs] = val\n      .replace('/', ' ')\n      .split(/\\s+/) as StringColorSpacedChannels;\n    if (cs === 'srgb-linear') {\n      [, r, g, b, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as ComputedColorChannels;\n    } else {\n      [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n      [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n    }\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n    [r, g, b] = transformMatrix(MATRIX_XYZ_TO_L_RGB, [x, y, z], true);\n  }\n  return [\n    Math.min(Math.max(r, 0), 1),\n    Math.min(Math.max(g, 0), 1),\n    Math.min(Math.max(b, 0), 1),\n    alpha\n  ];\n};\n\n/**\n * convert color value to rgb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject\n *   - [r, g, b, alpha] r|g|b: 0..255 alpha: 0..1\n */\nexport const convertColorToRgb = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let r, g, b, alpha;\n  if (format === VAL_MIX) {\n    let rgb;\n    if (value.startsWith(FN_COLOR)) {\n      rgb = resolveColorFunc(value, opt);\n    } else {\n      rgb = resolveColorValue(value, opt);\n    }\n    if (rgb instanceof NullObject) {\n      return rgb;\n    }\n    [, r, g, b, alpha] = rgb as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n    const [cs] = val\n      .replace('/', ' ')\n      .split(/\\s+/) as StringColorSpacedChannels;\n    if (cs === 'srgb') {\n      [, r, g, b, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as ComputedColorChannels;\n      r *= MAX_RGB;\n      g *= MAX_RGB;\n      b *= MAX_RGB;\n    } else {\n      [, r, g, b, alpha] = resolveColorFunc(value) as ComputedColorChannels;\n    }\n  } else if (/^(?:ok)?l(?:ab|ch)/.test(value)) {\n    [r, g, b, alpha] = convertColorToLinearRgb(value) as ColorChannels;\n    [r, g, b] = transformLinearRgbToRgb([r, g, b]);\n  } else {\n    [, r, g, b, alpha] = resolveColorValue(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n  }\n  return [r, g, b, alpha];\n};\n\n/**\n * convert color value to xyz\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [x, y, z, alpha]\n */\nexport const convertColorToXyz = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { d50 = false, format = '' } = opt;\n  let x, y, z, alpha;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const [, val] = value.match(REG_FN_COLOR) as MatchedRegExp;\n    const [cs] = val\n      .replace('/', ' ')\n      .split(/\\s+/) as StringColorSpacedChannels;\n    if (d50) {\n      if (cs === 'xyz-d50') {\n        [, x, y, z, alpha] = resolveColorFunc(value, {\n          format: VAL_COMP\n        }) as ComputedColorChannels;\n      } else {\n        [, x, y, z, alpha] = parseColorFunc(\n          value,\n          opt\n        ) as ComputedColorChannels;\n      }\n    } else if (/^xyz(?:-d65)?$/.test(cs)) {\n      [, x, y, z, alpha] = resolveColorFunc(value, {\n        format: VAL_COMP\n      }) as ComputedColorChannels;\n    } else {\n      [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n    }\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, opt) as ComputedColorChannels;\n  }\n  return [x, y, z, alpha];\n};\n\n/**\n * convert color value to hsl\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [h, s, l, alpha], hue may be powerless\n */\nexport const convertColorToHsl = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number | string, number, number, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let h, s, l, alpha;\n  if (REG_HSL.test(value)) {\n    [, h, s, l, alpha] = parseHsl(value, {\n      format: 'hsl'\n    }) as ComputedColorChannels;\n    if (format === 'hsl') {\n      return [Math.round(h), Math.round(s), Math.round(l), alpha];\n    }\n    return [h, s, l, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [h, s, l] = transformXyzToHsl([x, y, z], true) as TriColorChannels;\n  if (format === 'hsl') {\n    return [Math.round(h), Math.round(s), Math.round(l), alpha];\n  }\n  return [format === VAL_MIX && s === 0 ? NONE : h, s, l, alpha];\n};\n\n/**\n * convert color value to hwb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [h, w, b, alpha], hue may be powerless\n */\nexport const convertColorToHwb = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number | string, number, number, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let h, w, b, alpha;\n  if (REG_HWB.test(value)) {\n    [, h, w, b, alpha] = parseHwb(value, {\n      format: 'hwb'\n    }) as ComputedColorChannels;\n    if (format === 'hwb') {\n      return [Math.round(h), Math.round(w), Math.round(b), alpha];\n    }\n    return [h, w, b, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [h, w, b] = transformXyzToHwb([x, y, z], true) as TriColorChannels;\n  if (format === 'hwb') {\n    return [Math.round(h), Math.round(w), Math.round(b), alpha];\n  }\n  return [format === VAL_MIX && w + b >= 100 ? NONE : h, w, b, alpha];\n};\n\n/**\n * convert color value to lab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, a, b, alpha]\n */\nexport const convertColorToLab = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, a, b, alpha;\n  if (REG_LAB.test(value)) {\n    [, l, a, b, alpha] = parseLab(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, a, b, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    opt.d50 = true;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  }\n  [l, a, b] = transformXyzD50ToLab([x, y, z], true);\n  return [l, a, b, alpha];\n};\n\n/**\n * convert color value to lch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, c, h, alpha], hue may be powerless\n */\nexport const convertColorToLch = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number, number, number | string, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, c, h, alpha;\n  if (REG_LCH.test(value)) {\n    [, l, c, h, alpha] = parseLch(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, c, h, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    opt.d50 = true;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value, {\n      d50: true\n    }) as ComputedColorChannels;\n  }\n  [l, c, h] = transformXyzD50ToLch([x, y, z], true);\n  return [l, c, format === VAL_MIX && c === 0 ? NONE : h, alpha];\n};\n\n/**\n * convert color value to oklab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, a, b, alpha]\n */\nexport const convertColorToOklab = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, a, b, alpha;\n  if (REG_OKLAB.test(value)) {\n    [, l, a, b, alpha] = parseOklab(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, a, b, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [l, a, b] = transformXyzToOklab([x, y, z], true);\n  return [l, a, b, alpha];\n};\n\n/**\n * convert color value to oklch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels | NullObject - [l, c, h, alpha], hue may be powerless\n */\nexport const convertColorToOklch = (\n  value: string,\n  opt: Options = {}\n): ColorChannels | [number, number, number | string, number] | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '' } = opt;\n  let l, c, h, alpha;\n  if (REG_OKLCH.test(value)) {\n    [, l, c, h, alpha] = parseOklch(value, {\n      format: VAL_COMP\n    }) as ComputedColorChannels;\n    return [l, c, h, alpha];\n  }\n  let x, y, z;\n  if (format === VAL_MIX) {\n    let xyz;\n    if (value.startsWith(FN_COLOR)) {\n      xyz = parseColorFunc(value, opt);\n    } else {\n      xyz = parseColorValue(value, opt);\n    }\n    if (xyz instanceof NullObject) {\n      return xyz;\n    }\n    [, x, y, z, alpha] = xyz as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    [, x, y, z, alpha] = parseColorFunc(value) as ComputedColorChannels;\n  } else {\n    [, x, y, z, alpha] = parseColorValue(value) as ComputedColorChannels;\n  }\n  [l, c, h] = transformXyzToOklch([x, y, z], true) as TriColorChannels;\n  return [l, c, format === VAL_MIX && c === 0 ? NONE : h, alpha];\n};\n\n/**\n * resolve color-mix()\n * @param value - color-mix color value\n * @param [opt] - options\n * @returns resolved color - [cs, v1, v2, v3, alpha], '(empty)'\n */\nexport const resolveColorMix = (\n  value: string,\n  opt: Options = {}\n): SpecifiedColorChannels | string | NullObject => {\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { format = '', nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveColorMix',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    const cachedItem = cachedResult.item;\n    if (isString(cachedItem)) {\n      return cachedItem as string;\n    }\n    return cachedItem as SpecifiedColorChannels;\n  }\n  const nestedItems = [];\n  if (!REG_MIX.test(value)) {\n    if (value.startsWith(FN_MIX) && REG_MIX_NEST.test(value)) {\n      const regColorSpace = new RegExp(`^(?:${CS_RGB}|${CS_XYZ})$`);\n      const items = value.match(REG_MIX_NEST) as RegExpMatchArray;\n      for (const item of items) {\n        if (item) {\n          let val = resolveColorMix(item, {\n            format: format === VAL_SPEC ? format : VAL_COMP\n          }) as ComputedColorChannels | string;\n          // computed value\n          if (Array.isArray(val)) {\n            const [cs, v1, v2, v3, v4] = val as ComputedColorChannels;\n            if (v1 === 0 && v2 === 0 && v3 === 0 && v4 === 0) {\n              value = '';\n              break;\n            }\n            if (regColorSpace.test(cs)) {\n              if (v4 === 1) {\n                val = `color(${cs} ${v1} ${v2} ${v3})`;\n              } else {\n                val = `color(${cs} ${v1} ${v2} ${v3} / ${v4})`;\n              }\n            } else if (v4 === 1) {\n              val = `${cs}(${v1} ${v2} ${v3})`;\n            } else {\n              val = `${cs}(${v1} ${v2} ${v3} / ${v4})`;\n            }\n          } else if (!REG_MIX.test(val)) {\n            value = '';\n            break;\n          }\n          nestedItems.push(val);\n          value = value.replace(item, val);\n        }\n      }\n      if (!value) {\n        const res = cacheInvalidColorValue(cacheKey, format, nullable);\n        return res;\n      }\n    } else {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n  }\n  let colorSpace = '';\n  let hueArc = '';\n  let colorA = '';\n  let pctA = '';\n  let colorB = '';\n  let pctB = '';\n  if (nestedItems.length && format === VAL_SPEC) {\n    const regColorSpace = new RegExp(`^color-mix\\\\(\\\\s*in\\\\s+(${CS_MIX})\\\\s*,`);\n    const [, cs] = value.match(regColorSpace) as MatchedRegExp;\n    if (REG_CS_HUE.test(cs)) {\n      [, colorSpace, hueArc] = cs.match(REG_CS_HUE) as MatchedRegExp;\n    } else {\n      colorSpace = cs;\n    }\n    if (nestedItems.length === 2) {\n      let [itemA, itemB] = nestedItems as [string, string];\n      itemA = itemA.replace(/(?=[()])/g, '\\\\');\n      itemB = itemB.replace(/(?=[()])/g, '\\\\');\n      const regA = new RegExp(`(${itemA})(?:\\\\s+(${PCT}))?`);\n      const regB = new RegExp(`(${itemB})(?:\\\\s+(${PCT}))?`);\n      [, colorA, pctA] = value.match(regA) as MatchedRegExp;\n      [, colorB, pctB] = value.match(regB) as MatchedRegExp;\n    } else {\n      let [item] = nestedItems as [string];\n      item = item.replace(/(?=[()])/g, '\\\\');\n      const itemPart = `${item}(?:\\\\s+${PCT})?`;\n      const itemPartCapt = `(${item})(?:\\\\s+(${PCT}))?`;\n      const regItemPart = new RegExp(`^${itemPartCapt}$`);\n      const regLastItem = new RegExp(`${itemPartCapt}\\\\s*\\\\)$`);\n      const regColorPart = new RegExp(`^(${SYN_COLOR_TYPE})(?:\\\\s+(${PCT}))?$`);\n      // item is at the end\n      if (regLastItem.test(value)) {\n        const reg = new RegExp(\n          `(${SYN_MIX_PART})\\\\s*,\\\\s*(${itemPart})\\\\s*\\\\)$`\n        );\n        const [, colorPartA, colorPartB] = value.match(reg) as MatchedRegExp;\n        [, colorA, pctA] = colorPartA.match(regColorPart) as MatchedRegExp;\n        [, colorB, pctB] = colorPartB.match(regItemPart) as MatchedRegExp;\n      } else {\n        const reg = new RegExp(\n          `(${itemPart})\\\\s*,\\\\s*(${SYN_MIX_PART})\\\\s*\\\\)$`\n        );\n        const [, colorPartA, colorPartB] = value.match(reg) as MatchedRegExp;\n        [, colorA, pctA] = colorPartA.match(regItemPart) as MatchedRegExp;\n        [, colorB, pctB] = colorPartB.match(regColorPart) as MatchedRegExp;\n      }\n    }\n  } else {\n    const [, cs, colorPartA, colorPartB] = value.match(\n      REG_MIX_CAPT\n    ) as MatchedRegExp;\n    const reg = new RegExp(`^(${SYN_COLOR_TYPE})(?:\\\\s+(${PCT}))?$`);\n    [, colorA, pctA] = colorPartA.match(reg) as MatchedRegExp;\n    [, colorB, pctB] = colorPartB.match(reg) as MatchedRegExp;\n    if (REG_CS_HUE.test(cs)) {\n      [, colorSpace, hueArc] = cs.match(REG_CS_HUE) as MatchedRegExp;\n    } else {\n      colorSpace = cs;\n    }\n  }\n  // normalize percentages and set multipler\n  let pA, pB, m;\n  if (pctA && pctB) {\n    const p1 = parseFloat(pctA) / MAX_PCT;\n    const p2 = parseFloat(pctB) / MAX_PCT;\n    if (p1 < 0 || p1 > 1 || p2 < 0 || p2 > 1) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const factor = p1 + p2;\n    if (factor === 0) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    pA = p1 / factor;\n    pB = p2 / factor;\n    m = factor < 1 ? factor : 1;\n  } else {\n    if (pctA) {\n      pA = parseFloat(pctA) / MAX_PCT;\n      if (pA < 0 || pA > 1) {\n        const res = cacheInvalidColorValue(cacheKey, format, nullable);\n        return res;\n      }\n      pB = 1 - pA;\n    } else if (pctB) {\n      pB = parseFloat(pctB) / MAX_PCT;\n      if (pB < 0 || pB > 1) {\n        const res = cacheInvalidColorValue(cacheKey, format, nullable);\n        return res;\n      }\n      pA = 1 - pB;\n    } else {\n      pA = HALF;\n      pB = HALF;\n    }\n    m = 1;\n  }\n  if (colorSpace === 'xyz') {\n    colorSpace = 'xyz-d65';\n  }\n  // specified value\n  if (format === VAL_SPEC) {\n    let valueA = '';\n    let valueB = '';\n    if (colorA.startsWith(FN_MIX)) {\n      valueA = colorA;\n    } else if (colorA.startsWith(FN_COLOR)) {\n      const [cs, v1, v2, v3, v4] = parseColorFunc(\n        colorA,\n        opt\n      ) as SpecifiedColorChannels;\n      if (v4 === 1) {\n        valueA = `color(${cs} ${v1} ${v2} ${v3})`;\n      } else {\n        valueA = `color(${cs} ${v1} ${v2} ${v3} / ${v4})`;\n      }\n    } else {\n      const val = parseColorValue(colorA, opt);\n      if (Array.isArray(val)) {\n        const [cs, v1, v2, v3, v4] = val;\n        if (v4 === 1) {\n          if (cs === 'rgb') {\n            valueA = `${cs}(${v1}, ${v2}, ${v3})`;\n          } else {\n            valueA = `${cs}(${v1} ${v2} ${v3})`;\n          }\n        } else if (cs === 'rgb') {\n          valueA = `${cs}a(${v1}, ${v2}, ${v3}, ${v4})`;\n        } else {\n          valueA = `${cs}(${v1} ${v2} ${v3} / ${v4})`;\n        }\n      } else {\n        if (!isString(val) || !val) {\n          setCache(cacheKey, '');\n          return '';\n        }\n        valueA = val;\n      }\n    }\n    if (colorB.startsWith(FN_MIX)) {\n      valueB = colorB;\n    } else if (colorB.startsWith(FN_COLOR)) {\n      const [cs, v1, v2, v3, v4] = parseColorFunc(\n        colorB,\n        opt\n      ) as SpecifiedColorChannels;\n      if (v4 === 1) {\n        valueB = `color(${cs} ${v1} ${v2} ${v3})`;\n      } else {\n        valueB = `color(${cs} ${v1} ${v2} ${v3} / ${v4})`;\n      }\n    } else {\n      const val = parseColorValue(colorB, opt);\n      if (Array.isArray(val)) {\n        const [cs, v1, v2, v3, v4] = val;\n        if (v4 === 1) {\n          if (cs === 'rgb') {\n            valueB = `${cs}(${v1}, ${v2}, ${v3})`;\n          } else {\n            valueB = `${cs}(${v1} ${v2} ${v3})`;\n          }\n        } else if (cs === 'rgb') {\n          valueB = `${cs}a(${v1}, ${v2}, ${v3}, ${v4})`;\n        } else {\n          valueB = `${cs}(${v1} ${v2} ${v3} / ${v4})`;\n        }\n      } else {\n        if (!isString(val) || !val) {\n          setCache(cacheKey, '');\n          return '';\n        }\n        valueB = val;\n      }\n    }\n    if (pctA && pctB) {\n      valueA += ` ${parseFloat(pctA)}%`;\n      valueB += ` ${parseFloat(pctB)}%`;\n    } else if (pctA) {\n      const pA = parseFloat(pctA);\n      if (pA !== MAX_PCT * HALF) {\n        valueA += ` ${pA}%`;\n      }\n    } else if (pctB) {\n      const pA = MAX_PCT - parseFloat(pctB);\n      if (pA !== MAX_PCT * HALF) {\n        valueA += ` ${pA}%`;\n      }\n    }\n    if (hueArc) {\n      const res = `color-mix(in ${colorSpace} ${hueArc} hue, ${valueA}, ${valueB})`;\n      setCache(cacheKey, res);\n      return res;\n    } else {\n      const res = `color-mix(in ${colorSpace}, ${valueA}, ${valueB})`;\n      setCache(cacheKey, res);\n      return res;\n    }\n  }\n  let r = 0;\n  let g = 0;\n  let b = 0;\n  let alpha = 0;\n  // in srgb, srgb-linear\n  if (/^srgb(?:-linear)?$/.test(colorSpace)) {\n    let rgbA, rgbB;\n    if (colorSpace === 'srgb') {\n      if (REG_CURRENT.test(colorA)) {\n        rgbA = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbA = convertColorToRgb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        rgbB = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbB = convertColorToRgb(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        rgbA = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbA = convertColorToLinearRgb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        rgbB = [NONE, NONE, NONE, NONE];\n      } else {\n        rgbB = convertColorToLinearRgb(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (rgbA instanceof NullObject || rgbB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [rrA, ggA, bbA, aaA] = rgbA as NumStrColorChannels;\n    const [rrB, ggB, bbB, aaB] = rgbB as NumStrColorChannels;\n    const rNone = rrA === NONE && rrB === NONE;\n    const gNone = ggA === NONE && ggB === NONE;\n    const bNone = bbA === NONE && bbB === NONE;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    const [[rA, gA, bA, alphaA], [rB, gB, bB, alphaB]] =\n      normalizeColorComponents(\n        [rrA, ggA, bbA, aaA],\n        [rrB, ggB, bbB, aaB],\n        true\n      );\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    if (alpha === 0) {\n      r = rA * pA + rB * pB;\n      g = gA * pA + gB * pB;\n      b = bA * pA + bB * pB;\n    } else {\n      r = (rA * factorA + rB * factorB) / alpha;\n      g = (gA * factorA + gB * factorB) / alpha;\n      b = (bA * factorA + bB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        rNone ? NONE : roundToPrecision(r, HEX),\n        gNone ? NONE : roundToPrecision(g, HEX),\n        bNone ? NONE : roundToPrecision(b, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    r *= MAX_RGB;\n    g *= MAX_RGB;\n    b *= MAX_RGB;\n    // in xyz, xyz-d65, xyz-d50\n  } else if (REG_CS_XYZ.test(colorSpace)) {\n    let xyzA, xyzB;\n    if (REG_CURRENT.test(colorA)) {\n      xyzA = [NONE, NONE, NONE, NONE];\n    } else {\n      xyzA = convertColorToXyz(colorA, {\n        colorSpace,\n        d50: colorSpace === 'xyz-d50',\n        format: VAL_MIX\n      });\n    }\n    if (REG_CURRENT.test(colorB)) {\n      xyzB = [NONE, NONE, NONE, NONE];\n    } else {\n      xyzB = convertColorToXyz(colorB, {\n        colorSpace,\n        d50: colorSpace === 'xyz-d50',\n        format: VAL_MIX\n      });\n    }\n    if (xyzA instanceof NullObject || xyzB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [xxA, yyA, zzA, aaA] = xyzA;\n    const [xxB, yyB, zzB, aaB] = xyzB;\n    const xNone = xxA === NONE && xxB === NONE;\n    const yNone = yyA === NONE && yyB === NONE;\n    const zNone = zzA === NONE && zzB === NONE;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    const [[xA, yA, zA, alphaA], [xB, yB, zB, alphaB]] =\n      normalizeColorComponents(\n        [xxA, yyA, zzA, aaA],\n        [xxB, yyB, zzB, aaB],\n        true\n      );\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    let x, y, z;\n    if (alpha === 0) {\n      x = xA * pA + xB * pB;\n      y = yA * pA + yB * pB;\n      z = zA * pA + zB * pB;\n    } else {\n      x = (xA * factorA + xB * factorB) / alpha;\n      y = (yA * factorA + yB * factorB) / alpha;\n      z = (zA * factorA + zB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        xNone ? NONE : roundToPrecision(x, HEX),\n        yNone ? NONE : roundToPrecision(y, HEX),\n        zNone ? NONE : roundToPrecision(z, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (colorSpace === 'xyz-d50') {\n      [r, g, b] = transformXyzD50ToRgb([x, y, z], true);\n    } else {\n      [r, g, b] = transformXyzToRgb([x, y, z], true);\n    }\n    // in hsl, hwb\n  } else if (/^h(?:sl|wb)$/.test(colorSpace)) {\n    let hslA, hslB;\n    if (colorSpace === 'hsl') {\n      if (REG_CURRENT.test(colorA)) {\n        hslA = [NONE, NONE, NONE, NONE];\n      } else {\n        hslA = convertColorToHsl(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        hslB = [NONE, NONE, NONE, NONE];\n      } else {\n        hslB = convertColorToHsl(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        hslA = [NONE, NONE, NONE, NONE];\n      } else {\n        hslA = convertColorToHwb(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        hslB = [NONE, NONE, NONE, NONE];\n      } else {\n        hslB = convertColorToHwb(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (hslA instanceof NullObject || hslB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [hhA, ssA, llA, aaA] = hslA;\n    const [hhB, ssB, llB, aaB] = hslB;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    let [[hA, sA, lA, alphaA], [hB, sB, lB, alphaB]] = normalizeColorComponents(\n      [hhA, ssA, llA, aaA],\n      [hhB, ssB, llB, aaB],\n      true\n    );\n    if (hueArc) {\n      [hA, hB] = interpolateHue(hA, hB, hueArc);\n    }\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    const h = (hA * pA + hB * pB) % DEG;\n    let s, l;\n    if (alpha === 0) {\n      s = sA * pA + sB * pB;\n      l = lA * pA + lB * pB;\n    } else {\n      s = (sA * factorA + sB * factorB) / alpha;\n      l = (lA * factorA + lB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    [r, g, b] = convertColorToRgb(\n      `${colorSpace}(${h} ${s} ${l})`\n    ) as ColorChannels;\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        'srgb',\n        roundToPrecision(r / MAX_RGB, HEX),\n        roundToPrecision(g / MAX_RGB, HEX),\n        roundToPrecision(b / MAX_RGB, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    // in lch, oklch\n  } else if (/^(?:ok)?lch$/.test(colorSpace)) {\n    let lchA, lchB;\n    if (colorSpace === 'lch') {\n      if (REG_CURRENT.test(colorA)) {\n        lchA = [NONE, NONE, NONE, NONE];\n      } else {\n        lchA = convertColorToLch(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        lchB = [NONE, NONE, NONE, NONE];\n      } else {\n        lchB = convertColorToLch(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        lchA = [NONE, NONE, NONE, NONE];\n      } else {\n        lchA = convertColorToOklch(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        lchB = [NONE, NONE, NONE, NONE];\n      } else {\n        lchB = convertColorToOklch(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (lchA instanceof NullObject || lchB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [llA, ccA, hhA, aaA] = lchA;\n    const [llB, ccB, hhB, aaB] = lchB;\n    const lNone = llA === NONE && llB === NONE;\n    const cNone = ccA === NONE && ccB === NONE;\n    const hNone = hhA === NONE && hhB === NONE;\n    const alphaNone = aaA === NONE && aaB === NONE;\n    let [[lA, cA, hA, alphaA], [lB, cB, hB, alphaB]] = normalizeColorComponents(\n      [llA, ccA, hhA, aaA],\n      [llB, ccB, hhB, aaB],\n      true\n    );\n    if (hueArc) {\n      [hA, hB] = interpolateHue(hA, hB, hueArc);\n    }\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    const h = (hA * pA + hB * pB) % DEG;\n    let l, c;\n    if (alpha === 0) {\n      l = lA * pA + lB * pB;\n      c = cA * pA + cB * pB;\n    } else {\n      l = (lA * factorA + lB * factorB) / alpha;\n      c = (cA * factorA + cB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        lNone ? NONE : roundToPrecision(l, HEX),\n        cNone ? NONE : roundToPrecision(c, HEX),\n        hNone ? NONE : roundToPrecision(h, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [, r, g, b] = resolveColorValue(\n      `${colorSpace}(${l} ${c} ${h})`\n    ) as ComputedColorChannels;\n    // in lab, oklab\n  } else {\n    let labA, labB;\n    if (colorSpace === 'lab') {\n      if (REG_CURRENT.test(colorA)) {\n        labA = [NONE, NONE, NONE, NONE];\n      } else {\n        labA = convertColorToLab(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        labB = [NONE, NONE, NONE, NONE];\n      } else {\n        labB = convertColorToLab(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    } else {\n      if (REG_CURRENT.test(colorA)) {\n        labA = [NONE, NONE, NONE, NONE];\n      } else {\n        labA = convertColorToOklab(colorA, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n      if (REG_CURRENT.test(colorB)) {\n        labB = [NONE, NONE, NONE, NONE];\n      } else {\n        labB = convertColorToOklab(colorB, {\n          colorSpace,\n          format: VAL_MIX\n        });\n      }\n    }\n    if (labA instanceof NullObject || labB instanceof NullObject) {\n      const res = cacheInvalidColorValue(cacheKey, format, nullable);\n      return res;\n    }\n    const [llA, aaA, bbA, alA] = labA;\n    const [llB, aaB, bbB, alB] = labB;\n    const lNone = llA === NONE && llB === NONE;\n    const aNone = aaA === NONE && aaB === NONE;\n    const bNone = bbA === NONE && bbB === NONE;\n    const alphaNone = alA === NONE && alB === NONE;\n    const [[lA, aA, bA, alphaA], [lB, aB, bB, alphaB]] =\n      normalizeColorComponents(\n        [llA, aaA, bbA, alA],\n        [llB, aaB, bbB, alB],\n        true\n      );\n    const factorA = alphaA * pA;\n    const factorB = alphaB * pB;\n    alpha = factorA + factorB;\n    let l, aO, bO;\n    if (alpha === 0) {\n      l = lA * pA + lB * pB;\n      aO = aA * pA + aB * pB;\n      bO = bA * pA + bB * pB;\n    } else {\n      l = (lA * factorA + lB * factorB) / alpha;\n      aO = (aA * factorA + aB * factorB) / alpha;\n      bO = (bA * factorA + bB * factorB) / alpha;\n      alpha = parseFloat(alpha.toFixed(3));\n    }\n    if (format === VAL_COMP) {\n      const res: SpecifiedColorChannels = [\n        colorSpace,\n        lNone ? NONE : roundToPrecision(l, HEX),\n        aNone ? NONE : roundToPrecision(aO, HEX),\n        bNone ? NONE : roundToPrecision(bO, HEX),\n        alphaNone ? NONE : alpha * m\n      ];\n      setCache(cacheKey, res);\n      return res;\n    }\n    [, r, g, b] = resolveColorValue(\n      `${colorSpace}(${l} ${aO} ${bO})`\n    ) as ComputedColorChannels;\n  }\n  const res: SpecifiedColorChannels = [\n    'rgb',\n    Math.round(r),\n    Math.round(g),\n    Math.round(b),\n    parseFloat((alpha * m).toFixed(3))\n  ];\n  setCache(cacheKey, res);\n  return res;\n};\n", "/**\n * css-var\n */\n\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { isColor } from './util';\nimport { Options } from './typedef';\n\n/* constants */\nimport { FN_VAR, SYN_FN_CALC, SYN_FN_VAR, VAL_SPEC } from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  EOF,\n  Ident: IDENT,\n  Whitespace: W_SPACE\n} = TokenType;\nconst NAMESPACE = 'css-var';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve custom property\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns result - [tokens, resolvedValue]\n */\nexport function resolveCustomProperty(\n  tokens: CSSToken[],\n  opt: Options = {}\n): [CSSToken[], string] {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { customProperty = {} } = opt;\n  const items: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type, value] = token as [TokenType, string];\n    // end of var()\n    if (type === PAREN_CLOSE) {\n      break;\n    }\n    // nested var()\n    if (value === FN_VAR) {\n      const [restTokens, item] = resolveCustomProperty(tokens, opt);\n      tokens = restTokens;\n      if (item) {\n        items.push(item);\n      }\n    } else if (type === IDENT) {\n      if (value.startsWith('--')) {\n        let item;\n        if (Object.hasOwnProperty.call(customProperty, value)) {\n          item = customProperty[value] as string;\n        } else if (typeof customProperty.callback === 'function') {\n          item = customProperty.callback(value);\n        }\n        if (item) {\n          items.push(item);\n        }\n      } else if (value) {\n        items.push(value);\n      }\n    }\n  }\n  let resolveAsColor = false;\n  if (items.length > 1) {\n    const lastValue = items[items.length - 1];\n    resolveAsColor = isColor(lastValue);\n  }\n  let resolvedValue = '';\n  for (let item of items) {\n    item = item.trim();\n    if (REG_FN_VAR.test(item)) {\n      // recurse resolveVar()\n      const resolvedItem = resolveVar(item, opt);\n      if (isString(resolvedItem)) {\n        if (resolveAsColor) {\n          if (isColor(resolvedItem)) {\n            resolvedValue = resolvedItem;\n          }\n        } else {\n          resolvedValue = resolvedItem;\n        }\n      }\n    } else if (REG_FN_CALC.test(item)) {\n      item = cssCalc(item, opt);\n      if (resolveAsColor) {\n        if (isColor(item)) {\n          resolvedValue = item;\n        }\n      } else {\n        resolvedValue = item;\n      }\n    } else if (\n      item &&\n      !/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(item)\n    ) {\n      if (resolveAsColor) {\n        if (isColor(item)) {\n          resolvedValue = item;\n        }\n      } else {\n        resolvedValue = item;\n      }\n    }\n    if (resolvedValue) {\n      break;\n    }\n  }\n  return [tokens, resolvedValue];\n}\n\n/**\n * parse tokens\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns parsed tokens\n */\nexport function parseTokens(\n  tokens: CSSToken[],\n  opt: Options = {}\n): string[] | NullObject {\n  const res: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    const [type = '', value = ''] = token as [TokenType, string];\n    if (value === FN_VAR) {\n      const [restTokens, resolvedValue] = resolveCustomProperty(tokens, opt);\n      if (!resolvedValue) {\n        return new NullObject();\n      }\n      tokens = restTokens;\n      res.push(resolvedValue);\n    } else {\n      switch (type) {\n        case PAREN_CLOSE: {\n          if (res.length) {\n            const lastValue = res[res.length - 1];\n            if (lastValue === ' ') {\n              res.splice(-1, 1, value);\n            } else {\n              res.push(value);\n            }\n          } else {\n            res.push(value);\n          }\n          break;\n        }\n        case W_SPACE: {\n          if (res.length) {\n            const lastValue = res[res.length - 1];\n            if (\n              isString(lastValue) &&\n              !lastValue.endsWith('(') &&\n              lastValue !== ' '\n            ) {\n              res.push(value);\n            }\n          }\n          break;\n        }\n        default: {\n          if (type !== COMMENT && type !== EOF) {\n            res.push(value);\n          }\n        }\n      }\n    }\n  }\n  return res;\n}\n\n/**\n * resolve CSS var()\n * @param value - CSS value including var()\n * @param [opt] - options\n * @returns resolved value\n */\nexport function resolveVar(\n  value: string,\n  opt: Options = {}\n): string | NullObject {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (!REG_FN_VAR.test(value) || format === VAL_SPEC) {\n      return value;\n    }\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveVar',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  const tokens = tokenize({ css: value });\n  const values = parseTokens(tokens, opt);\n  if (Array.isArray(values)) {\n    let color = values.join('');\n    if (REG_FN_CALC.test(color)) {\n      color = cssCalc(color, opt);\n    }\n    setCache(cacheKey, color);\n    return color;\n  } else {\n    setCache(cacheKey, null);\n    return new NullObject();\n  }\n}\n\n/**\n * CSS var()\n * @param value - CSS value including var()\n * @param [opt] - options\n * @returns resolved value\n */\nexport const cssVar = (value: string, opt: Options = {}): string => {\n  const resolvedValue = resolveVar(value, opt);\n  if (isString(resolvedValue)) {\n    return resolvedValue;\n  }\n  return '';\n};\n", "/**\n * relative-color\n */\n\nimport { SyntaxFlag, color as colorParser } from '@csstools/css-color-parser';\nimport {\n  ComponentValue,\n  parseComponentValue\n} from '@csstools/css-parser-algorithms';\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { NAMED_COLORS, convertColorToRgb } from './color';\nimport { isString, isStringOrNumber } from './common';\nimport { resolveDimension, serializeCalc } from './css-calc';\nimport { resolveColor } from './resolve';\nimport { roundToPrecision } from './util';\nimport {\n  ColorChannels,\n  MatchedRegExp,\n  Options,\n  StringColorChannels\n} from './typedef';\n\n/* constants */\nimport {\n  CS_LAB,\n  CS_LCH,\n  FN_REL,\n  FN_REL_CAPT,\n  FN_VAR,\n  NONE,\n  SYN_COLOR_TYPE,\n  SYN_FN_MATH_START,\n  SYN_FN_VAR,\n  SYN_MIX,\n  VAL_SPEC\n} from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  Dimension: DIM,\n  EOF,\n  Function: FUNC,\n  Ident: IDENT,\n  Number: NUM,\n  OpenParen: PAREN_OPEN,\n  Percentage: PCT,\n  Whitespace: W_SPACE\n} = TokenType;\nconst { HasNoneKeywords: KEY_NONE } = SyntaxFlag;\nconst NAMESPACE = 'relative-color';\n\n/* numeric constants */\nconst OCT = 8;\nconst DEC = 10;\nconst HEX = 16;\nconst MAX_PCT = 100;\nconst MAX_RGB = 255;\n\n/* type definitions */\n/**\n * @type NumberOrStringColorChannels - color channel\n */\ntype NumberOrStringColorChannels = ColorChannels & StringColorChannels;\n\n/* regexp */\nconst REG_COLOR_CAPT = new RegExp(\n  `^${FN_REL}(${SYN_COLOR_TYPE}|${SYN_MIX})\\\\s+`\n);\nconst REG_CS_HSL = /(?:hsla?|hwb)$/;\nconst REG_CS_CIE = new RegExp(`^(?:${CS_LAB}|${CS_LCH})$`);\nconst REG_FN_MATH_START = new RegExp(SYN_FN_MATH_START);\nconst REG_FN_REL = new RegExp(FN_REL);\nconst REG_FN_REL_CAPT = new RegExp(`^${FN_REL_CAPT}`);\nconst REG_FN_REL_START = new RegExp(`^${FN_REL}`);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve relative color channels\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns resolved color channels\n */\nexport function resolveColorChannels(\n  tokens: CSSToken[],\n  opt: Options = {}\n): NumberOrStringColorChannels | NullObject {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { colorSpace = '', format = '' } = opt;\n  const colorChannels = new Map([\n    ['color', ['r', 'g', 'b', 'alpha']],\n    ['hsl', ['h', 's', 'l', 'alpha']],\n    ['hsla', ['h', 's', 'l', 'alpha']],\n    ['hwb', ['h', 'w', 'b', 'alpha']],\n    ['lab', ['l', 'a', 'b', 'alpha']],\n    ['lch', ['l', 'c', 'h', 'alpha']],\n    ['oklab', ['l', 'a', 'b', 'alpha']],\n    ['oklch', ['l', 'c', 'h', 'alpha']],\n    ['rgb', ['r', 'g', 'b', 'alpha']],\n    ['rgba', ['r', 'g', 'b', 'alpha']]\n  ]);\n  const colorChannel = colorChannels.get(colorSpace);\n  // invalid color channel\n  if (!colorChannel) {\n    return new NullObject();\n  }\n  const mathFunc = new Set();\n  const channels: [\n    (number | string)[],\n    (number | string)[],\n    (number | string)[],\n    (number | string)[]\n  ] = [[], [], [], []];\n  let i = 0;\n  let nest = 0;\n  let func = false;\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type, value, , , detail] = token as [\n      TokenType,\n      string,\n      number,\n      number,\n      { value: string | number } | undefined\n    ];\n    const channel = channels[i];\n    if (Array.isArray(channel)) {\n      switch (type) {\n        case DIM: {\n          const resolvedValue = resolveDimension(token, opt);\n          if (isString(resolvedValue)) {\n            channel.push(resolvedValue);\n          } else {\n            channel.push(value);\n          }\n          break;\n        }\n        case FUNC: {\n          channel.push(value);\n          func = true;\n          nest++;\n          if (REG_FN_MATH_START.test(value)) {\n            mathFunc.add(nest);\n          }\n          break;\n        }\n        case IDENT: {\n          // invalid channel key\n          if (!colorChannel.includes(value)) {\n            return new NullObject();\n          }\n          channel.push(value);\n          if (!func) {\n            i++;\n          }\n          break;\n        }\n        case NUM: {\n          channel.push(Number(detail?.value));\n          if (!func) {\n            i++;\n          }\n          break;\n        }\n        case PAREN_OPEN: {\n          channel.push(value);\n          nest++;\n          break;\n        }\n        case PAREN_CLOSE: {\n          if (func) {\n            const lastValue = channel[channel.length - 1];\n            if (lastValue === ' ') {\n              channel.splice(-1, 1, value);\n            } else {\n              channel.push(value);\n            }\n            if (mathFunc.has(nest)) {\n              mathFunc.delete(nest);\n            }\n            nest--;\n            if (nest === 0) {\n              func = false;\n              i++;\n            }\n          }\n          break;\n        }\n        case PCT: {\n          channel.push(Number(detail?.value) / MAX_PCT);\n          if (!func) {\n            i++;\n          }\n          break;\n        }\n        case W_SPACE: {\n          if (channel.length && func) {\n            const lastValue = channel[channel.length - 1];\n            if (typeof lastValue === 'number') {\n              channel.push(value);\n            } else if (\n              isString(lastValue) &&\n              !lastValue.endsWith('(') &&\n              lastValue !== ' '\n            ) {\n              channel.push(value);\n            }\n          }\n          break;\n        }\n        default: {\n          if (type !== COMMENT && type !== EOF && func) {\n            channel.push(value);\n          }\n        }\n      }\n    }\n  }\n  const channelValues = [];\n  for (const channel of channels) {\n    if (channel.length === 1) {\n      const [resolvedValue] = channel;\n      if (isStringOrNumber(resolvedValue)) {\n        channelValues.push(resolvedValue);\n      }\n    } else if (channel.length) {\n      const resolvedValue = serializeCalc(channel.join(''), {\n        format\n      });\n      channelValues.push(resolvedValue);\n    }\n  }\n  return channelValues as NumberOrStringColorChannels;\n}\n\n/**\n * extract origin color\n * @param value - CSS color value\n * @param [opt] - options\n * @returns origin color value\n */\nexport function extractOriginColor(\n  value: string,\n  opt: Options = {}\n): string | NullObject {\n  const { currentColor = '', format = '' } = opt;\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n    if (!value) {\n      return new NullObject();\n    }\n    if (!REG_FN_REL_START.test(value)) {\n      return value;\n    }\n  } else {\n    return new NullObject();\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'extractOriginColor',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (/currentcolor/.test(value)) {\n    if (currentColor) {\n      value = value.replace(/currentcolor/g, currentColor);\n    } else {\n      setCache(cacheKey, null);\n      return new NullObject();\n    }\n  }\n  let colorSpace = '';\n  if (REG_FN_REL_CAPT.test(value)) {\n    [, colorSpace] = value.match(REG_FN_REL_CAPT) as MatchedRegExp;\n  }\n  opt.colorSpace = colorSpace;\n  if (REG_COLOR_CAPT.test(value)) {\n    const [, originColor] = value.match(REG_COLOR_CAPT) as MatchedRegExp;\n    const [, restValue] = value.split(originColor) as MatchedRegExp;\n    if (/^[a-z]+$/.test(originColor)) {\n      if (\n        !/^transparent$/.test(originColor) &&\n        !Object.prototype.hasOwnProperty.call(NAMED_COLORS, originColor)\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n    } else if (format === VAL_SPEC) {\n      const resolvedOriginColor = resolveColor(originColor, opt);\n      if (isString(resolvedOriginColor)) {\n        value = value.replace(originColor, resolvedOriginColor);\n      }\n    }\n    if (format === VAL_SPEC) {\n      const tokens = tokenize({ css: restValue });\n      const channelValues = resolveColorChannels(tokens, opt);\n      if (channelValues instanceof NullObject) {\n        setCache(cacheKey, null);\n        return channelValues;\n      }\n      const [v1, v2, v3, v4] = channelValues;\n      let channelValue = '';\n      if (isStringOrNumber(v4)) {\n        channelValue = ` ${v1} ${v2} ${v3} / ${v4})`;\n      } else {\n        channelValue = ` ${channelValues.join(' ')})`;\n      }\n      if (restValue !== channelValue) {\n        value = value.replace(restValue, channelValue);\n      }\n    }\n    // nested relative color\n  } else {\n    const [, restValue] = value.split(REG_FN_REL_START) as MatchedRegExp;\n    const tokens = tokenize({ css: restValue });\n    const originColor: string[] = [];\n    let nest = 0;\n    while (tokens.length) {\n      const [type, tokenValue] = tokens.shift() as [TokenType, string];\n      switch (type) {\n        case FUNC:\n        case PAREN_OPEN: {\n          originColor.push(tokenValue);\n          nest++;\n          break;\n        }\n        case PAREN_CLOSE: {\n          const lastValue = originColor[originColor.length - 1];\n          if (lastValue === ' ') {\n            originColor.splice(-1, 1, tokenValue);\n          } else if (isString(lastValue)) {\n            originColor.push(tokenValue);\n          }\n          nest--;\n          break;\n        }\n        case W_SPACE: {\n          const lastValue = originColor[originColor.length - 1];\n          if (\n            isString(lastValue) &&\n            !lastValue.endsWith('(') &&\n            lastValue !== ' '\n          ) {\n            originColor.push(tokenValue);\n          }\n          break;\n        }\n        default: {\n          if (type !== COMMENT && type !== EOF) {\n            originColor.push(tokenValue);\n          }\n        }\n      }\n      if (nest === 0) {\n        break;\n      }\n    }\n    const resolvedOriginColor = resolveRelativeColor(\n      originColor.join('').trim(),\n      opt\n    );\n    if (resolvedOriginColor instanceof NullObject) {\n      setCache(cacheKey, null);\n      return resolvedOriginColor;\n    }\n    const channelValues = resolveColorChannels(tokens, opt);\n    if (channelValues instanceof NullObject) {\n      setCache(cacheKey, null);\n      return channelValues;\n    }\n    const [v1, v2, v3, v4] = channelValues;\n    let channelValue = '';\n    if (isStringOrNumber(v4)) {\n      channelValue = ` ${v1} ${v2} ${v3} / ${v4})`;\n    } else {\n      channelValue = ` ${channelValues.join(' ')})`;\n    }\n    value = value.replace(restValue, `${resolvedOriginColor}${channelValue}`);\n  }\n  setCache(cacheKey, value);\n  return value;\n}\n\n/**\n * resolve relative color\n * @param value - CSS relative color value\n * @param [opt] - options\n * @returns resolved value\n */\nexport function resolveRelativeColor(\n  value: string,\n  opt: Options = {}\n): string | NullObject {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (REG_FN_VAR.test(value)) {\n      if (format === VAL_SPEC) {\n        return value;\n        // var() must be resolved before resolveRelativeColor()\n      } else {\n        throw new SyntaxError(`Unexpected token ${FN_VAR} found.`);\n      }\n    } else if (!REG_FN_REL.test(value)) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveRelativeColor',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  const originColor = extractOriginColor(value, opt);\n  if (originColor instanceof NullObject) {\n    setCache(cacheKey, null);\n    return originColor;\n  }\n  value = originColor;\n  if (format === VAL_SPEC) {\n    if (value.startsWith('rgba(')) {\n      value = value.replace(/^rgba\\(/, 'rgb(');\n    } else if (value.startsWith('hsla(')) {\n      value = value.replace(/^hsla\\(/, 'hsl(');\n    }\n    return value;\n  }\n  const tokens = tokenize({ css: value });\n  const components = parseComponentValue(tokens) as ComponentValue;\n  const parsedComponents = colorParser(components);\n  if (!parsedComponents) {\n    setCache(cacheKey, null);\n    return new NullObject();\n  }\n  const {\n    alpha: alphaComponent,\n    channels: channelsComponent,\n    colorNotation,\n    syntaxFlags\n  } = parsedComponents;\n  let alpha: number | string;\n  if (Number.isNaN(Number(alphaComponent))) {\n    if (syntaxFlags instanceof Set && syntaxFlags.has(KEY_NONE)) {\n      alpha = NONE;\n    } else {\n      alpha = 0;\n    }\n  } else {\n    alpha = roundToPrecision(Number(alphaComponent), OCT);\n  }\n  let v1: number | string;\n  let v2: number | string;\n  let v3: number | string;\n  [v1, v2, v3] = channelsComponent;\n  let resolvedValue;\n  if (REG_CS_CIE.test(colorNotation)) {\n    const hasNone = syntaxFlags instanceof Set && syntaxFlags.has(KEY_NONE);\n    if (Number.isNaN(v1)) {\n      if (hasNone) {\n        v1 = NONE;\n      } else {\n        v1 = 0;\n      }\n    } else {\n      v1 = roundToPrecision(v1, HEX);\n    }\n    if (Number.isNaN(v2)) {\n      if (hasNone) {\n        v2 = NONE;\n      } else {\n        v2 = 0;\n      }\n    } else {\n      v2 = roundToPrecision(v2, HEX);\n    }\n    if (Number.isNaN(v3)) {\n      if (hasNone) {\n        v3 = NONE;\n      } else {\n        v3 = 0;\n      }\n    } else {\n      v3 = roundToPrecision(v3, HEX);\n    }\n    if (alpha === 1) {\n      resolvedValue = `${colorNotation}(${v1} ${v2} ${v3})`;\n    } else {\n      resolvedValue = `${colorNotation}(${v1} ${v2} ${v3} / ${alpha})`;\n    }\n  } else if (REG_CS_HSL.test(colorNotation)) {\n    if (Number.isNaN(v1)) {\n      v1 = 0;\n    }\n    if (Number.isNaN(v2)) {\n      v2 = 0;\n    }\n    if (Number.isNaN(v3)) {\n      v3 = 0;\n    }\n    let [r, g, b] = convertColorToRgb(\n      `${colorNotation}(${v1} ${v2} ${v3} / ${alpha})`\n    ) as ColorChannels;\n    r = roundToPrecision(r / MAX_RGB, DEC);\n    g = roundToPrecision(g / MAX_RGB, DEC);\n    b = roundToPrecision(b / MAX_RGB, DEC);\n    if (alpha === 1) {\n      resolvedValue = `color(srgb ${r} ${g} ${b})`;\n    } else {\n      resolvedValue = `color(srgb ${r} ${g} ${b} / ${alpha})`;\n    }\n  } else {\n    const cs = colorNotation === 'rgb' ? 'srgb' : colorNotation;\n    const hasNone = syntaxFlags instanceof Set && syntaxFlags.has(KEY_NONE);\n    if (Number.isNaN(v1)) {\n      if (hasNone) {\n        v1 = NONE;\n      } else {\n        v1 = 0;\n      }\n    } else {\n      v1 = roundToPrecision(v1, DEC);\n    }\n    if (Number.isNaN(v2)) {\n      if (hasNone) {\n        v2 = NONE;\n      } else {\n        v2 = 0;\n      }\n    } else {\n      v2 = roundToPrecision(v2, DEC);\n    }\n    if (Number.isNaN(v3)) {\n      if (hasNone) {\n        v3 = NONE;\n      } else {\n        v3 = 0;\n      }\n    } else {\n      v3 = roundToPrecision(v3, DEC);\n    }\n    if (alpha === 1) {\n      resolvedValue = `color(${cs} ${v1} ${v2} ${v3})`;\n    } else {\n      resolvedValue = `color(${cs} ${v1} ${v2} ${v3} / ${alpha})`;\n    }\n  }\n  setCache(cacheKey, resolvedValue);\n  return resolvedValue;\n}\n", "/**\n * resolve\n */\n\nimport {\n  <PERSON>acheItem,\n  NullObject,\n  createC<PERSON><PERSON><PERSON>,\n  getCache,\n  setCache\n} from './cache';\nimport {\n  convertRgbToHex,\n  resolveColorFunc,\n  resolveColorMix,\n  resolveColorValue\n} from './color';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { resolveVar } from './css-var';\nimport { resolveRelativeColor } from './relative-color';\nimport {\n  ComputedColorChannels,\n  Options,\n  SpecifiedColorChannels\n} from './typedef';\n\n/* constants */\nimport {\n  FN_COLOR,\n  FN_MIX,\n  SYN_FN_CALC,\n  SYN_FN_REL,\n  SYN_FN_VAR,\n  VAL_COMP,\n  VAL_SPEC\n} from './constant';\nconst NAMESPACE = 'resolve';\nconst RGB_TRANSPARENT = 'rgba(0, 0, 0, 0)';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_REL = new RegExp(SYN_FN_REL);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve color\n * @param value - CSS color value\n * @param [opt] - options\n * @returns resolved color\n */\nexport const resolveColor = (\n  value: string,\n  opt: Options = {}\n): string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { currentColor = '', format = VAL_COMP, nullable = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolve',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (REG_FN_VAR.test(value)) {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    const resolvedValue = resolveVar(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      switch (format) {\n        case 'hex':\n        case 'hexAlpha': {\n          setCache(cacheKey, resolvedValue);\n          return resolvedValue;\n        }\n        default: {\n          if (nullable) {\n            setCache(cacheKey, resolvedValue);\n            return resolvedValue;\n          }\n          const res = RGB_TRANSPARENT;\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    } else {\n      value = resolvedValue;\n    }\n  }\n  if (opt.format !== format) {\n    opt.format = format;\n  }\n  value = value.toLowerCase();\n  if (REG_FN_REL.test(value)) {\n    const resolvedValue = resolveRelativeColor(value, opt);\n    if (format === VAL_COMP) {\n      let res;\n      if (resolvedValue instanceof NullObject) {\n        if (nullable) {\n          res = resolvedValue;\n        } else {\n          res = RGB_TRANSPARENT;\n        }\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (format === VAL_SPEC) {\n      let res = '';\n      if (resolvedValue instanceof NullObject) {\n        res = '';\n      } else {\n        res = resolvedValue;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n    if (resolvedValue instanceof NullObject) {\n      value = '';\n    } else {\n      value = resolvedValue;\n    }\n  }\n  if (REG_FN_CALC.test(value)) {\n    value = cssCalc(value, opt);\n  }\n  let cs = '';\n  let r = NaN;\n  let g = NaN;\n  let b = NaN;\n  let alpha = NaN;\n  if (value === 'transparent') {\n    switch (format) {\n      case VAL_SPEC: {\n        setCache(cacheKey, value);\n        return value;\n      }\n      case 'hex': {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      case 'hexAlpha': {\n        const res = '#00000000';\n        setCache(cacheKey, res);\n        return res;\n      }\n      case VAL_COMP:\n      default: {\n        const res = RGB_TRANSPARENT;\n        setCache(cacheKey, res);\n        return res;\n      }\n    }\n  } else if (value === 'currentcolor') {\n    if (format === VAL_SPEC) {\n      setCache(cacheKey, value);\n      return value;\n    }\n    if (currentColor) {\n      let resolvedValue;\n      if (currentColor.startsWith(FN_MIX)) {\n        resolvedValue = resolveColorMix(currentColor, opt);\n      } else if (currentColor.startsWith(FN_COLOR)) {\n        resolvedValue = resolveColorFunc(currentColor, opt);\n      } else {\n        resolvedValue = resolveColorValue(currentColor, opt);\n      }\n      if (resolvedValue instanceof NullObject) {\n        setCache(cacheKey, resolvedValue);\n        return resolvedValue;\n      }\n      [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n    } else if (format === VAL_COMP) {\n      const res = RGB_TRANSPARENT;\n      setCache(cacheKey, res);\n      return res;\n    }\n  } else if (format === VAL_SPEC) {\n    if (value.startsWith(FN_MIX)) {\n      const res = resolveColorMix(value, opt) as string;\n      setCache(cacheKey, res);\n      return res;\n    } else if (value.startsWith(FN_COLOR)) {\n      const [scs, rr, gg, bb, aa] = resolveColorFunc(\n        value,\n        opt\n      ) as SpecifiedColorChannels;\n      let res = '';\n      if (aa === 1) {\n        res = `color(${scs} ${rr} ${gg} ${bb})`;\n      } else {\n        res = `color(${scs} ${rr} ${gg} ${bb} / ${aa})`;\n      }\n      setCache(cacheKey, res);\n      return res;\n    } else {\n      const rgb = resolveColorValue(value, opt);\n      if (isString(rgb)) {\n        setCache(cacheKey, rgb);\n        return rgb;\n      }\n      const [scs, rr, gg, bb, aa] = rgb as SpecifiedColorChannels;\n      let res = '';\n      if (scs === 'rgb') {\n        if (aa === 1) {\n          res = `${scs}(${rr}, ${gg}, ${bb})`;\n        } else {\n          res = `${scs}a(${rr}, ${gg}, ${bb}, ${aa})`;\n        }\n      } else if (aa === 1) {\n        res = `${scs}(${rr} ${gg} ${bb})`;\n      } else {\n        res = `${scs}(${rr} ${gg} ${bb} / ${aa})`;\n      }\n      setCache(cacheKey, res);\n      return res;\n    }\n  } else if (value.startsWith(FN_MIX)) {\n    if (/currentcolor/.test(value)) {\n      if (currentColor) {\n        value = value.replace(/currentcolor/g, currentColor);\n      }\n    }\n    if (/transparent/.test(value)) {\n      value = value.replace(/transparent/g, RGB_TRANSPARENT);\n    }\n    const resolvedValue = resolveColorMix(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  } else if (value.startsWith(FN_COLOR)) {\n    const resolvedValue = resolveColorFunc(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  } else if (value) {\n    const resolvedValue = resolveColorValue(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      setCache(cacheKey, resolvedValue);\n      return resolvedValue;\n    }\n    [cs, r, g, b, alpha] = resolvedValue as ComputedColorChannels;\n  }\n  let res = '';\n  switch (format) {\n    case 'hex': {\n      if (\n        Number.isNaN(r) ||\n        Number.isNaN(g) ||\n        Number.isNaN(b) ||\n        Number.isNaN(alpha) ||\n        alpha === 0\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      res = convertRgbToHex([r, g, b, 1]);\n      break;\n    }\n    case 'hexAlpha': {\n      if (\n        Number.isNaN(r) ||\n        Number.isNaN(g) ||\n        Number.isNaN(b) ||\n        Number.isNaN(alpha)\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n      res = convertRgbToHex([r, g, b, alpha]);\n      break;\n    }\n    case VAL_COMP:\n    default: {\n      switch (cs) {\n        case 'rgb': {\n          if (alpha === 1) {\n            res = `${cs}(${r}, ${g}, ${b})`;\n          } else {\n            res = `${cs}a(${r}, ${g}, ${b}, ${alpha})`;\n          }\n          break;\n        }\n        case 'lab':\n        case 'lch':\n        case 'oklab':\n        case 'oklch': {\n          if (alpha === 1) {\n            res = `${cs}(${r} ${g} ${b})`;\n          } else {\n            res = `${cs}(${r} ${g} ${b} / ${alpha})`;\n          }\n          break;\n        }\n        // color()\n        default: {\n          if (alpha === 1) {\n            res = `color(${cs} ${r} ${g} ${b})`;\n          } else {\n            res = `color(${cs} ${r} ${g} ${b} / ${alpha})`;\n          }\n        }\n      }\n    }\n  }\n  setCache(cacheKey, res);\n  return res;\n};\n\n/**\n * resolve CSS color\n * @param value\n *   - CSS color value\n *   - system colors are not supported\n * @param [opt] - options\n * @param [opt.currentColor]\n *   - color to use for `currentcolor` keyword\n *   - if omitted, it will be treated as a missing color\n *     i.e. `rgb(none none none / none)`\n * @param [opt.customProperty]\n *   - custom properties\n *   - pair of `--` prefixed property name and value,\n *     e.g. `customProperty: { '--some-color': '#0000ff' }`\n *   - and/or `callback` function to get the value of the custom property,\n *     e.g. `customProperty: { callback: someDeclaration.getPropertyValue }`\n * @param [opt.dimension]\n *   - dimension, convert relative length to pixels\n *   - pair of unit and it's value as a number in pixels,\n *     e.g. `dimension: { em: 12, rem: 16, vw: 10.26 }`\n *   - and/or `callback` function to get the value as a number in pixels,\n *     e.g. `dimension: { callback: convertUnitToPixel }`\n * @param [opt.format]\n *   - output format, one of below\n *   - `computedValue` (default), [computed value][139] of the color\n *   - `specifiedValue`, [specified value][140] of the color\n *   - `hex`, hex color notation, i.e. `rrggbb`\n *   - `hexAlpha`, hex color notation with alpha channel, i.e. `#rrggbbaa`\n * @returns\n *   - one of rgba?(), #rrggbb(aa)?, color-name, '(empty-string)',\n *     color(color-space r g b / alpha), color(color-space x y z / alpha),\n *     lab(l a b / alpha), lch(l c h / alpha), oklab(l a b / alpha),\n *     oklch(l c h / alpha), null\n *   - in `computedValue`, values are numbers, however `rgb()` values are\n *     integers\n *   - in `specifiedValue`, returns `empty string` for unknown and/or invalid\n *     color\n *   - in `hex`, returns `null` for `transparent`, and also returns `null` if\n *     any of `r`, `g`, `b`, `alpha` is not a number\n *   - in `hexAlpha`, returns `#00000000` for `transparent`,\n *     however returns `null` if any of `r`, `g`, `b`, `alpha` is not a number\n */\nexport const resolve = (value: string, opt: Options = {}): string | null => {\n  opt.nullable = false;\n  const resolvedValue = resolveColor(value, opt);\n  if (resolvedValue instanceof NullObject) {\n    return null;\n  }\n  return resolvedValue as string;\n};\n", "/**\n * css-gradient\n */\n\nimport { CacheItem, createCache<PERSON><PERSON>, getCache, setCache } from './cache';\nimport { isString } from './common';\nimport { MatchedRegExp, Options } from './typedef';\nimport { isColor, splitValue } from './util';\n\n/* constants */\nimport {\n  ANGLE,\n  CS_HUE,\n  CS_RECT,\n  LENGTH,\n  NUM,\n  NUM_POSITIVE,\n  PCT\n} from './constant';\nconst NAMESPACE = 'css-gradient';\nconst DIM_ANGLE = `${NUM}(?:${ANGLE})`;\nconst DIM_ANGLE_PCT = `${DIM_ANGLE}|${PCT}`;\nconst DIM_LEN = `${NUM}(?:${LENGTH})|0`;\nconst DIM_LEN_PCT = `${DIM_LEN}|${PCT}`;\nconst DIM_LEN_PCT_POSI = `${NUM_POSITIVE}(?:${LENGTH}|%)|0`;\nconst DIM_LEN_POSI = `${NUM_POSITIVE}(?:${LENGTH})|0`;\nconst CTR = 'center';\nconst L_R = 'left|right';\nconst T_B = 'top|bottom';\nconst S_E = 'start|end';\nconst AXIS_X = `${L_R}|x-(?:${S_E})`;\nconst AXIS_Y = `${T_B}|y-(?:${S_E})`;\nconst BLOCK = `block-(?:${S_E})`;\nconst INLINE = `inline-(?:${S_E})`;\nconst POS_1 = `${CTR}|${AXIS_X}|${AXIS_Y}|${BLOCK}|${INLINE}|${DIM_LEN_PCT}`;\nconst POS_2 = [\n  `(?:${CTR}|${AXIS_X})\\\\s+(?:${CTR}|${AXIS_Y})`,\n  `(?:${CTR}|${AXIS_Y})\\\\s+(?:${CTR}|${AXIS_X})`,\n  `(?:${CTR}|${AXIS_X}|${DIM_LEN_PCT})\\\\s+(?:${CTR}|${AXIS_Y}|${DIM_LEN_PCT})`,\n  `(?:${CTR}|${BLOCK})\\\\s+(?:${CTR}|${INLINE})`,\n  `(?:${CTR}|${INLINE})\\\\s+(?:${CTR}|${BLOCK})`,\n  `(?:${CTR}|${S_E})\\\\s+(?:${CTR}|${S_E})`\n].join('|');\nconst POS_4 = [\n  `(?:${AXIS_X})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${AXIS_Y})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${AXIS_Y})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${AXIS_X})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${BLOCK})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${INLINE})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${INLINE})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${BLOCK})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${S_E})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${S_E})\\\\s+(?:${DIM_LEN_PCT})`\n].join('|');\nconst RAD_EXTENT = '(?:clos|farth)est-(?:corner|side)';\nconst RAD_SIZE = [\n  `${RAD_EXTENT}(?:\\\\s+${RAD_EXTENT})?`,\n  `${DIM_LEN_POSI}`,\n  `(?:${DIM_LEN_PCT_POSI})\\\\s+(?:${DIM_LEN_PCT_POSI})`\n].join('|');\nconst RAD_SHAPE = 'circle|ellipse';\nconst FROM_ANGLE = `from\\\\s+${DIM_ANGLE}`;\nconst AT_POSITION = `at\\\\s+(?:${POS_1}|${POS_2}|${POS_4})`;\nconst TO_SIDE_CORNER = `to\\\\s+(?:(?:${L_R})(?:\\\\s(?:${T_B}))?|(?:${T_B})(?:\\\\s(?:${L_R}))?)`;\nconst IN_COLOR_SPACE = `in\\\\s+(?:${CS_RECT}|${CS_HUE})`;\n\n/* type definitions */\n/**\n * @type ColorStopList - list of color stops\n */\ntype ColorStopList = [string, string, ...string[]];\n\n/**\n * @typedef Gradient - parsed CSS gradient\n * @property value - input value\n * @property type - gradient type\n * @property [gradientLine] - gradient line\n * @property colorStopList - list of color stops\n */\ninterface Gradient {\n  value: string;\n  type: string;\n  gradientLine?: string;\n  colorStopList: ColorStopList;\n}\n\n/* regexp */\nconst REG_GRAD = /^(?:repeating-)?(?:conic|linear|radial)-gradient\\(/;\nconst REG_GRAD_CAPT = /^((?:repeating-)?(?:conic|linear|radial)-gradient)\\(/;\n\n/**\n * get gradient type\n * @param value - gradient value\n * @returns gradient type\n */\nexport const getGradientType = (value: string): string => {\n  if (isString(value)) {\n    value = value.trim();\n    if (REG_GRAD.test(value)) {\n      const [, type] = value.match(REG_GRAD_CAPT) as MatchedRegExp;\n      return type;\n    }\n  }\n  return '';\n};\n\n/**\n * validate gradient line\n * @param value - gradient line value\n * @param type - gradient type\n * @returns result\n */\nexport const validateGradientLine = (value: string, type: string): boolean => {\n  if (isString(value) && isString(type)) {\n    value = value.trim();\n    type = type.trim();\n    let lineSyntax = '';\n    if (/^(?:repeating-)?linear-gradient$/.test(type)) {\n      /*\n       * <linear-gradient-line> = [\n       *   [ <angle> | to <side-or-corner> ] ||\n       *   <color-interpolation-method>\n       * ]\n       */\n      lineSyntax = [\n        `(?:${DIM_ANGLE}|${TO_SIDE_CORNER})(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+(?:${DIM_ANGLE}|${TO_SIDE_CORNER}))?`\n      ].join('|');\n    } else if (/^(?:repeating-)?radial-gradient$/.test(type)) {\n      /*\n       * <radial-gradient-line> = [\n       *   [ [ <radial-shape> || <radial-size> ]? [ at <position> ]? ] ||\n       *   <color-interpolation-method>]?\n       */\n      lineSyntax = [\n        `(?:${RAD_SHAPE})(?:\\\\s+(?:${RAD_SIZE}))?(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `(?:${RAD_SIZE})(?:\\\\s+(?:${RAD_SHAPE}))?(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${AT_POSITION}(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${RAD_SHAPE})(?:\\\\s+(?:${RAD_SIZE}))?(?:\\\\s+${AT_POSITION})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${RAD_SIZE})(?:\\\\s+(?:${RAD_SHAPE}))?(?:\\\\s+${AT_POSITION})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${AT_POSITION})?`\n      ].join('|');\n    } else if (/^(?:repeating-)?conic-gradient$/.test(type)) {\n      /*\n       * <conic-gradient-line> = [\n       *   [ [ from <angle> ]? [ at <position> ]? ] ||\n       *   <color-interpolation-method>\n       * ]\n       */\n      lineSyntax = [\n        `${FROM_ANGLE}(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${AT_POSITION}(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${FROM_ANGLE})?(?:\\\\s+${AT_POSITION})?`\n      ].join('|');\n    }\n    if (lineSyntax) {\n      const reg = new RegExp(`^(?:${lineSyntax})$`);\n      return reg.test(value);\n    }\n  }\n  return false;\n};\n\n/**\n * validate color stop list\n * @param list\n * @param type\n * @param [opt]\n * @returns result\n */\nexport const validateColorStopList = (\n  list: string[],\n  type: string,\n  opt: Options = {}\n): boolean => {\n  if (Array.isArray(list) && list.length > 1) {\n    const dimension = /^(?:repeating-)?conic-gradient$/.test(type)\n      ? DIM_ANGLE_PCT\n      : DIM_LEN_PCT;\n    const regColorHint = new RegExp(`^(?:${dimension})$`);\n    const regDimension = new RegExp(`(?:\\\\s+(?:${dimension})){1,2}$`);\n    const arr = [];\n    for (const item of list) {\n      if (isString(item)) {\n        if (regColorHint.test(item)) {\n          arr.push('hint');\n        } else {\n          const color = item.replace(regDimension, '');\n          if (isColor(color, opt)) {\n            arr.push('color');\n          } else {\n            return false;\n          }\n        }\n      }\n    }\n    const value = arr.join(',');\n    return /^color(?:,(?:hint,)?color)+$/.test(value);\n  }\n  return false;\n};\n\n/**\n * parse CSS gradient\n * @param value - gradient value\n * @param [opt] - options\n * @returns parsed result\n */\nexport const parseGradient = (\n  value: string,\n  opt: Options = {}\n): Gradient | null => {\n  if (isString(value)) {\n    value = value.trim();\n    const cacheKey: string = createCacheKey(\n      {\n        namespace: NAMESPACE,\n        name: 'parseGradient',\n        value\n      },\n      opt\n    );\n    const cachedResult = getCache(cacheKey);\n    if (cachedResult instanceof CacheItem) {\n      if (cachedResult.isNull) {\n        return null;\n      }\n      return cachedResult.item as Gradient;\n    }\n    const type = getGradientType(value);\n    const gradValue = value.replace(REG_GRAD, '').replace(/\\)$/, '');\n    if (type && gradValue) {\n      const [lineOrColorStop = '', ...colorStops] = splitValue(gradValue, {\n        delimiter: ','\n      });\n      const dimension = /^(?:repeating-)?conic-gradient$/.test(type)\n        ? DIM_ANGLE_PCT\n        : DIM_LEN_PCT;\n      const regDimension = new RegExp(`(?:\\\\s+(?:${dimension})){1,2}$`);\n      let isColorStop = false;\n      if (regDimension.test(lineOrColorStop)) {\n        const colorStop = lineOrColorStop.replace(regDimension, '');\n        if (isColor(colorStop, opt)) {\n          isColorStop = true;\n        }\n      } else if (isColor(lineOrColorStop, opt)) {\n        isColorStop = true;\n      }\n      if (isColorStop) {\n        colorStops.unshift(lineOrColorStop);\n        const valid = validateColorStopList(colorStops, type, opt);\n        if (valid) {\n          const res: Gradient = {\n            value,\n            type,\n            colorStopList: colorStops as ColorStopList\n          };\n          setCache(cacheKey, res);\n          return res;\n        }\n      } else if (colorStops.length > 1) {\n        const gradientLine = lineOrColorStop;\n        const valid =\n          validateGradientLine(gradientLine, type) &&\n          validateColorStopList(colorStops, type, opt);\n        if (valid) {\n          const res: Gradient = {\n            value,\n            type,\n            gradientLine,\n            colorStopList: colorStops as ColorStopList\n          };\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    }\n    setCache(cacheKey, null);\n    return null;\n  }\n  return null;\n};\n\n/**\n * is CSS gradient\n * @param value - CSS value\n * @param [opt] - options\n * @returns result\n */\nexport const isGradient = (value: string, opt: Options = {}): boolean => {\n  const gradient = parseGradient(value, opt);\n  return gradient !== null;\n};\n", "/**\n * convert\n */\n\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport {\n  convertColorToHsl,\n  convertColorToHwb,\n  convertColorToLab,\n  convertColorToLch,\n  convertColorToOklab,\n  convertColorToOklch,\n  convertColorToRgb,\n  numberToHexString,\n  parseColorFunc,\n  parseColorValue\n} from './color';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { resolveVar } from './css-var';\nimport { resolveRelativeColor } from './relative-color';\nimport { resolveColor } from './resolve';\nimport { ColorChannels, ComputedColorChannels, Options } from './typedef';\n\n/* constants */\nimport { SYN_FN_CALC, SYN_FN_REL, SYN_FN_VAR, VAL_COMP } from './constant';\nconst NAMESPACE = 'convert';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_REL = new RegExp(SYN_FN_REL);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * pre process\n * @param value - CSS color value\n * @param [opt] - options\n * @returns value\n */\nexport const preProcess = (\n  value: string,\n  opt: Options = {}\n): string | NullObject => {\n  if (isString(value)) {\n    value = value.trim();\n    if (!value) {\n      return new NullObject();\n    }\n  } else {\n    return new NullObject();\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'preProcess',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (REG_FN_VAR.test(value)) {\n    const resolvedValue = resolveVar(value, opt);\n    if (isString(resolvedValue)) {\n      value = resolvedValue;\n    } else {\n      setCache(cacheKey, null);\n      return new NullObject();\n    }\n  }\n  if (REG_FN_REL.test(value)) {\n    const resolvedValue = resolveRelativeColor(value, opt);\n    if (isString(resolvedValue)) {\n      value = resolvedValue;\n    } else {\n      setCache(cacheKey, null);\n      return new NullObject();\n    }\n  } else if (REG_FN_CALC.test(value)) {\n    value = cssCalc(value, opt);\n  }\n  if (value.startsWith('color-mix')) {\n    const clonedOpt = structuredClone(opt);\n    clonedOpt.format = VAL_COMP;\n    clonedOpt.nullable = true;\n    const resolvedValue = resolveColor(value, clonedOpt);\n    setCache(cacheKey, resolvedValue);\n    return resolvedValue;\n  }\n  setCache(cacheKey, value);\n  return value;\n};\n\n/**\n * convert number to hex string\n * @param value - numeric value\n * @returns hex string: 00..ff\n */\nexport const numberToHex = (value: number): string => {\n  const hex = numberToHexString(value);\n  return hex;\n};\n\n/**\n * convert color to hex\n * @param value - CSS color value\n * @param [opt] - options\n * @param [opt.alpha] - enable alpha channel\n * @returns #rrggbb | #rrggbbaa | null\n */\nexport const colorToHex = (value: string, opt: Options = {}): string | null => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return null;\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const { alpha = false } = opt;\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToHex',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return null;\n    }\n    return cachedResult.item as string;\n  }\n  let hex;\n  opt.nullable = true;\n  if (alpha) {\n    opt.format = 'hexAlpha';\n    hex = resolveColor(value, opt);\n  } else {\n    opt.format = 'hex';\n    hex = resolveColor(value, opt);\n  }\n  if (isString(hex)) {\n    setCache(cacheKey, hex);\n    return hex;\n  }\n  setCache(cacheKey, null);\n  return null;\n};\n\n/**\n * convert color to hsl\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [h, s, l, alpha]\n */\nexport const colorToHsl = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToHsl',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  opt.format = 'hsl';\n  const hsl = convertColorToHsl(value, opt) as ColorChannels;\n  setCache(cacheKey, hsl);\n  return hsl;\n};\n\n/**\n * convert color to hwb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [h, w, b, alpha]\n */\nexport const colorToHwb = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToHwb',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  opt.format = 'hwb';\n  const hwb = convertColorToHwb(value, opt) as ColorChannels;\n  setCache(cacheKey, hwb);\n  return hwb;\n};\n\n/**\n * convert color to lab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, a, b, alpha]\n */\nexport const colorToLab = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToLab',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lab = convertColorToLab(value, opt) as ColorChannels;\n  setCache(cacheKey, lab);\n  return lab;\n};\n\n/**\n * convert color to lch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, c, h, alpha]\n */\nexport const colorToLch = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToLch',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lch = convertColorToLch(value, opt) as ColorChannels;\n  setCache(cacheKey, lch);\n  return lch;\n};\n\n/**\n * convert color to oklab\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, a, b, alpha]\n */\nexport const colorToOklab = (\n  value: string,\n  opt: Options = {}\n): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToOklab',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lab = convertColorToOklab(value, opt) as ColorChannels;\n  setCache(cacheKey, lab);\n  return lab;\n};\n\n/**\n * convert color to oklch\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [l, c, h, alpha]\n */\nexport const colorToOklch = (\n  value: string,\n  opt: Options = {}\n): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToOklch',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const lch = convertColorToOklch(value, opt) as ColorChannels;\n  setCache(cacheKey, lch);\n  return lch;\n};\n\n/**\n * convert color to rgb\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [r, g, b, alpha]\n */\nexport const colorToRgb = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToRgb',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  const rgb = convertColorToRgb(value, opt) as ColorChannels;\n  setCache(cacheKey, rgb);\n  return rgb;\n};\n\n/**\n * convert color to xyz\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [x, y, z, alpha]\n */\nexport const colorToXyz = (value: string, opt: Options = {}): ColorChannels => {\n  if (isString(value)) {\n    const resolvedValue = preProcess(value, opt);\n    if (resolvedValue instanceof NullObject) {\n      return [0, 0, 0, 0];\n    }\n    value = resolvedValue.toLowerCase();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'colorToXyz',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as ColorChannels;\n  }\n  let xyz;\n  if (value.startsWith('color(')) {\n    [, ...xyz] = parseColorFunc(value, opt) as ComputedColorChannels;\n  } else {\n    [, ...xyz] = parseColorValue(value, opt) as ComputedColorChannels;\n  }\n  setCache(cacheKey, xyz);\n  return xyz as ColorChannels;\n};\n\n/**\n * convert color to xyz-d50\n * @param value - CSS color value\n * @param [opt] - options\n * @returns ColorChannels - [x, y, z, alpha]\n */\nexport const colorToXyzD50 = (\n  value: string,\n  opt: Options = {}\n): ColorChannels => {\n  opt.d50 = true;\n  return colorToXyz(value, opt);\n};\n\n/* convert */\nexport const convert = {\n  colorToHex,\n  colorToHsl,\n  colorToHwb,\n  colorToLab,\n  colorToLch,\n  colorToOklab,\n  colorToOklch,\n  colorToRgb,\n  colorToXyz,\n  colorToXyzD50,\n  numberToHex\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA,iBAAAA;AAAA,EAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;;;ACIA,IAAAC,mBAAqB;AACrB,IAAAC,wBAA8C;;;ACD9C,uBAAyB;;;ACAzB,IAAAC,wBAAoC;;;ACiB7B,IAAM,WAAW,CAAC,MACvB,OAAO,MAAM,YAAY,aAAa;AAOjC,IAAM,mBAAmB,CAAC,MAC/B,SAAS,CAAC,KAAK,OAAO,MAAM;;;ACzB9B,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ,GAAG,QAAQ,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK;AAC7D,IAAM,QAAQ,QAAQ,KAAK;AAC3B,IAAM,OAAO,OAAO,KAAK;AAClB,IAAM,QAAQ;AACd,IAAM,SACX;AACK,IAAM,MAAM,WAAW,MAAM,8BAA8B,MAAM;AACjE,IAAM,eAAe,UAAU,MAAM,8BAA8B,MAAM;AACzE,IAAM,OAAO;AACb,IAAM,MAAM,GAAG,GAAG;AAClB,IAAM,cAAc,OAAO,KAAK,4BAA4B,KAAK;AACjE,IAAM,oBAAoB,OAAO,KAAK;AACtC,IAAM,aAAa;AACnB,IAAM,mBAAmB,OAAO,IAAI;AAG3C,IAAM,SAAS,oBAAoB,GAAG,IAAI,GAAG,IAAI,IAAI;AACrD,IAAM,aAAa,kBAAkB,GAAG,IAAI,GAAG;AAC/C,IAAM,cAAc;AACpB,IAAM,aAAa;AACnB,IAAM,UAAU;AAChB,IAAM,cAAc;AACpB,IAAM,aAAa,GAAG,GAAG,MAAM,KAAK;AACpC,IAAM,kBAAkB,MAAM,GAAG,MAAM,KAAK,MAAM,IAAI;AACtD,IAAM,gBAAgB,MAAM,GAAG,IAAI,GAAG,IAAI,IAAI;AACvC,IAAM,SAAS,MAAM,OAAO,aAAa,WAAW;AACpD,IAAM,cAAc,IAAI,OAAO,WAAW,WAAW;AACrD,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,SAAS,2CAA2C,OAAO;AACjE,IAAM,SAAS;AACf,IAAM,UAAU,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM;AAC7C,IAAM,SAAS,GAAG,MAAM,IAAI,OAAO;AACnC,IAAM,WAAW;AACjB,IAAM,SAAS;AACf,IAAM,SAAS,MAAM,WAAW;AAChC,IAAM,cAAc,IAAI,WAAW;AACnC,IAAM,SAAS;AACf,IAAM,eAAe,MAAM,MAAM,IAAI,MAAM,WAAW,aAAa,OAAO,MAAM;AAChF,IAAM,aAAa,IAAI,MAAM,cAAc,MAAM;AACjD,IAAM,UAAU,GAAG,eAAe,UAAU,aAAa,OAAO,MAAM;AACtE,IAAM,cAAc,GAAG,UAAU,eAAe,GAAG,OAAO,UAAU;AACpE,IAAM,UAAU,MAAM,aAAa,WAAW,eAAe,GAAG,MAAM;AACtE,IAAM,UAAU,GAAG,aAAa,UAAU,aAAa,OAAO,MAAM;AACpE,IAAM,cAAc,MAAM,GAAG,eAAe,GAAG,QAAQ,GAAG,eAAe,GAAG,QAAQ,UAAU;AAC9F,IAAM,iBAAiB,GAAG,UAAU,gBAAgB,WAAW,uBAAuB,WAAW,+BAA+B,OAAO,sCAAsC,OAAO,4BAA4B,OAAO,uBAAuB,YAAY;AAC1P,IAAM,eAAe,MAAM,cAAc,WAAW,GAAG;AACvD,IAAM,UAAU,4BAA4B,MAAM,aAAa,YAAY,YAAY,YAAY;AACnG,IAAM,eAAe,0BAA0B,MAAM,cAAc,YAAY,cAAc,YAAY;AAGzG,IAAM,WAAW;AACjB,IAAM,UAAU;AAChB,IAAM,WAAW;;;ACZxB,IAAM,YAAY;AAGlB,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,MAAM;AACZ,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,cAAc,MAAM;AAC1B,IAAM,YAAY,QAAQ;AA4B1B,IAAM,MAAwB;AAAA,EAC5B,SAAS;AAAA,EACT;AAAA,GACC,IAAM,SAAS,UAAU;AAC5B;AACA,IAAM,oBAAiC;AAAA,EACrC,CAAC,mBAAmB,sBAAsB,mBAAmB;AAAA,EAC7D,CAAC,qBAAqB,oBAAoB,oBAAoB;AAAA,EAC9D,CAAC,sBAAsB,uBAAuB,iBAAiB;AACjE;AACA,IAAM,oBAAiC;AAAA,EACrC,CAAC,oBAAoB,sBAAsB,oBAAoB;AAAA,EAC/D,CAAC,qBAAqB,oBAAoB,qBAAqB;AAAA,EAC/D,CAAC,uBAAuB,sBAAsB,kBAAkB;AAClE;AAGA,IAAM,sBAAmC;AAAA,EACvC,CAAC,SAAS,SAAS,QAAQ,QAAQ,QAAQ,KAAK;AAAA,EAChD,CAAC,QAAQ,QAAQ,SAAS,QAAQ,QAAQ,MAAM;AAAA,EAChD,CAAC,OAAO,QAAQ,QAAQ,QAAQ,UAAU,OAAO;AACnD;AACA,IAAM,sBAAmC;AAAA,EACvC,CAAC,QAAQ,MAAM,OAAO,KAAK,QAAQ,IAAI;AAAA,EACvC,CAAC,UAAU,QAAQ,UAAU,QAAQ,QAAQ,MAAM;AAAA,EACnD,CAAC,MAAM,OAAO,QAAQ,OAAO,MAAM,GAAG;AACxC;AACA,IAAM,oBAAiC;AAAA,EACrC,CAAC,mBAAmB,oBAAoB,mBAAmB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,kBAAkB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,kBAAkB;AAC7D;AACA,IAAM,oBAAiC;AAAA,EACrC,CAAC,oBAAoB,qBAAqB,kBAAkB;AAAA,EAC5D,CAAC,qBAAqB,mBAAmB,mBAAmB;AAAA,EAC5D,CAAC,qBAAqB,qBAAqB,kBAAkB;AAC/D;AACA,IAAM,sBAAmC;AAAA,EACvC,CAAC,GAAK,oBAAoB,kBAAkB;AAAA,EAC5C,CAAC,GAAK,qBAAqB,mBAAmB;AAAA,EAC9C,CAAC,GAAK,qBAAqB,mBAAmB;AAChD;AACA,IAAM,sBAAmC;AAAA,EACvC,CAAC,mBAAmB,oBAAoB,mBAAmB;AAAA,EAC3D,CAAC,oBAAoB,mBAAqB,iBAAiB;AAAA,EAC3D,CAAC,oBAAoB,oBAAoB,mBAAmB;AAC9D;AACA,IAAM,mBAAgC;AAAA,EACpC,CAAC,SAAS,SAAS,SAAS,QAAQ,SAAS,OAAO;AAAA,EACpD,CAAC,QAAQ,QAAQ,SAAS,QAAQ,SAAS,OAAO;AAAA,EAClD,CAAC,IAAI,GAAG,QAAQ,QAAQ,UAAU,OAAO;AAC3C;AACA,IAAM,wBAAqC;AAAA,EACzC,CAAC,WAAW,UAAU,WAAW,WAAW,WAAW,SAAS;AAAA,EAChE,CAAC,WAAW,UAAU,YAAY,WAAW,UAAU,SAAS;AAAA,EAChE,CAAC,IAAI,GAAG,WAAW,WAAW,YAAY,SAAS;AACrD;AACA,IAAM,oBAAiC;AAAA,EACrC,CAAC,SAAS,QAAQ,SAAS,SAAS,SAAS,MAAM;AAAA,EACnD,CAAC,SAAS,SAAS,UAAU,SAAS,SAAS,OAAO;AAAA,EACtD,CAAC,QAAQ,SAAS,SAAS,SAAS,UAAU,OAAO;AACvD;AACA,IAAM,6BAA0C;AAAA,EAC9C,CAAC,oBAAoB,qBAAqB,kBAAkB;AAAA,EAC5D,CAAC,oBAAoB,mBAAmB,iBAAmB;AAAA,EAC3D,CAAC,GAAK,GAAK,kBAAkB;AAC/B;AAGA,IAAM,YAAY,IAAI,OAAO,OAAO,cAAc,IAAI;AACtD,IAAM,aAAa,IAAI,OAAO,IAAI,WAAW,GAAG;AAChD,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,eAAe,IAAI,OAAO,iBAAiB,YAAY,WAAW;AACxE,IAAM,UAAU,IAAI,OAAO,iBAAiB,OAAO,IAAI,WAAW,WAAW;AAC7E,IAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,IAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,IAAM,UAAU,IAAI,OAAO,eAAe,OAAO,WAAW;AAC5D,IAAM,UAAU,IAAI,OAAO,IAAI,OAAO,GAAG;AACzC,IAAM,eAAe,IAAI,OAAO,IAAI,YAAY,GAAG;AACnD,IAAM,eAAe,IAAI,OAAO,GAAG,OAAO,IAAI,GAAG;AACjD,IAAM,YAAY,IAAI,OAAO,iBAAiB,OAAO,WAAW;AAChE,IAAM,YAAY,IAAI,OAAO,iBAAiB,OAAO,WAAW;AAChE,IAAM,WAAW;AAKV,IAAM,eAAe;AAAA,EAC1B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,OAAO,CAAC,GAAM,GAAM,CAAI;AAAA,EACxB,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,MAAM,CAAC,GAAM,GAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,IAAM,GAAI;AAAA,EAC7B,OAAO,CAAC,KAAM,IAAM,EAAI;AAAA,EACxB,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,CAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,OAAO,CAAC,KAAM,KAAM,EAAI;AAAA,EACxB,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,SAAS,CAAC,KAAM,IAAM,EAAI;AAAA,EAC1B,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,UAAU,CAAC,GAAM,GAAM,GAAI;AAAA,EAC3B,UAAU,CAAC,GAAM,KAAM,GAAI;AAAA,EAC3B,eAAe,CAAC,KAAM,KAAM,EAAI;AAAA,EAChC,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,WAAW,CAAC,GAAM,KAAM,CAAI;AAAA,EAC5B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,GAAM,GAAI;AAAA,EAC9B,gBAAgB,CAAC,IAAM,KAAM,EAAI;AAAA,EACjC,YAAY,CAAC,KAAM,KAAM,CAAI;AAAA,EAC7B,YAAY,CAAC,KAAM,IAAM,GAAI;AAAA,EAC7B,SAAS,CAAC,KAAM,GAAM,CAAI;AAAA,EAC1B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,eAAe,CAAC,IAAM,IAAM,GAAI;AAAA,EAChC,eAAe,CAAC,IAAM,IAAM,EAAI;AAAA,EAChC,eAAe,CAAC,IAAM,IAAM,EAAI;AAAA,EAChC,eAAe,CAAC,GAAM,KAAM,GAAI;AAAA,EAChC,YAAY,CAAC,KAAM,GAAM,GAAI;AAAA,EAC7B,UAAU,CAAC,KAAM,IAAM,GAAI;AAAA,EAC3B,aAAa,CAAC,GAAM,KAAM,GAAI;AAAA,EAC9B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,YAAY,CAAC,IAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,IAAM,EAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,aAAa,CAAC,IAAM,KAAM,EAAI;AAAA,EAC9B,SAAS,CAAC,KAAM,GAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,MAAM,CAAC,KAAM,KAAM,CAAI;AAAA,EACvB,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,OAAO,CAAC,GAAM,KAAM,CAAI;AAAA,EACxB,aAAa,CAAC,KAAM,KAAM,EAAI;AAAA,EAC9B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,IAAM,EAAI;AAAA,EAC5B,QAAQ,CAAC,IAAM,GAAM,GAAI;AAAA,EACzB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,WAAW,CAAC,KAAM,KAAM,CAAI;AAAA,EAC5B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,sBAAsB,CAAC,KAAM,KAAM,GAAI;AAAA,EACvC,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,eAAe,CAAC,IAAM,KAAM,GAAI;AAAA,EAChC,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,gBAAgB,CAAC,KAAM,KAAM,GAAI;AAAA,EACjC,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,MAAM,CAAC,GAAM,KAAM,CAAI;AAAA,EACvB,WAAW,CAAC,IAAM,KAAM,EAAI;AAAA,EAC5B,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,SAAS,CAAC,KAAM,GAAM,GAAI;AAAA,EAC1B,QAAQ,CAAC,KAAM,GAAM,CAAI;AAAA,EACzB,kBAAkB,CAAC,KAAM,KAAM,GAAI;AAAA,EACnC,YAAY,CAAC,GAAM,GAAM,GAAI;AAAA,EAC7B,cAAc,CAAC,KAAM,IAAM,GAAI;AAAA,EAC/B,cAAc,CAAC,KAAM,KAAM,GAAI;AAAA,EAC/B,gBAAgB,CAAC,IAAM,KAAM,GAAI;AAAA,EACjC,iBAAiB,CAAC,KAAM,KAAM,GAAI;AAAA,EAClC,mBAAmB,CAAC,GAAM,KAAM,GAAI;AAAA,EACpC,iBAAiB,CAAC,IAAM,KAAM,GAAI;AAAA,EAClC,iBAAiB,CAAC,KAAM,IAAM,GAAI;AAAA,EAClC,cAAc,CAAC,IAAM,IAAM,GAAI;AAAA,EAC/B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,aAAa,CAAC,KAAM,KAAM,GAAI;AAAA,EAC9B,MAAM,CAAC,GAAM,GAAM,GAAI;AAAA,EACvB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,OAAO,CAAC,KAAM,KAAM,CAAI;AAAA,EACxB,WAAW,CAAC,KAAM,KAAM,EAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,CAAI;AAAA,EACzB,WAAW,CAAC,KAAM,IAAM,CAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,eAAe,CAAC,KAAM,KAAM,GAAI;AAAA,EAChC,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,EAAI;AAAA,EACvB,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,QAAQ,CAAC,KAAM,GAAM,GAAI;AAAA,EACzB,eAAe,CAAC,KAAM,IAAM,GAAI;AAAA,EAChC,KAAK,CAAC,KAAM,GAAM,CAAI;AAAA,EACtB,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,aAAa,CAAC,KAAM,IAAM,EAAI;AAAA,EAC9B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,YAAY,CAAC,KAAM,KAAM,EAAI;AAAA,EAC7B,UAAU,CAAC,IAAM,KAAM,EAAI;AAAA,EAC3B,UAAU,CAAC,KAAM,KAAM,GAAI;AAAA,EAC3B,QAAQ,CAAC,KAAM,IAAM,EAAI;AAAA,EACzB,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,WAAW,CAAC,KAAM,IAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,WAAW,CAAC,KAAM,KAAM,GAAI;AAAA,EAC5B,MAAM,CAAC,KAAM,KAAM,GAAI;AAAA,EACvB,aAAa,CAAC,GAAM,KAAM,GAAI;AAAA,EAC9B,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,KAAK,CAAC,KAAM,KAAM,GAAI;AAAA,EACtB,MAAM,CAAC,GAAM,KAAM,GAAI;AAAA,EACvB,SAAS,CAAC,KAAM,KAAM,GAAI;AAAA,EAC1B,QAAQ,CAAC,KAAM,IAAM,EAAI;AAAA,EACzB,WAAW,CAAC,IAAM,KAAM,GAAI;AAAA,EAC5B,QAAQ,CAAC,KAAM,KAAM,GAAI;AAAA,EACzB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,OAAO,CAAC,KAAM,KAAM,GAAI;AAAA,EACxB,YAAY,CAAC,KAAM,KAAM,GAAI;AAAA,EAC7B,QAAQ,CAAC,KAAM,KAAM,CAAI;AAAA,EACzB,aAAa,CAAC,KAAM,KAAM,EAAI;AAChC;AAUO,IAAM,yBAAyB,CACpC,UACA,QACA,WAAoB,UAC6B;AACjD,MAAI,WAAW,UAAU;AACvB,UAAMC,OAAM;AACZ,aAAS,UAAUA,IAAG;AACtB,WAAOA;AAAA,EACT;AACA,MAAI,UAAU;AACZ,aAAS,UAAU,IAAI;AACvB,WAAO,IAAI,WAAW;AAAA,EACxB;AACA,QAAM,MAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AACtD,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,2BAA2B,CACtC,QACA,WAAoB,UAC6B;AACjD,UAAQ,QAAQ;AAAA,IACd,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK,SAAS;AACZ,aAAO,IAAI,WAAW;AAAA,IACxB;AAAA,IACA,KAAK,UAAU;AACb,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,UAAI,UAAU;AACZ,eAAO,IAAI,WAAW;AAAA,MACxB;AACA,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAC3B;AAAA,EACF;AACF;AAcO,IAAM,0BAA0B,CACrC,KACA,MAOI,CAAC,MACgC;AACrC,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,UAAU,GAAG,GAAG,mBAAmB;AAAA,EAC/C;AACA,QAAM;AAAA,IACJ,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,YAAY;AAAA,IACZ,WAAW;AAAA,IACX,WAAW;AAAA,IACX,gBAAgB;AAAA,EAClB,IAAI;AACJ,MAAI,CAAC,OAAO,SAAS,SAAS,GAAG;AAC/B,UAAM,IAAI,UAAU,GAAG,SAAS,mBAAmB;AAAA,EACrD;AACA,MAAI,CAAC,OAAO,SAAS,SAAS,GAAG;AAC/B,UAAM,IAAI,UAAU,GAAG,SAAS,mBAAmB;AAAA,EACrD;AACA,MAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,UAAM,IAAI,UAAU,GAAG,QAAQ,mBAAmB;AAAA,EACpD;AACA,MAAI,CAAC,OAAO,SAAS,QAAQ,GAAG;AAC9B,UAAM,IAAI,UAAU,GAAG,QAAQ,mBAAmB;AAAA,EACpD;AACA,QAAM,IAAI,IAAI;AACd,MAAI,IAAI,aAAa,IAAI,WAAW;AAClC,UAAM,IAAI,MAAM,2BAA2B,CAAC,GAAG;AAAA,EACjD;AACA,MAAI,IAAI;AACR,SAAO,IAAI,GAAG;AACZ,UAAM,IAAI,IAAI,CAAC;AACf,QAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,YAAM,IAAI,UAAU,GAAG,CAAC,mBAAmB;AAAA,IAC7C,WAAW,IAAI,QAAQ,kBAAkB,IAAI,YAAY,IAAI,WAAW;AACtE,YAAM,IAAI,WAAW,GAAG,CAAC,mBAAmB,QAAQ,QAAQ,QAAQ,GAAG;AAAA,IACzE,WAAW,MAAM,SAAS,IAAI,KAAK,IAAI,IAAI;AACzC,YAAM,IAAI,WAAW,GAAG,CAAC,0BAA0B;AAAA,IACrD;AACA;AAAA,EACF;AACA,MAAI,SAAS,MAAM,MAAM;AACvB,QAAI,KAAK,CAAC;AAAA,EACZ;AACA,SAAO;AACT;AASO,IAAM,kBAAkB,CAC7B,KACA,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM,QAAQ,GAAG,GAAG;AACvB,UAAM,IAAI,UAAU,GAAG,GAAG,mBAAmB;AAAA,EAC/C,WAAW,IAAI,WAAW,MAAM;AAC9B,UAAM,IAAI,MAAM,2BAA2B,IAAI,MAAM,GAAG;AAAA,EAC1D,WAAW,CAAC,MAAM;AAChB,aAAS,KAAK,KAAK;AACjB,UAAI,wBAAwB,GAAuB;AAAA,QACjD,WAAW;AAAA,QACX,eAAe;AAAA,MACjB,CAAC;AAAA,IACH;AAAA,EACF;AACA,QAAM,CAAC,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,MAAM,IAAI,CAAC,IAAI;AACrE,MAAI,IAAI,IAAI;AACZ,MAAI,MAAM;AACR,KAAC,IAAI,IAAI,EAAE,IAAI;AAAA,EACjB,OAAO;AACL,KAAC,IAAI,IAAI,EAAE,IAAI,wBAAwB,KAAK;AAAA,MAC1C,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,QAAM,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO;AAC1C,SAAO,CAAC,IAAI,IAAI,EAAE;AACpB;AASO,IAAM,2BAA2B,CACtC,QACA,QACA,OAAgB,UACmB;AACnC,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD,WAAW,OAAO,WAAW,MAAM;AACjC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAC7D;AACA,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD,WAAW,OAAO,WAAW,MAAM;AACjC,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAC7D;AACA,MAAI,IAAI;AACR,SAAO,IAAI,MAAM;AACf,QAAI,OAAO,CAAC,MAAM,QAAQ,OAAO,CAAC,MAAM,MAAM;AAC5C,aAAO,CAAC,IAAI;AACZ,aAAO,CAAC,IAAI;AAAA,IACd,WAAW,OAAO,CAAC,MAAM,MAAM;AAC7B,aAAO,CAAC,IAAI,OAAO,CAAC;AAAA,IACtB,WAAW,OAAO,CAAC,MAAM,MAAM;AAC7B,aAAO,CAAC,IAAI,OAAO,CAAC;AAAA,IACtB;AACA;AAAA,EACF;AACA,MAAI,MAAM;AACR,WAAO,CAAC,QAAyB,MAAuB;AAAA,EAC1D;AACA,QAAM,kBAAkB,wBAAwB,QAAyB;AAAA,IACvE,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,CAAC;AACD,QAAM,kBAAkB,wBAAwB,QAAyB;AAAA,IACvE,WAAW;AAAA,IACX,eAAe;AAAA,EACjB,CAAC;AACD,SAAO,CAAC,iBAAkC,eAAgC;AAC5E;AAOO,IAAM,oBAAoB,CAAC,UAA0B;AAC1D,MAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD,OAAO;AACL,YAAQ,KAAK,MAAM,KAAK;AACxB,QAAI,QAAQ,KAAK,QAAQ,SAAS;AAChC,YAAM,IAAI,WAAW,GAAG,KAAK,yBAAyB,OAAO,GAAG;AAAA,IAClE;AAAA,EACF;AACA,MAAI,MAAM,MAAM,SAAS,GAAG;AAC5B,MAAI,IAAI,WAAW,GAAG;AACpB,UAAM,IAAI,GAAG;AAAA,EACf;AACA,SAAO;AACT;AAOO,IAAM,aAAa,CAAC,UAA0B;AACnD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,OAAO,MAAM;AACnB,QAAM,MAAM,OAAO,KAAK,KAAK;AAC7B,QAAM,MAAM,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,KAAK;AAC9C,MAAI,CAAC,IAAI,KAAK,KAAK,GAAG;AACpB,UAAM,IAAI,YAAY,2BAA2B,KAAK,EAAE;AAAA,EAC1D;AACA,QAAM,CAAC,EAAE,OAAO,IAAI,IAAI,MAAM,MAAM,GAAG;AACvC,MAAI;AACJ,UAAQ,MAAM;AAAA,IACZ,KAAK;AACH,YAAM,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF,KAAK;AACH,YAAM,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF,KAAK;AACH,YAAM,WAAW,KAAK,IAAI;AAC1B;AAAA,IACF;AACE,YAAM,WAAW,KAAK;AAAA,EAC1B;AACA,SAAO;AACP,MAAI,MAAM,GAAG;AACX,WAAO;AAAA,EACT,WAAW,OAAO,GAAG,KAAK,EAAE,GAAG;AAC7B,UAAM;AAAA,EACR;AACA,SAAO;AACT;AAOO,IAAM,aAAa,CAAC,QAAgB,OAAe;AACxD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,CAAC,OAAO;AACV,cAAQ;AAAA,IACV,WAAW,UAAU,MAAM;AACzB,cAAQ;AAAA,IACV,OAAO;AACL,UAAI;AACJ,UAAI,MAAM,SAAS,GAAG,GAAG;AACvB,YAAI,WAAW,KAAK,IAAI;AAAA,MAC1B,OAAO;AACL,YAAI,WAAW,KAAK;AAAA,MACtB;AACA,UAAI,CAAC,OAAO,SAAS,CAAC,GAAG;AACvB,cAAM,IAAI,UAAU,GAAG,CAAC,0BAA0B;AAAA,MACpD;AACA,UAAI,IAAI,MAAM;AACZ,gBAAQ;AAAA,MACV,WAAW,IAAI,GAAG;AAChB,gBAAQ;AAAA,MACV,OAAO;AACL,gBAAQ,EAAE,QAAQ,IAAI;AAAA,MACxB;AAAA,IACF;AAAA,EACF,OAAO;AACL,YAAQ;AAAA,EACV;AACA,SAAO,WAAW,KAAK;AACzB;AAOO,IAAM,gBAAgB,CAAC,UAA0B;AACtD,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,UAAU,IAAI;AAChB,YAAM,IAAI,YAAY,wCAAwC;AAAA,IAChE;AACA,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,MAAI,QAAQ,SAAS,OAAO,GAAG;AAC/B,MAAI,SAAS,GAAG;AACd,WAAO;AAAA,EACT;AACA,MAAI,SAAS,SAAS;AACpB,WAAO;AAAA,EACT;AACA,QAAM,WAAW,oBAAI,IAAI;AACzB,WAAS,IAAI,GAAG,IAAI,SAAS,KAAK;AAChC,aAAS,IAAI,KAAK,MAAO,IAAI,UAAW,OAAO,GAAG,CAAC;AAAA,EACrD;AACA,MAAI,SAAS,IAAI,KAAK,GAAG;AACvB,YAAQ,SAAS,IAAI,KAAK,IAAI;AAAA,EAChC,OAAO;AACL,YAAQ,KAAK,MAAM,QAAQ,UAAU,IAAI,IAAI;AAAA,EAC/C;AACA,SAAO,WAAW,MAAM,QAAQ,IAAI,CAAC;AACvC;AAQO,IAAM,0BAA0B,CACrC,KACA,OAAgB,UACK;AACrB,MAAI,IAAI,IAAI;AACZ,MAAI,MAAM;AACR,KAAC,IAAI,IAAI,EAAE,IAAI;AAAA,EACjB,OAAO;AACL,KAAC,IAAI,IAAI,EAAE,IAAI,wBAAwB,KAAK;AAAA,MAC1C,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,MAAI,IAAI,KAAK;AACb,QAAM,WAAW;AACjB,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EACpE,OAAO;AACL,SAAK;AAAA,EACP;AACA,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EACpE,OAAO;AACL,SAAK;AAAA,EACP;AACA,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,KAAK,IAAI,kBAAkB,IAAI,gBAAgB,UAAU;AAAA,EACpE,OAAO;AACL,SAAK;AAAA,EACP;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,IAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,UAAU;AAAA,IACZ,CAAC;AAAA,EACH;AACA,QAAM,wBAAwB,KAAK,IAAI;AACvC,QAAM,MAAM,gBAAgB,qBAAqB,KAAK,IAAI;AAC1D,SAAO;AACT;AAqBO,IAAM,0BAA0B,CACrC,KACA,QAAiB,UACI;AACrB,MAAI,CAAC,GAAG,GAAG,CAAC,IAAI,wBAAwB,KAAK;AAAA,IAC3C,WAAW;AAAA,EACb,CAAC;AACD,QAAM,WAAW,MAAM;AACvB,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAC1D,OAAO;AACL,SAAK;AAAA,EACP;AACA,OAAK;AACL,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAC1D,OAAO;AACL,SAAK;AAAA,EACP;AACA,OAAK;AACL,MAAI,IAAI,UAAU;AAChB,QAAI,KAAK,IAAI,GAAG,IAAI,UAAU,KAAK,IAAI,iBAAiB;AAAA,EAC1D,OAAO;AACL,SAAK;AAAA,EACP;AACA,OAAK;AACL,SAAO;AAAA,IACL,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACxB,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,IACxB,QAAQ,KAAK,MAAM,CAAC,IAAI;AAAA,EAC1B;AACF;AAQO,IAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACA,MAAI,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,KAAK,IAAI;AAC9D,GAAC,GAAG,GAAG,CAAC,IAAI;AAAA,IACV;AAAA,MACE,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,MAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,MAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC5B;AAAA,IACA;AAAA,EACF;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAwBO,IAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,IAAI,IAAI,EAAE,IAAI,kBAAkB,KAAK,IAAI;AAChD,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,IAAI,KAAK;AACf,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,MAAM,KAAK,IAAI,GAAG,GAAG,CAAC;AAC5B,QAAM,IAAI,MAAM;AAChB,QAAM,KAAK,MAAM,OAAO,OAAO;AAC/B,MAAI,GAAG;AACP,MAAI,KAAK,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,MAAM,SAAS;AACpD,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAK,KAAK,IAAI,KAAK,IAAI,MAAM,MAAM,CAAC,KAAM;AAC1C,QAAI,MAAM,GAAG;AACX,UAAI;AAAA,IACN,OAAO;AACL,cAAQ,KAAK;AAAA,QACX,KAAK;AACH,eAAK,IAAI,KAAK;AACd;AAAA,QACF,KAAK;AACH,eAAK,IAAI,KAAK,IAAI;AAClB;AAAA,QACF,KAAK;AAAA,QACL;AACE,eAAK,IAAI,KAAK,IAAI;AAClB;AAAA,MACJ;AACA,UAAK,IAAI,OAAQ;AACjB,UAAI,IAAI,GAAG;AACT,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,IAAM,oBAAoB,CAC/B,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,KAAK,IAAI;AAC7C,QAAM,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC,IAAI;AAC/B,QAAM,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,CAAC,IAAI;AACnC,MAAI;AACJ,MAAI,KAAK,OAAO,GAAG;AACjB,QAAI;AAAA,EACN,OAAO;AACL,KAAC,CAAC,IAAI,kBAAkB,GAAG;AAAA,EAC7B;AACA,SAAO,CAAC,GAAG,KAAK,SAAS,KAAK,OAAO;AACvC;AAQO,IAAM,sBAAsB,CACjC,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,MAAM,gBAAgB,mBAAmB,KAAK,IAAI;AACxD,QAAM,SAAS,IAAI,IAAI,OAAK,KAAK,KAAK,CAAC,CAAC;AACxC,MAAI,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,QAAQ,IAAI;AACjE,MAAI,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAC9B,QAAM,OAAO,KAAK,MAAM,WAAW,EAAE,QAAQ,IAAI,CAAC,IAAI,OAAO;AAC7D,MAAI,SAAS,KAAK,SAAS,SAAS;AAClC,QAAI;AACJ,QAAI;AAAA,EACN;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,IAAM,sBAAsB,CACjC,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,oBAAoB,KAAK,IAAI;AAC/C,MAAI,GAAG;AACP,QAAM,OAAO,KAAK,MAAM,WAAW,EAAE,QAAQ,IAAI,CAAC,IAAI,OAAO;AAC7D,MAAI,SAAS,KAAK,SAAS,SAAS;AAClC,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;AACtE,QAAI,WAAW,EAAE,QAAQ,IAAI,CAAC,MAAM,GAAG;AACrC,UAAI;AAAA,IACN,OAAO;AACL,UAAK,KAAK,MAAM,GAAG,CAAC,IAAI,WAAY,KAAK;AACzC,UAAI,IAAI,GAAG;AACT,aAAK;AAAA,MACP;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,IAAM,uBAAuB,CAClC,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,SAAS,gBAAgB,mBAAmB,KAAK,IAAI;AAC3D,QAAM,MAAM,kBAAkB,QAAQ,IAAI;AAC1C,SAAO;AACT;AAQO,IAAM,uBAAuB,CAClC,KACA,OAAgB,UACK;AACrB,MAAI,CAAC,MAAM;AACT,UAAM,wBAAwB,KAAK;AAAA,MACjC,WAAW;AAAA,MACX,eAAe;AAAA,IACjB,CAAC;AAAA,EACH;AACA,QAAM,SAAS,IAAI,IAAI,CAAC,KAAK,MAAM,MAAO,IAAI,CAAC,CAAY;AAC3D,QAAM,CAAC,IAAI,IAAI,EAAE,IAAI,OAAO;AAAA,IAAI,SAC9B,MAAM,cAAc,KAAK,KAAK,GAAG,KAAK,MAAM,YAAY,OAAO;AAAA,EACjE;AACA,QAAM,IAAI,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,CAAC,GAAG,OAAO;AACzD,MAAI,GAAG;AACP,MAAI,MAAM,KAAK,MAAM,SAAS;AAC5B,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,SAAK,KAAK,MAAM;AAChB,SAAK,KAAK,MAAM;AAAA,EAClB;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAQO,IAAM,uBAAuB,CAClC,KACA,OAAgB,UACK;AACrB,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,qBAAqB,KAAK,IAAI;AAChD,MAAI,GAAG;AACP,MAAI,MAAM,KAAK,MAAM,SAAS;AAC5B,QAAI;AACJ,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,KAAK,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;AACtE,QAAK,KAAK,MAAM,GAAG,CAAC,IAAI,WAAY,KAAK;AACzC,QAAI,IAAI,GAAG;AACT,WAAK;AAAA,IACP;AAAA,EACF;AACA,SAAO,CAAC,GAAG,GAAG,CAAC;AACjB;AAOO,IAAM,kBAAkB,CAAC,QAA+B;AAC7D,QAAM,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAAA,IACpD,OAAO;AAAA,IACP,UAAU;AAAA,EACZ,CAAC;AACD,QAAM,KAAK,kBAAkB,CAAC;AAC9B,QAAM,KAAK,kBAAkB,CAAC;AAC9B,QAAM,KAAK,kBAAkB,CAAC;AAC9B,QAAM,KAAK,kBAAkB,QAAQ,OAAO;AAC5C,MAAI;AACJ,MAAI,OAAO,MAAM;AACf,UAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE;AAAA,EACxB,OAAO;AACL,UAAM,IAAI,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE;AAAA,EAC7B;AACA,SAAO;AACT;AAmFO,IAAM,kBAAkB,CAAC,UAAiC;AAC/D,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,MACE,EACE,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,KAC1B,gBAAgB,KAAK,KAAK,IAE5B;AACA,UAAM,IAAI,YAAY,2BAA2B,KAAK,EAAE;AAAA,EAC1D;AACA,QAAM,MAAgB,CAAC;AACvB,MAAI,gBAAgB,KAAK,KAAK,GAAG;AAC/B,UAAM,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM;AAAA,MACxB;AAAA,IACF;AACA,QAAI;AAAA,MACF,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB;AAAA,IACF;AAAA,EACF,WAAW,gBAAgB,KAAK,KAAK,GAAG;AACtC,UAAM,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM;AAAA,MAC/B;AAAA,IACF;AACA,QAAI;AAAA,MACF,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,SAAS,GAAG,CAAC,GAAG,CAAC,IAAI,GAAG;AAAA,MACxB,cAAc,GAAG,KAAK,GAAG,KAAK,EAAE;AAAA,IAClC;AAAA,EACF,WAAW,gBAAgB,KAAK,KAAK,GAAG;AACtC,UAAM,CAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,MAAM;AAAA,MAC/B;AAAA,IACF;AACA,QAAI;AAAA,MACF,SAAS,GAAG,GAAG;AAAA,MACf,SAAS,GAAG,GAAG;AAAA,MACf,SAAS,GAAG,GAAG;AAAA,MACf,cAAc,KAAK;AAAA,IACrB;AAAA,EACF,OAAO;AACL,UAAM,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,MAAM;AAAA,MACxB;AAAA,IACF;AACA,QAAI,KAAK,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,SAAS,GAAG,GAAG,GAAG,CAAC;AAAA,EAClE;AACA,SAAO;AACT;AAOO,IAAM,wBAAwB,CAAC,UAAiC;AACrE,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,IAAI,gBAAgB,KAAK;AACjD,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,wBAAwB,CAAC,IAAI,IAAI,EAAE,GAAG,IAAI;AAC5D,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAOO,IAAM,kBAAkB,CAAC,UAAiC;AAC/D,QAAM,CAAC,GAAG,GAAG,GAAG,KAAK,IAAI,sBAAsB,KAAK;AACpD,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACtE,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,IAAM,WAAW,CACtB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,QAAM,MAAM,IAAI,OAAO,iBAAiB,OAAO,IAAI,WAAW,WAAW;AACzE,MAAI,CAAC,IAAI,KAAK,KAAK,GAAG;AACpB,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,GAAG;AAC/B,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,SAAS,GAAG,EACpB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,GAAG;AACpB,UAAK,WAAW,EAAE,IAAI,UAAW;AAAA,IACnC,OAAO;AACL,UAAI,WAAW,EAAE;AAAA,IACnB;AACA,QAAI,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAC7D;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,GAAG;AACpB,UAAK,WAAW,EAAE,IAAI,UAAW;AAAA,IACnC,OAAO;AACL,UAAI,WAAW,EAAE;AAAA,IACnB;AACA,QAAI,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAC7D;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,GAAG;AACpB,UAAK,WAAW,EAAE,IAAI,UAAW;AAAA,IACnC,OAAO;AACL,UAAI,WAAW,EAAE;AAAA,IACnB;AACA,QAAI,KAAK,IAAI,KAAK,IAAI,iBAAiB,GAAG,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,EAC7D;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,SAAO,CAAC,OAAO,GAAG,GAAG,GAAG,WAAW,WAAW,OAAO,OAAO,OAAO,KAAK;AAC1E;AAQO,IAAM,WAAW,CACtB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,SAAS,GAAG,EACpB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,WAAW,EAAE;AAAA,EACnB;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO;AAAA,EACnD;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO;AAAA,EACnD;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,WAAW,OAAO;AACpB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,MAAK,IAAI,MAAO;AAChB,OAAK;AACL,QAAM,KAAM,IAAI,UAAW,KAAK,IAAI,GAAG,IAAI,CAAC;AAC5C,QAAM,KAAK,IAAI;AACf,QAAM,MAAM,IAAI,KAAK;AACrB,QAAM,MAAM,IAAI,KAAK;AACrB,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AAC5E,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AAC5E,QAAM,IAAI,IAAI,KAAK,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,MAAM,QAAQ,UAAU,IAAI,CAAC,CAAC;AAC5E,SAAO;AAAA,IACL;AAAA,IACA,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK,IAAI,KAAK,IAAI,iBAAiB,IAAI,SAAS,GAAG,GAAG,CAAC,GAAG,OAAO;AAAA,IACjE;AAAA,EACF;AACF;AAQO,IAAM,WAAW,CACtB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,IAAI;AACX,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,WAAW,EAAE;AAAA,EACnB;AACA,MAAI,OAAO,MAAM;AACf,SAAK;AAAA,EACP,OAAO;AACL,SAAK,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO,IAAI;AAAA,EACxD;AACA,MAAI,OAAO,MAAM;AACf,SAAK;AAAA,EACP,OAAO;AACL,SAAK,KAAK,IAAI,KAAK,IAAI,WAAW,EAAE,GAAG,CAAC,GAAG,OAAO,IAAI;AAAA,EACxD;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,WAAW,OAAO;AACpB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK;AAAA,MACnB,OAAO,OAAO,KAAK,KAAK;AAAA,MACxB,OAAO,OAAO,KAAK,KAAK;AAAA,MACxB,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,MAAI,KAAK,MAAM,GAAG;AAChB,UAAM,IAAI,iBAAkB,MAAM,KAAK,MAAO,SAAS,GAAG;AAC1D,WAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAAA,EAC/B;AACA,QAAM,UAAU,IAAI,KAAK,MAAM;AAC/B,MAAI,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,UAAU;AAC7C,MAAI,kBAAkB,IAAI,SAAS,MAAM,SAAS,GAAG;AACrD,MAAI,kBAAkB,IAAI,SAAS,MAAM,SAAS,GAAG;AACrD,MAAI,kBAAkB,IAAI,SAAS,MAAM,SAAS,GAAG;AACrD,SAAO;AAAA,IACL;AAAA,IACA,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,OAAO;AAAA,IAChC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,OAAO;AAAA,IAChC,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,OAAO;AAAA,IAChC;AAAA,EACF;AACF;AASO,IAAM,WAAW,CACtB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,QAAM,WAAW;AACjB,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,GAAG;AACpB,UAAI,WAAW,EAAE;AACjB,UAAI,IAAI,SAAS;AACf,YAAI;AAAA,MACN;AAAA,IACF,OAAO;AACL,UAAI,WAAW,EAAE;AAAA,IACnB;AACA,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,WAAW,WAAW,EAAE;AAAA,EAClE;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,WAAW,WAAW,EAAE;AAAA,EAClE;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAM,GAAG;AACzB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,QAAM,MAAM,IAAI,OAAO;AACvB,QAAM,KAAK,IAAI,QAAQ;AACvB,QAAM,KAAK,KAAK,IAAI;AACpB,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,QAAQ,KAAK,IAAI,IAAI,QAAQ;AACnC,QAAM,MAAM;AAAA,IACV,QAAQ,cAAc,SAAS,KAAK,QAAQ,OAAO;AAAA,IACnD,IAAI,WAAW,QAAQ,IAAI;AAAA,IAC3B,QAAQ,cAAc,SAAS,KAAK,QAAQ,OAAO;AAAA,EACrD;AACA,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,IAAI;AAAA,IACpB,CAACC,MAAK,MAAMA,OAAO,IAAI,CAAC;AAAA,EAC1B;AACA,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,IAAM,WAAW,CACtB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,OAAO;AACnC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,WAAW,EAAE;AACjB,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,WAAW,WAAW,EAAE;AAAA,EAClE;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,WAAW,EAAE;AAAA,EACnB;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAM,GAAG;AACzB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AAClD,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,IAAM,aAAa,CACxB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,SAAS;AACrC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAC/D,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,WAAW,GAAG,SAAS,GAAG,GAAG;AAC3B,QAAK,WAAW,EAAE,IAAI,WAAY;AAAA,EACpC,OAAO;AACL,QAAI,WAAW,EAAE;AAAA,EACnB;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,WAAW,GAAG,SAAS,GAAG,GAAG;AAC3B,QAAK,WAAW,EAAE,IAAI,WAAY;AAAA,EACpC,OAAO;AACL,QAAI,WAAW,EAAE;AAAA,EACnB;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAM,GAAG;AACzB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,QAAM,MAAM,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1D,QAAM,SAAS,IAAI,IAAI,OAAK,KAAK,IAAI,GAAG,QAAQ,CAAC;AACjD,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,QAAQ,IAAI;AACjE,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,IAAM,aAAa,CACxB,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,WAAW;AACjB,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,SAAS;AACrC,QAAM,CAAC,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC3B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAC/D,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,GAAG;AACpB,UAAK,WAAW,EAAE,IAAI,WAAY;AAAA,IACpC,OAAO;AACL,UAAI,WAAW,EAAE;AAAA,IACnB;AACA,QAAI,IAAI,GAAG;AACT,UAAI;AAAA,IACN;AAAA,EACF;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,WAAW,EAAE;AAAA,EACnB;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAM,GAAG;AACzB,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,IAAI,IAAI,KAAK,IAAK,IAAI,KAAK,KAAM,QAAQ;AAC/C,QAAM,MAAM,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1D,QAAM,SAAS,IAAI,IAAI,QAAM,KAAK,IAAI,IAAI,QAAQ,CAAC;AACnD,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,QAAQ,IAAI;AACjE,SAAO;AAAA,IACL;AAAA,IACA,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AAUO,IAAM,iBAAiB,CAC5B,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,aAAa,IAAI,MAAM,OAAO,SAAS,IAAI,WAAW,MAAM,IAAI;AACxE,MAAI,CAAC,aAAa,KAAK,KAAK,GAAG;AAC7B,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,YAAY;AACxC,MAAI,CAAC,IAAI,IAAI,IAAI,IAAI,KAAK,EAAE,IAAI,IAC7B,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,OAAO;AAChB,SAAK;AAAA,EACP;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EACjE;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EACjE;AACA,MAAI,OAAO,MAAM;AACf,QAAI;AAAA,EACN,OAAO;AACL,QAAI,GAAG,SAAS,GAAG,IAAI,WAAW,EAAE,IAAI,UAAU,WAAW,EAAE;AAAA,EACjE;AACA,QAAM,QAAQ,WAAW,EAAE;AAC3B,MAAI,SAAS,KAAK,MAAM,KAAM,WAAW,WAAW,OAAO,YAAa;AACtE,WAAO;AAAA,MACL;AAAA,MACA,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK,iBAAiB,GAAG,GAAG;AAAA,MAC1C,OAAO,OAAO,KAAK;AAAA,IACrB;AAAA,EACF;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AAER,MAAI,OAAO,eAAe;AACxB,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAC1D,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,OAAO,cAAc;AAC9B,UAAM,YAAY,wBAAwB;AAAA,MACxC,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,IACN,CAAC;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,kBAAkB,SAAS;AACvD,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,OAAO,WAAW;AAC3B,UAAM,QAAQ;AACd,UAAM,OAAO;AACb,UAAM,WAAW;AACjB,UAAM,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,OAAK;AAC7B,UAAI;AACJ,UAAI,IAAI,OAAO,WAAW,KAAK;AAC7B,aAAK,KAAK,WAAW;AAAA,MACvB,OAAO;AACL,aAAK,KAAK,KAAK,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ;AAAA,MACrD;AACA,aAAO;AAAA,IACT,CAAC;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,uBAAuB,GAAG;AACtD,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,OAAO,WAAW;AAC3B,UAAM,UAAU,MAAM;AACtB,UAAM,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,OAAK;AAC7B,YAAM,KAAK,KAAK,IAAI,GAAG,OAAO;AAC9B,aAAO;AAAA,IACT,CAAC;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,GAAG;AAClD,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,OAAO,gBAAgB;AAChC,UAAM,eAAe;AACrB,UAAM,MAAM,CAAC,GAAG,GAAG,CAAC,EAAE,IAAI,OAAK;AAC7B,UAAI;AACJ,UAAI,IAAI,KAAK,MAAM,MAAM;AACvB,aAAK,KAAK,IAAI,GAAG,YAAY;AAAA,MAC/B,OAAO;AACL,aAAK,IAAI;AAAA,MACX;AACA,aAAO;AAAA,IACT,CAAC;AACD,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,4BAA4B,GAAG;AAC3D,QAAI,CAAC,KAAK;AACR,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,wBAAwB,KAAK,EAAE,GAAG;AAC3C,KAAC,GAAG,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC;AACpB,QAAI,OAAO,WAAW;AACpB,UAAI,CAAC,KAAK;AACR,SAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,MAC1D;AAAA,IACF,WAAW,KAAK;AACd,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,OAAO;AACL,KAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,IAAI,SAAS,IAAI,SAAS,IAAI,OAAO,CAAC;AACrE,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,WAAW,WAAW,OAAO,OAAO,KAAK;AAAA,EAC3C;AACF;AAUO,IAAM,kBAAkB,CAC7B,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,MAAM,OAAO,SAAS,IAAI,WAAW,MAAM,IAAI;AACvD,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,UAAM,MAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,QAAI,SAAS,GAAG,GAAG;AACjB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AAEZ,MAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,QAAI,WAAW,UAAU;AACvB,aAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAC3B;AACA,QAAI,WAAW,UAAU;AACvB,aAAO;AAAA,IACT;AAAA,EAEF,WAAW,WAAW,KAAK,KAAK,GAAG;AACjC,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GAAG;AAC7D,UAAI,WAAW,UAAU;AACvB,eAAO;AAAA,MACT;AACA,YAAM,CAAC,GAAG,GAAG,CAAC,IAAI,aAChB,KACF;AACA,cAAQ;AACR,UAAI,WAAW,UAAU;AACvB,eAAO,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAAA,MAC/B;AACA,OAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,UAAI,KAAK;AACP,SAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,MAChE;AAAA,IACF,OAAO;AACL,cAAQ,QAAQ;AAAA,QACd,KAAK,UAAU;AACb,cAAI,YAAY,UAAU,eAAe;AACvC,mBAAO,IAAI,WAAW;AAAA,UACxB;AACA,iBAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,QAC3B;AAAA,QACA,KAAK,UAAU;AACb,cAAI,UAAU,eAAe;AAC3B,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,UAAU,eAAe;AAC3B,mBAAO,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,UAC3B;AACA,iBAAO,IAAI,WAAW;AAAA,QACxB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EAEF,WAAW,MAAM,CAAC,MAAM,KAAK;AAC3B,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,YAAM,MAAM,gBAAgB,KAAK;AACjC,aAAO,CAAC,OAAO,GAAG,GAAG;AAAA,IACvB;AACA,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AACxC,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,MAAM,WAAW,KAAK,GAAG;AAClC,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,SAAS,OAAO,GAAG;AAAA,IAC5B;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AACnC,QAAI,CAAC,KAAK;AACR,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,MAAM,WAAW,KAAK,GAAG;AAClC,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,SAAS,OAAO,GAAG;AAAA,IAC5B;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AACnC,QAAI,CAAC,KAAK;AACR,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,MAAM,WAAW,OAAO,GAAG;AACpC,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,WAAW,OAAO,GAAG;AAAA,IAC9B;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,KAAK;AACrC,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EAEF,WAAW,MAAM,WAAW,OAAO,GAAG;AACpC,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,WAAW,OAAO,GAAG;AAAA,IAC9B;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,KAAK;AACrC,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EACF,OAAO;AACL,QAAI,GAAG,GAAG;AAEV,QAAI,MAAM,WAAW,KAAK,GAAG;AAC3B,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,IAErC,WAAW,MAAM,WAAW,KAAK,GAAG;AAClC,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,KAAK;AAAA,IAErC,OAAO;AACL,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAC1C;AACA,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,aAAO,CAAC,OAAO,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IACnE;AACA,KAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC;AACvC,QAAI,KAAK;AACP,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,mBAAmB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAChE;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM,YAAY;AAAA,IAClB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB,iBAAiB,GAAG,GAAG;AAAA,IACvB;AAAA,EACF;AACF;AASO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,aAAa,IAAI,SAAS,IAAI,WAAW,MAAM,IAAI;AAC3D,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,aAAa;AAChC,QAAI,SAAS,UAAU,GAAG;AACxB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,CAAC,UAAU,KAAK,KAAK,GAAG;AAC1B,UAAMC,OAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAIA,gBAAe,YAAY;AAC7B,eAAS,UAAU,IAAI;AACvB,aAAOA;AAAA,IACT;AACA,aAAS,UAAUA,IAAG;AACtB,QAAI,SAASA,IAAG,GAAG;AACjB,aAAOA;AAAA,IACT;AACA,WAAOA;AAAA,EACT;AACA,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AAEZ,MAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACxB,aAAO;AAAA,IACT;AAAA,EAEF,WAAW,WAAW,KAAK,KAAK,GAAG;AACjC,QAAI,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GAAG;AAC7D,UAAI,WAAW,UAAU;AACvB,iBAAS,UAAU,KAAK;AACxB,eAAO;AAAA,MACT;AACA,OAAC,GAAG,GAAG,CAAC,IAAI,aACV,KACF;AACA,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ,QAAQ;AAAA,QACd,KAAK,UAAU;AACb,cAAI,UAAU,eAAe;AAC3B,qBAAS,UAAU,KAAK;AACxB,mBAAO;AAAA,UACT;AACA,gBAAMA,OAAM;AACZ,mBAAS,UAAUA,IAAG;AACtB,iBAAOA;AAAA,QACT;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,UAAU,eAAe;AAC3B,kBAAMA,OAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AACtD,qBAAS,UAAUA,IAAG;AACtB,mBAAOA;AAAA,UACT;AACA,mBAAS,UAAU,IAAI;AACvB,iBAAO,IAAI,WAAW;AAAA,QACxB;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,cAAI,YAAY,UAAU,eAAe;AACvC,qBAAS,UAAU,IAAI;AACvB,mBAAO,IAAI,WAAW;AAAA,UACxB;AACA,gBAAMA,OAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,CAAC;AACtD,mBAAS,UAAUA,IAAG;AACtB,iBAAOA;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EAEF,WAAW,MAAM,CAAC,MAAM,KAAK;AAC3B,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAE1C,WAAW,MAAM,WAAW,KAAK,GAAG;AAClC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAE1C,WAAW,MAAM,WAAW,KAAK,GAAG;AAClC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAE1C,WAAW,cAAc,KAAK,KAAK,GAAG;AACpC,QAAI,GAAG,GAAG;AACV,QAAI,MAAM,WAAW,KAAK,GAAG;AAC3B,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAC5C,OAAO;AACL,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,IAC5C;AACA,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,YAAMA,OAA8B,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AACvD,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,KAAC,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAE5C,WAAW,gBAAgB,KAAK,KAAK,GAAG;AACtC,QAAI,GAAG,GAAG;AACV,QAAI,MAAM,WAAW,OAAO,GAAG;AAC7B,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO,GAAG;AAAA,IAC9C,OAAO;AACL,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO,GAAG;AAAA,IAC9C;AACA,QAAI,SAAS,KAAK,MAAM,GAAG;AACzB,YAAMA,OAA8B,CAAC,IAAI,GAAG,GAAG,GAAG,KAAK;AACvD,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,KAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAEzC,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO,GAAG;AAAA,EAC1C;AACA,MAAI,WAAW,WAAW,eAAe,QAAQ;AAC/C,UAAMA,OAA8B;AAAA,MAClC;AAAA,MACA,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ,IAAI;AAAA,MACJ;AAAA,IACF;AACA,aAAS,UAAUA,IAAG;AACtB,WAAOA;AAAA,EACT;AACA,QAAM,MAA8B;AAAA,IAClC;AAAA,IACA,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ;AAAA,EACF;AACA,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,mBAAmB,CAC9B,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,aAAa,IAAI,SAAS,IAAI,WAAW,MAAM,IAAI;AAC3D,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,aAAa;AAChC,QAAI,SAAS,UAAU,GAAG;AACxB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,MAAI,CAAC,aAAa,KAAK,KAAK,GAAG;AAC7B,UAAMA,OAAM,yBAAyB,QAAQ,QAAQ;AACrD,QAAIA,gBAAe,YAAY;AAC7B,eAAS,UAAU,IAAI;AACvB,aAAOA;AAAA,IACT;AACA,aAAS,UAAUA,IAAG;AACtB,QAAI,SAASA,IAAG,GAAG;AACjB,aAAOA;AAAA,IACT;AACA,WAAOA;AAAA,EACT;AACA,QAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,IAC3B;AAAA,IACA;AAAA,EACF;AACA,MAAI,SAAS,KAAK,MAAM,KAAM,WAAW,WAAW,OAAO,YAAa;AACtE,UAAMA,OAA8B,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE;AACvD,aAAS,UAAUA,IAAG;AACtB,WAAOA;AAAA,EACT;AACA,QAAM,IAAI,WAAW,GAAG,EAAE,EAAE;AAC5B,QAAM,IAAI,WAAW,GAAG,EAAE,EAAE;AAC5B,QAAM,IAAI,WAAW,GAAG,EAAE,EAAE;AAC5B,QAAM,QAAQ,WAAW,GAAG,EAAE,EAAE;AAChC,QAAM,CAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AACnD,QAAM,MAA8B,CAAC,OAAO,GAAG,GAAG,GAAG,KAAK;AAC1D,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,0BAA0B,CACrC,OACA,MAGI,CAAC,MAC0B;AAC/B,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,aAAa,IAAI,SAAS,GAAG,IAAI;AACzC,MAAI,KAAK;AACT,MAAI,GAAG,GAAG,GAAG,OAAO,GAAG,GAAG;AAC1B,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AACvB,QAAI,OAAO,YAAY;AACrB,aAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,IACxB;AACA,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,EAClE,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,YAAY;AACxC,UAAM,CAACC,GAAE,IAAI,IACV,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,QAAIA,QAAO,eAAe;AACxB,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,OAAO;AACL,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AACzC,OAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAClE;AAAA,EACF,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAC1C,KAAC,GAAG,GAAG,CAAC,IAAI,gBAAgB,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,EAClE;AACA,SAAO;AAAA,IACL,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B,KAAK,IAAI,KAAK,IAAI,GAAG,CAAC,GAAG,CAAC;AAAA,IAC1B;AAAA,EACF;AACF;AASO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MACe;AAC/B,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,iBAAiB,OAAO,GAAG;AAAA,IACnC,OAAO;AACL,YAAM,kBAAkB,OAAO,GAAG;AAAA,IACpC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,YAAY;AACxC,UAAM,CAAC,EAAE,IAAI,IACV,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,QAAI,OAAO,QAAQ;AACjB,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MACV,CAAC;AACD,WAAK;AACL,WAAK;AACL,WAAK;AAAA,IACP,OAAO;AACL,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,KAAK;AAAA,IAC7C;AAAA,EACF,WAAW,qBAAqB,KAAK,KAAK,GAAG;AAC3C,KAAC,GAAG,GAAG,GAAG,KAAK,IAAI,wBAAwB,KAAK;AAChD,KAAC,GAAG,GAAG,CAAC,IAAI,wBAAwB,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAC/C,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,kBAAkB,OAAO;AAAA,MAC5C,QAAQ;AAAA,IACV,CAAC;AAAA,EACH;AACA,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MACe;AAC/B,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,MAAM,OAAO,SAAS,GAAG,IAAI;AACrC,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,CAAC,EAAE,GAAG,IAAI,MAAM,MAAM,YAAY;AACxC,UAAM,CAAC,EAAE,IAAI,IACV,QAAQ,KAAK,GAAG,EAChB,MAAM,KAAK;AACd,QAAI,KAAK;AACP,UAAI,OAAO,WAAW;AACpB,SAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,UAC3C,QAAQ;AAAA,QACV,CAAC;AAAA,MACH,OAAO;AACL,SAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,UACnB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,iBAAiB,KAAK,EAAE,GAAG;AACpC,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,iBAAiB,OAAO;AAAA,QAC3C,QAAQ;AAAA,MACV,CAAC;AAAA,IACH,OAAO;AACL,OAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,IAC3C;AAAA,EACF,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO,GAAG;AAAA,EACjD;AACA,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MAC2D;AAC3E,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,WAAW,OAAO;AACpB,aAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IAC5D;AACA,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EACxB;AACA,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAC3C,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAC5C;AACA,GAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,EAC5D;AACA,SAAO,CAAC,WAAW,WAAW,MAAM,IAAI,OAAO,GAAG,GAAG,GAAG,KAAK;AAC/D;AAQO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MAC2D;AAC3E,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IACV,CAAC;AACD,QAAI,WAAW,OAAO;AACpB,aAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,IAC5D;AACA,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EACxB;AACA,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAC3C,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAC5C;AACA,GAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC7C,MAAI,WAAW,OAAO;AACpB,WAAO,CAAC,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,CAAC,GAAG,KAAK;AAAA,EAC5D;AACA,SAAO,CAAC,WAAW,WAAW,IAAI,KAAK,MAAM,OAAO,GAAG,GAAG,GAAG,KAAK;AACpE;AAQO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MACe;AAC/B,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EACxB;AACA,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM;AACV,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO;AAAA,MACzC,KAAK;AAAA,IACP,CAAC;AAAA,EACH,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO;AAAA,MAC1C,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AACA,GAAC,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAChD,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,IAAM,oBAAoB,CAC/B,OACA,MAAe,CAAC,MAC2D;AAC3E,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,QAAQ,KAAK,KAAK,GAAG;AACvB,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,SAAS,OAAO;AAAA,MACnC,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EACxB;AACA,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM;AACV,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,OAAO;AAAA,MACzC,KAAK;AAAA,IACP,CAAC;AAAA,EACH,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,OAAO;AAAA,MAC1C,KAAK;AAAA,IACP,CAAC;AAAA,EACH;AACA,GAAC,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAChD,SAAO,CAAC,GAAG,GAAG,WAAW,WAAW,MAAM,IAAI,OAAO,GAAG,KAAK;AAC/D;AAQO,IAAM,sBAAsB,CACjC,OACA,MAAe,CAAC,MACe;AAC/B,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,UAAU,KAAK,KAAK,GAAG;AACzB,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO;AAAA,MACrC,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EACxB;AACA,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAC3C,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAC5C;AACA,GAAC,GAAG,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC/C,SAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AACxB;AAQO,IAAM,sBAAsB,CACjC,OACA,MAAe,CAAC,MAC2D;AAC3E,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,GAAG,GAAG,GAAG;AACb,MAAI,UAAU,KAAK,KAAK,GAAG;AACzB,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,WAAW,OAAO;AAAA,MACrC,QAAQ;AAAA,IACV,CAAC;AACD,WAAO,CAAC,GAAG,GAAG,GAAG,KAAK;AAAA,EACxB;AACA,MAAI,GAAG,GAAG;AACV,MAAI,WAAW,SAAS;AACtB,QAAI;AACJ,QAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,YAAM,eAAe,OAAO,GAAG;AAAA,IACjC,OAAO;AACL,YAAM,gBAAgB,OAAO,GAAG;AAAA,IAClC;AACA,QAAI,eAAe,YAAY;AAC7B,aAAO;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACvB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,eAAe,KAAK;AAAA,EAC3C,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,GAAG,KAAK,IAAI,gBAAgB,KAAK;AAAA,EAC5C;AACA,GAAC,GAAG,GAAG,CAAC,IAAI,oBAAoB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAC/C,SAAO,CAAC,GAAG,GAAG,WAAW,WAAW,MAAM,IAAI,OAAO,GAAG,KAAK;AAC/D;AAQO,IAAM,kBAAkB,CAC7B,OACA,MAAe,CAAC,MACiC;AACjD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,SAAS,IAAI,WAAW,MAAM,IAAI;AAC1C,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,UAAM,aAAa,aAAa;AAChC,QAAI,SAAS,UAAU,GAAG;AACxB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,QAAM,cAAc,CAAC;AACrB,MAAI,CAAC,QAAQ,KAAK,KAAK,GAAG;AACxB,QAAI,MAAM,WAAW,MAAM,KAAK,aAAa,KAAK,KAAK,GAAG;AACxD,YAAM,gBAAgB,IAAI,OAAO,OAAO,MAAM,IAAI,MAAM,IAAI;AAC5D,YAAM,QAAQ,MAAM,MAAM,YAAY;AACtC,iBAAW,QAAQ,OAAO;AACxB,YAAI,MAAM;AACR,cAAI,MAAM,gBAAgB,MAAM;AAAA,YAC9B,QAAQ,WAAW,WAAW,SAAS;AAAA,UACzC,CAAC;AAED,cAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,kBAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAC7B,gBAAI,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,OAAO,GAAG;AAChD,sBAAQ;AACR;AAAA,YACF;AACA,gBAAI,cAAc,KAAK,EAAE,GAAG;AAC1B,kBAAI,OAAO,GAAG;AACZ,sBAAM,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,cACrC,OAAO;AACL,sBAAM,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,cAC7C;AAAA,YACF,WAAW,OAAO,GAAG;AACnB,oBAAM,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,YAC/B,OAAO;AACL,oBAAM,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,YACvC;AAAA,UACF,WAAW,CAAC,QAAQ,KAAK,GAAG,GAAG;AAC7B,oBAAQ;AACR;AAAA,UACF;AACA,sBAAY,KAAK,GAAG;AACpB,kBAAQ,MAAM,QAAQ,MAAM,GAAG;AAAA,QACjC;AAAA,MACF;AACA,UAAI,CAAC,OAAO;AACV,cAAMD,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,eAAOA;AAAA,MACT;AAAA,IACF,OAAO;AACL,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AAAA,EACF;AACA,MAAI,aAAa;AACjB,MAAI,SAAS;AACb,MAAI,SAAS;AACb,MAAI,OAAO;AACX,MAAI,SAAS;AACb,MAAI,OAAO;AACX,MAAI,YAAY,UAAU,WAAW,UAAU;AAC7C,UAAM,gBAAgB,IAAI,OAAO,2BAA2B,MAAM,QAAQ;AAC1E,UAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,aAAa;AACxC,QAAI,WAAW,KAAK,EAAE,GAAG;AACvB,OAAC,EAAE,YAAY,MAAM,IAAI,GAAG,MAAM,UAAU;AAAA,IAC9C,OAAO;AACL,mBAAa;AAAA,IACf;AACA,QAAI,YAAY,WAAW,GAAG;AAC5B,UAAI,CAAC,OAAO,KAAK,IAAI;AACrB,cAAQ,MAAM,QAAQ,aAAa,IAAI;AACvC,cAAQ,MAAM,QAAQ,aAAa,IAAI;AACvC,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,GAAG,KAAK;AACrD,YAAM,OAAO,IAAI,OAAO,IAAI,KAAK,YAAY,GAAG,KAAK;AACrD,OAAC,EAAE,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI;AACnC,OAAC,EAAE,QAAQ,IAAI,IAAI,MAAM,MAAM,IAAI;AAAA,IACrC,OAAO;AACL,UAAI,CAAC,IAAI,IAAI;AACb,aAAO,KAAK,QAAQ,aAAa,IAAI;AACrC,YAAM,WAAW,GAAG,IAAI,UAAU,GAAG;AACrC,YAAM,eAAe,IAAI,IAAI,YAAY,GAAG;AAC5C,YAAM,cAAc,IAAI,OAAO,IAAI,YAAY,GAAG;AAClD,YAAM,cAAc,IAAI,OAAO,GAAG,YAAY,UAAU;AACxD,YAAM,eAAe,IAAI,OAAO,KAAK,cAAc,YAAY,GAAG,MAAM;AAExE,UAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,cAAM,MAAM,IAAI;AAAA,UACd,IAAI,YAAY,cAAc,QAAQ;AAAA,QACxC;AACA,cAAM,CAAC,EAAE,YAAY,UAAU,IAAI,MAAM,MAAM,GAAG;AAClD,SAAC,EAAE,QAAQ,IAAI,IAAI,WAAW,MAAM,YAAY;AAChD,SAAC,EAAE,QAAQ,IAAI,IAAI,WAAW,MAAM,WAAW;AAAA,MACjD,OAAO;AACL,cAAM,MAAM,IAAI;AAAA,UACd,IAAI,QAAQ,cAAc,YAAY;AAAA,QACxC;AACA,cAAM,CAAC,EAAE,YAAY,UAAU,IAAI,MAAM,MAAM,GAAG;AAClD,SAAC,EAAE,QAAQ,IAAI,IAAI,WAAW,MAAM,WAAW;AAC/C,SAAC,EAAE,QAAQ,IAAI,IAAI,WAAW,MAAM,YAAY;AAAA,MAClD;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,CAAC,EAAE,IAAI,YAAY,UAAU,IAAI,MAAM;AAAA,MAC3C;AAAA,IACF;AACA,UAAM,MAAM,IAAI,OAAO,KAAK,cAAc,YAAY,GAAG,MAAM;AAC/D,KAAC,EAAE,QAAQ,IAAI,IAAI,WAAW,MAAM,GAAG;AACvC,KAAC,EAAE,QAAQ,IAAI,IAAI,WAAW,MAAM,GAAG;AACvC,QAAI,WAAW,KAAK,EAAE,GAAG;AACvB,OAAC,EAAE,YAAY,MAAM,IAAI,GAAG,MAAM,UAAU;AAAA,IAC9C,OAAO;AACL,mBAAa;AAAA,IACf;AAAA,EACF;AAEA,MAAI,IAAI,IAAI;AACZ,MAAI,QAAQ,MAAM;AAChB,UAAM,KAAK,WAAW,IAAI,IAAI;AAC9B,UAAM,KAAK,WAAW,IAAI,IAAI;AAC9B,QAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,GAAG;AACxC,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,GAAG;AAChB,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,SAAK,KAAK;AACV,SAAK,KAAK;AACV,QAAI,SAAS,IAAI,SAAS;AAAA,EAC5B,OAAO;AACL,QAAI,MAAM;AACR,WAAK,WAAW,IAAI,IAAI;AACxB,UAAI,KAAK,KAAK,KAAK,GAAG;AACpB,cAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,eAAOA;AAAA,MACT;AACA,WAAK,IAAI;AAAA,IACX,WAAW,MAAM;AACf,WAAK,WAAW,IAAI,IAAI;AACxB,UAAI,KAAK,KAAK,KAAK,GAAG;AACpB,cAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,eAAOA;AAAA,MACT;AACA,WAAK,IAAI;AAAA,IACX,OAAO;AACL,WAAK;AACL,WAAK;AAAA,IACP;AACA,QAAI;AAAA,EACN;AACA,MAAI,eAAe,OAAO;AACxB,iBAAa;AAAA,EACf;AAEA,MAAI,WAAW,UAAU;AACvB,QAAI,SAAS;AACb,QAAI,SAAS;AACb,QAAI,OAAO,WAAW,MAAM,GAAG;AAC7B,eAAS;AAAA,IACX,WAAW,OAAO,WAAW,QAAQ,GAAG;AACtC,YAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,UAAI,OAAO,GAAG;AACZ,iBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MACxC,OAAO;AACL,iBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAChD;AAAA,IACF,OAAO;AACL,YAAM,MAAM,gBAAgB,QAAQ,GAAG;AACvC,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,cAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAC7B,YAAI,OAAO,GAAG;AACZ,cAAI,OAAO,OAAO;AAChB,qBAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,UACpC,OAAO;AACL,qBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAClC;AAAA,QACF,WAAW,OAAO,OAAO;AACvB,mBAAS,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAC5C,OAAO;AACL,mBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAC1C;AAAA,MACF,OAAO;AACL,YAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;AAC1B,mBAAS,UAAU,EAAE;AACrB,iBAAO;AAAA,QACT;AACA,iBAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,OAAO,WAAW,MAAM,GAAG;AAC7B,eAAS;AAAA,IACX,WAAW,OAAO,WAAW,QAAQ,GAAG;AACtC,YAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AACA,UAAI,OAAO,GAAG;AACZ,iBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MACxC,OAAO;AACL,iBAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAChD;AAAA,IACF,OAAO;AACL,YAAM,MAAM,gBAAgB,QAAQ,GAAG;AACvC,UAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,cAAM,CAAC,IAAI,IAAI,IAAI,IAAI,EAAE,IAAI;AAC7B,YAAI,OAAO,GAAG;AACZ,cAAI,OAAO,OAAO;AAChB,qBAAS,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,UACpC,OAAO;AACL,qBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,UAClC;AAAA,QACF,WAAW,OAAO,OAAO;AACvB,mBAAS,GAAG,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAC5C,OAAO;AACL,mBAAS,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,QAC1C;AAAA,MACF,OAAO;AACL,YAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK;AAC1B,mBAAS,UAAU,EAAE;AACrB,iBAAO;AAAA,QACT;AACA,iBAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,QAAQ,MAAM;AAChB,gBAAU,IAAI,WAAW,IAAI,CAAC;AAC9B,gBAAU,IAAI,WAAW,IAAI,CAAC;AAAA,IAChC,WAAW,MAAM;AACf,YAAME,MAAK,WAAW,IAAI;AAC1B,UAAIA,QAAO,UAAU,MAAM;AACzB,kBAAU,IAAIA,GAAE;AAAA,MAClB;AAAA,IACF,WAAW,MAAM;AACf,YAAMA,MAAK,UAAU,WAAW,IAAI;AACpC,UAAIA,QAAO,UAAU,MAAM;AACzB,kBAAU,IAAIA,GAAE;AAAA,MAClB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,YAAMF,OAAM,gBAAgB,UAAU,IAAI,MAAM,SAAS,MAAM,KAAK,MAAM;AAC1E,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT,OAAO;AACL,YAAMA,OAAM,gBAAgB,UAAU,KAAK,MAAM,KAAK,MAAM;AAC5D,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AAAA,EACF;AACA,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AAEZ,MAAI,qBAAqB,KAAK,UAAU,GAAG;AACzC,QAAI,MAAM;AACV,QAAI,eAAe,QAAQ;AACzB,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,wBAAwB,QAAQ;AAAA,UACrC;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,wBAAwB,QAAQ;AAAA,UACrC;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,UAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAC/C;AAAA,MACE,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACF,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,UAAU,GAAG;AACf,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,KAAK,KAAK;AAAA,IACrB,OAAO;AACL,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,SAAK;AACL,SAAK;AACL,SAAK;AAAA,EAEP,WAAW,WAAW,KAAK,UAAU,GAAG;AACtC,QAAI,MAAM;AACV,QAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,aAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAChC,OAAO;AACL,aAAO,kBAAkB,QAAQ;AAAA,QAC/B;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,QAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,aAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,IAChC,OAAO;AACL,aAAO,kBAAkB,QAAQ;AAAA,QAC/B;AAAA,QACA,KAAK,eAAe;AAAA,QACpB,QAAQ;AAAA,MACV,CAAC;AAAA,IACH;AACA,QAAI,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,UAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAC/C;AAAA,MACE,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACF,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,GAAG,GAAG;AACV,QAAI,UAAU,GAAG;AACf,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,KAAK,KAAK;AAAA,IACrB,OAAO;AACL,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,QAAI,eAAe,WAAW;AAC5B,OAAC,GAAG,GAAG,CAAC,IAAI,qBAAqB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAClD,OAAO;AACL,OAAC,GAAG,GAAG,CAAC,IAAI,kBAAkB,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAC/C;AAAA,EAEF,WAAW,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACxB,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,QAAI,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MACjD,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,OAAC,IAAI,EAAE,IAAI,eAAe,IAAI,IAAI,MAAM;AAAA,IAC1C;AACA,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,UAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAI,GAAG;AACP,QAAI,UAAU,GAAG;AACf,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,KAAK,KAAK;AAAA,IACrB,OAAO;AACL,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IACrC;AACA,KAAC,GAAG,GAAG,CAAC,IAAI;AAAA,MACV,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IAC9B;AACA,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,iBAAiB,IAAI,SAAS,GAAG;AAAA,QACjC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AAAA,EAEF,WAAW,eAAe,KAAK,UAAU,GAAG;AAC1C,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACxB,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,QAAI,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAAI;AAAA,MACjD,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACA,QAAI,QAAQ;AACV,OAAC,IAAI,EAAE,IAAI,eAAe,IAAI,IAAI,MAAM;AAAA,IAC1C;AACA,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,UAAM,KAAK,KAAK,KAAK,KAAK,MAAM;AAChC,QAAI,GAAG;AACP,QAAI,UAAU,GAAG;AACf,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,KAAK,KAAK,KAAK;AAAA,IACrB,OAAO;AACL,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,CAAC,IAAI;AAAA,MACZ,GAAG,UAAU,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IAC9B;AAAA,EAEF,OAAO;AACL,QAAI,MAAM;AACV,QAAI,eAAe,OAAO;AACxB,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,kBAAkB,QAAQ;AAAA,UAC/B;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AACA,UAAI,YAAY,KAAK,MAAM,GAAG;AAC5B,eAAO,CAAC,MAAM,MAAM,MAAM,IAAI;AAAA,MAChC,OAAO;AACL,eAAO,oBAAoB,QAAQ;AAAA,UACjC;AAAA,UACA,QAAQ;AAAA,QACV,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,gBAAgB,cAAc,gBAAgB,YAAY;AAC5D,YAAMA,OAAM,uBAAuB,UAAU,QAAQ,QAAQ;AAC7D,aAAOA;AAAA,IACT;AACA,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,CAAC,KAAK,KAAK,KAAK,GAAG,IAAI;AAC7B,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,QAAQ,QAAQ,QAAQ,QAAQ;AACtC,UAAM,YAAY,QAAQ,QAAQ,QAAQ;AAC1C,UAAM,CAAC,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,CAAC,IAAI,IAAI,IAAI,MAAM,CAAC,IAC/C;AAAA,MACE,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB,CAAC,KAAK,KAAK,KAAK,GAAG;AAAA,MACnB;AAAA,IACF;AACF,UAAM,UAAU,SAAS;AACzB,UAAM,UAAU,SAAS;AACzB,YAAQ,UAAU;AAClB,QAAI,GAAG,IAAI;AACX,QAAI,UAAU,GAAG;AACf,UAAI,KAAK,KAAK,KAAK;AACnB,WAAK,KAAK,KAAK,KAAK;AACpB,WAAK,KAAK,KAAK,KAAK;AAAA,IACtB,OAAO;AACL,WAAK,KAAK,UAAU,KAAK,WAAW;AACpC,YAAM,KAAK,UAAU,KAAK,WAAW;AACrC,YAAM,KAAK,UAAU,KAAK,WAAW;AACrC,cAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAAA,IACrC;AACA,QAAI,WAAW,UAAU;AACvB,YAAMA,OAA8B;AAAA,QAClC;AAAA,QACA,QAAQ,OAAO,iBAAiB,GAAG,GAAG;AAAA,QACtC,QAAQ,OAAO,iBAAiB,IAAI,GAAG;AAAA,QACvC,QAAQ,OAAO,iBAAiB,IAAI,GAAG;AAAA,QACvC,YAAY,OAAO,QAAQ;AAAA,MAC7B;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,KAAC,EAAE,GAAG,GAAG,CAAC,IAAI;AAAA,MACZ,GAAG,UAAU,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE;AAAA,IAChC;AAAA,EACF;AACA,QAAM,MAA8B;AAAA,IAClC;AAAA,IACA,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ,KAAK,MAAM,CAAC;AAAA,IACZ,YAAY,QAAQ,GAAG,QAAQ,CAAC,CAAC;AAAA,EACnC;AACA,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;;;AC93GA,2BAA8C;AAe9C,IAAM;AAAA,EACJ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT;AAAA,EACA,OAAO;AAAA,EACP,YAAY;AACd,IAAI;AACJ,IAAMG,aAAY;AAGlB,IAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,IAAM,aAAa,IAAI,OAAO,UAAU;AAQjC,SAAS,sBACd,QACA,MAAe,CAAC,GACM;AACtB,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD;AACA,QAAM,EAAE,iBAAiB,CAAC,EAAE,IAAI;AAChC,QAAM,QAAkB,CAAC;AACzB,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAM;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IACjD;AACA,UAAM,CAAC,MAAM,KAAK,IAAI;AAEtB,QAAI,SAAS,aAAa;AACxB;AAAA,IACF;AAEA,QAAI,UAAU,QAAQ;AACpB,YAAM,CAAC,YAAY,IAAI,IAAI,sBAAsB,QAAQ,GAAG;AAC5D,eAAS;AACT,UAAI,MAAM;AACR,cAAM,KAAK,IAAI;AAAA,MACjB;AAAA,IACF,WAAW,SAAS,OAAO;AACzB,UAAI,MAAM,WAAW,IAAI,GAAG;AAC1B,YAAI;AACJ,YAAI,OAAO,eAAe,KAAK,gBAAgB,KAAK,GAAG;AACrD,iBAAO,eAAe,KAAK;AAAA,QAC7B,WAAW,OAAO,eAAe,aAAa,YAAY;AACxD,iBAAO,eAAe,SAAS,KAAK;AAAA,QACtC;AACA,YAAI,MAAM;AACR,gBAAM,KAAK,IAAI;AAAA,QACjB;AAAA,MACF,WAAW,OAAO;AAChB,cAAM,KAAK,KAAK;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAI,iBAAiB;AACrB,MAAI,MAAM,SAAS,GAAG;AACpB,UAAM,YAAY,MAAM,MAAM,SAAS,CAAC;AACxC,qBAAiB,QAAQ,SAAS;AAAA,EACpC;AACA,MAAI,gBAAgB;AACpB,WAAS,QAAQ,OAAO;AACtB,WAAO,KAAK,KAAK;AACjB,QAAI,WAAW,KAAK,IAAI,GAAG;AAEzB,YAAM,eAAe,WAAW,MAAM,GAAG;AACzC,UAAI,SAAS,YAAY,GAAG;AAC1B,YAAI,gBAAgB;AAClB,cAAI,QAAQ,YAAY,GAAG;AACzB,4BAAgB;AAAA,UAClB;AAAA,QACF,OAAO;AACL,0BAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF,WAAW,YAAY,KAAK,IAAI,GAAG;AACjC,aAAO,QAAQ,MAAM,GAAG;AACxB,UAAI,gBAAgB;AAClB,YAAI,QAAQ,IAAI,GAAG;AACjB,0BAAgB;AAAA,QAClB;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF,WACE,QACA,CAAC,gDAAgD,KAAK,IAAI,GAC1D;AACA,UAAI,gBAAgB;AAClB,YAAI,QAAQ,IAAI,GAAG;AACjB,0BAAgB;AAAA,QAClB;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AACA,QAAI,eAAe;AACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,QAAQ,aAAa;AAC/B;AAQO,SAAS,YACd,QACA,MAAe,CAAC,GACO;AACvB,QAAM,MAAgB,CAAC;AACvB,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAM;AAC3B,UAAM,CAAC,OAAO,IAAI,QAAQ,EAAE,IAAI;AAChC,QAAI,UAAU,QAAQ;AACpB,YAAM,CAAC,YAAY,aAAa,IAAI,sBAAsB,QAAQ,GAAG;AACrE,UAAI,CAAC,eAAe;AAClB,eAAO,IAAI,WAAW;AAAA,MACxB;AACA,eAAS;AACT,UAAI,KAAK,aAAa;AAAA,IACxB,OAAO;AACL,cAAQ,MAAM;AAAA,QACZ,KAAK,aAAa;AAChB,cAAI,IAAI,QAAQ;AACd,kBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,gBAAI,cAAc,KAAK;AACrB,kBAAI,OAAO,IAAI,GAAG,KAAK;AAAA,YACzB,OAAO;AACL,kBAAI,KAAK,KAAK;AAAA,YAChB;AAAA,UACF,OAAO;AACL,gBAAI,KAAK,KAAK;AAAA,UAChB;AACA;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,IAAI,QAAQ;AACd,kBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,gBACE,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,kBAAI,KAAK,KAAK;AAAA,YAChB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,cAAI,SAAS,WAAW,SAAS,KAAK;AACpC,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,SAAS,WACd,OACA,MAAe,CAAC,GACK;AACrB,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,CAAC,WAAW,KAAK,KAAK,KAAK,WAAW,UAAU;AAClD,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,aAAS,+BAAS,EAAE,KAAK,MAAM,CAAC;AACtC,QAAM,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,QAAI,QAAQ,OAAO,KAAK,EAAE;AAC1B,QAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,cAAQ,QAAQ,OAAO,GAAG;AAAA,IAC5B;AACA,aAAS,UAAU,KAAK;AACxB,WAAO;AAAA,EACT,OAAO;AACL,aAAS,UAAU,IAAI;AACvB,WAAO,IAAI,WAAW;AAAA,EACxB;AACF;AAQO,IAAM,SAAS,CAAC,OAAe,MAAe,CAAC,MAAc;AAClE,QAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,MAAI,SAAS,aAAa,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;ACrPA,8BAAiD;AACjD,mCAGO;AACP,IAAAC,wBAA8C;AAkC9C,IAAM;AAAA,EACJ,YAAYC;AAAA,EACZ,SAASC;AAAA,EACT,WAAW;AAAA,EACX,KAAAC;AAAA,EACA,UAAU;AAAA,EACV,OAAOC;AAAA,EACP,QAAQC;AAAA,EACR,WAAW;AAAA,EACX,YAAYC;AAAA,EACZ,YAAYC;AACd,IAAI;AACJ,IAAM,EAAE,iBAAiB,SAAS,IAAI;AACtC,IAAMC,aAAY;AAGlB,IAAMC,OAAM;AACZ,IAAMC,OAAM;AACZ,IAAMC,OAAM;AACZ,IAAMC,WAAU;AAChB,IAAMC,WAAU;AAShB,IAAM,iBAAiB,IAAI;AAAA,EACzB,IAAI,MAAM,IAAI,cAAc,IAAI,OAAO;AACzC;AACA,IAAM,aAAa;AACnB,IAAM,aAAa,IAAI,OAAO,OAAO,MAAM,IAAI,MAAM,IAAI;AACzD,IAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,IAAM,aAAa,IAAI,OAAO,MAAM;AACpC,IAAM,kBAAkB,IAAI,OAAO,IAAI,WAAW,EAAE;AACpD,IAAM,mBAAmB,IAAI,OAAO,IAAI,MAAM,EAAE;AAChD,IAAMC,cAAa,IAAI,OAAO,UAAU;AAQjC,SAAS,qBACd,QACA,MAAe,CAAC,GAC0B;AAC1C,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD;AACA,QAAM,EAAE,aAAa,IAAI,SAAS,GAAG,IAAI;AACzC,QAAM,gBAAgB,oBAAI,IAAI;AAAA,IAC5B,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IACjC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClC,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,EACnC,CAAC;AACD,QAAM,eAAe,cAAc,IAAI,UAAU;AAEjD,MAAI,CAAC,cAAc;AACjB,WAAO,IAAI,WAAW;AAAA,EACxB;AACA,QAAM,WAAW,oBAAI,IAAI;AACzB,QAAM,WAKF,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;AACnB,MAAI,IAAI;AACR,MAAI,OAAO;AACX,MAAI,OAAO;AACX,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAM;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IACjD;AACA,UAAM,CAAC,MAAM,OAAO,EAAE,EAAE,MAAM,IAAI;AAOlC,UAAM,UAAU,SAAS,CAAC;AAC1B,QAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACR,gBAAM,gBAAgB,iBAAiB,OAAO,GAAG;AACjD,cAAI,SAAS,aAAa,GAAG;AAC3B,oBAAQ,KAAK,aAAa;AAAA,UAC5B,OAAO;AACL,oBAAQ,KAAK,KAAK;AAAA,UACpB;AACA;AAAA,QACF;AAAA,QACA,KAAK,MAAM;AACT,kBAAQ,KAAK,KAAK;AAClB,iBAAO;AACP;AACA,cAAI,kBAAkB,KAAK,KAAK,GAAG;AACjC,qBAAS,IAAI,IAAI;AAAA,UACnB;AACA;AAAA,QACF;AAAA,QACA,KAAKV,QAAO;AAEV,cAAI,CAAC,aAAa,SAAS,KAAK,GAAG;AACjC,mBAAO,IAAI,WAAW;AAAA,UACxB;AACA,kBAAQ,KAAK,KAAK;AAClB,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAKC,MAAK;AACR,kBAAQ,KAAK,OAAO,QAAQ,KAAK,CAAC;AAClC,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,YAAY;AACf,kBAAQ,KAAK,KAAK;AAClB;AACA;AAAA,QACF;AAAA,QACA,KAAKJ,cAAa;AAChB,cAAI,MAAM;AACR,kBAAM,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC5C,gBAAI,cAAc,KAAK;AACrB,sBAAQ,OAAO,IAAI,GAAG,KAAK;AAAA,YAC7B,OAAO;AACL,sBAAQ,KAAK,KAAK;AAAA,YACpB;AACA,gBAAI,SAAS,IAAI,IAAI,GAAG;AACtB,uBAAS,OAAO,IAAI;AAAA,YACtB;AACA;AACA,gBAAI,SAAS,GAAG;AACd,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAKK,MAAK;AACR,kBAAQ,KAAK,OAAO,QAAQ,KAAK,IAAIM,QAAO;AAC5C,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAKL,UAAS;AACZ,cAAI,QAAQ,UAAU,MAAM;AAC1B,kBAAM,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC5C,gBAAI,OAAO,cAAc,UAAU;AACjC,sBAAQ,KAAK,KAAK;AAAA,YACpB,WACE,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,sBAAQ,KAAK,KAAK;AAAA,YACpB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,cAAI,SAASL,YAAW,SAASC,QAAO,MAAM;AAC5C,oBAAQ,KAAK,KAAK;AAAA,UACpB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,QAAM,gBAAgB,CAAC;AACvB,aAAW,WAAW,UAAU;AAC9B,QAAI,QAAQ,WAAW,GAAG;AACxB,YAAM,CAAC,aAAa,IAAI;AACxB,UAAI,iBAAiB,aAAa,GAAG;AACnC,sBAAc,KAAK,aAAa;AAAA,MAClC;AAAA,IACF,WAAW,QAAQ,QAAQ;AACzB,YAAM,gBAAgB,cAAc,QAAQ,KAAK,EAAE,GAAG;AAAA,QACpD;AAAA,MACF,CAAC;AACD,oBAAc,KAAK,aAAa;AAAA,IAClC;AAAA,EACF;AACA,SAAO;AACT;AAQO,SAAS,mBACd,OACA,MAAe,CAAC,GACK;AACrB,QAAM,EAAE,eAAe,IAAI,SAAS,GAAG,IAAI;AAC3C,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AACjC,QAAI,CAAC,OAAO;AACV,aAAO,IAAI,WAAW;AAAA,IACxB;AACA,QAAI,CAAC,iBAAiB,KAAK,KAAK,GAAG;AACjC,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,WAAO,IAAI,WAAW;AAAA,EACxB;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWK;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,MAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,QAAI,cAAc;AAChB,cAAQ,MAAM,QAAQ,iBAAiB,YAAY;AAAA,IACrD,OAAO;AACL,eAAS,UAAU,IAAI;AACvB,aAAO,IAAI,WAAW;AAAA,IACxB;AAAA,EACF;AACA,MAAI,aAAa;AACjB,MAAI,gBAAgB,KAAK,KAAK,GAAG;AAC/B,KAAC,EAAE,UAAU,IAAI,MAAM,MAAM,eAAe;AAAA,EAC9C;AACA,MAAI,aAAa;AACjB,MAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,UAAM,CAAC,EAAE,WAAW,IAAI,MAAM,MAAM,cAAc;AAClD,UAAM,CAAC,EAAE,SAAS,IAAI,MAAM,MAAM,WAAW;AAC7C,QAAI,WAAW,KAAK,WAAW,GAAG;AAChC,UACE,CAAC,gBAAgB,KAAK,WAAW,KACjC,CAAC,OAAO,UAAU,eAAe,KAAK,cAAc,WAAW,GAC/D;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MACxB;AAAA,IACF,WAAW,WAAW,UAAU;AAC9B,YAAM,sBAAsB,aAAa,aAAa,GAAG;AACzD,UAAI,SAAS,mBAAmB,GAAG;AACjC,gBAAQ,MAAM,QAAQ,aAAa,mBAAmB;AAAA,MACxD;AAAA,IACF;AACA,QAAI,WAAW,UAAU;AACvB,YAAM,aAAS,gCAAS,EAAE,KAAK,UAAU,CAAC;AAC1C,YAAM,gBAAgB,qBAAqB,QAAQ,GAAG;AACtD,UAAI,yBAAyB,YAAY;AACvC,iBAAS,UAAU,IAAI;AACvB,eAAO;AAAA,MACT;AACA,YAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,UAAI,eAAe;AACnB,UAAI,iBAAiB,EAAE,GAAG;AACxB,uBAAe,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAC3C,OAAO;AACL,uBAAe,IAAI,cAAc,KAAK,GAAG,CAAC;AAAA,MAC5C;AACA,UAAI,cAAc,cAAc;AAC9B,gBAAQ,MAAM,QAAQ,WAAW,YAAY;AAAA,MAC/C;AAAA,IACF;AAAA,EAEF,OAAO;AACL,UAAM,CAAC,EAAE,SAAS,IAAI,MAAM,MAAM,gBAAgB;AAClD,UAAM,aAAS,gCAAS,EAAE,KAAK,UAAU,CAAC;AAC1C,UAAM,cAAwB,CAAC;AAC/B,QAAI,OAAO;AACX,WAAO,OAAO,QAAQ;AACpB,YAAM,CAAC,MAAM,UAAU,IAAI,OAAO,MAAM;AACxC,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK,YAAY;AACf,sBAAY,KAAK,UAAU;AAC3B;AACA;AAAA,QACF;AAAA,QACA,KAAKP,cAAa;AAChB,gBAAM,YAAY,YAAY,YAAY,SAAS,CAAC;AACpD,cAAI,cAAc,KAAK;AACrB,wBAAY,OAAO,IAAI,GAAG,UAAU;AAAA,UACtC,WAAW,SAAS,SAAS,GAAG;AAC9B,wBAAY,KAAK,UAAU;AAAA,UAC7B;AACA;AACA;AAAA,QACF;AAAA,QACA,KAAKM,UAAS;AACZ,gBAAM,YAAY,YAAY,YAAY,SAAS,CAAC;AACpD,cACE,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,wBAAY,KAAK,UAAU;AAAA,UAC7B;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,cAAI,SAASL,YAAW,SAASC,MAAK;AACpC,wBAAY,KAAK,UAAU;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AACA,UAAI,SAAS,GAAG;AACd;AAAA,MACF;AAAA,IACF;AACA,UAAM,sBAAsB;AAAA,MAC1B,YAAY,KAAK,EAAE,EAAE,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,+BAA+B,YAAY;AAC7C,eAAS,UAAU,IAAI;AACvB,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,qBAAqB,QAAQ,GAAG;AACtD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,IAAI;AACvB,aAAO;AAAA,IACT;AACA,UAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,QAAI,eAAe;AACnB,QAAI,iBAAiB,EAAE,GAAG;AACxB,qBAAe,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,IAC3C,OAAO;AACL,qBAAe,IAAI,cAAc,KAAK,GAAG,CAAC;AAAA,IAC5C;AACA,YAAQ,MAAM,QAAQ,WAAW,GAAG,mBAAmB,GAAG,YAAY,EAAE;AAAA,EAC1E;AACA,WAAS,UAAU,KAAK;AACxB,SAAO;AACT;AAQO,SAAS,qBACd,OACA,MAAe,CAAC,GACK;AACrB,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAIW,YAAW,KAAK,KAAK,GAAG;AAC1B,UAAI,WAAW,UAAU;AACvB,eAAO;AAAA,MAET,OAAO;AACL,cAAM,IAAI,YAAY,oBAAoB,MAAM,SAAS;AAAA,MAC3D;AAAA,IACF,WAAW,CAAC,WAAW,KAAK,KAAK,GAAG;AAClC,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWN;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,cAAc,mBAAmB,OAAO,GAAG;AACjD,MAAI,uBAAuB,YAAY;AACrC,aAAS,UAAU,IAAI;AACvB,WAAO;AAAA,EACT;AACA,UAAQ;AACR,MAAI,WAAW,UAAU;AACvB,QAAI,MAAM,WAAW,OAAO,GAAG;AAC7B,cAAQ,MAAM,QAAQ,WAAW,MAAM;AAAA,IACzC,WAAW,MAAM,WAAW,OAAO,GAAG;AACpC,cAAQ,MAAM,QAAQ,WAAW,MAAM;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AACA,QAAM,aAAS,gCAAS,EAAE,KAAK,MAAM,CAAC;AACtC,QAAM,iBAAa,kDAAoB,MAAM;AAC7C,QAAM,uBAAmB,wBAAAO,OAAY,UAAU;AAC/C,MAAI,CAAC,kBAAkB;AACrB,aAAS,UAAU,IAAI;AACvB,WAAO,IAAI,WAAW;AAAA,EACxB;AACA,QAAM;AAAA,IACJ,OAAO;AAAA,IACP,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI;AACJ,MAAI,OAAO,MAAM,OAAO,cAAc,CAAC,GAAG;AACxC,QAAI,uBAAuB,OAAO,YAAY,IAAI,QAAQ,GAAG;AAC3D,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF,OAAO;AACL,YAAQ,iBAAiB,OAAO,cAAc,GAAGN,IAAG;AAAA,EACtD;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,GAAC,IAAI,IAAI,EAAE,IAAI;AACf,MAAI;AACJ,MAAI,WAAW,KAAK,aAAa,GAAG;AAClC,UAAM,UAAU,uBAAuB,OAAO,YAAY,IAAI,QAAQ;AACtE,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACX,aAAK;AAAA,MACP,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,IAAIE,IAAG;AAAA,IAC/B;AACA,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACX,aAAK;AAAA,MACP,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,IAAIA,IAAG;AAAA,IAC/B;AACA,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACX,aAAK;AAAA,MACP,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,IAAIA,IAAG;AAAA,IAC/B;AACA,QAAI,UAAU,GAAG;AACf,sBAAgB,GAAG,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,IACpD,OAAO;AACL,sBAAgB,GAAG,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,KAAK;AAAA,IAC/D;AAAA,EACF,WAAW,WAAW,KAAK,aAAa,GAAG;AACzC,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,WAAK;AAAA,IACP;AACA,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,WAAK;AAAA,IACP;AACA,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,WAAK;AAAA,IACP;AACA,QAAI,CAAC,GAAG,GAAG,CAAC,IAAI;AAAA,MACd,GAAG,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,KAAK;AAAA,IAC/C;AACA,QAAI,iBAAiB,IAAIE,UAASH,IAAG;AACrC,QAAI,iBAAiB,IAAIG,UAASH,IAAG;AACrC,QAAI,iBAAiB,IAAIG,UAASH,IAAG;AACrC,QAAI,UAAU,GAAG;AACf,sBAAgB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IAC3C,OAAO;AACL,sBAAgB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,IACtD;AAAA,EACF,OAAO;AACL,UAAM,KAAK,kBAAkB,QAAQ,SAAS;AAC9C,UAAM,UAAU,uBAAuB,OAAO,YAAY,IAAI,QAAQ;AACtE,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACX,aAAK;AAAA,MACP,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,IAAIA,IAAG;AAAA,IAC/B;AACA,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACX,aAAK;AAAA,MACP,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,IAAIA,IAAG;AAAA,IAC/B;AACA,QAAI,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACX,aAAK;AAAA,MACP,OAAO;AACL,aAAK;AAAA,MACP;AAAA,IACF,OAAO;AACL,WAAK,iBAAiB,IAAIA,IAAG;AAAA,IAC/B;AACA,QAAI,UAAU,GAAG;AACf,sBAAgB,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,IAC/C,OAAO;AACL,sBAAgB,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,KAAK;AAAA,IAC1D;AAAA,EACF;AACA,WAAS,UAAU,aAAa;AAChC,SAAO;AACT;;;AC9hBA,IAAMM,aAAY;AAClB,IAAM,kBAAkB;AAGxB,IAAMC,eAAc,IAAI,OAAO,WAAW;AAC1C,IAAMC,cAAa,IAAI,OAAO,UAAU;AACxC,IAAMC,cAAa,IAAI,OAAO,UAAU;AAQjC,IAAM,eAAe,CAC1B,OACA,MAAe,CAAC,MACQ;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,eAAe,IAAI,SAAS,UAAU,WAAW,MAAM,IAAI;AACnE,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWH;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,MAAIG,YAAW,KAAK,KAAK,GAAG;AAC1B,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACxB,aAAO;AAAA,IACT;AACA,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,cAAQ,QAAQ;AAAA,QACd,KAAK;AAAA,QACL,KAAK,YAAY;AACf,mBAAS,UAAU,aAAa;AAChC,iBAAO;AAAA,QACT;AAAA,QACA,SAAS;AACP,cAAI,UAAU;AACZ,qBAAS,UAAU,aAAa;AAChC,mBAAO;AAAA,UACT;AACA,gBAAMC,OAAM;AACZ,mBAAS,UAAUA,IAAG;AACtB,iBAAOA;AAAA,QACT;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAI,IAAI,WAAW,QAAQ;AACzB,QAAI,SAAS;AAAA,EACf;AACA,UAAQ,MAAM,YAAY;AAC1B,MAAIF,YAAW,KAAK,KAAK,GAAG;AAC1B,UAAM,gBAAgB,qBAAqB,OAAO,GAAG;AACrD,QAAI,WAAW,UAAU;AACvB,UAAIE;AACJ,UAAI,yBAAyB,YAAY;AACvC,YAAI,UAAU;AACZ,UAAAA,OAAM;AAAA,QACR,OAAO;AACL,UAAAA,OAAM;AAAA,QACR;AAAA,MACF,OAAO;AACL,QAAAA,OAAM;AAAA,MACR;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,QAAI,WAAW,UAAU;AACvB,UAAIA,OAAM;AACV,UAAI,yBAAyB,YAAY;AACvC,QAAAA,OAAM;AAAA,MACR,OAAO;AACL,QAAAA,OAAM;AAAA,MACR;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AACA,QAAI,yBAAyB,YAAY;AACvC,cAAQ;AAAA,IACV,OAAO;AACL,cAAQ;AAAA,IACV;AAAA,EACF;AACA,MAAIH,aAAY,KAAK,KAAK,GAAG;AAC3B,YAAQ,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,KAAK;AACT,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,IAAI;AACR,MAAI,QAAQ;AACZ,MAAI,UAAU,eAAe;AAC3B,YAAQ,QAAQ;AAAA,MACd,KAAK,UAAU;AACb,iBAAS,UAAU,KAAK;AACxB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,OAAO;AACV,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MACxB;AAAA,MACA,KAAK,YAAY;AACf,cAAMG,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACtB,eAAOA;AAAA,MACT;AAAA,MACA,KAAK;AAAA,MACL,SAAS;AACP,cAAMA,OAAM;AACZ,iBAAS,UAAUA,IAAG;AACtB,eAAOA;AAAA,MACT;AAAA,IACF;AAAA,EACF,WAAW,UAAU,gBAAgB;AACnC,QAAI,WAAW,UAAU;AACvB,eAAS,UAAU,KAAK;AACxB,aAAO;AAAA,IACT;AACA,QAAI,cAAc;AAChB,UAAI;AACJ,UAAI,aAAa,WAAW,MAAM,GAAG;AACnC,wBAAgB,gBAAgB,cAAc,GAAG;AAAA,MACnD,WAAW,aAAa,WAAW,QAAQ,GAAG;AAC5C,wBAAgB,iBAAiB,cAAc,GAAG;AAAA,MACpD,OAAO;AACL,wBAAgB,kBAAkB,cAAc,GAAG;AAAA,MACrD;AACA,UAAI,yBAAyB,YAAY;AACvC,iBAAS,UAAU,aAAa;AAChC,eAAO;AAAA,MACT;AACA,OAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,IACzB,WAAW,WAAW,UAAU;AAC9B,YAAMA,OAAM;AACZ,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AAAA,EACF,WAAW,WAAW,UAAU;AAC9B,QAAI,MAAM,WAAW,MAAM,GAAG;AAC5B,YAAMA,OAAM,gBAAgB,OAAO,GAAG;AACtC,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,YAAM,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,MACF;AACA,UAAIA,OAAM;AACV,UAAI,OAAO,GAAG;AACZ,QAAAA,OAAM,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MACtC,OAAO;AACL,QAAAA,OAAM,SAAS,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAC9C;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT,OAAO;AACL,YAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,UAAI,SAAS,GAAG,GAAG;AACjB,iBAAS,UAAU,GAAG;AACtB,eAAO;AAAA,MACT;AACA,YAAM,CAAC,KAAK,IAAI,IAAI,IAAI,EAAE,IAAI;AAC9B,UAAIA,OAAM;AACV,UAAI,QAAQ,OAAO;AACjB,YAAI,OAAO,GAAG;AACZ,UAAAA,OAAM,GAAG,GAAG,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAClC,OAAO;AACL,UAAAA,OAAM,GAAG,GAAG,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE;AAAA,QAC1C;AAAA,MACF,WAAW,OAAO,GAAG;AACnB,QAAAA,OAAM,GAAG,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,MAChC,OAAO;AACL,QAAAA,OAAM,GAAG,GAAG,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MACxC;AACA,eAAS,UAAUA,IAAG;AACtB,aAAOA;AAAA,IACT;AAAA,EACF,WAAW,MAAM,WAAW,MAAM,GAAG;AACnC,QAAI,eAAe,KAAK,KAAK,GAAG;AAC9B,UAAI,cAAc;AAChB,gBAAQ,MAAM,QAAQ,iBAAiB,YAAY;AAAA,MACrD;AAAA,IACF;AACA,QAAI,cAAc,KAAK,KAAK,GAAG;AAC7B,cAAQ,MAAM,QAAQ,gBAAgB,eAAe;AAAA,IACvD;AACA,UAAM,gBAAgB,gBAAgB,OAAO,GAAG;AAChD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACzB,WAAW,MAAM,WAAW,QAAQ,GAAG;AACrC,UAAM,gBAAgB,iBAAiB,OAAO,GAAG;AACjD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACzB,WAAW,OAAO;AAChB,UAAM,gBAAgB,kBAAkB,OAAO,GAAG;AAClD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,aAAa;AAChC,aAAO;AAAA,IACT;AACA,KAAC,IAAI,GAAG,GAAG,GAAG,KAAK,IAAI;AAAA,EACzB;AACA,MAAI,MAAM;AACV,UAAQ,QAAQ;AAAA,IACd,KAAK,OAAO;AACV,UACE,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,KAAK,KAClB,UAAU,GACV;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MACxB;AACA,YAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;AAClC;AAAA,IACF;AAAA,IACA,KAAK,YAAY;AACf,UACE,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,CAAC,KACd,OAAO,MAAM,KAAK,GAClB;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MACxB;AACA,YAAM,gBAAgB,CAAC,GAAG,GAAG,GAAG,KAAK,CAAC;AACtC;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,SAAS;AACP,cAAQ,IAAI;AAAA,QACV,KAAK,OAAO;AACV,cAAI,UAAU,GAAG;AACf,kBAAM,GAAG,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;AAAA,UAC9B,OAAO;AACL,kBAAM,GAAG,EAAE,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,KAAK;AAAA,UACzC;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,SAAS;AACZ,cAAI,UAAU,GAAG;AACf,kBAAM,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UAC5B,OAAO;AACL,kBAAM,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,UACvC;AACA;AAAA,QACF;AAAA;AAAA,QAEA,SAAS;AACP,cAAI,UAAU,GAAG;AACf,kBAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UAClC,OAAO;AACL,kBAAM,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,UAC7C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AA4CO,IAAM,UAAU,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC1E,MAAI,WAAW;AACf,QAAM,gBAAgB,aAAa,OAAO,GAAG;AAC7C,MAAI,yBAAyB,YAAY;AACvC,WAAO;AAAA,EACT;AACA,SAAO;AACT;;;AN7WA,IAAM;AAAA,EACJ,YAAYC;AAAA,EACZ,OAAO;AAAA,EACP,SAASC;AAAA,EACT,OAAO;AAAA,EACP,KAAAC;AAAA,EACA,UAAUC;AAAA,EACV,OAAOC;AAAA,EACP,WAAWC;AAAA,EACX,YAAYC;AACd,IAAI;AACJ,IAAMC,aAAY;AAGlB,IAAMC,OAAM;AACZ,IAAMC,OAAM;AACZ,IAAMC,OAAM;AACZ,IAAMC,YAAW;AAGjB,IAAMC,aAAY,IAAI,OAAO,OAAO,cAAc,IAAI;AACtD,IAAMC,gBACJ;AACF,IAAMC,WAAU,IAAI,OAAO,OAAO;AAU3B,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAgB;AACxE,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,YAAY,KAAK,kBAAkB,MAAM,IAAI;AACrD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWP;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,MACE;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,MAAI;AACJ,MAAI,cAAc,KAAK;AACrB,mBAAe;AAAA,EACjB,WAAW,cAAc,KAAK;AAC5B,mBAAe;AAAA,EACjB,OAAO;AACL,mBAAe;AAAA,EACjB;AACA,QAAM,aAAS,gCAAS,EAAE,KAAK,MAAM,CAAC;AACtC,MAAI,OAAO;AACX,MAAI,MAAM;AACV,QAAM,MAAgB,CAAC;AACvB,SAAO,OAAO,QAAQ;AACpB,UAAM,CAAC,MAAMQ,MAAK,IAAI,OAAO,MAAM;AACnC,YAAQ,MAAM;AAAA,MACZ,KAAK,OAAO;AACV,YAAI,aAAa,KAAKA,MAAK,GAAG;AAC5B,cAAI,SAAS,GAAG;AACd,gBAAI,KAAK,IAAI,KAAK,CAAC;AACnB,kBAAM;AAAA,UACR,OAAO;AACL,mBAAOA;AAAA,UACT;AAAA,QACF,OAAO;AACL,iBAAOA;AAAA,QACT;AACA;AAAA,MACF;AAAA,MACA,KAAK,OAAO;AACV,YAAI,aAAa,KAAKA,MAAK,GAAG;AAC5B,cAAI,SAAS,GAAG;AACd,gBAAI,KAAK,IAAI,KAAK,CAAC;AACnB,kBAAM;AAAA,UACR,OAAO;AACL,mBAAOA;AAAA,UACT;AAAA,QACF,OAAO;AACL,iBAAOA;AAAA,QACT;AACA;AAAA,MACF;AAAA,MACA,KAAKd,UAAS;AACZ,YAAI,oBAAoB,cAAc,OAAO,cAAc,MAAM;AAC/D,iBAAOc;AAAA,QACT;AACA;AAAA,MACF;AAAA,MACA,KAAKZ;AAAA,MACL,KAAKE,aAAY;AACf,eAAOU;AACP;AACA;AAAA,MACF;AAAA,MACA,KAAKf,cAAa;AAChB,eAAOe;AACP;AACA;AAAA,MACF;AAAA,MACA,KAAKT,UAAS;AACZ,YAAI,aAAa,KAAKS,MAAK,GAAG;AAC5B,cAAI,SAAS,GAAG;AACd,gBAAI,KAAK;AACP,kBAAI,KAAK,IAAI,KAAK,CAAC;AACnB,oBAAM;AAAA,YACR;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF,WAAW,CAAC,IAAI,SAAS,GAAG,GAAG;AAC7B,iBAAO;AAAA,QACT;AACA;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,SAASb,MAAK;AAChB,cAAI,KAAK,IAAI,KAAK,CAAC;AACnB,gBAAM;AAAA,QACR,OAAO;AACL,iBAAOa;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAOO,IAAM,qBAAqB,CAAC,UAA4B;AAC7D,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AAAA,EACrB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB,eAAe;AAAA,IACtC,WAAWR;AAAA,IACX,MAAM;AAAA,IACN;AAAA,EACF,CAAC;AACD,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,aAAS,gCAAS,EAAE,KAAK,MAAM,CAAC;AACtC,QAAM,QAAQ,oBAAI,IAAI;AACtB,SAAO,OAAO,QAAQ;AACpB,UAAM,CAAC,MAAMQ,MAAK,IAAI,OAAO,MAAM;AACnC,QAAI,SAASX,UAASW,OAAM,WAAW,IAAI,GAAG;AAC5C,YAAM,IAAIA,MAAK;AAAA,IACjB;AAAA,EACF;AACA,QAAM,MAAM,CAAC,GAAG,KAAK;AACrB,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,UAAU,CAAC,OAAgB,MAAe,CAAC,MAAe;AACrE,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,YAAY,EAAE,KAAK;AACjC,QAAI,SAAS,SAAS,KAAK,GAAG;AAC5B,UAAI,WAAW,KAAK,KAAK,GAAG;AAC1B,YACE,iCAAiC,KAAK,KAAK,KAC3C,OAAO,UAAU,eAAe,KAAK,cAAc,KAAK,GACxD;AACA,iBAAO;AAAA,QACT;AAAA,MACF,WAAWH,WAAU,KAAK,KAAK,KAAKE,SAAQ,KAAK,KAAK,GAAG;AACvD,eAAO;AAAA,MACT,WAAWD,cAAa,KAAK,KAAK,GAAG;AACnC,YAAI,WAAW;AACf,YAAI,CAAC,IAAI,QAAQ;AACf,cAAI,SAAS;AAAA,QACf;AACA,cAAM,gBAAgB,aAAa,OAAO,GAAG;AAC7C,YAAI,eAAe;AACjB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,oBAAoB,CAC/B,OACA,OAAgB,UACL;AACX,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;AAAA,EACT;AACA,QAAM,MAAM,KAAK,UAAU,OAAO,CAAC,MAAM,QAAQ;AAC/C,QAAI;AACJ,QAAI,OAAO,QAAQ,aAAa;AAC9B,sBAAgB;AAAA,IAClB,WAAW,OAAO,QAAQ,YAAY;AACpC,UAAI,MAAM;AACR,wBAAgB,IAAI,SAAS,EAAE,QAAQ,OAAO,EAAE,EAAE,UAAU,GAAGJ,IAAG;AAAA,MACpE,OAAO;AACL,wBAAgB,IAAI;AAAA,MACtB;AAAA,IACF,WAAW,eAAe,OAAO,eAAe,KAAK;AACnD,sBAAgB,CAAC,GAAG,GAAG;AAAA,IACzB,WAAW,OAAO,QAAQ,UAAU;AAClC,sBAAgB,IAAI,SAAS;AAAA,IAC/B,OAAO;AACL,sBAAgB;AAAA,IAClB;AACA,WAAO;AAAA,EACT,CAAC;AACD,SAAO;AACT;AAQO,IAAM,mBAAmB,CAAC,OAAe,MAAc,MAAc;AAC1E,MAAI,CAAC,OAAO,SAAS,KAAK,GAAG;AAC3B,UAAM,IAAI,UAAU,GAAG,KAAK,0BAA0B;AAAA,EACxD;AACA,MAAI,CAAC,OAAO,SAAS,GAAG,GAAG;AACzB,UAAM,IAAI,UAAU,GAAG,GAAG,0BAA0B;AAAA,EACtD,WAAW,MAAM,KAAK,MAAMA,MAAK;AAC/B,UAAM,IAAI,WAAW,GAAG,GAAG,yBAAyBA,IAAG,GAAG;AAAA,EAC5D;AACA,MAAI,QAAQ,GAAG;AACb,WAAO,KAAK,MAAM,KAAK;AAAA,EACzB;AACA,MAAI;AACJ,MAAI,QAAQA,MAAK;AACf,UAAM,MAAM,YAAY,CAAC;AAAA,EAC3B,WAAW,MAAMD,MAAK;AACpB,UAAM,MAAM,YAAY,CAAC;AAAA,EAC3B,OAAO;AACL,UAAM,MAAM,YAAY,CAAC;AAAA,EAC3B;AACA,SAAO,WAAW,GAAG;AACvB;AASO,IAAM,iBAAiB,CAC5B,MACA,MACA,MAAc,cACO;AACrB,MAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,IAAI,0BAA0B;AAAA,EACvD;AACA,MAAI,CAAC,OAAO,SAAS,IAAI,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,IAAI,0BAA0B;AAAA,EACvD;AACA,UAAQ,KAAK;AAAA,IACX,KAAK,cAAc;AACjB,UAAI,OAAO,MAAM;AACf,gBAAQE;AAAA,MACV;AACA;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,UAAI,OAAO,MAAM;AACf,gBAAQA;AAAA,MACV;AACA;AAAA,IACF;AAAA,IACA,KAAK,UAAU;AACb,UAAI,OAAO,QAAQ,OAAO,OAAOC,WAAU;AACzC,gBAAQD;AAAA,MACV,WAAW,OAAO,OAAOC,YAAW,MAAM,QAAQ,MAAM;AACtD,gBAAQD;AAAA,MACV;AACA;AAAA,IACF;AAAA,IACA,KAAK;AAAA,IACL,SAAS;AACP,UAAI,OAAO,OAAOC,WAAU;AAC1B,gBAAQD;AAAA,MACV,WAAW,OAAO,OAAOC,YAAW,IAAI;AACtC,gBAAQD;AAAA,MACV;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,MAAM,IAAI;AACpB;;;ADtUA,IAAM,YAAY;AAKX,IAAM,YAAN,MAAgB;AAAA;AAAA,EAErB;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAe,SAAkB,OAAO;AAClD,SAAK,QAAQ;AACb,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,OAAO;AACT,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AACF;AAKO,IAAM,aAAN,cAAyB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIxC,cAAc;AACZ,UAAM,OAAO,MAAM,GAAG,IAAI;AAAA,EAC5B;AACF;AAKO,IAAM,WAAW,IAAI,0BAAS;AAAA,EACnC,KAAK;AACP,CAAC;AAQM,IAAM,WAAW,CAAC,KAAa,UAAyB;AAC7D,MAAI,KAAK;AACP,QAAI,UAAU,MAAM;AAClB,eAAS,IAAI,KAAK,IAAI,WAAW,CAAC;AAAA,IACpC,WAAW,iBAAiB,WAAW;AACrC,eAAS,IAAI,KAAK,KAAK;AAAA,IACzB,OAAO;AACL,eAAS,IAAI,KAAK,IAAI,UAAU,KAAK,CAAC;AAAA,IACxC;AAAA,EACF;AACF;AAOO,IAAM,WAAW,CAAC,QAAqC;AAC5D,MAAI,OAAO,SAAS,IAAI,GAAG,GAAG;AAC5B,UAAM,OAAO,SAAS,IAAI,GAAG;AAC7B,QAAI,gBAAgB,WAAW;AAC7B,aAAO;AAAA,IACT;AAEA,aAAS,OAAO,GAAG;AACnB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,IAAM,iBAAiB,CAC5B,SACA,MAAe,CAAC,MACL;AACX,QAAM,EAAE,iBAAiB,CAAC,GAAG,YAAY,CAAC,EAAE,IAAI;AAChD,MAAI,WAAW;AACf,MACE,WACA,OAAO,KAAK,OAAO,EAAE,UACrB,OAAO,eAAe,aAAa,cACnC,OAAO,UAAU,aAAa,YAC9B;AACA,YAAQ,MAAM,kBAAkB,GAAG;AACnC,eAAW,kBAAkB,OAAO;AAAA,EACtC;AACA,SAAO;AACT;;;ADpFA,IAAM;AAAA,EACJ,YAAYM;AAAA,EACZ,SAASC;AAAA,EACT,WAAWC;AAAA,EACX,KAAAC;AAAA,EACA,UAAUC;AAAA,EACV,WAAWC;AAAA,EACX,YAAYC;AACd,IAAI;AACJ,IAAMC,aAAY;AAGlB,IAAMC,QAAO;AACb,IAAMC,OAAM;AACZ,IAAMC,WAAU;AAGhB,IAAMC,eAAc,IAAI,OAAO,WAAW;AAC1C,IAAM,kBAAkB,IAAI,OAAO,YAAY,GAAG,OAAO;AACzD,IAAMC,qBAAoB,IAAI,OAAO,iBAAiB;AACtD,IAAMC,cAAa,IAAI,OAAO,UAAU;AACxC,IAAM,mBAAmB,IAAI,OAAO,gBAAgB;AACpD,IAAM,eAAe;AACrB,IAAM,eAAe,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,IAAI;AAChE,IAAM,mBAAmB,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,MAAM;AACtE,IAAM,eAAe,IAAI,OAAO,KAAK,GAAG,KAAK;AAKtC,IAAM,aAAN,MAAiB;AAAA;AAAA;AAAA,EAGtB;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AAEZ,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAAA,EAClB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,SAAK,UAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AAEN,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAEhB,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAChB,SAAK,UAAU,CAAC;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,SAAmB,CAAC,GAAa;AACpC,UAAM,MAAM,CAAC,GAAG,MAAM;AACtB,QAAI,IAAI,SAAS,GAAG;AAClB,UAAI,KAAK,CAAC,GAAG,MAAM;AACjB,YAAI;AACJ,YAAI,iBAAiB,KAAK,CAAC,KAAK,iBAAiB,KAAK,CAAC,GAAG;AACxD,gBAAM,CAAC,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,gBAAgB;AAChD,gBAAM,CAAC,EAAE,MAAM,KAAK,IAAI,EAAE,MAAM,gBAAgB;AAChD,cAAI,UAAU,OAAO;AACnB,gBAAI,OAAO,IAAI,MAAM,OAAO,IAAI,GAAG;AACjC,oBAAM;AAAA,YACR,WAAW,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG;AACtC,oBAAM;AAAA,YACR,OAAO;AACL,oBAAM;AAAA,YACR;AAAA,UACF,WAAW,QAAQ,OAAO;AACxB,kBAAM;AAAA,UACR,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF,OAAO;AACL,cAAI,MAAM,GAAG;AACX,kBAAM;AAAA,UACR,WAAW,IAAI,GAAG;AAChB,kBAAM;AAAA,UACR,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAmB;AACjB,UAAM,QAAQ,CAAC;AACf,QAAI;AACJ,QAAI,KAAK,SAAS;AAChB,YAAM;AACN,iBAAW,KAAK,KAAK,SAAS;AAC5B,eAAO;AACP,YAAI,QAAQ,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC3D;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,KAAK,WAAW,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ;AAClD,YAAI,OAAO,SAAS,GAAG,GAAG;AACxB,gBAAM,iBAAiB,KAAKJ,IAAG;AAAA,QACjC;AACA,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM;AAAA,MACR;AACA,iBAAW,KAAK,KAAK,SAAS;AAC5B,eAAO;AACP,YAAI,QAAQ,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC3D;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,cAAM,GAAG,iBAAiB,KAAKA,IAAG,CAAC;AAAA,MACrC;AACA,UAAI,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ;AACjC,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,KAAK,QAAQ,QAAQ;AACvB,YAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,WAAC,GAAG,IAAI,KAAK;AAAA,QACf,OAAO;AACL,gBAAM,GAAG,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK,CAAC;AAAA,QAC9C;AAAA,MACF;AACA,UAAI,KAAK,QAAQ,QAAQ;AACvB,YAAI,KAAK,QAAQ,WAAW,GAAG;AAC7B,WAAC,GAAG,IAAI,KAAK;AAAA,QACf,OAAO;AACL,gBAAM,GAAG,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK,CAAC;AAAA,QAC9C;AAAA,MACF;AACA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,YAAI,KAAK;AACP,cAAI,KAAK;AACP,gBAAI,IAAI,SAAS,GAAG,GAAG;AACrB,wBAAM,uBAAK,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM;AAAA,gBAC7C,kBAAkB;AAAA,cACpB,CAAC;AAAA,YACH,OAAO;AACL,wBAAM,uBAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK;AAAA,gBAC3C,kBAAkB;AAAA,cACpB,CAAC;AAAA,YACH;AAAA,UACF,OAAO;AACL,sBAAM,uBAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,cAClC,kBAAkB;AAAA,YACpB,CAAC;AAAA,UACH;AAAA,QACF,WAAW,IAAI,SAAS,GAAG,GAAG;AAC5B,oBAAM,uBAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,YACpC,kBAAkB;AAAA,UACpB,CAAC;AAAA,QACH,OAAO;AACL,oBAAM,uBAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,YAClC,kBAAkB;AAAA,UACpB,CAAC;AAAA,QACH;AACA,cAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MACrC,OAAO;AACL,YAAI,CAAC,MAAM,UAAU,QAAQ,QAAW;AACtC,gBAAM,KAAK,GAAG;AAAA,QAChB;AACA,YAAI,KAAK;AACP,cAAI,KAAK;AACP,gBAAI,IAAI,SAAS,GAAG,GAAG;AACrB,wBAAM,uBAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,gBACpC,kBAAkB;AAAA,cACpB,CAAC;AAAA,YACH,OAAO;AACL,wBAAM,uBAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,gBAClC,kBAAkB;AAAA,cACpB,CAAC;AAAA,YACH;AAAA,UACF,OAAO;AACL,sBAAM,uBAAK,QAAQ,GAAG,KAAK;AAAA,cACzB,kBAAkB;AAAA,YACpB,CAAC;AAAA,UACH;AACA,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAC1C,OAAO;AACL,kBAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UACrC;AAAA,QACF,OAAO;AACL,oBAAM,uBAAK,QAAQ,GAAG,KAAK;AAAA,YACzB,kBAAkB;AAAA,UACpB,CAAC;AACD,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAC1C,OAAO;AACL,kBAAM,KAAK,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAC/C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,QAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,UAAU,QAAQ,QAAW;AACtC,gBAAM,KAAK,GAAG;AAAA,QAChB;AACA,cAAM,MAAM,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK;AAC9C,YAAI,MAAM,QAAQ;AAChB,gBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,QACvB,OAAO;AACL,gBAAM,KAAK,GAAG,GAAG,EAAE;AAAA,QACrB;AAAA,MACF;AACA,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK;AAC9C,YAAI,IAAI,SAAS,GAAG,GAAG;AACrB,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UACzB,OAAO;AACL,kBAAM,KAAK,QAAQ,GAAG,GAAG;AAAA,UAC3B;AAAA,QACF,WAAW,MAAM,QAAQ;AACvB,gBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,QACvB,OAAO;AACL,gBAAM,KAAK,OAAO,GAAG,EAAE;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,QAAQ;AAChB,aAAO,MAAM,KAAK,GAAG;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc;AACZ,UAAM,QAAQ,CAAC;AACf,QAAI,KAAK,SAAS;AAChB,UAAI,MAAM;AACV,iBAAW,KAAK,KAAK,SAAS;AAC5B,eAAO;AACP,YAAI,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC9C;AAAA,QACF;AAAA,MACF;AACA,YAAM,KAAK,GAAG;AAAA,IAChB;AACA,QAAI,KAAK,SAAS;AAChB,UAAI,MAAuB;AAC3B,iBAAW,KAAK,KAAK,SAAS;AAC5B,eAAO;AACP,YAAI,CAAC,OAAO,SAAS,GAAG,GAAG;AACzB;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,cAAM,GAAG,GAAG;AAAA,MACd;AACA,UAAI,MAAM,QAAQ;AAChB,cAAM,KAAK,KAAK,GAAG,EAAE;AAAA,MACvB,OAAO;AACL,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,KAAK;AACd,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK;AAAA,MAC1C;AACA,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,KAAK,KAAK,KAAK,OAAO,EAAE,KAAK,KAAK;AAAA,MAC1C;AACA,UAAI,KAAK;AACP,YAAI,KAAK;AACP,cAAI,IAAI,SAAS,GAAG,GAAG;AACrB,sBAAM,uBAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,cACpC,kBAAkB;AAAA,YACpB,CAAC;AAAA,UACH,OAAO;AACL,sBAAM,uBAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,cAClC,kBAAkB;AAAA,YACpB,CAAC;AAAA,UACH;AAAA,QACF,OAAO;AACL,oBAAM,uBAAK,QAAQ,GAAG,KAAK;AAAA,YACzB,kBAAkB;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF,OAAO;AACL,kBAAM,uBAAK,cAAc,GAAG,MAAM;AAAA,UAChC,kBAAkB;AAAA,QACpB,CAAC;AAAA,MACH;AACA,UAAI,MAAM,QAAQ;AAChB,cAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MAC1C,OAAO;AACL,cAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MACrC;AAAA,IACF;AACA,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,KAAK,OAAO,EAC/B,IAAI,UAAQ;AACX,cAAI;AACJ,cACE,aAAa,KAAK,IAAI,KACtB,CAAC,KAAK,WAAW,GAAG,KACpB,CAAC,KAAK,SAAS,GAAG,GAClB;AACA,kBAAM,IAAI,IAAI;AAAA,UAChB,OAAO;AACL,kBAAM;AAAA,UACR;AACA,iBAAO;AAAA,QACT,CAAC,EACA,KAAK,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,kBAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UACzB,OAAO;AACL,kBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,UACvB;AAAA,QACF,OAAO;AACL,gBAAM,KAAK,GAAG,GAAG,EAAE;AAAA,QACrB;AAAA,MACF;AACA,UAAI,KAAK,QAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,KAAK,OAAO,EAC/B,IAAI,UAAQ;AACX,cAAI;AACJ,cACE,aAAa,KAAK,IAAI,KACtB,CAAC,KAAK,WAAW,GAAG,KACpB,CAAC,KAAK,SAAS,GAAG,GAClB;AACA,kBAAM,IAAI,IAAI;AAAA,UAChB,OAAO;AACL,kBAAM;AAAA,UACR;AACA,iBAAO;AAAA,QACT,CAAC,EACA,KAAK,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,kBAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UACzB,OAAO;AACL,kBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,UACvB;AAAA,QACF,WAAW,KAAK,QAAQ,SAAS,GAAG;AAClC,gBAAM,KAAK,SAAS,GAAG,GAAG;AAAA,QAC5B,OAAO;AACL,gBAAM,KAAK,QAAQ,GAAG,EAAE;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,QAAQ;AAChB,aAAO,MAAM,KAAK,GAAG;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACF;AAQO,IAAM,iBAAiB,CAC5B,SAA8B,CAAC,GAC/B,WAAoB,UACT;AACX,MAAI,OAAO,SAASD,OAAM;AACxB,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAC7D;AACA,QAAM,QAAQ,OAAO,MAAM;AAC3B,MAAI,CAAC,SAAS,KAAK,KAAK,CAAC,MAAM,SAAS,GAAG,GAAG;AAC5C,UAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,EAC9C;AACA,QAAM,MAAM,OAAO,IAAI;AACvB,MAAI,QAAQ,KAAK;AACf,UAAM,IAAI,MAAM,oBAAoB,GAAG,GAAG;AAAA,EAC5C;AACA,MAAI,OAAO,WAAW,GAAG;AACvB,UAAM,CAAC,KAAK,IAAI;AAChB,QAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,IAC9C;AACA,WAAO,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAAA,EAC/B;AACA,QAAM,eAAe,CAAC;AACtB,QAAM,MAAM,IAAI,WAAW;AAC3B,MAAI,WAAmB;AACvB,QAAM,IAAI,OAAO;AACjB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,IAC9C;AACA,QAAI,UAAU,OAAO,UAAU,KAAK;AAClC,iBAAW;AAAA,IACb,WAAW,UAAU,OAAO,UAAU,KAAK;AACzC,YAAM,cAAc,IAAI,SAAS;AACjC,UAAI,aAAa;AACf,qBAAa,KAAK,aAAa,KAAK;AAAA,MACtC;AACA,UAAI,MAAM;AACV,iBAAW;AAAA,IACb,OAAO;AACL,YAAM,WAAW,OAAO,KAAK;AAC7B,YAAM,WAAW,GAAG,KAAK;AACzB,cAAQ,UAAU;AAAA,QAChB,KAAK,KAAK;AACR,cAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,IAAI,QAAQ;AAAA,UAC9B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,kBAAM,CAAC,EAAE,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAME,WAAUA,WAAW,OAAO,GAAG,CAAC;AAAA,UACnD,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B,OAAO;AACL,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,cAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,kBAAM,CAAC,EAAE,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,UAC7B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B,OAAO;AACL,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,IAAI,GAAG;AACf,YAAM,cAAc,IAAI,SAAS;AACjC,UAAI,aAAa;AACf,qBAAa,KAAK,WAAW;AAAA,MAC/B;AACA,UAAI,MAAM;AACV,iBAAW;AAAA,IACb;AAAA,EACF;AACA,MAAI,gBAAgB;AACpB,MAAI,aAAa,aAAa,SAAS,GAAG,KAAK,aAAa,SAAS,GAAG,IAAI;AAC1E,UAAM,kBAAkB,CAAC;AACzB,QAAI,MAAM;AACV,eAAW;AACX,UAAMI,KAAI,aAAa;AACvB,aAAS,IAAI,GAAG,IAAIA,IAAG,KAAK;AAC1B,YAAM,QAAQ,aAAa,CAAC;AAC5B,UAAI,iBAAiB,KAAK,GAAG;AAC3B,YAAI,UAAU,OAAO,UAAU,KAAK;AAClC,qBAAW;AAAA,QACb,OAAO;AACL,gBAAM,WAAW,OAAO,KAAK;AAC7B,gBAAM,WAAW,GAAG,KAAK;AACzB,kBAAQ,UAAU;AAAA,YAChB,KAAK,KAAK;AACR,kBAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,KAAK,QAAQ;AAAA,cAC/B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,sBAAM,CAAC,EAAE,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cAClC,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B,OAAO;AACL,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B;AACA;AAAA,YACF;AAAA,YACA,KAAK;AAAA,YACL,SAAS;AACP,kBAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,sBAAM,CAAC,EAAE,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,cAC7B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B,OAAO;AACL,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAMA,KAAI,GAAG;AACf,cAAM,cAAc,IAAI,IAAI;AAC5B,YAAI,aAAa;AACf,0BAAgB,KAAK,WAAW;AAAA,QAClC;AACA,YAAI,MAAM;AACV,mBAAW;AAAA,MACb;AAAA,IACF;AACA,oBAAgB,gBAAgB,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,EAClE,OAAO;AACL,oBAAgB,aAAa,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,EAC/D;AACA,MACE,cAAc,WAAW,GAAG,KAC5B,cAAc,SAAS,GAAG,KAC1B,cAAc,YAAY,GAAG,MAAM,KACnC,cAAc,QAAQ,GAAG,MAAM,cAAc,SAAS,GACtD;AACA,oBAAgB,cAAc,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAAA,EACpE;AACA,SAAO,GAAG,KAAK,GAAG,aAAa,GAAG,GAAG;AACvC;AAQO,IAAM,gBAAgB,CAAC,OAAe,MAAe,CAAC,MAAc;AACzE,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,CAAC,iBAAiB,KAAK,KAAK,KAAK,WAAW,UAAU;AACxD,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWP;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,YAAkB,gCAAS,EAAE,KAAK,MAAM,CAAC,EAC5C,IAAI,CAAC,UAA4B;AAChC,UAAM,CAAC,MAAMQ,MAAK,IAAI;AACtB,QAAI,MAAM;AACV,QAAI,SAAST,YAAW,SAASL,UAAS;AACxC,YAAMc;AAAA,IACR;AACA,WAAO;AAAA,EACT,CAAC,EACA,OAAO,OAAK,CAAC;AAChB,MAAI,aAAa,MAAM,cAAc,CAAC,SAAiB,MAAM,KAAK,IAAI,CAAC;AACvE,SAAO,YAAY;AACjB,UAAM,WAAW,MAAM,UAAU,CAAC,MAAe,UAAkB;AACjE,aAAO,SAAS,OAAO,QAAQ;AAAA,IACjC,CAAC;AACD,UAAM,eAAyB,MAAM,MAAM,YAAY,WAAW,CAAC;AACnE,QAAI,kBAA0B,eAAe,YAAY;AACzD,QAAI,iBAAiB,KAAK,eAAe,GAAG;AAC1C,4BAAkB,uBAAK,iBAAiB;AAAA,QACtC,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,UAAM,OAAO,YAAY,WAAW,aAAa,GAAG,eAAe;AACnE,iBAAa,MAAM,cAAc,CAAC,SAAiB,MAAM,KAAK,IAAI,CAAC;AAAA,EACrE;AACA,QAAM,iBAAiB,eAAe,OAAO,IAAI;AACjD,WAAS,UAAU,cAAc;AACjC,SAAO;AACT;AAQO,IAAM,mBAAmB,CAC9B,OACA,MAAe,CAAC,MACQ;AACxB,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,CAAC,EAAE,EAAE,EAAE,EAAE,SAAS,CAAC,CAAC,IAAI;AAC9B,QAAM,EAAE,MAAM,MAAM,IAAI;AAIxB,QAAM,EAAE,YAAY,CAAC,EAAE,IAAI;AAC3B,MAAI,SAAS,MAAM;AACjB,WAAO,GAAG,KAAK,GAAG,IAAI;AAAA,EACxB;AACA,QAAM,gBAAgB,OAAO,KAAK;AAClC,MAAI,QAAQ,OAAO,SAAS,aAAa,GAAG;AAC1C,QAAI;AACJ,QAAI,OAAO,eAAe,KAAK,WAAW,IAAI,GAAG;AAC/C,mBAAa,UAAU,IAAI;AAAA,IAC7B,WAAW,OAAO,UAAU,aAAa,YAAY;AACnD,mBAAa,UAAU,SAAS,IAAI;AAAA,IACtC;AACA,iBAAa,OAAO,UAAU;AAC9B,QAAI,OAAO,SAAS,UAAU,GAAG;AAC/B,aAAO,GAAG,gBAAgB,UAAU;AAAA,IACtC;AAAA,EACF;AACA,SAAO,IAAI,WAAW;AACxB;AAQO,IAAMC,eAAc,CACzB,QACA,MAAe,CAAC,MACH;AACb,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD;AACA,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,QAAM,WAAW,oBAAI,IAAI;AACzB,MAAI,OAAO;AACX,QAAM,MAAgB,CAAC;AACvB,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAM;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IACjD;AACA,UAAM,CAAC,OAAO,IAAI,QAAQ,EAAE,IAAI;AAChC,YAAQ,MAAM;AAAA,MACZ,KAAKd,MAAK;AACR,YAAI,WAAW,YAAY,CAAC,SAAS,IAAI,IAAI,GAAG;AAC9C,cAAI,KAAK,KAAK;AAAA,QAChB,OAAO;AACL,gBAAM,gBAAgB,iBAAiB,OAAO,GAAG;AACjD,cAAI,SAAS,aAAa,GAAG;AAC3B,gBAAI,KAAK,aAAa;AAAA,UACxB,OAAO;AACL,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAKE;AAAA,MACL,KAAKC,aAAY;AACf,YAAI,KAAK,KAAK;AACd;AACA,YAAIO,mBAAkB,KAAK,KAAK,GAAG;AACjC,mBAAS,IAAI,IAAI;AAAA,QACnB;AACA;AAAA,MACF;AAAA,MACA,KAAKZ,cAAa;AAChB,YAAI,IAAI,QAAQ;AACd,gBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,cAAI,cAAc,KAAK;AACrB,gBAAI,OAAO,IAAI,GAAG,KAAK;AAAA,UACzB,OAAO;AACL,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF,OAAO;AACL,cAAI,KAAK,KAAK;AAAA,QAChB;AACA,YAAI,SAAS,IAAI,IAAI,GAAG;AACtB,mBAAS,OAAO,IAAI;AAAA,QACtB;AACA;AACA;AAAA,MACF;AAAA,MACA,KAAKM,UAAS;AACZ,YAAI,IAAI,QAAQ;AACd,gBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,cACE,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,SAASL,YAAW,SAASE,MAAK;AACpC,cAAI,KAAK,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,UAAU,CAAC,OAAe,MAAe,CAAC,MAAc;AACnE,QAAM,EAAE,SAAS,GAAG,IAAI;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAIU,YAAW,KAAK,KAAK,GAAG;AAC1B,UAAI,WAAW,UAAU;AACvB,eAAO;AAAA,MACT,OAAO;AACL,cAAMI,iBAAgB,WAAW,OAAO,GAAG;AAC3C,YAAI,SAASA,cAAa,GAAG;AAC3B,iBAAOA;AAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,WAAW,CAACN,aAAY,KAAK,KAAK,GAAG;AACnC,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,YAAY,EAAE,KAAK;AAAA,EACnC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWJ;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,aAAS,gCAAS,EAAE,KAAK,MAAM,CAAC;AACtC,QAAM,SAASS,aAAY,QAAQ,GAAG;AACtC,MAAI,oBAAwB,uBAAK,OAAO,KAAK,EAAE,GAAG;AAAA,IAChD,kBAAkB;AAAA,EACpB,CAAC;AACD,MAAI,iBAAiB,KAAK,KAAK,GAAG;AAChC,QAAI,iBAAiB,KAAK,aAAa,GAAG;AACxC,YAAM,CAAC,EAAE,KAAK,IAAI,IAAI,cAAc;AAAA,QAClC;AAAA,MACF;AACA,sBAAgB,GAAG,iBAAiB,OAAO,GAAG,GAAGP,IAAG,CAAC,GAAG,IAAI;AAAA,IAC9D;AAEA,QACE,iBACA,CAAC,iBAAiB,KAAK,aAAa,KACpC,WAAW,UACX;AACA,sBAAgB,QAAQ,aAAa;AAAA,IACvC;AAAA,EACF;AACA,MAAI,WAAW,UAAU;AACvB,QAAI,aAAa,KAAK,aAAa,KAAK,CAAC,cAAc,SAAS,KAAK,GAAG;AACtE,sBAAgB,cAAc,eAAe,GAAG;AAAA,IAClD,WAAW,gBAAgB,KAAK,aAAa,GAAG;AAC9C,YAAM,CAAC,EAAE,GAAG,IAAI,cAAc,MAAM,eAAe;AACnD,sBAAgB,QAAQ,iBAAiB,OAAO,GAAG,GAAGA,IAAG,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,WAAS,UAAU,aAAa;AAChC,SAAO;AACT;;;ASj7BA,IAAMS,aAAY;AAClB,IAAM,YAAY,GAAG,GAAG,MAAM,KAAK;AACnC,IAAM,gBAAgB,GAAG,SAAS,IAAI,GAAG;AACzC,IAAM,UAAU,GAAG,GAAG,MAAM,MAAM;AAClC,IAAM,cAAc,GAAG,OAAO,IAAI,GAAG;AACrC,IAAM,mBAAmB,GAAG,YAAY,MAAM,MAAM;AACpD,IAAM,eAAe,GAAG,YAAY,MAAM,MAAM;AAChD,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,SAAS,GAAG,GAAG,SAAS,GAAG;AACjC,IAAM,SAAS,GAAG,GAAG,SAAS,GAAG;AACjC,IAAM,QAAQ,YAAY,GAAG;AAC7B,IAAM,SAAS,aAAa,GAAG;AAC/B,IAAM,QAAQ,GAAG,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,WAAW;AAC1E,IAAM,QAAQ;AAAA,EACZ,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM;AAAA,EAC3C,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM;AAAA,EAC3C,MAAM,GAAG,IAAI,MAAM,IAAI,WAAW,WAAW,GAAG,IAAI,MAAM,IAAI,WAAW;AAAA,EACzE,MAAM,GAAG,IAAI,KAAK,WAAW,GAAG,IAAI,MAAM;AAAA,EAC1C,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,KAAK;AAAA,EAC1C,MAAM,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG;AACvC,EAAE,KAAK,GAAG;AACV,IAAM,QAAQ;AAAA,EACZ,MAAM,MAAM,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACzE,MAAM,MAAM,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACzE,MAAM,KAAK,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACxE,MAAM,MAAM,WAAW,WAAW,WAAW,KAAK,WAAW,WAAW;AAAA,EACxE,MAAM,GAAG,WAAW,WAAW,WAAW,GAAG,WAAW,WAAW;AACrE,EAAE,KAAK,GAAG;AACV,IAAM,aAAa;AACnB,IAAM,WAAW;AAAA,EACf,GAAG,UAAU,UAAU,UAAU;AAAA,EACjC,GAAG,YAAY;AAAA,EACf,MAAM,gBAAgB,WAAW,gBAAgB;AACnD,EAAE,KAAK,GAAG;AACV,IAAM,YAAY;AAClB,IAAM,aAAa,WAAW,SAAS;AACvC,IAAM,cAAc,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK;AACvD,IAAM,iBAAiB,eAAe,GAAG,aAAa,GAAG,UAAU,GAAG,aAAa,GAAG;AACtF,IAAM,iBAAiB,YAAY,OAAO,IAAI,MAAM;AAuBpD,IAAM,WAAW;AACjB,IAAM,gBAAgB;AAOf,IAAM,kBAAkB,CAAC,UAA0B;AACxD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,SAAS,KAAK,KAAK,GAAG;AACxB,YAAM,CAAC,EAAE,IAAI,IAAI,MAAM,MAAM,aAAa;AAC1C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,uBAAuB,CAAC,OAAe,SAA0B;AAC5E,MAAI,SAAS,KAAK,KAAK,SAAS,IAAI,GAAG;AACrC,YAAQ,MAAM,KAAK;AACnB,WAAO,KAAK,KAAK;AACjB,QAAI,aAAa;AACjB,QAAI,mCAAmC,KAAK,IAAI,GAAG;AAOjD,mBAAa;AAAA,QACX,MAAM,SAAS,IAAI,cAAc,WAAW,cAAc;AAAA,QAC1D,GAAG,cAAc,aAAa,SAAS,IAAI,cAAc;AAAA,MAC3D,EAAE,KAAK,GAAG;AAAA,IACZ,WAAW,mCAAmC,KAAK,IAAI,GAAG;AAMxD,mBAAa;AAAA,QACX,MAAM,SAAS,cAAc,QAAQ,aAAa,WAAW,YAAY,cAAc;AAAA,QACvF,MAAM,QAAQ,cAAc,SAAS,aAAa,WAAW,YAAY,cAAc;AAAA,QACvF,GAAG,WAAW,UAAU,cAAc;AAAA,QACtC,GAAG,cAAc,UAAU,SAAS,cAAc,QAAQ,aAAa,WAAW;AAAA,QAClF,GAAG,cAAc,UAAU,QAAQ,cAAc,SAAS,aAAa,WAAW;AAAA,QAClF,GAAG,cAAc,UAAU,WAAW;AAAA,MACxC,EAAE,KAAK,GAAG;AAAA,IACZ,WAAW,kCAAkC,KAAK,IAAI,GAAG;AAOvD,mBAAa;AAAA,QACX,GAAG,UAAU,UAAU,WAAW,YAAY,cAAc;AAAA,QAC5D,GAAG,WAAW,UAAU,cAAc;AAAA,QACtC,GAAG,cAAc,UAAU,UAAU,YAAY,WAAW;AAAA,MAC9D,EAAE,KAAK,GAAG;AAAA,IACZ;AACA,QAAI,YAAY;AACd,YAAM,MAAM,IAAI,OAAO,OAAO,UAAU,IAAI;AAC5C,aAAO,IAAI,KAAK,KAAK;AAAA,IACvB;AAAA,EACF;AACA,SAAO;AACT;AASO,IAAM,wBAAwB,CACnC,MACA,MACA,MAAe,CAAC,MACJ;AACZ,MAAI,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AAC1C,UAAM,YAAY,kCAAkC,KAAK,IAAI,IACzD,gBACA;AACJ,UAAM,eAAe,IAAI,OAAO,OAAO,SAAS,IAAI;AACpD,UAAM,eAAe,IAAI,OAAO,aAAa,SAAS,UAAU;AAChE,UAAM,MAAM,CAAC;AACb,eAAW,QAAQ,MAAM;AACvB,UAAI,SAAS,IAAI,GAAG;AAClB,YAAI,aAAa,KAAK,IAAI,GAAG;AAC3B,cAAI,KAAK,MAAM;AAAA,QACjB,OAAO;AACL,gBAAM,QAAQ,KAAK,QAAQ,cAAc,EAAE;AAC3C,cAAI,QAAQ,OAAO,GAAG,GAAG;AACvB,gBAAI,KAAK,OAAO;AAAA,UAClB,OAAO;AACL,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,QAAQ,IAAI,KAAK,GAAG;AAC1B,WAAO,+BAA+B,KAAK,KAAK;AAAA,EAClD;AACA,SAAO;AACT;AAQO,IAAM,gBAAgB,CAC3B,OACA,MAAe,CAAC,MACI;AACpB,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,UAAM,WAAmB;AAAA,MACvB;AAAA,QACE,WAAWA;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MACF;AAAA,MACA;AAAA,IACF;AACA,UAAM,eAAe,SAAS,QAAQ;AACtC,QAAI,wBAAwB,WAAW;AACrC,UAAI,aAAa,QAAQ;AACvB,eAAO;AAAA,MACT;AACA,aAAO,aAAa;AAAA,IACtB;AACA,UAAM,OAAO,gBAAgB,KAAK;AAClC,UAAM,YAAY,MAAM,QAAQ,UAAU,EAAE,EAAE,QAAQ,OAAO,EAAE;AAC/D,QAAI,QAAQ,WAAW;AACrB,YAAM,CAAC,kBAAkB,IAAI,GAAG,UAAU,IAAI,WAAW,WAAW;AAAA,QAClE,WAAW;AAAA,MACb,CAAC;AACD,YAAM,YAAY,kCAAkC,KAAK,IAAI,IACzD,gBACA;AACJ,YAAM,eAAe,IAAI,OAAO,aAAa,SAAS,UAAU;AAChE,UAAI,cAAc;AAClB,UAAI,aAAa,KAAK,eAAe,GAAG;AACtC,cAAM,YAAY,gBAAgB,QAAQ,cAAc,EAAE;AAC1D,YAAI,QAAQ,WAAW,GAAG,GAAG;AAC3B,wBAAc;AAAA,QAChB;AAAA,MACF,WAAW,QAAQ,iBAAiB,GAAG,GAAG;AACxC,sBAAc;AAAA,MAChB;AACA,UAAI,aAAa;AACf,mBAAW,QAAQ,eAAe;AAClC,cAAM,QAAQ,sBAAsB,YAAY,MAAM,GAAG;AACzD,YAAI,OAAO;AACT,gBAAM,MAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UACjB;AACA,mBAAS,UAAU,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,WAAW,SAAS,GAAG;AAChC,cAAM,eAAe;AACrB,cAAM,QACJ,qBAAqB,cAAc,IAAI,KACvC,sBAAsB,YAAY,MAAM,GAAG;AAC7C,YAAI,OAAO;AACT,gBAAM,MAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UACjB;AACA,mBAAS,UAAU,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,aAAS,UAAU,IAAI;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAe;AACvE,QAAM,WAAW,cAAc,OAAO,GAAG;AACzC,SAAO,aAAa;AACtB;;;AChQA,IAAMC,aAAY;AAGlB,IAAMC,eAAc,IAAI,OAAO,WAAW;AAC1C,IAAMC,cAAa,IAAI,OAAO,UAAU;AACxC,IAAMC,cAAa,IAAI,OAAO,UAAU;AAQjC,IAAM,aAAa,CACxB,OACA,MAAe,CAAC,MACQ;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAK;AACnB,QAAI,CAAC,OAAO;AACV,aAAO,IAAI,WAAW;AAAA,IACxB;AAAA,EACF,OAAO;AACL,WAAO,IAAI,WAAW;AAAA,EACxB;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWH;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,MAAIG,YAAW,KAAK,KAAK,GAAG;AAC1B,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,SAAS,aAAa,GAAG;AAC3B,cAAQ;AAAA,IACV,OAAO;AACL,eAAS,UAAU,IAAI;AACvB,aAAO,IAAI,WAAW;AAAA,IACxB;AAAA,EACF;AACA,MAAID,YAAW,KAAK,KAAK,GAAG;AAC1B,UAAM,gBAAgB,qBAAqB,OAAO,GAAG;AACrD,QAAI,SAAS,aAAa,GAAG;AAC3B,cAAQ;AAAA,IACV,OAAO;AACL,eAAS,UAAU,IAAI;AACvB,aAAO,IAAI,WAAW;AAAA,IACxB;AAAA,EACF,WAAWD,aAAY,KAAK,KAAK,GAAG;AAClC,YAAQ,QAAQ,OAAO,GAAG;AAAA,EAC5B;AACA,MAAI,MAAM,WAAW,WAAW,GAAG;AACjC,UAAM,YAAY,gBAAgB,GAAG;AACrC,cAAU,SAAS;AACnB,cAAU,WAAW;AACrB,UAAM,gBAAgB,aAAa,OAAO,SAAS;AACnD,aAAS,UAAU,aAAa;AAChC,WAAO;AAAA,EACT;AACA,WAAS,UAAU,KAAK;AACxB,SAAO;AACT;AAOO,IAAM,cAAc,CAAC,UAA0B;AACpD,QAAM,MAAM,kBAAkB,KAAK;AACnC,SAAO;AACT;AASO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO;AAAA,IACT;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,EAAE,QAAQ,MAAM,IAAI;AAC1B,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWD;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,MAAI;AACJ,MAAI,WAAW;AACf,MAAI,OAAO;AACT,QAAI,SAAS;AACb,UAAM,aAAa,OAAO,GAAG;AAAA,EAC/B,OAAO;AACL,QAAI,SAAS;AACb,UAAM,aAAa,OAAO,GAAG;AAAA,EAC/B;AACA,MAAI,SAAS,GAAG,GAAG;AACjB,aAAS,UAAU,GAAG;AACtB,WAAO;AAAA,EACT;AACA,WAAS,UAAU,IAAI;AACvB,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,MAAI,SAAS;AACb,QAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,MAAI,SAAS;AACb,QAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,eAAe,CAC1B,OACA,MAAe,CAAC,MACE;AAClB,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,MAAM,oBAAoB,OAAO,GAAG;AAC1C,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,eAAe,CAC1B,OACA,MAAe,CAAC,MACE;AAClB,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,MAAM,oBAAoB,OAAO,GAAG;AAC1C,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,MAAM,kBAAkB,OAAO,GAAG;AACxC,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,aAAa,CAAC,OAAe,MAAe,CAAC,MAAqB;AAC7E,MAAI,SAAS,KAAK,GAAG;AACnB,UAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,QAAI,yBAAyB,YAAY;AACvC,aAAO,CAAC,GAAG,GAAG,GAAG,CAAC;AAAA,IACpB;AACA,YAAQ,cAAc,YAAY;AAAA,EACpC,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAWA;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACA,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,MAAI;AACJ,MAAI,MAAM,WAAW,QAAQ,GAAG;AAC9B,KAAC,EAAE,GAAG,GAAG,IAAI,eAAe,OAAO,GAAG;AAAA,EACxC,OAAO;AACL,KAAC,EAAE,GAAG,GAAG,IAAI,gBAAgB,OAAO,GAAG;AAAA,EACzC;AACA,WAAS,UAAU,GAAG;AACtB,SAAO;AACT;AAQO,IAAM,gBAAgB,CAC3B,OACA,MAAe,CAAC,MACE;AAClB,MAAI,MAAM;AACV,SAAO,WAAW,OAAO,GAAG;AAC9B;AAGO,IAAM,UAAU;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;;;AXrcO,IAAM,QAAQ;AAAA,EACnB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGO,IAAMI,WAAU,MAAM;AACtB,IAAMC,WAAU,MAAM;", "names": ["cssCalc", "isColor", "import_css_calc", "import_css_tokenizer", "import_css_tokenizer", "res", "val", "res", "cs", "pA", "NAMESPACE", "import_css_tokenizer", "PAREN_CLOSE", "COMMENT", "EOF", "IDENT", "NUM", "PCT", "W_SPACE", "NAMESPACE", "OCT", "DEC", "HEX", "MAX_PCT", "MAX_RGB", "REG_FN_VAR", "color<PERSON><PERSON><PERSON>", "NAMESPACE", "REG_FN_CALC", "REG_FN_REL", "REG_FN_VAR", "res", "PAREN_CLOSE", "COMMENT", "EOF", "FUNC", "IDENT", "PAREN_OPEN", "W_SPACE", "NAMESPACE", "DEC", "HEX", "DEG", "DEG_HALF", "REG_COLOR", "REG_FN_COLOR", "REG_MIX", "value", "PAREN_CLOSE", "COMMENT", "DIM", "EOF", "FUNC", "PAREN_OPEN", "W_SPACE", "NAMESPACE", "TRIA", "HEX", "MAX_PCT", "REG_FN_CALC", "REG_FN_MATH_START", "REG_FN_VAR", "l", "value", "parseTokens", "resolvedValue", "NAMESPACE", "NAMESPACE", "REG_FN_CALC", "REG_FN_REL", "REG_FN_VAR", "isColor", "cssCalc"]}