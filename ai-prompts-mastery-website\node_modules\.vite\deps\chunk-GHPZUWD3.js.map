{"version": 3, "sources": ["../../highlight.js/lib/languages/clojure-repl.js"], "sourcesContent": ["/*\nLanguage: Clojure REPL\nDescription: Clojure REPL sessions\nAuthor: <PERSON> <<EMAIL>>\nRequires: clojure.js\nWebsite: https://clojure.org\nCategory: lisp\n*/\n\n/** @type LanguageFn */\nfunction clojureRepl(hljs) {\n  return {\n    name: 'Clojure REPL',\n    contains: [\n      {\n        className: 'meta',\n        begin: /^([\\w.-]+|\\s*#_)?=>/,\n        starts: {\n          end: /$/,\n          subLanguage: 'clojure'\n        }\n      }\n    ]\n  };\n}\n\nmodule.exports = clojureRepl;\n"], "mappings": ";;;;;AAAA;AAAA;AAUA,aAAS,YAAY,MAAM;AACzB,aAAO;AAAA,QACL,MAAM;AAAA,QACN,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,KAAK;AAAA,cACL,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}