{"version": 3, "names": ["_core", "require", "helper", "template", "statement", "helperIDs", "WeakMap", "addCallSuperHelper", "file", "has", "t", "cloneNode", "clone", "get", "addHelper", "_unused", "id", "scope", "generateUidIdentifier", "set", "fn", "CALL_SUPER", "GET_PROTOTYPE_OF", "POSSIBLE_CONSTRUCTOR_RETURN", "path", "unshiftContainer", "registerDeclaration"], "sources": ["../src/inline-callSuper-helpers.ts"], "sourcesContent": ["import { template, types as t, type File } from \"@babel/core\";\n\nconst helper = template.statement`\n  function CALL_SUPER(\n    _this,\n    derived,\n    args,\n  ) {\n    function isNativeReflectConstruct() {\n      if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n\n      // core-js@3\n      if (Reflect.construct.sham) return false;\n\n      // Proxy can't be polyfilled. Every browser implemented\n      // proxies before or at the same time as Reflect.construct,\n      // so if they support Proxy they also support Reflect.construct.\n      if (typeof Proxy === \"function\") return true;\n\n      // Since Reflect.construct can't be properly polyfilled, some\n      // implementations (e.g. core-js@2) don't set the correct internal slots.\n      // Those polyfills don't allow us to subclass built-ins, so we need to\n      // use our fallback implementation.\n      try {\n        // If the internal slots aren't set, this throws an error similar to\n        //   TypeError: this is not a Boolean object.\n        return !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}),);\n      } catch (e) {\n        return false;\n      }\n    }\n\n    // Super\n    derived = GET_PROTOTYPE_OF(derived);\n    return POSSIBLE_CONSTRUCTOR_RETURN(\n      _this,\n      isNativeReflectConstruct()\n        ? // NOTE: This doesn't work if this.__proto__.constructor has been modified.\n          Reflect.construct(\n            derived,\n            args || [],\n            GET_PROTOTYPE_OF(_this).constructor,\n          )\n        : derived.apply(_this, args),\n    );\n  }\n`;\n\nconst helperIDs = new WeakMap();\n\nexport default function addCallSuperHelper(file: File) {\n  if (helperIDs.has(file)) {\n    // TODO: Only use t.cloneNode in Babel 8\n    // t.cloneNode isn't supported in every version\n    return (t.cloneNode || t.clone)(helperIDs.get(file));\n  }\n\n  try {\n    return file.addHelper(\"callSuper\");\n  } catch {\n    // old Babel doesn't support the helper.\n  }\n\n  const id = file.scope.generateUidIdentifier(\"callSuper\");\n  helperIDs.set(file, id);\n\n  const fn = helper({\n    CALL_SUPER: id,\n    GET_PROTOTYPE_OF: file.addHelper(\"getPrototypeOf\"),\n    POSSIBLE_CONSTRUCTOR_RETURN: file.addHelper(\"possibleConstructorReturn\"),\n  });\n\n  file.path.unshiftContainer(\"body\", [fn]);\n  file.scope.registerDeclaration(file.path.get(\"body.0\"));\n\n  return t.cloneNode(id);\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,OAAA;AAEA,MAAMC,MAAM,GAAGC,cAAQ,CAACC,SAAS;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,CAAC;AAED,MAAMC,SAAS,GAAG,IAAIC,OAAO,CAAC,CAAC;AAEhB,SAASC,kBAAkBA,CAACC,IAAU,EAAE;EACrD,IAAIH,SAAS,CAACI,GAAG,CAACD,IAAI,CAAC,EAAE;IAGvB,OAAO,CAACE,WAAC,CAACC,SAAS,IAAID,WAAC,CAACE,KAAK,EAAEP,SAAS,CAACQ,GAAG,CAACL,IAAI,CAAC,CAAC;EACtD;EAEA,IAAI;IACF,OAAOA,IAAI,CAACM,SAAS,CAAC,WAAW,CAAC;EACpC,CAAC,CAAC,OAAAC,OAAA,EAAM,CAER;EAEA,MAAMC,EAAE,GAAGR,IAAI,CAACS,KAAK,CAACC,qBAAqB,CAAC,WAAW,CAAC;EACxDb,SAAS,CAACc,GAAG,CAACX,IAAI,EAAEQ,EAAE,CAAC;EAEvB,MAAMI,EAAE,GAAGlB,MAAM,CAAC;IAChBmB,UAAU,EAAEL,EAAE;IACdM,gBAAgB,EAAEd,IAAI,CAACM,SAAS,CAAC,gBAAgB,CAAC;IAClDS,2BAA2B,EAAEf,IAAI,CAACM,SAAS,CAAC,2BAA2B;EACzE,CAAC,CAAC;EAEFN,IAAI,CAACgB,IAAI,CAACC,gBAAgB,CAAC,MAAM,EAAE,CAACL,EAAE,CAAC,CAAC;EACxCZ,IAAI,CAACS,KAAK,CAACS,mBAAmB,CAAClB,IAAI,CAACgB,IAAI,CAACX,GAAG,CAAC,QAAQ,CAAC,CAAC;EAEvD,OAAOH,WAAC,CAACC,SAAS,CAACK,EAAE,CAAC;AACxB", "ignoreList": []}