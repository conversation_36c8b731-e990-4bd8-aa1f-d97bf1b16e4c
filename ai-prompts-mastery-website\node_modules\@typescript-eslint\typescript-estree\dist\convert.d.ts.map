{"version": 3, "file": "convert.d.ts", "sourceRoot": "", "sources": ["../src/convert.ts"], "names": [], "mappings": "AAGA,OAAO,KAAK,EAAE,MAAM,YAAY,CAAC;AAGjC,OAAO,KAAK,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AA6B5C,OAAO,KAAK,EACV,aAAa,EACb,2BAA2B,EAC5B,MAAM,kBAAkB,CAAC;AAC1B,OAAO,KAAK,EAAE,wBAAwB,EAAE,MAAM,gCAAgC,CAAC;AAC/E,OAAO,KAAK,EAAE,QAAQ,EAAoB,MAAM,EAAE,MAAM,aAAa,CAAC;AAKtE,MAAM,WAAW,gBAAgB;IAC/B,eAAe,CAAC,EAAE,OAAO,CAAC;IAC1B,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC,sBAAsB,CAAC,EAAE,OAAO,CAAC;IACjC,kCAAkC,CAAC,EAAE,OAAO,CAAC;CAC9C;AAED;;;;GAIG;AACH,wBAAgB,YAAY,CAC1B,KAAK,EAAE,wBAAwB,GAAG,EAAE,CAAC,sBAAsB,GAC1D,OAAO,CAMT;AAED,MAAM,WAAW,OAAO;IACtB,qBAAqB,EAAE,2BAA2B,CAAC;IACnD,qBAAqB,EAAE,aAAa,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;CAC7D;AAED,qBAAa,SAAS;;IACpB,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAgB;IACpC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAmB;IAC3C,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAiB;IACvD,OAAO,CAAC,QAAQ,CAAC,qBAAqB,CAAiB;IAEvD,OAAO,CAAC,YAAY,CAAS;IAE7B;;;;;OAKG;gBACS,GAAG,EAAE,EAAE,CAAC,UAAU,EAAE,OAAO,CAAC,EAAE,gBAAgB;IAK1D,UAAU,IAAI,OAAO;IAOrB,cAAc,IAAI,QAAQ,CAAC,OAAO;IAIlC;;;;;;OAMG;IACH,OAAO,CAAC,SAAS;IA8BjB;;;;;OAKG;IACH,OAAO,CAAC,UAAU;IAiFlB;;OAEG;IACH,OAAO,CAAC,uBAAuB;IAW/B;;;;;OAKG;IACH,OAAO,CAAC,cAAc;IAItB;;;;;OAKG;IACH,OAAO,CAAC,YAAY;IAIpB,OAAO,CAAC,UAAU;IAelB,OAAO,CAAC,oCAAoC;IAe5C;;;;;;OAMG;IACH,OAAO,CAAC,qBAAqB;IAsB7B;;;;;OAKG;IACH,OAAO,CAAC,sBAAsB;IAiC9B;;;;;OAKG;IACH,OAAO,CAAC,gDAAgD;IAexD;;;;OAIG;IACH,OAAO,CAAC,kDAAkD;IAmB1D;;;;OAIG;IACH,OAAO,CAAC,iBAAiB;IAgBzB,OAAO,CAAC,sBAAsB;IA4C9B;;;;OAIG;IACH,OAAO,CAAC,UAAU;IA+FlB,OAAO,CAAC,oBAAoB;IAW5B,OAAO,CAAC,+BAA+B;IAiDvC;;;;OAIG;IACH,OAAO,CAAC,iBAAiB;IA8BzB,OAAO,CAAC,sBAAsB;IAoC9B,OAAO,CAAC,uBAAuB;IAQ/B;;;;OAIG;IACH,OAAO,CAAC,iBAAiB;IAczB,OAAO,CAAC,qBAAqB;IAsB7B;;;;;;;OAOG;IACH,OAAO,CAAC,WAAW;CAouFpB"}