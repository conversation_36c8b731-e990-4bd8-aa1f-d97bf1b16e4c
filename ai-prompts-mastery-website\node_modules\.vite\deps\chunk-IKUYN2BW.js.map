{"version": 3, "sources": ["../../highlight.js/lib/languages/mercury.js"], "sourcesContent": ["/*\nLanguage: Mercury\nAuthor: m<PERSON><PERSON> <<EMAIL>>\nDescription: Mercury is a logic/functional programming language which combines the clarity and expressiveness of declarative programming with advanced static analysis and error detection features.\nWebsite: https://www.mercurylang.org\n*/\n\nfunction mercury(hljs) {\n  const KEYWORDS = {\n    keyword:\n      'module use_module import_module include_module end_module initialise ' +\n      'mutable initialize finalize finalise interface implementation pred ' +\n      'mode func type inst solver any_pred any_func is semidet det nondet ' +\n      'multi erroneous failure cc_nondet cc_multi typeclass instance where ' +\n      'pragma promise external trace atomic or_else require_complete_switch ' +\n      'require_det require_semidet require_multi require_nondet ' +\n      'require_cc_multi require_cc_nondet require_erroneous require_failure',\n    meta:\n      // pragma\n      'inline no_inline type_spec source_file fact_table obsolete memo ' +\n      'loop_check minimal_model terminates does_not_terminate ' +\n      'check_termination promise_equivalent_clauses ' +\n      // preprocessor\n      'foreign_proc foreign_decl foreign_code foreign_type ' +\n      'foreign_import_module foreign_export_enum foreign_export ' +\n      'foreign_enum may_call_mercury will_not_call_mercury thread_safe ' +\n      'not_thread_safe maybe_thread_safe promise_pure promise_semipure ' +\n      'tabled_for_io local untrailed trailed attach_to_io_state ' +\n      'can_pass_as_mercury_type stable will_not_throw_exception ' +\n      'may_modify_trail will_not_modify_trail may_duplicate ' +\n      'may_not_duplicate affects_liveness does_not_affect_liveness ' +\n      'doesnt_affect_liveness no_sharing unknown_sharing sharing',\n    built_in:\n      'some all not if then else true fail false try catch catch_any ' +\n      'semidet_true semidet_false semidet_fail impure_true impure semipure'\n  };\n\n  const COMMENT = hljs.COMMENT('%', '$');\n\n  const NUMCODE = {\n    className: 'number',\n    begin: \"0'.\\\\|0[box][0-9a-fA-F]*\"\n  };\n\n  const ATOM = hljs.inherit(hljs.APOS_STRING_MODE, {\n    relevance: 0\n  });\n  const STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {\n    relevance: 0\n  });\n  const STRING_FMT = {\n    className: 'subst',\n    begin: '\\\\\\\\[abfnrtv]\\\\|\\\\\\\\x[0-9a-fA-F]*\\\\\\\\\\\\|%[-+# *.0-9]*[dioxXucsfeEgGp]',\n    relevance: 0\n  };\n  STRING.contains = STRING.contains.slice(); // we need our own copy of contains\n  STRING.contains.push(STRING_FMT);\n\n  const IMPLICATION = {\n    className: 'built_in',\n    variants: [\n      {\n        begin: '<=>'\n      },\n      {\n        begin: '<=',\n        relevance: 0\n      },\n      {\n        begin: '=>',\n        relevance: 0\n      },\n      {\n        begin: '/\\\\\\\\'\n      },\n      {\n        begin: '\\\\\\\\/'\n      }\n    ]\n  };\n\n  const HEAD_BODY_CONJUNCTION = {\n    className: 'built_in',\n    variants: [\n      {\n        begin: ':-\\\\|-->'\n      },\n      {\n        begin: '=',\n        relevance: 0\n      }\n    ]\n  };\n\n  return {\n    name: 'Mercury',\n    aliases: [\n      'm',\n      'moo'\n    ],\n    keywords: KEYWORDS,\n    contains: [\n      IMPLICATION,\n      HEAD_BODY_CONJUNCTION,\n      COMMENT,\n      hljs.C_BLOCK_COMMENT_MODE,\n      NUMCODE,\n      hljs.NUMBER_MODE,\n      ATOM,\n      STRING,\n      { // relevance booster\n        begin: /:-/\n      },\n      { // relevance booster\n        begin: /\\.$/\n      }\n    ]\n  };\n}\n\nmodule.exports = mercury;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,QAAQ,MAAM;AACrB,YAAM,WAAW;AAAA,QACf,SACE;AAAA,QAOF;AAAA;AAAA,UAEE;AAAA;AAAA,QAaF,UACE;AAAA,MAEJ;AAEA,YAAM,UAAU,KAAK,QAAQ,KAAK,GAAG;AAErC,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,OAAO,KAAK,QAAQ,KAAK,kBAAkB;AAAA,QAC/C,WAAW;AAAA,MACb,CAAC;AACD,YAAM,SAAS,KAAK,QAAQ,KAAK,mBAAmB;AAAA,QAClD,WAAW;AAAA,MACb,CAAC;AACD,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,aAAO,WAAW,OAAO,SAAS,MAAM;AACxC,aAAO,SAAS,KAAK,UAAU;AAE/B,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,wBAAwB;AAAA,QAC5B,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA,KAAK;AAAA,UACL;AAAA,UACA;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}