{"version": 3, "sources": ["../../highlight.js/lib/languages/crystal.js"], "sourcesContent": ["/*\nLanguage: Crystal\nAuthor: T<PERSON><PERSON><PERSON><PERSON><PERSON> Kitsune <<EMAIL>>\nWebsite: https://crystal-lang.org\n*/\n\n/** @type LanguageFn */\nfunction crystal(hljs) {\n  const INT_SUFFIX = '(_?[ui](8|16|32|64|128))?';\n  const FLOAT_SUFFIX = '(_?f(32|64))?';\n  const CRYSTAL_IDENT_RE = '[a-zA-Z_]\\\\w*[!?=]?';\n  const CRYSTAL_METHOD_RE = '[a-zA-Z_]\\\\w*[!?=]?|[-+~]@|<<|>>|[=!]~|===?|<=>|[<>]=?|\\\\*\\\\*|[-/+%^&*~|]|//|//=|&[-+*]=?|&\\\\*\\\\*|\\\\[\\\\][=?]?';\n  const CRYSTAL_PATH_RE = '[A-Za-z_]\\\\w*(::\\\\w+)*(\\\\?|!)?';\n  const CRYSTAL_KEYWORDS = {\n    $pattern: CRYSTAL_IDENT_RE,\n    keyword:\n      'abstract alias annotation as as? asm begin break case class def do else elsif end ensure enum extend for fun if ' +\n      'include instance_sizeof is_a? lib macro module next nil? of out pointerof private protected rescue responds_to? ' +\n      'return require select self sizeof struct super then type typeof union uninitialized unless until verbatim when while with yield ' +\n      '__DIR__ __END_LINE__ __FILE__ __LINE__',\n    literal: 'false nil true'\n  };\n  const SUBST = {\n    className: 'subst',\n    begin: /#\\{/,\n    end: /\\}/,\n    keywords: CRYSTAL_KEYWORDS\n  };\n  const EXPANSION = {\n    className: 'template-variable',\n    variants: [\n      {\n        begin: '\\\\{\\\\{',\n        end: '\\\\}\\\\}'\n      },\n      {\n        begin: '\\\\{%',\n        end: '%\\\\}'\n      }\n    ],\n    keywords: CRYSTAL_KEYWORDS\n  };\n\n  function recursiveParen(begin, end) {\n    const\n        contains = [\n          {\n            begin: begin,\n            end: end\n          }\n        ];\n    contains[0].contains = contains;\n    return contains;\n  }\n  const STRING = {\n    className: 'string',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /`/,\n        end: /`/\n      },\n      {\n        begin: '%[Qwi]?\\\\(',\n        end: '\\\\)',\n        contains: recursiveParen('\\\\(', '\\\\)')\n      },\n      {\n        begin: '%[Qwi]?\\\\[',\n        end: '\\\\]',\n        contains: recursiveParen('\\\\[', '\\\\]')\n      },\n      {\n        begin: '%[Qwi]?\\\\{',\n        end: /\\}/,\n        contains: recursiveParen(/\\{/, /\\}/)\n      },\n      {\n        begin: '%[Qwi]?<',\n        end: '>',\n        contains: recursiveParen('<', '>')\n      },\n      {\n        begin: '%[Qwi]?\\\\|',\n        end: '\\\\|'\n      },\n      {\n        begin: /<<-\\w+$/,\n        end: /^\\s*\\w+$/\n      }\n    ],\n    relevance: 0\n  };\n  const Q_STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: '%q\\\\(',\n        end: '\\\\)',\n        contains: recursiveParen('\\\\(', '\\\\)')\n      },\n      {\n        begin: '%q\\\\[',\n        end: '\\\\]',\n        contains: recursiveParen('\\\\[', '\\\\]')\n      },\n      {\n        begin: '%q\\\\{',\n        end: /\\}/,\n        contains: recursiveParen(/\\{/, /\\}/)\n      },\n      {\n        begin: '%q<',\n        end: '>',\n        contains: recursiveParen('<', '>')\n      },\n      {\n        begin: '%q\\\\|',\n        end: '\\\\|'\n      },\n      {\n        begin: /<<-'\\w+'$/,\n        end: /^\\s*\\w+$/\n      }\n    ],\n    relevance: 0\n  };\n  const REGEXP = {\n    begin: '(?!%\\\\})(' + hljs.RE_STARTERS_RE + '|\\\\n|\\\\b(case|if|select|unless|until|when|while)\\\\b)\\\\s*',\n    keywords: 'case if select unless until when while',\n    contains: [\n      {\n        className: 'regexp',\n        contains: [\n          hljs.BACKSLASH_ESCAPE,\n          SUBST\n        ],\n        variants: [\n          {\n            begin: '//[a-z]*',\n            relevance: 0\n          },\n          {\n            begin: '/(?!\\\\/)',\n            end: '/[a-z]*'\n          }\n        ]\n      }\n    ],\n    relevance: 0\n  };\n  const REGEXP2 = {\n    className: 'regexp',\n    contains: [\n      hljs.BACKSLASH_ESCAPE,\n      SUBST\n    ],\n    variants: [\n      {\n        begin: '%r\\\\(',\n        end: '\\\\)',\n        contains: recursiveParen('\\\\(', '\\\\)')\n      },\n      {\n        begin: '%r\\\\[',\n        end: '\\\\]',\n        contains: recursiveParen('\\\\[', '\\\\]')\n      },\n      {\n        begin: '%r\\\\{',\n        end: /\\}/,\n        contains: recursiveParen(/\\{/, /\\}/)\n      },\n      {\n        begin: '%r<',\n        end: '>',\n        contains: recursiveParen('<', '>')\n      },\n      {\n        begin: '%r\\\\|',\n        end: '\\\\|'\n      }\n    ],\n    relevance: 0\n  };\n  const ATTRIBUTE = {\n    className: 'meta',\n    begin: '@\\\\[',\n    end: '\\\\]',\n    contains: [\n      hljs.inherit(hljs.QUOTE_STRING_MODE, {\n        className: 'meta-string'\n      })\n    ]\n  };\n  const CRYSTAL_DEFAULT_CONTAINS = [\n    EXPANSION,\n    STRING,\n    Q_STRING,\n    REGEXP2,\n    REGEXP,\n    ATTRIBUTE,\n    hljs.HASH_COMMENT_MODE,\n    {\n      className: 'class',\n      beginKeywords: 'class module struct',\n      end: '$|;',\n      illegal: /=/,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: CRYSTAL_PATH_RE\n        }),\n        { // relevance booster for inheritance\n          begin: '<'\n        }\n      ]\n    },\n    {\n      className: 'class',\n      beginKeywords: 'lib enum union',\n      end: '$|;',\n      illegal: /=/,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: CRYSTAL_PATH_RE\n        })\n      ]\n    },\n    {\n      beginKeywords: 'annotation',\n      end: '$|;',\n      illegal: /=/,\n      contains: [\n        hljs.HASH_COMMENT_MODE,\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: CRYSTAL_PATH_RE\n        })\n      ],\n      relevance: 2\n    },\n    {\n      className: 'function',\n      beginKeywords: 'def',\n      end: /\\B\\b/,\n      contains: [\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: CRYSTAL_METHOD_RE,\n          endsParent: true\n        })\n      ]\n    },\n    {\n      className: 'function',\n      beginKeywords: 'fun macro',\n      end: /\\B\\b/,\n      contains: [\n        hljs.inherit(hljs.TITLE_MODE, {\n          begin: CRYSTAL_METHOD_RE,\n          endsParent: true\n        })\n      ],\n      relevance: 2\n    },\n    {\n      className: 'symbol',\n      begin: hljs.UNDERSCORE_IDENT_RE + '(!|\\\\?)?:',\n      relevance: 0\n    },\n    {\n      className: 'symbol',\n      begin: ':',\n      contains: [\n        STRING,\n        {\n          begin: CRYSTAL_METHOD_RE\n        }\n      ],\n      relevance: 0\n    },\n    {\n      className: 'number',\n      variants: [\n        {\n          begin: '\\\\b0b([01_]+)' + INT_SUFFIX\n        },\n        {\n          begin: '\\\\b0o([0-7_]+)' + INT_SUFFIX\n        },\n        {\n          begin: '\\\\b0x([A-Fa-f0-9_]+)' + INT_SUFFIX\n        },\n        {\n          begin: '\\\\b([1-9][0-9_]*[0-9]|[0-9])(\\\\.[0-9][0-9_]*)?([eE]_?[-+]?[0-9_]*)?' + FLOAT_SUFFIX + '(?!_)'\n        },\n        {\n          begin: '\\\\b([1-9][0-9_]*|0)' + INT_SUFFIX\n        }\n      ],\n      relevance: 0\n    }\n  ];\n  SUBST.contains = CRYSTAL_DEFAULT_CONTAINS;\n  EXPANSION.contains = CRYSTAL_DEFAULT_CONTAINS.slice(1); // without EXPANSION\n\n  return {\n    name: 'Crystal',\n    aliases: [ 'cr' ],\n    keywords: CRYSTAL_KEYWORDS,\n    contains: CRYSTAL_DEFAULT_CONTAINS\n  };\n}\n\nmodule.exports = crystal;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,QAAQ,MAAM;AACrB,YAAM,aAAa;AACnB,YAAM,eAAe;AACrB,YAAM,mBAAmB;AACzB,YAAM,oBAAoB;AAC1B,YAAM,kBAAkB;AACxB,YAAM,mBAAmB;AAAA,QACvB,UAAU;AAAA,QACV,SACE;AAAA,QAIF,SAAS;AAAA,MACX;AACA,YAAM,QAAQ;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,MACZ;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,MACZ;AAEA,eAAS,eAAe,OAAO,KAAK;AAClC,cACI,WAAW;AAAA,UACT;AAAA,YACE;AAAA,YACA;AAAA,UACF;AAAA,QACF;AACJ,iBAAS,CAAC,EAAE,WAAW;AACvB,eAAO;AAAA,MACT;AACA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,OAAO,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,OAAO,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,MAAM,IAAI;AAAA,UACrC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,KAAK,GAAG;AAAA,UACnC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,OAAO,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,OAAO,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,MAAM,IAAI;AAAA,UACrC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,KAAK,GAAG;AAAA,UACnC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,SAAS;AAAA,QACb,OAAO,cAAc,KAAK,iBAAiB;AAAA,QAC3C,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,UAAU;AAAA,cACR,KAAK;AAAA,cACL;AAAA,YACF;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,OAAO;AAAA,gBACP,WAAW;AAAA,cACb;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,gBACP,KAAK;AAAA,cACP;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,OAAO,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,OAAO,KAAK;AAAA,UACvC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,MAAM,IAAI;AAAA,UACrC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,eAAe,KAAK,GAAG;AAAA,UACnC;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,WAAW;AAAA,MACb;AACA,YAAM,YAAY;AAAA,QAChB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,mBAAmB;AAAA,YACnC,WAAW;AAAA,UACb,CAAC;AAAA,QACH;AAAA,MACF;AACA,YAAM,2BAA2B;AAAA,QAC/B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK;AAAA,YACL,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,YACT,CAAC;AAAA,YACD;AAAA;AAAA,cACE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK;AAAA,YACL,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA;AAAA,UACE,eAAe;AAAA,UACf,KAAK;AAAA,UACL,SAAS;AAAA,UACT,UAAU;AAAA,YACR,KAAK;AAAA,YACL,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,YACT,CAAC;AAAA,UACH;AAAA,UACA,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,UAAU;AAAA,YACR,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,cACP,YAAY;AAAA,YACd,CAAC;AAAA,UACH;AAAA,QACF;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,eAAe;AAAA,UACf,KAAK;AAAA,UACL,UAAU;AAAA,YACR,KAAK,QAAQ,KAAK,YAAY;AAAA,cAC5B,OAAO;AAAA,cACP,YAAY;AAAA,YACd,CAAC;AAAA,UACH;AAAA,UACA,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO,KAAK,sBAAsB;AAAA,UAClC,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,OAAO;AAAA,UACP,UAAU;AAAA,YACR;AAAA,YACA;AAAA,cACE,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA,QACA;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO,kBAAkB;AAAA,YAC3B;AAAA,YACA;AAAA,cACE,OAAO,mBAAmB;AAAA,YAC5B;AAAA,YACA;AAAA,cACE,OAAO,yBAAyB;AAAA,YAClC;AAAA,YACA;AAAA,cACE,OAAO,wEAAwE,eAAe;AAAA,YAChG;AAAA,YACA;AAAA,cACE,OAAO,wBAAwB;AAAA,YACjC;AAAA,UACF;AAAA,UACA,WAAW;AAAA,QACb;AAAA,MACF;AACA,YAAM,WAAW;AACjB,gBAAU,WAAW,yBAAyB,MAAM,CAAC;AAErD,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAE,IAAK;AAAA,QAChB,UAAU;AAAA,QACV,UAAU;AAAA,MACZ;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}