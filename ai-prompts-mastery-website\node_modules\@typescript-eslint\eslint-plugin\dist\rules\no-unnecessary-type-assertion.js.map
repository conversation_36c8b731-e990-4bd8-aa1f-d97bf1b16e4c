{"version": 3, "file": "no-unnecessary-type-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-assertion.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA2E;AAC3E,wEAAsE;AACtE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAQiB;AASjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,uEAAuE;YACzE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,oFAAoF;YACtF,uBAAuB,EACrB,+FAA+F;SAClG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,aAAa,EAAE;wBACb,WAAW,EAAE,iCAAiC;wBAC9C,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IACpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,eAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAE9D;;;WAGG;QACH,SAAS,gBAAgB,CAAC,IAAmB;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAExC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC5B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;YAEV,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEhC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;oBACvB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBACZ,0DAA0D;wBAC1D,OAAO,KAAK,CAAC;oBACf,CAAC;oBACD,MAAM;gBACR,CAAC;YACH,CAAC;YACD,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBAClC,IAAI,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBACvD,OAAO,KAAK,CAAC,CAAC,iEAAiE;gBACjF,CAAC;YACH,CAAC;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;WAEG;QACH,SAAS,4BAA4B,CAAC,IAAyB;YAC7D,MAAM,WAAW,GAAG,IAAA,qBAAc,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;YACnD,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,+EAA+E;gBAC/E,OAAO,IAAI,CAAC;YACd,CAAC;YAED;YACE,iEAAiE;YACjE,OAAO,CAAC,6BAA6B,CACnC,eAAe,EACf,kBAAkB,CACnB;gBACD,2DAA2D;gBAC3D,sEAAsE;gBACtE,EAAE,CAAC,qBAAqB,CAAC,WAAW,CAAC;gBACrC,2BAA2B;gBAC3B,WAAW,CAAC,WAAW,KAAK,SAAS;gBACrC,WAAW,CAAC,gBAAgB,KAAK,SAAS;gBAC1C,WAAW,CAAC,IAAI,KAAK,SAAS,EAC9B,CAAC;gBACD,kEAAkE;gBAClE,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBAC1D,IAAI,eAAe,KAAK,IAAI,EAAE,CAAC;oBAC7B,iDAAiD;oBACjD,6FAA6F;oBAC7F,EAAE;oBACF,6CAA6C;oBAC7C,uDAAuD;oBACvD,OAAO,IAAI,CAAC;gBACd,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAuB;YAC/C,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBAC5C,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC/B,CAAC;QACJ,CAAC;QAED,OAAO;YACL,mBAAmB,CAAC,IAAI;gBACtB,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,oBAAoB;oBACxD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B,CAAC;oBACD,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;wBAC9B,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI;4BACJ,SAAS,EAAE,yBAAyB;4BACpC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;oCACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;iCACd,CAAC,CAAC;4BACL,CAAC;yBACF,CAAC,CAAC;oBACL,CAAC;oBACD,wDAAwD;oBACxD,2EAA2E;oBAC3E,8EAA8E;oBAC9E,qBAAqB;oBACrB,OAAO;gBACT,CAAC;gBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;gBAE9D,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;gBAErE,IAAI,CAAC,IAAA,qBAAc,EAAC,IAAI,CAAC,EAAE,CAAC;oBAC1B,IACE,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;wBAClD,4BAA4B,CAAC,IAAI,CAAC,UAAU,CAAC,EAC7C,CAAC;wBACD,OAAO;oBACT,CAAC;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,+BAA+B;oBAC/B,+EAA+E;oBAE/E,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBAChE,IAAI,cAAc,EAAE,CAAC;wBACnB,kFAAkF;wBAClF,sCAAsC;wBACtC,MAAM,qBAAqB,GAAG,IAAA,oBAAa,EACzC,IAAI,EACJ,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,CAAC;wBACF,MAAM,gBAAgB,GAAG,IAAA,oBAAa,EAAC,IAAI,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;wBAEhE,MAAM,+BAA+B,GAAG,IAAA,oBAAa,EACnD,cAAc,EACd,EAAE,CAAC,SAAS,CAAC,SAAS,CACvB,CAAC;wBACF,MAAM,0BAA0B,GAAG,IAAA,oBAAa,EAC9C,cAAc,EACd,EAAE,CAAC,SAAS,CAAC,IAAI,CAClB,CAAC;wBAEF,mDAAmD;wBACnD,gFAAgF;wBAChF,MAAM,gBAAgB,GAAG,qBAAqB;4BAC5C,CAAC,CAAC,+BAA+B;4BACjC,CAAC,CAAC,IAAI,CAAC;wBACT,MAAM,WAAW,GAAG,gBAAgB;4BAClC,CAAC,CAAC,0BAA0B;4BAC5B,CAAC,CAAC,IAAI,CAAC;wBAET,IAAI,gBAAgB,IAAI,WAAW,EAAE,CAAC;4BACpC,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,yBAAyB;gCACpC,GAAG,CAAC,KAAK;oCACP,OAAO,KAAK,CAAC,WAAW,CAAC;wCACvB,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;wCACxB,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;qCACd,CAAC,CAAC;gCACL,CAAC;6BACF,CAAC,CAAC;wBACL,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;YACD,iCAAiC,CAC/B,IAAwD;gBAExD,IACE,OAAO,CAAC,aAAa,EAAE,QAAQ,CAC7B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CACxC;oBACD,gBAAgB,CAAC,IAAI,CAAC,cAAc,CAAC,EACrC,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,MAAM,QAAQ,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;gBAElD,IACE,IAAA,oBAAa,EAAC,QAAQ,EAAE,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;oBAC7C,CAAC,OAAO,CAAC,YAAY,CAAC,QAAQ,CAAC;wBAC7B,CAAC,OAAO,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC;4BACtD,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAChC,CAAC;oBACD,mEAAmE;oBACnE,+DAA+D;oBAC/D,OAAO;gBACT,CAAC;gBAED,MAAM,UAAU,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAE/D,IAAI,UAAU,KAAK,QAAQ,EAAE,CAAC;oBAC5B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe,EAAE,CAAC;gCACjD,MAAM,mBAAmB,GAAG,UAAU,CAAC,cAAc,CACnD,IAAI,CAAC,cAAc,EACnB,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;oCACzC,KAAK,CAAC,KAAK,KAAK,GAAG,CACrB,CAAC;gCACH,MAAM,mBAAmB,GAAG,UAAU,CAAC,aAAa,CAClD,IAAI,CAAC,cAAc,EACnB,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;oCACzC,KAAK,CAAC,KAAK,KAAK,GAAG,CACrB,CAAC;gCACH,2BAA2B;gCAC3B,iBAAiB;gCACjB,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;oCAC5B,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;iCAC7B,CAAC,CAAC;4BACL,CAAC;4BACD,2CAA2C;4BAC3C,MAAM,OAAO,GAAG,UAAU,CAAC,aAAa,CACtC,IAAI,CAAC,UAAU,EACf,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,uBAAe,CAAC,UAAU;gCACzC,KAAK,CAAC,KAAK,KAAK,IAAI,CACtB,CAAC;4BACH,MAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,OAAO,EAAE;gCACvD,eAAe,EAAE,IAAI;6BACtB,CAAE,CAAC;4BACJ,wBAAwB;4BACxB,wBAAwB;4BACxB,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBACpE,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;gBAED,qDAAqD;YACvD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}