{"version": 3, "sources": ["../../highlight.js/lib/languages/lisp.js"], "sourcesContent": ["/*\nLanguage: Lisp\nDescription: Generic lisp syntax\nAuthor: <PERSON><PERSON> <<EMAIL>>\nCategory: lisp\n*/\n\nfunction lisp(hljs) {\n  var LISP_IDENT_RE = '[a-zA-Z_\\\\-+\\\\*\\\\/<=>&#][a-zA-Z0-9_\\\\-+*\\\\/<=>&#!]*';\n  var MEC_RE = '\\\\|[^]*?\\\\|';\n  var LISP_SIMPLE_NUMBER_RE = '(-|\\\\+)?\\\\d+(\\\\.\\\\d+|\\\\/\\\\d+)?((d|e|f|l|s|D|E|F|L|S)(\\\\+|-)?\\\\d+)?';\n  var LITERAL = {\n    className: 'literal',\n    begin: '\\\\b(t{1}|nil)\\\\b'\n  };\n  var NUMBER = {\n    className: 'number',\n    variants: [\n      {begin: LISP_SIMPLE_NUMBER_RE, relevance: 0},\n      {begin: '#(b|B)[0-1]+(/[0-1]+)?'},\n      {begin: '#(o|O)[0-7]+(/[0-7]+)?'},\n      {begin: '#(x|X)[0-9a-fA-F]+(/[0-9a-fA-F]+)?'},\n      {begin: '#(c|C)\\\\(' + LISP_SIMPLE_NUMBER_RE + ' +' + LISP_SIMPLE_NUMBER_RE, end: '\\\\)'}\n    ]\n  };\n  var STRING = hljs.inherit(hljs.QUOTE_STRING_MODE, {illegal: null});\n  var COMMENT = hljs.COMMENT(\n    ';', '$',\n    {\n      relevance: 0\n    }\n  );\n  var VARIABLE = {\n    begin: '\\\\*', end: '\\\\*'\n  };\n  var KEYWORD = {\n    className: 'symbol',\n    begin: '[:&]' + LISP_IDENT_RE\n  };\n  var IDENT = {\n    begin: LISP_IDENT_RE,\n    relevance: 0\n  };\n  var MEC = {\n    begin: MEC_RE\n  };\n  var QUOTED_LIST = {\n    begin: '\\\\(', end: '\\\\)',\n    contains: ['self', LITERAL, STRING, NUMBER, IDENT]\n  };\n  var QUOTED = {\n    contains: [NUMBER, STRING, VARIABLE, KEYWORD, QUOTED_LIST, IDENT],\n    variants: [\n      {\n        begin: '[\\'`]\\\\(', end: '\\\\)'\n      },\n      {\n        begin: '\\\\(quote ', end: '\\\\)',\n        keywords: {name: 'quote'}\n      },\n      {\n        begin: '\\'' + MEC_RE\n      }\n    ]\n  };\n  var QUOTED_ATOM = {\n    variants: [\n      {begin: '\\'' + LISP_IDENT_RE},\n      {begin: '#\\'' + LISP_IDENT_RE + '(::' + LISP_IDENT_RE + ')*'}\n    ]\n  };\n  var LIST = {\n    begin: '\\\\(\\\\s*', end: '\\\\)'\n  };\n  var BODY = {\n    endsWithParent: true,\n    relevance: 0\n  };\n  LIST.contains = [\n    {\n      className: 'name',\n      variants: [\n        {\n          begin: LISP_IDENT_RE,\n          relevance: 0,\n        },\n        {begin: MEC_RE}\n      ]\n    },\n    BODY\n  ];\n  BODY.contains = [QUOTED, QUOTED_ATOM, LIST, LITERAL, NUMBER, STRING, COMMENT, VARIABLE, KEYWORD, MEC, IDENT];\n\n  return {\n    name: 'Lisp',\n    illegal: /\\S/,\n    contains: [\n      NUMBER,\n      hljs.SHEBANG(),\n      LITERAL,\n      STRING,\n      COMMENT,\n      QUOTED,\n      QUOTED_ATOM,\n      LIST,\n      IDENT\n    ]\n  };\n}\n\nmodule.exports = lisp;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,KAAK,MAAM;AAClB,UAAI,gBAAgB;AACpB,UAAI,SAAS;AACb,UAAI,wBAAwB;AAC5B,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,UAAI,SAAS;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR,EAAC,OAAO,uBAAuB,WAAW,EAAC;AAAA,UAC3C,EAAC,OAAO,yBAAwB;AAAA,UAChC,EAAC,OAAO,yBAAwB;AAAA,UAChC,EAAC,OAAO,qCAAoC;AAAA,UAC5C,EAAC,OAAO,cAAc,wBAAwB,OAAO,uBAAuB,KAAK,MAAK;AAAA,QACxF;AAAA,MACF;AACA,UAAI,SAAS,KAAK,QAAQ,KAAK,mBAAmB,EAAC,SAAS,KAAI,CAAC;AACjE,UAAI,UAAU,KAAK;AAAA,QACjB;AAAA,QAAK;AAAA,QACL;AAAA,UACE,WAAW;AAAA,QACb;AAAA,MACF;AACA,UAAI,WAAW;AAAA,QACb,OAAO;AAAA,QAAO,KAAK;AAAA,MACrB;AACA,UAAI,UAAU;AAAA,QACZ,WAAW;AAAA,QACX,OAAO,SAAS;AAAA,MAClB;AACA,UAAI,QAAQ;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,UAAI,MAAM;AAAA,QACR,OAAO;AAAA,MACT;AACA,UAAI,cAAc;AAAA,QAChB,OAAO;AAAA,QAAO,KAAK;AAAA,QACnB,UAAU,CAAC,QAAQ,SAAS,QAAQ,QAAQ,KAAK;AAAA,MACnD;AACA,UAAI,SAAS;AAAA,QACX,UAAU,CAAC,QAAQ,QAAQ,UAAU,SAAS,aAAa,KAAK;AAAA,QAChE,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YAAY,KAAK;AAAA,UAC1B;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YAAa,KAAK;AAAA,YACzB,UAAU,EAAC,MAAM,QAAO;AAAA,UAC1B;AAAA,UACA;AAAA,YACE,OAAO,MAAO;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AACA,UAAI,cAAc;AAAA,QAChB,UAAU;AAAA,UACR,EAAC,OAAO,MAAO,cAAa;AAAA,UAC5B,EAAC,OAAO,OAAQ,gBAAgB,QAAQ,gBAAgB,KAAI;AAAA,QAC9D;AAAA,MACF;AACA,UAAI,OAAO;AAAA,QACT,OAAO;AAAA,QAAW,KAAK;AAAA,MACzB;AACA,UAAI,OAAO;AAAA,QACT,gBAAgB;AAAA,QAChB,WAAW;AAAA,MACb;AACA,WAAK,WAAW;AAAA,QACd;AAAA,UACE,WAAW;AAAA,UACX,UAAU;AAAA,YACR;AAAA,cACE,OAAO;AAAA,cACP,WAAW;AAAA,YACb;AAAA,YACA,EAAC,OAAO,OAAM;AAAA,UAChB;AAAA,QACF;AAAA,QACA;AAAA,MACF;AACA,WAAK,WAAW,CAAC,QAAQ,aAAa,MAAM,SAAS,QAAQ,QAAQ,SAAS,UAAU,SAAS,KAAK,KAAK;AAE3G,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,QACT,UAAU;AAAA,UACR;AAAA,UACA,KAAK,QAAQ;AAAA,UACb;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}