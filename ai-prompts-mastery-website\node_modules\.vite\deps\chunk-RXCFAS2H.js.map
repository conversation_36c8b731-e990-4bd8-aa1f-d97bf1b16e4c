{"version": 3, "sources": ["../../refractor/lang/velocity.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = velocity\nvelocity.displayName = 'velocity'\nvelocity.aliases = []\nfunction velocity(Prism) {\n  ;(function (Prism) {\n    Prism.languages.velocity = Prism.languages.extend('markup', {})\n    var velocity = {\n      variable: {\n        pattern:\n          /(^|[^\\\\](?:\\\\\\\\)*)\\$!?(?:[a-z][\\w-]*(?:\\([^)]*\\))?(?:\\.[a-z][\\w-]*(?:\\([^)]*\\))?|\\[[^\\]]+\\])*|\\{[^}]+\\})/i,\n        lookbehind: true,\n        inside: {} // See below\n      },\n      string: {\n        pattern: /\"[^\"]*\"|'[^']*'/,\n        greedy: true\n      },\n      number: /\\b\\d+\\b/,\n      boolean: /\\b(?:false|true)\\b/,\n      operator:\n        /[=!<>]=?|[+*/%-]|&&|\\|\\||\\.\\.|\\b(?:eq|g[et]|l[et]|n(?:e|ot))\\b/,\n      punctuation: /[(){}[\\]:,.]/\n    }\n    velocity.variable.inside = {\n      string: velocity['string'],\n      function: {\n        pattern: /([^\\w-])[a-z][\\w-]*(?=\\()/,\n        lookbehind: true\n      },\n      number: velocity['number'],\n      boolean: velocity['boolean'],\n      punctuation: velocity['punctuation']\n    }\n    Prism.languages.insertBefore('velocity', 'comment', {\n      unparsed: {\n        pattern: /(^|[^\\\\])#\\[\\[[\\s\\S]*?\\]\\]#/,\n        lookbehind: true,\n        greedy: true,\n        inside: {\n          punctuation: /^#\\[\\[|\\]\\]#$/\n        }\n      },\n      'velocity-comment': [\n        {\n          pattern: /(^|[^\\\\])#\\*[\\s\\S]*?\\*#/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'comment'\n        },\n        {\n          pattern: /(^|[^\\\\])##.*/,\n          lookbehind: true,\n          greedy: true,\n          alias: 'comment'\n        }\n      ],\n      directive: {\n        pattern:\n          /(^|[^\\\\](?:\\\\\\\\)*)#@?(?:[a-z][\\w-]*|\\{[a-z][\\w-]*\\})(?:\\s*\\((?:[^()]|\\([^()]*\\))*\\))?/i,\n        lookbehind: true,\n        inside: {\n          keyword: {\n            pattern: /^#@?(?:[a-z][\\w-]*|\\{[a-z][\\w-]*\\})|\\bin\\b/,\n            inside: {\n              punctuation: /[{}]/\n            }\n          },\n          rest: velocity\n        }\n      },\n      variable: velocity['variable']\n    })\n    Prism.languages.velocity['tag'].inside['attr-value'].inside.rest =\n      Prism.languages.velocity\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,WAAWA,OAAM,UAAU,OAAO,UAAU,CAAC,CAAC;AAC9D,YAAIC,YAAW;AAAA,UACb,UAAU;AAAA,YACR,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ,CAAC;AAAA;AAAA,UACX;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,UACE;AAAA,UACF,aAAa;AAAA,QACf;AACA,QAAAA,UAAS,SAAS,SAAS;AAAA,UACzB,QAAQA,UAAS,QAAQ;AAAA,UACzB,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,QAAQA,UAAS,QAAQ;AAAA,UACzB,SAASA,UAAS,SAAS;AAAA,UAC3B,aAAaA,UAAS,aAAa;AAAA,QACrC;AACA,QAAAD,OAAM,UAAU,aAAa,YAAY,WAAW;AAAA,UAClD,UAAU;AAAA,YACR,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,oBAAoB;AAAA,YAClB;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,cACR,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,WAAW;AAAA,YACT,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,MAAMC;AAAA,YACR;AAAA,UACF;AAAA,UACA,UAAUA,UAAS,UAAU;AAAA,QAC/B,CAAC;AACD,QAAAD,OAAM,UAAU,SAAS,KAAK,EAAE,OAAO,YAAY,EAAE,OAAO,OAC1DA,OAAM,UAAU;AAAA,MACpB,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "velocity"]}