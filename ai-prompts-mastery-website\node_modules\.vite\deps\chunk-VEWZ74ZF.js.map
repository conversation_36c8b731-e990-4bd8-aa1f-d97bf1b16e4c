{"version": 3, "sources": ["../../refractor/lang/vala.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = vala\nvala.displayName = 'vala'\nvala.aliases = []\nfunction vala(Prism) {\n  Prism.languages.vala = Prism.languages.extend('clike', {\n    // Classes copied from prism-csharp\n    'class-name': [\n      {\n        // (Foo bar, Bar baz)\n        pattern: /\\b[A-Z]\\w*(?:\\.\\w+)*\\b(?=(?:\\?\\s+|\\*?\\s+\\*?)\\w)/,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // [Foo]\n        pattern: /(\\[)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // class Foo : Bar\n        pattern:\n          /(\\b(?:class|interface)\\s+[A-Z]\\w*(?:\\.\\w+)*\\s*:\\s*)[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      {\n        // class Foo\n        pattern:\n          /((?:\\b(?:class|enum|interface|new|struct)\\s+)|(?:catch\\s+\\())[A-Z]\\w*(?:\\.\\w+)*\\b/,\n        lookbehind: true,\n        inside: {\n          punctuation: /\\./\n        }\n      }\n    ],\n    keyword:\n      /\\b(?:abstract|as|assert|async|base|bool|break|case|catch|char|class|const|construct|continue|default|delegate|delete|do|double|dynamic|else|ensures|enum|errordomain|extern|finally|float|for|foreach|get|if|in|inline|int|int16|int32|int64|int8|interface|internal|is|lock|long|namespace|new|null|out|override|owned|params|private|protected|public|ref|requires|return|set|short|signal|sizeof|size_t|ssize_t|static|string|struct|switch|this|throw|throws|try|typeof|uchar|uint|uint16|uint32|uint64|uint8|ulong|unichar|unowned|ushort|using|value|var|virtual|void|volatile|weak|while|yield)\\b/i,\n    function: /\\b\\w+(?=\\s*\\()/,\n    number:\n      /(?:\\b0x[\\da-f]+\\b|(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:e[+-]?\\d+)?)(?:f|u?l?)?/i,\n    operator:\n      /\\+\\+|--|&&|\\|\\||<<=?|>>=?|=>|->|~|[+\\-*\\/%&^|=!<>]=?|\\?\\??|\\.\\.\\./,\n    punctuation: /[{}[\\];(),.:]/,\n    constant: /\\b[A-Z0-9_]+\\b/\n  })\n  Prism.languages.insertBefore('vala', 'string', {\n    'raw-string': {\n      pattern: /\"\"\"[\\s\\S]*?\"\"\"/,\n      greedy: true,\n      alias: 'string'\n    },\n    'template-string': {\n      pattern: /@\"[\\s\\S]*?\"/,\n      greedy: true,\n      inside: {\n        interpolation: {\n          pattern: /\\$(?:\\([^)]*\\)|[a-zA-Z]\\w*)/,\n          inside: {\n            delimiter: {\n              pattern: /^\\$\\(?|\\)$/,\n              alias: 'punctuation'\n            },\n            rest: Prism.languages.vala\n          }\n        },\n        string: /[\\s\\S]+/\n      }\n    }\n  })\n  Prism.languages.insertBefore('vala', 'keyword', {\n    regex: {\n      pattern:\n        /\\/(?:\\[(?:[^\\]\\\\\\r\\n]|\\\\.)*\\]|\\\\.|[^/\\\\\\[\\r\\n])+\\/[imsx]{0,4}(?=\\s*(?:$|[\\r\\n,.;})\\]]))/,\n      greedy: true,\n      inside: {\n        'regex-source': {\n          pattern: /^(\\/)[\\s\\S]+(?=\\/[a-z]*$)/,\n          lookbehind: true,\n          alias: 'language-regex',\n          inside: Prism.languages.regex\n        },\n        'regex-delimiter': /^\\//,\n        'regex-flags': /^[a-z]+$/\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,SAAS;AAAA;AAAA,QAErD,cAAc;AAAA,UACZ;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,QACF;AAAA,QACA,SACE;AAAA,QACF,UAAU;AAAA,QACV,QACE;AAAA,QACF,UACE;AAAA,QACF,aAAa;AAAA,QACb,UAAU;AAAA,MACZ,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,UAAU;AAAA,QAC7C,cAAc;AAAA,UACZ,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AAAA,QACA,mBAAmB;AAAA,UACjB,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,eAAe;AAAA,cACb,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,MAAM,MAAM,UAAU;AAAA,cACxB;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,aAAa,QAAQ,WAAW;AAAA,QAC9C,OAAO;AAAA,UACL,SACE;AAAA,UACF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,gBAAgB;AAAA,cACd,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,OAAO;AAAA,cACP,QAAQ,MAAM,UAAU;AAAA,YAC1B;AAAA,YACA,mBAAmB;AAAA,YACnB,eAAe;AAAA,UACjB;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}