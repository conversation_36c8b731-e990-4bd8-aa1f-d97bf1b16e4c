{"version": 3, "sources": ["../../refractor/lang/soy.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = soy\nsoy.displayName = 'soy'\nsoy.aliases = []\nfunction soy(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  ;(function (Prism) {\n    var stringPattern = /([\"'])(?:\\\\(?:\\r\\n|[\\s\\S])|(?!\\1)[^\\\\\\r\\n])*\\1/\n    var numberPattern = /\\b\\d+(?:\\.\\d+)?(?:[eE][+-]?\\d+)?\\b|\\b0x[\\dA-F]+\\b/\n    Prism.languages.soy = {\n      comment: [\n        /\\/\\*[\\s\\S]*?\\*\\//,\n        {\n          pattern: /(\\s)\\/\\/.*/,\n          lookbehind: true,\n          greedy: true\n        }\n      ],\n      'command-arg': {\n        pattern:\n          /(\\{+\\/?\\s*(?:alias|call|delcall|delpackage|deltemplate|namespace|template)\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'string',\n        inside: {\n          punctuation: /\\./\n        }\n      },\n      parameter: {\n        pattern: /(\\{+\\/?\\s*@?param\\??\\s+)\\.?[\\w.]+/,\n        lookbehind: true,\n        alias: 'variable'\n      },\n      keyword: [\n        {\n          pattern:\n            /(\\{+\\/?[^\\S\\r\\n]*)(?:\\\\[nrt]|alias|call|case|css|default|delcall|delpackage|deltemplate|else(?:if)?|fallbackmsg|for(?:each)?|if(?:empty)?|lb|let|literal|msg|namespace|nil|@?param\\??|rb|sp|switch|template|xid)/,\n          lookbehind: true\n        },\n        /\\b(?:any|as|attributes|bool|css|float|html|in|int|js|list|map|null|number|string|uri)\\b/\n      ],\n      delimiter: {\n        pattern: /^\\{+\\/?|\\/?\\}+$/,\n        alias: 'punctuation'\n      },\n      property: /\\w+(?==)/,\n      variable: {\n        pattern: /\\$[^\\W\\d]\\w*(?:\\??(?:\\.\\w+|\\[[^\\]]+\\]))*/,\n        inside: {\n          string: {\n            pattern: stringPattern,\n            greedy: true\n          },\n          number: numberPattern,\n          punctuation: /[\\[\\].?]/\n        }\n      },\n      string: {\n        pattern: stringPattern,\n        greedy: true\n      },\n      function: [\n        /\\w+(?=\\()/,\n        {\n          pattern: /(\\|[^\\S\\r\\n]*)\\w+/,\n          lookbehind: true\n        }\n      ],\n      boolean: /\\b(?:false|true)\\b/,\n      number: numberPattern,\n      operator: /\\?:?|<=?|>=?|==?|!=|[+*/%-]|\\b(?:and|not|or)\\b/,\n      punctuation: /[{}()\\[\\]|.,:]/\n    } // Tokenize all inline Soy expressions\n    Prism.hooks.add('before-tokenize', function (env) {\n      var soyPattern = /\\{\\{.+?\\}\\}|\\{.+?\\}|\\s\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//g\n      var soyLitteralStart = '{literal}'\n      var soyLitteralEnd = '{/literal}'\n      var soyLitteralMode = false\n      Prism.languages['markup-templating'].buildPlaceholders(\n        env,\n        'soy',\n        soyPattern,\n        function (match) {\n          // Soy tags inside {literal} block are ignored\n          if (match === soyLitteralEnd) {\n            soyLitteralMode = false\n          }\n          if (!soyLitteralMode) {\n            if (match === soyLitteralStart) {\n              soyLitteralMode = true\n            }\n            return true\n          }\n          return false\n        }\n      )\n    }) // Re-insert the tokens after tokenizing\n    Prism.hooks.add('after-tokenize', function (env) {\n      Prism.languages['markup-templating'].tokenizePlaceholders(env, 'soy')\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB,YAAM,SAAS,yBAAyB;AACvC,OAAC,SAAUA,QAAO;AACjB,YAAI,gBAAgB;AACpB,YAAI,gBAAgB;AACpB,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,SAAS;AAAA,YACP;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,eAAe;AAAA,YACb,SACE;AAAA,YACF,YAAY;AAAA,YACZ,OAAO;AAAA,YACP,QAAQ;AAAA,cACN,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,YACd;AAAA,YACA;AAAA,UACF;AAAA,UACA,WAAW;AAAA,YACT,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UAAU;AAAA,UACV,UAAU;AAAA,YACR,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,QAAQ;AAAA,gBACN,SAAS;AAAA,gBACT,QAAQ;AAAA,cACV;AAAA,cACA,QAAQ;AAAA,cACR,aAAa;AAAA,YACf;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,UAAU;AAAA,YACR;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,QAAAA,OAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,cAAI,aAAa;AACjB,cAAI,mBAAmB;AACvB,cAAI,iBAAiB;AACrB,cAAI,kBAAkB;AACtB,UAAAA,OAAM,UAAU,mBAAmB,EAAE;AAAA,YACnC;AAAA,YACA;AAAA,YACA;AAAA,YACA,SAAU,OAAO;AAEf,kBAAI,UAAU,gBAAgB;AAC5B,kCAAkB;AAAA,cACpB;AACA,kBAAI,CAAC,iBAAiB;AACpB,oBAAI,UAAU,kBAAkB;AAC9B,oCAAkB;AAAA,gBACpB;AACA,uBAAO;AAAA,cACT;AACA,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,UAAAA,OAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,KAAK;AAAA,QACtE,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}