{"version": 3, "sources": ["../../refractor/lang/js-extras.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = jsExtras\njsExtras.displayName = 'jsExtras'\njsExtras.aliases = []\nfunction jsExtras(Prism) {\n  ;(function (Prism) {\n    Prism.languages.insertBefore('javascript', 'function-variable', {\n      'method-variable': {\n        pattern: RegExp(\n          '(\\\\.\\\\s*)' +\n            Prism.languages.javascript['function-variable'].pattern.source\n        ),\n        lookbehind: true,\n        alias: ['function-variable', 'method', 'function', 'property-access']\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'function', {\n      method: {\n        pattern: RegExp(\n          '(\\\\.\\\\s*)' + Prism.languages.javascript['function'].source\n        ),\n        lookbehind: true,\n        alias: ['function', 'property-access']\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'constant', {\n      'known-class-name': [\n        {\n          // standard built-ins\n          // https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects\n          pattern:\n            /\\b(?:(?:Float(?:32|64)|(?:Int|Uint)(?:8|16|32)|Uint8Clamped)?Array|ArrayBuffer|BigInt|Boolean|DataView|Date|Error|Function|Intl|JSON|(?:Weak)?(?:Map|Set)|Math|Number|Object|Promise|Proxy|Reflect|RegExp|String|Symbol|WebAssembly)\\b/,\n          alias: 'class-name'\n        },\n        {\n          // errors\n          pattern: /\\b(?:[A-Z]\\w*)Error\\b/,\n          alias: 'class-name'\n        }\n      ]\n    })\n    /**\n     * Replaces the `<ID>` placeholder in the given pattern with a pattern for general JS identifiers.\n     *\n     * @param {string} source\n     * @param {string} [flags]\n     * @returns {RegExp}\n     */\n    function withId(source, flags) {\n      return RegExp(\n        source.replace(/<ID>/g, function () {\n          return /(?!\\s)[_$a-zA-Z\\xA0-\\uFFFF](?:(?!\\s)[$\\w\\xA0-\\uFFFF])*/.source\n        }),\n        flags\n      )\n    }\n    Prism.languages.insertBefore('javascript', 'keyword', {\n      imports: {\n        // https://tc39.es/ecma262/#sec-imports\n        pattern: withId(\n          /(\\bimport\\b\\s*)(?:<ID>(?:\\s*,\\s*(?:\\*\\s*as\\s+<ID>|\\{[^{}]*\\}))?|\\*\\s*as\\s+<ID>|\\{[^{}]*\\})(?=\\s*\\bfrom\\b)/\n            .source\n        ),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      },\n      exports: {\n        // https://tc39.es/ecma262/#sec-exports\n        pattern: withId(\n          /(\\bexport\\b\\s*)(?:\\*(?:\\s*as\\s+<ID>)?(?=\\s*\\bfrom\\b)|\\{[^{}]*\\})/\n            .source\n        ),\n        lookbehind: true,\n        inside: Prism.languages.javascript\n      }\n    })\n    Prism.languages.javascript['keyword'].unshift(\n      {\n        pattern: /\\b(?:as|default|export|from|import)\\b/,\n        alias: 'module'\n      },\n      {\n        pattern:\n          /\\b(?:await|break|catch|continue|do|else|finally|for|if|return|switch|throw|try|while|yield)\\b/,\n        alias: 'control-flow'\n      },\n      {\n        pattern: /\\bnull\\b/,\n        alias: ['null', 'nil']\n      },\n      {\n        pattern: /\\bundefined\\b/,\n        alias: 'nil'\n      }\n    )\n    Prism.languages.insertBefore('javascript', 'operator', {\n      spread: {\n        pattern: /\\.{3}/,\n        alias: 'operator'\n      },\n      arrow: {\n        pattern: /=>/,\n        alias: 'operator'\n      }\n    })\n    Prism.languages.insertBefore('javascript', 'punctuation', {\n      'property-access': {\n        pattern: withId(/(\\.\\s*)#?<ID>/.source),\n        lookbehind: true\n      },\n      'maybe-class-name': {\n        pattern: /(^|[^$\\w\\xA0-\\uFFFF])[A-Z][$\\w\\xA0-\\uFFFF]+/,\n        lookbehind: true\n      },\n      dom: {\n        // this contains only a few commonly used DOM variables\n        pattern:\n          /\\b(?:document|(?:local|session)Storage|location|navigator|performance|window)\\b/,\n        alias: 'variable'\n      },\n      console: {\n        pattern: /\\bconsole(?=\\s*\\.)/,\n        alias: 'class-name'\n      }\n    }) // add 'maybe-class-name' to tokens which might be a class name\n    var maybeClassNameTokens = [\n      'function',\n      'function-variable',\n      'method',\n      'method-variable',\n      'property-access'\n    ]\n    for (var i = 0; i < maybeClassNameTokens.length; i++) {\n      var token = maybeClassNameTokens[i]\n      var value = Prism.languages.javascript[token] // convert regex to object\n      if (Prism.util.type(value) === 'RegExp') {\n        value = Prism.languages.javascript[token] = {\n          pattern: value\n        }\n      } // keep in mind that we don't support arrays\n      var inside = value.inside || {}\n      value.inside = inside\n      inside['maybe-class-name'] = /^[A-Z][\\s\\S]*/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,aAAS,cAAc;AACvB,aAAS,UAAU,CAAC;AACpB,aAAS,SAAS,OAAO;AACvB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,aAAa,cAAc,qBAAqB;AAAA,UAC9D,mBAAmB;AAAA,YACjB,SAAS;AAAA,cACP,cACEA,OAAM,UAAU,WAAW,mBAAmB,EAAE,QAAQ;AAAA,YAC5D;AAAA,YACA,YAAY;AAAA,YACZ,OAAO,CAAC,qBAAqB,UAAU,YAAY,iBAAiB;AAAA,UACtE;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,UACrD,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,cAAcA,OAAM,UAAU,WAAW,UAAU,EAAE;AAAA,YACvD;AAAA,YACA,YAAY;AAAA,YACZ,OAAO,CAAC,YAAY,iBAAiB;AAAA,UACvC;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,UACrD,oBAAoB;AAAA,YAClB;AAAA;AAAA;AAAA,cAGE,SACE;AAAA,cACF,OAAO;AAAA,YACT;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF,CAAC;AAQD,iBAAS,OAAO,QAAQ,OAAO;AAC7B,iBAAO;AAAA,YACL,OAAO,QAAQ,SAAS,WAAY;AAClC,qBAAO,yDAAyD;AAAA,YAClE,CAAC;AAAA,YACD;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,cAAc,WAAW;AAAA,UACpD,SAAS;AAAA;AAAA,YAEP,SAAS;AAAA,cACP,4GACG;AAAA,YACL;AAAA,YACA,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,UACA,SAAS;AAAA;AAAA,YAEP,SAAS;AAAA,cACP,mEACG;AAAA,YACL;AAAA,YACA,YAAY;AAAA,YACZ,QAAQA,OAAM,UAAU;AAAA,UAC1B;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,WAAW,SAAS,EAAE;AAAA,UACpC;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SACE;AAAA,YACF,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO,CAAC,QAAQ,KAAK;AAAA,UACvB;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,cAAc,YAAY;AAAA,UACrD,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,cAAc,eAAe;AAAA,UACxD,mBAAmB;AAAA,YACjB,SAAS,OAAO,gBAAgB,MAAM;AAAA,YACtC,YAAY;AAAA,UACd;AAAA,UACA,oBAAoB;AAAA,YAClB,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,UACA,KAAK;AAAA;AAAA,YAEH,SACE;AAAA,YACF,OAAO;AAAA,UACT;AAAA,UACA,SAAS;AAAA,YACP,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF,CAAC;AACD,YAAI,uBAAuB;AAAA,UACzB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,IAAI,qBAAqB,QAAQ,KAAK;AACpD,cAAI,QAAQ,qBAAqB,CAAC;AAClC,cAAI,QAAQA,OAAM,UAAU,WAAW,KAAK;AAC5C,cAAIA,OAAM,KAAK,KAAK,KAAK,MAAM,UAAU;AACvC,oBAAQA,OAAM,UAAU,WAAW,KAAK,IAAI;AAAA,cAC1C,SAAS;AAAA,YACX;AAAA,UACF;AACA,cAAI,SAAS,MAAM,UAAU,CAAC;AAC9B,gBAAM,SAAS;AACf,iBAAO,kBAAkB,IAAI;AAAA,QAC/B;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}