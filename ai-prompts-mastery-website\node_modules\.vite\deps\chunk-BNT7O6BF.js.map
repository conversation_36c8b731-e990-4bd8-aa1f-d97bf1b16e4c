{"version": 3, "sources": ["../../highlight.js/lib/languages/brainfuck.js"], "sourcesContent": ["/*\nLanguage: Brainfuck\nAuthor: <PERSON><PERSON><PERSON> <<EMAIL>>\nWebsite: https://esolangs.org/wiki/Brainfuck\n*/\n\n/** @type LanguageFn */\nfunction brainfuck(hljs) {\n  const LITERAL = {\n    className: 'literal',\n    begin: /[+-]/,\n    relevance: 0\n  };\n  return {\n    name: '<PERSON>fu<PERSON>',\n    aliases: ['bf'],\n    contains: [\n      hljs.COMMENT(\n        '[^\\\\[\\\\]\\\\.,\\\\+\\\\-<> \\r\\n]',\n        '[\\\\[\\\\]\\\\.,\\\\+\\\\-<> \\r\\n]',\n        {\n          returnEnd: true,\n          relevance: 0\n        }\n      ),\n      {\n        className: 'title',\n        begin: '[\\\\[\\\\]]',\n        relevance: 0\n      },\n      {\n        className: 'string',\n        begin: '[\\\\.,]',\n        relevance: 0\n      },\n      {\n        // this mode works as the only relevance counter\n        begin: /(?:\\+\\+|--)/,\n        contains: [LITERAL]\n      },\n      LITERAL\n    ]\n  };\n}\n\nmodule.exports = brainfuck;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,UAAU,MAAM;AACvB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS,CAAC,IAAI;AAAA,QACd,UAAU;AAAA,UACR,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,WAAW;AAAA,cACX,WAAW;AAAA,YACb;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,UAAU,CAAC,OAAO;AAAA,UACpB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}