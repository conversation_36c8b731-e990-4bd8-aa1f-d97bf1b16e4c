# character-entities-legacy

[![Build][build-badge]][build]
[![Downloads][downloads-badge]][downloads]
[![Size][size-badge]][size]

HTML legacy character entity information: for legacy reasons some character
entities are not required to have a trailing semicolon: `&copy` is perfectly
okay for `©`.

## Install

[npm][]:

```sh
npm install character-entities-legacy
```

## Use

```js
var characterEntitiesLegacy = require('character-entities-legacy')

console.log(characterEntitiesLegacy.copy) // => '©'
console.log(characterEntitiesLegacy.frac34) // => '¾'
console.log(characterEntitiesLegacy.sup1) // => '¹'
```

## API

### `characterEntitiesLegacy`

Mapping between (case-sensitive) legacy character entity names to replacements.

## Support

See [`whatwg/html`][html].

## Related

*   [`character-entities`](https://github.com/wooorm/character-entities)
    — HTML character entity info
*   [`character-entities-html4`](https://github.com/wooorm/character-entities-html4)
    — HTML 4 character entity info
*   [`parse-entities`](https://github.com/wooorm/parse-entities)
    — Parse HTML character references
*   [`stringify-entities`](https://github.com/wooorm/stringify-entities)
    — Serialize HTML character references

## License

[MIT][license] © [Titus Wormer][author]

<!-- Definitions -->

[build-badge]: https://img.shields.io/travis/wooorm/character-entities-legacy.svg

[build]: https://travis-ci.org/wooorm/character-entities-legacy

[downloads-badge]: https://img.shields.io/npm/dm/character-entities-legacy.svg

[downloads]: https://www.npmjs.com/package/character-entities-legacy

[size-badge]: https://img.shields.io/bundlephobia/minzip/character-entities-legacy.svg

[size]: https://bundlephobia.com/result?p=character-entities-legacy

[npm]: https://docs.npmjs.com/cli/install

[license]: license

[author]: https://wooorm.com

[html]: https://raw.githubusercontent.com/whatwg/html/master/json-entities-legacy.inc
