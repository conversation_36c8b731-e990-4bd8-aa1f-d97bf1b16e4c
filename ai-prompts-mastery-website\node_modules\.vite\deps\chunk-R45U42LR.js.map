{"version": 3, "sources": ["../../refractor/lang/liquid.js"], "sourcesContent": ["'use strict'\nvar refractorMarkupTemplating = require('./markup-templating.js')\nmodule.exports = liquid\nliquid.displayName = 'liquid'\nliquid.aliases = []\nfunction liquid(Prism) {\n  Prism.register(refractorMarkupTemplating)\n  Prism.languages.liquid = {\n    comment: {\n      pattern: /(^\\{%\\s*comment\\s*%\\})[\\s\\S]+(?=\\{%\\s*endcomment\\s*%\\}$)/,\n      lookbehind: true\n    },\n    delimiter: {\n      pattern: /^\\{(?:\\{\\{|[%\\{])-?|-?(?:\\}\\}|[%\\}])\\}$/,\n      alias: 'punctuation'\n    },\n    string: {\n      pattern: /\"[^\"]*\"|'[^']*'/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:as|assign|break|(?:end)?(?:capture|case|comment|for|form|if|paginate|raw|style|tablerow|unless)|continue|cycle|decrement|echo|else|elsif|in|include|increment|limit|liquid|offset|range|render|reversed|section|when|with)\\b/,\n    object:\n      /\\b(?:address|all_country_option_tags|article|block|blog|cart|checkout|collection|color|country|country_option_tags|currency|current_page|current_tags|customer|customer_address|date|discount_allocation|discount_application|external_video|filter|filter_value|font|forloop|fulfillment|generic_file|gift_card|group|handle|image|line_item|link|linklist|localization|location|measurement|media|metafield|model|model_source|order|page|page_description|page_image|page_title|part|policy|product|product_option|recommendations|request|robots|routes|rule|script|search|selling_plan|selling_plan_allocation|selling_plan_group|shipping_method|shop|shop_locale|sitemap|store_availability|tax_line|template|theme|transaction|unit_price_measurement|user_agent|variant|video|video_source)\\b/,\n    function: [\n      {\n        pattern: /(\\|\\s*)\\w+/,\n        lookbehind: true,\n        alias: 'filter'\n      },\n      {\n        // array functions\n        pattern: /(\\.\\s*)(?:first|last|size)/,\n        lookbehind: true\n      }\n    ],\n    boolean: /\\b(?:false|nil|true)\\b/,\n    range: {\n      pattern: /\\.\\./,\n      alias: 'operator'\n    },\n    // https://github.com/Shopify/liquid/blob/698f5e0d967423e013f6169d9111bd969bd78337/lib/liquid/lexer.rb#L21\n    number: /\\b\\d+(?:\\.\\d+)?\\b/,\n    operator: /[!=]=|<>|[<>]=?|[|?:=-]|\\b(?:and|contains(?=\\s)|or)\\b/,\n    punctuation: /[.,\\[\\]()]/,\n    empty: {\n      pattern: /\\bempty\\b/,\n      alias: 'keyword'\n    }\n  }\n  Prism.hooks.add('before-tokenize', function (env) {\n    var liquidPattern =\n      /\\{%\\s*comment\\s*%\\}[\\s\\S]*?\\{%\\s*endcomment\\s*%\\}|\\{(?:%[\\s\\S]*?%|\\{\\{[\\s\\S]*?\\}\\}|\\{[\\s\\S]*?\\})\\}/g\n    var insideRaw = false\n    Prism.languages['markup-templating'].buildPlaceholders(\n      env,\n      'liquid',\n      liquidPattern,\n      function (match) {\n        var tagMatch = /^\\{%-?\\s*(\\w+)/.exec(match)\n        if (tagMatch) {\n          var tag = tagMatch[1]\n          if (tag === 'raw' && !insideRaw) {\n            insideRaw = true\n            return true\n          } else if (tag === 'endraw') {\n            insideRaw = false\n            return true\n          }\n        }\n        return !insideRaw\n      }\n    )\n  })\n  Prism.hooks.add('after-tokenize', function (env) {\n    Prism.languages['markup-templating'].tokenizePlaceholders(env, 'liquid')\n  })\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,4BAA4B;AAChC,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB,YAAM,SAAS,yBAAyB;AACxC,YAAM,UAAU,SAAS;AAAA,QACvB,SAAS;AAAA,UACP,SAAS;AAAA,UACT,YAAY;AAAA,QACd;AAAA,QACA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,QACE;AAAA,QACF,UAAU;AAAA,UACR;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AAAA,QACA,SAAS;AAAA,QACT,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA;AAAA,QAEA,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,aAAa;AAAA,QACb,OAAO;AAAA,UACL,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AAAA,MACF;AACA,YAAM,MAAM,IAAI,mBAAmB,SAAU,KAAK;AAChD,YAAI,gBACF;AACF,YAAI,YAAY;AAChB,cAAM,UAAU,mBAAmB,EAAE;AAAA,UACnC;AAAA,UACA;AAAA,UACA;AAAA,UACA,SAAU,OAAO;AACf,gBAAI,WAAW,iBAAiB,KAAK,KAAK;AAC1C,gBAAI,UAAU;AACZ,kBAAI,MAAM,SAAS,CAAC;AACpB,kBAAI,QAAQ,SAAS,CAAC,WAAW;AAC/B,4BAAY;AACZ,uBAAO;AAAA,cACT,WAAW,QAAQ,UAAU;AAC3B,4BAAY;AACZ,uBAAO;AAAA,cACT;AAAA,YACF;AACA,mBAAO,CAAC;AAAA,UACV;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,MAAM,IAAI,kBAAkB,SAAU,KAAK;AAC/C,cAAM,UAAU,mBAAmB,EAAE,qBAAqB,KAAK,QAAQ;AAAA,MACzE,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}