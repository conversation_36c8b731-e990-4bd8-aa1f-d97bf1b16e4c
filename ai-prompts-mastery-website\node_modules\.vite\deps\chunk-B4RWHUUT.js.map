{"version": 3, "sources": ["../../highlight.js/lib/languages/dsconfig.js"], "sourcesContent": ["/*\n Language: dsconfig\n Description: dsconfig batch configuration language for LDAP directory servers\n Contributors: <PERSON> <<EMAIL>>\n Category: enterprise, config\n */\n\n /** @type LanguageFn */\nfunction dsconfig(hljs) {\n  const QUOTED_PROPERTY = {\n    className: 'string',\n    begin: /\"/,\n    end: /\"/\n  };\n  const APOS_PROPERTY = {\n    className: 'string',\n    begin: /'/,\n    end: /'/\n  };\n  const UNQUOTED_PROPERTY = {\n    className: 'string',\n    begin: /[\\w\\-?]+:\\w+/,\n    end: /\\W/,\n    relevance: 0\n  };\n  const VALUELESS_PROPERTY = {\n    className: 'string',\n    begin: /\\w+(\\-\\w+)*/,\n    end: /(?=\\W)/,\n    relevance: 0\n  };\n\n  return {\n    keywords: 'dsconfig',\n    contains: [\n      {\n        className: 'keyword',\n        begin: '^dsconfig',\n        end: /\\s/,\n        excludeEnd: true,\n        relevance: 10\n      },\n      {\n        className: 'built_in',\n        begin: /(list|create|get|set|delete)-(\\w+)/,\n        end: /\\s/,\n        excludeEnd: true,\n        illegal: '!@#$%^&*()',\n        relevance: 10\n      },\n      {\n        className: 'built_in',\n        begin: /--(\\w+)/,\n        end: /\\s/,\n        excludeEnd: true\n      },\n      QUOTED_PROPERTY,\n      APOS_PROPERTY,\n      UNQUOTED_PROPERTY,\n      VALUELESS_PROPERTY,\n      hljs.HASH_COMMENT_MODE\n    ]\n  };\n}\n\nmodule.exports = dsconfig;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,SAAS,MAAM;AACtB,YAAM,kBAAkB;AAAA,QACtB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,oBAAoB;AAAA,QACxB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AACA,YAAM,qBAAqB;AAAA,QACzB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,WAAW;AAAA,MACb;AAEA,aAAO;AAAA,QACL,UAAU;AAAA,QACV,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,YACZ,SAAS;AAAA,YACT,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,YAAY;AAAA,UACd;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,KAAK;AAAA,QACP;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}