import { D as DiffOptions } from './types-9l4niLY8.js';
import 'pretty-format';

declare function serializeError(val: any, seen?: WeakMap<WeakKey, any>): any;
declare function processError(err: any, diffOptions?: DiffOptions): any;
declare function replaceAsymmetricMatcher(actual: any, expected: any, actualReplaced?: WeakSet<WeakKey>, expectedReplaced?: WeakSet<WeakKey>): {
    replacedActual: any;
    replacedExpected: any;
};

export { processError, replaceAsymmetricMatcher, serializeError };
