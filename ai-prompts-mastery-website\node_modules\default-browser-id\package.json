{"name": "default-browser-id", "version": "3.0.0", "description": "Get the bundle identifier of the default browser (macOS). Example: com.apple.Safari", "license": "MIT", "repository": "sindresorhus/default-browser-id", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": ">=12"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["macos", "browser", "default", "plist", "web", "bundle", "bundleid", "id", "identifier", "uti"], "dependencies": {"bplist-parser": "^0.2.0", "untildify": "^4.0.0"}, "devDependencies": {"ava": "^3.15.0", "xo": "^0.38.2"}}