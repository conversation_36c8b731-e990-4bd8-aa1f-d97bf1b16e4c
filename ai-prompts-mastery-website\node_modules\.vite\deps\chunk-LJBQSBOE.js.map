{"version": 3, "sources": ["../../refractor/lang/web-idl.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = webIdl\nwebIdl.displayName = 'webIdl'\nwebIdl.aliases = []\nfunction webIdl(Prism) {\n  ;(function (Prism) {\n    var id = /(?:\\B-|\\b_|\\b)[A-Za-z][\\w-]*(?![\\w-])/.source\n    var type =\n      '(?:' +\n      /\\b(?:unsigned\\s+)?long\\s+long(?![\\w-])/.source +\n      '|' +\n      /\\b(?:unrestricted|unsigned)\\s+[a-z]+(?![\\w-])/.source +\n      '|' +\n      /(?!(?:unrestricted|unsigned)\\b)/.source +\n      id +\n      /(?:\\s*<(?:[^<>]|<[^<>]*>)*>)?/.source +\n      ')' +\n      /(?:\\s*\\?)?/.source\n    var typeInside = {}\n    Prism.languages['web-idl'] = {\n      comment: {\n        pattern: /\\/\\/.*|\\/\\*[\\s\\S]*?\\*\\//,\n        greedy: true\n      },\n      string: {\n        pattern: /\"[^\"]*\"/,\n        greedy: true\n      },\n      namespace: {\n        pattern: RegExp(/(\\bnamespace\\s+)/.source + id),\n        lookbehind: true\n      },\n      'class-name': [\n        {\n          pattern:\n            /(^|[^\\w-])(?:iterable|maplike|setlike)\\s*<(?:[^<>]|<[^<>]*>)*>/,\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          pattern: RegExp(\n            /(\\b(?:attribute|const|deleter|getter|optional|setter)\\s+)/.source +\n              type\n          ),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // callback return type\n          pattern: RegExp(\n            '(' + /\\bcallback\\s+/.source + id + /\\s*=\\s*/.source + ')' + type\n          ),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          // typedef\n          pattern: RegExp(/(\\btypedef\\b\\s*)/.source + type),\n          lookbehind: true,\n          inside: typeInside\n        },\n        {\n          pattern: RegExp(\n            /(\\b(?:callback|dictionary|enum|interface(?:\\s+mixin)?)\\s+)(?!(?:interface|mixin)\\b)/\n              .source + id\n          ),\n          lookbehind: true\n        },\n        {\n          // inheritance\n          pattern: RegExp(/(:\\s*)/.source + id),\n          lookbehind: true\n        }, // includes and implements\n        RegExp(id + /(?=\\s+(?:implements|includes)\\b)/.source),\n        {\n          pattern: RegExp(/(\\b(?:implements|includes)\\s+)/.source + id),\n          lookbehind: true\n        },\n        {\n          // function return type, parameter types, and dictionary members\n          pattern: RegExp(\n            type +\n              '(?=' +\n              /\\s*(?:\\.{3}\\s*)?/.source +\n              id +\n              /\\s*[(),;=]/.source +\n              ')'\n          ),\n          inside: typeInside\n        }\n      ],\n      builtin:\n        /\\b(?:ArrayBuffer|BigInt64Array|BigUint64Array|ByteString|DOMString|DataView|Float32Array|Float64Array|FrozenArray|Int16Array|Int32Array|Int8Array|ObservableArray|Promise|USVString|Uint16Array|Uint32Array|Uint8Array|Uint8ClampedArray)\\b/,\n      keyword: [\n        /\\b(?:async|attribute|callback|const|constructor|deleter|dictionary|enum|getter|implements|includes|inherit|interface|mixin|namespace|null|optional|or|partial|readonly|required|setter|static|stringifier|typedef|unrestricted)\\b/, // type keywords\n        /\\b(?:any|bigint|boolean|byte|double|float|iterable|long|maplike|object|octet|record|sequence|setlike|short|symbol|undefined|unsigned|void)\\b/\n      ],\n      boolean: /\\b(?:false|true)\\b/,\n      number: {\n        pattern:\n          /(^|[^\\w-])-?(?:0x[0-9a-f]+|(?:\\d+(?:\\.\\d*)?|\\.\\d+)(?:e[+-]?\\d+)?|NaN|Infinity)(?![\\w-])/i,\n        lookbehind: true\n      },\n      operator: /\\.{3}|[=:?<>-]/,\n      punctuation: /[(){}[\\].,;]/\n    }\n    for (var key in Prism.languages['web-idl']) {\n      if (key !== 'class-name') {\n        typeInside[key] = Prism.languages['web-idl'][key]\n      }\n    }\n    Prism.languages['webidl'] = Prism.languages['web-idl']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,WAAO,cAAc;AACrB,WAAO,UAAU,CAAC;AAClB,aAAS,OAAO,OAAO;AACrB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,KAAK,wCAAwC;AACjD,YAAI,OACF,QACA,yCAAyC,SACzC,MACA,gDAAgD,SAChD,MACA,kCAAkC,SAClC,KACA,gCAAgC,SAChC,MACA,aAAa;AACf,YAAI,aAAa,CAAC;AAClB,QAAAA,OAAM,UAAU,SAAS,IAAI;AAAA,UAC3B,SAAS;AAAA,YACP,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT,SAAS,OAAO,mBAAmB,SAAS,EAAE;AAAA,YAC9C,YAAY;AAAA,UACd;AAAA,UACA,cAAc;AAAA,YACZ;AAAA,cACE,SACE;AAAA,cACF,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,4DAA4D,SAC1D;AAAA,cACJ;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,gBACP,MAAM,gBAAgB,SAAS,KAAK,UAAU,SAAS,MAAM;AAAA,cAC/D;AAAA,cACA,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA;AAAA,cAEE,SAAS,OAAO,mBAAmB,SAAS,IAAI;AAAA,cAChD,YAAY;AAAA,cACZ,QAAQ;AAAA,YACV;AAAA,YACA;AAAA,cACE,SAAS;AAAA,gBACP,sFACG,SAAS;AAAA,cACd;AAAA,cACA,YAAY;AAAA,YACd;AAAA,YACA;AAAA;AAAA,cAEE,SAAS,OAAO,SAAS,SAAS,EAAE;AAAA,cACpC,YAAY;AAAA,YACd;AAAA;AAAA,YACA,OAAO,KAAK,mCAAmC,MAAM;AAAA,YACrD;AAAA,cACE,SAAS,OAAO,iCAAiC,SAAS,EAAE;AAAA,cAC5D,YAAY;AAAA,YACd;AAAA,YACA;AAAA;AAAA,cAEE,SAAS;AAAA,gBACP,OACE,QACA,mBAAmB,SACnB,KACA,aAAa,SACb;AAAA,cACJ;AAAA,cACA,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,UACA,SACE;AAAA,UACF,SAAS;AAAA,YACP;AAAA;AAAA,YACA;AAAA,UACF;AAAA,UACA,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,SACE;AAAA,YACF,YAAY;AAAA,UACd;AAAA,UACA,UAAU;AAAA,UACV,aAAa;AAAA,QACf;AACA,iBAAS,OAAOA,OAAM,UAAU,SAAS,GAAG;AAC1C,cAAI,QAAQ,cAAc;AACxB,uBAAW,GAAG,IAAIA,OAAM,UAAU,SAAS,EAAE,GAAG;AAAA,UAClD;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,QAAQ,IAAIA,OAAM,UAAU,SAAS;AAAA,MACvD,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}