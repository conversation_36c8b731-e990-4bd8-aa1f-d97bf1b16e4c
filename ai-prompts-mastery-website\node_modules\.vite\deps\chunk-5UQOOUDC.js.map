{"version": 3, "sources": ["../../refractor/lang/regex.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = regex\nregex.displayName = 'regex'\nregex.aliases = []\nfunction regex(Prism) {\n  ;(function (Prism) {\n    var specialEscape = {\n      pattern: /\\\\[\\\\(){}[\\]^$+*?|.]/,\n      alias: 'escape'\n    }\n    var escape =\n      /\\\\(?:x[\\da-fA-F]{2}|u[\\da-fA-F]{4}|u\\{[\\da-fA-F]+\\}|0[0-7]{0,2}|[123][0-7]{2}|c[a-zA-Z]|.)/\n    var charSet = {\n      pattern: /\\.|\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    }\n    var charSetWithoutDot = {\n      pattern: /\\\\[wsd]|\\\\p\\{[^{}]+\\}/i,\n      alias: 'class-name'\n    }\n    var rangeChar = '(?:[^\\\\\\\\-]|' + escape.source + ')'\n    var range = RegExp(rangeChar + '-' + rangeChar) // the name of a capturing group\n    var groupName = {\n      pattern: /(<|')[^<>']+(?=[>']$)/,\n      lookbehind: true,\n      alias: 'variable'\n    }\n    Prism.languages.regex = {\n      'char-class': {\n        pattern: /((?:^|[^\\\\])(?:\\\\\\\\)*)\\[(?:[^\\\\\\]]|\\\\[\\s\\S])*\\]/,\n        lookbehind: true,\n        inside: {\n          'char-class-negation': {\n            pattern: /(^\\[)\\^/,\n            lookbehind: true,\n            alias: 'operator'\n          },\n          'char-class-punctuation': {\n            pattern: /^\\[|\\]$/,\n            alias: 'punctuation'\n          },\n          range: {\n            pattern: range,\n            inside: {\n              escape: escape,\n              'range-punctuation': {\n                pattern: /-/,\n                alias: 'operator'\n              }\n            }\n          },\n          'special-escape': specialEscape,\n          'char-set': charSetWithoutDot,\n          escape: escape\n        }\n      },\n      'special-escape': specialEscape,\n      'char-set': charSet,\n      backreference: [\n        {\n          // a backreference which is not an octal escape\n          pattern: /\\\\(?![123][0-7]{2})[1-9]/,\n          alias: 'keyword'\n        },\n        {\n          pattern: /\\\\k<[^<>']+>/,\n          alias: 'keyword',\n          inside: {\n            'group-name': groupName\n          }\n        }\n      ],\n      anchor: {\n        pattern: /[$^]|\\\\[ABbGZz]/,\n        alias: 'function'\n      },\n      escape: escape,\n      group: [\n        {\n          // https://docs.oracle.com/javase/10/docs/api/java/util/regex/Pattern.html\n          // https://docs.microsoft.com/en-us/dotnet/standard/base-types/regular-expression-language-quick-reference?view=netframework-4.7.2#grouping-constructs\n          // (), (?<name>), (?'name'), (?>), (?:), (?=), (?!), (?<=), (?<!), (?is-m), (?i-m:)\n          pattern:\n            /\\((?:\\?(?:<[^<>']+>|'[^<>']+'|[>:]|<?[=!]|[idmnsuxU]+(?:-[idmnsuxU]+)?:?))?/,\n          alias: 'punctuation',\n          inside: {\n            'group-name': groupName\n          }\n        },\n        {\n          pattern: /\\)/,\n          alias: 'punctuation'\n        }\n      ],\n      quantifier: {\n        pattern: /(?:[+*?]|\\{\\d+(?:,\\d*)?\\})[?+]?/,\n        alias: 'number'\n      },\n      alternation: {\n        pattern: /\\|/,\n        alias: 'keyword'\n      }\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB;AAAC,OAAC,SAAUA,QAAO;AACjB,YAAI,gBAAgB;AAAA,UAClB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AACA,YAAI,SACF;AACF,YAAI,UAAU;AAAA,UACZ,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AACA,YAAI,oBAAoB;AAAA,UACtB,SAAS;AAAA,UACT,OAAO;AAAA,QACT;AACA,YAAI,YAAY,iBAAiB,OAAO,SAAS;AACjD,YAAI,QAAQ,OAAO,YAAY,MAAM,SAAS;AAC9C,YAAI,YAAY;AAAA,UACd,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,QACT;AACA,QAAAA,OAAM,UAAU,QAAQ;AAAA,UACtB,cAAc;AAAA,YACZ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,cACN,uBAAuB;AAAA,gBACrB,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,OAAO;AAAA,cACT;AAAA,cACA,0BAA0B;AAAA,gBACxB,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,cACA,OAAO;AAAA,gBACL,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN;AAAA,kBACA,qBAAqB;AAAA,oBACnB,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,cACA,kBAAkB;AAAA,cAClB,YAAY;AAAA,cACZ;AAAA,YACF;AAAA,UACF;AAAA,UACA,kBAAkB;AAAA,UAClB,YAAY;AAAA,UACZ,eAAe;AAAA,YACb;AAAA;AAAA,cAEE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,cAAc;AAAA,cAChB;AAAA,YACF;AAAA,UACF;AAAA,UACA,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA;AAAA,UACA,OAAO;AAAA,YACL;AAAA;AAAA;AAAA;AAAA,cAIE,SACE;AAAA,cACF,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,cAAc;AAAA,cAChB;AAAA,YACF;AAAA,YACA;AAAA,cACE,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,aAAa;AAAA,YACX,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}