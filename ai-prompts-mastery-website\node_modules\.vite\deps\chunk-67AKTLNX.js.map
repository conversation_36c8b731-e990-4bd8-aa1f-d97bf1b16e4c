{"version": 3, "sources": ["../../refractor/lang/hlsl.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = hlsl\nhlsl.displayName = 'hlsl'\nhlsl.aliases = []\nfunction hlsl(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.hlsl = Prism.languages.extend('c', {\n    // Regarding keywords and class names:\n    // The list of all keywords was split into 'keyword' and 'class-name' tokens based on whether they are capitalized.\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-keywords\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-reserved-words\n    'class-name': [\n      Prism.languages.c['class-name'],\n      /\\b(?:AppendStructuredBuffer|BlendState|Buffer|ByteAddressBuffer|CompileShader|ComputeShader|ConsumeStructuredBuffer|DepthStencilState|DepthStencilView|DomainShader|GeometryShader|Hullshader|InputPatch|LineStream|OutputPatch|PixelShader|PointStream|RWBuffer|RWByteAddressBuffer|RWStructuredBuffer|RWTexture(?:1D|1DArray|2D|2DArray|3D)|RasterizerState|RenderTargetView|SamplerComparisonState|SamplerState|StructuredBuffer|Texture(?:1D|1DArray|2D|2DArray|2DMS|2DMSArray|3D|Cube|CubeArray)|TriangleStream|VertexShader)\\b/\n    ],\n    keyword: [\n      // HLSL keyword\n      /\\b(?:asm|asm_fragment|auto|break|case|catch|cbuffer|centroid|char|class|column_major|compile|compile_fragment|const|const_cast|continue|default|delete|discard|do|dynamic_cast|else|enum|explicit|export|extern|for|friend|fxgroup|goto|groupshared|if|in|inline|inout|interface|line|lineadj|linear|long|matrix|mutable|namespace|new|nointerpolation|noperspective|operator|out|packoffset|pass|pixelfragment|point|precise|private|protected|public|register|reinterpret_cast|return|row_major|sample|sampler|shared|short|signed|sizeof|snorm|stateblock|stateblock_state|static|static_cast|string|struct|switch|tbuffer|technique|technique10|technique11|template|texture|this|throw|triangle|triangleadj|try|typedef|typename|uniform|union|unorm|unsigned|using|vector|vertexfragment|virtual|void|volatile|while)\\b/, // scalar, vector, and matrix types\n      /\\b(?:bool|double|dword|float|half|int|min(?:10float|12int|16(?:float|int|uint))|uint)(?:[1-4](?:x[1-4])?)?\\b/\n    ],\n    // https://docs.microsoft.com/en-us/windows/win32/direct3dhlsl/dx-graphics-hlsl-appendix-grammar#floating-point-numbers\n    number:\n      /(?:(?:\\b\\d+(?:\\.\\d*)?|\\B\\.\\d+)(?:[eE][+-]?\\d+)?|\\b0x[\\da-fA-F]+)[fFhHlLuU]?\\b/,\n    boolean: /\\b(?:false|true)\\b/\n  })\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,aAAa;AACjB,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,SAAS,UAAU;AACzB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,QAKjD,cAAc;AAAA,UACZ,MAAM,UAAU,EAAE,YAAY;AAAA,UAC9B;AAAA,QACF;AAAA,QACA,SAAS;AAAA;AAAA,UAEP;AAAA;AAAA,UACA;AAAA,QACF;AAAA;AAAA,QAEA,QACE;AAAA,QACF,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}