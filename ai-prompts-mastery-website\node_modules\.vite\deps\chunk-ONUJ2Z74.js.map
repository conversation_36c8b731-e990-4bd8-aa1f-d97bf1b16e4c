{"version": 3, "sources": ["../../refractor/lang/coq.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = coq\ncoq.displayName = 'coq'\ncoq.aliases = []\nfunction coq(Prism) {\n  ;(function (Prism) {\n    // https://github.com/coq/coq\n    var commentSource = /\\(\\*(?:[^(*]|\\((?!\\*)|\\*(?!\\))|<self>)*\\*\\)/.source\n    for (var i = 0; i < 2; i++) {\n      commentSource = commentSource.replace(/<self>/g, function () {\n        return commentSource\n      })\n    }\n    commentSource = commentSource.replace(/<self>/g, '[]')\n    Prism.languages.coq = {\n      comment: RegExp(commentSource),\n      string: {\n        pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n        greedy: true\n      },\n      attribute: [\n        {\n          pattern: RegExp(\n            /#\\[(?:[^\\[\\](\"]|\"(?:[^\"]|\"\")*\"(?!\")|\\((?!\\*)|<comment>)*\\]/.source.replace(\n              /<comment>/g,\n              function () {\n                return commentSource\n              }\n            )\n          ),\n          greedy: true,\n          alias: 'attr-name',\n          inside: {\n            comment: RegExp(commentSource),\n            string: {\n              pattern: /\"(?:[^\"]|\"\")*\"(?!\")/,\n              greedy: true\n            },\n            operator: /=/,\n            punctuation: /^#\\[|\\]$|[,()]/\n          }\n        },\n        {\n          pattern:\n            /\\b(?:Cumulative|Global|Local|Monomorphic|NonCumulative|Polymorphic|Private|Program)\\b/,\n          alias: 'attr-name'\n        }\n      ],\n      keyword:\n        /\\b(?:Abort|About|Add|Admit|Admitted|All|Arguments|As|Assumptions|Axiom|Axioms|Back|BackTo|Backtrace|BinOp|BinOpSpec|BinRel|Bind|Blacklist|Canonical|Case|Cd|Check|Class|Classes|Close|CoFixpoint|CoInductive|Coercion|Coercions|Collection|Combined|Compute|Conjecture|Conjectures|Constant|Constants|Constraint|Constructors|Context|Corollary|Create|CstOp|Custom|Cut|Debug|Declare|Defined|Definition|Delimit|Dependencies|Dependent|Derive|Diffs|Drop|Elimination|End|Entry|Equality|Eval|Example|Existential|Existentials|Existing|Export|Extern|Extraction|Fact|Fail|Field|File|Firstorder|Fixpoint|Flags|Focus|From|Funclass|Function|Functional|GC|Generalizable|Goal|Grab|Grammar|Graph|Guarded|Haskell|Heap|Hide|Hint|HintDb|Hints|Hypotheses|Hypothesis|IF|Identity|Immediate|Implicit|Implicits|Import|Include|Induction|Inductive|Infix|Info|Initial|InjTyp|Inline|Inspect|Instance|Instances|Intro|Intros|Inversion|Inversion_clear|JSON|Language|Left|Lemma|Let|Lia|Libraries|Library|Load|LoadPath|Locate|Ltac|Ltac2|ML|Match|Method|Minimality|Module|Modules|Morphism|Next|NoInline|Notation|Number|OCaml|Obligation|Obligations|Opaque|Open|Optimize|Parameter|Parameters|Parametric|Path|Paths|Prenex|Preterm|Primitive|Print|Profile|Projections|Proof|Prop|PropBinOp|PropOp|PropUOp|Property|Proposition|Pwd|Qed|Quit|Rec|Record|Recursive|Redirect|Reduction|Register|Relation|Remark|Remove|Require|Reserved|Reset|Resolve|Restart|Rewrite|Right|Ring|Rings|SProp|Saturate|Save|Scheme|Scope|Scopes|Search|SearchHead|SearchPattern|SearchRewrite|Section|Separate|Set|Setoid|Show|Signatures|Solve|Solver|Sort|Sortclass|Sorted|Spec|Step|Strategies|Strategy|String|Structure|SubClass|Subgraph|SuchThat|Tactic|Term|TestCompile|Theorem|Time|Timeout|To|Transparent|Type|Typeclasses|Types|Typing|UnOp|UnOpSpec|Undelimit|Undo|Unfocus|Unfocused|Unfold|Universe|Universes|Unshelve|Variable|Variables|Variant|Verbose|View|Visibility|Zify|_|apply|as|at|by|cofix|else|end|exists|exists2|fix|for|forall|fun|if|in|let|match|measure|move|removed|return|struct|then|using|wf|where|with)\\b/,\n      number:\n        /\\b(?:0x[a-f0-9][a-f0-9_]*(?:\\.[a-f0-9_]+)?(?:p[+-]?\\d[\\d_]*)?|\\d[\\d_]*(?:\\.[\\d_]+)?(?:e[+-]?\\d[\\d_]*)?)\\b/i,\n      punct: {\n        pattern: /@\\{|\\{\\||\\[=|:>/,\n        alias: 'punctuation'\n      },\n      operator:\n        /\\/\\\\|\\\\\\/|\\.{2,3}|:{1,2}=|\\*\\*|[-=]>|<(?:->?|[+:=>]|<:)|>(?:=|->)|\\|[-|]?|[-!%&*+/<=>?@^~']/,\n      punctuation: /\\.\\(|`\\(|@\\{|`\\{|\\{\\||\\[=|:>|[:.,;(){}\\[\\]]/\n    }\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,QAAI,cAAc;AAClB,QAAI,UAAU,CAAC;AACf,aAAS,IAAI,OAAO;AAClB;AAAC,OAAC,SAAUA,QAAO;AAEjB,YAAI,gBAAgB,8CAA8C;AAClE,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,0BAAgB,cAAc,QAAQ,WAAW,WAAY;AAC3D,mBAAO;AAAA,UACT,CAAC;AAAA,QACH;AACA,wBAAgB,cAAc,QAAQ,WAAW,IAAI;AACrD,QAAAA,OAAM,UAAU,MAAM;AAAA,UACpB,SAAS,OAAO,aAAa;AAAA,UAC7B,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA,WAAW;AAAA,YACT;AAAA,cACE,SAAS;AAAA,gBACP,6DAA6D,OAAO;AAAA,kBAClE;AAAA,kBACA,WAAY;AACV,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF;AAAA,cACA,QAAQ;AAAA,cACR,OAAO;AAAA,cACP,QAAQ;AAAA,gBACN,SAAS,OAAO,aAAa;AAAA,gBAC7B,QAAQ;AAAA,kBACN,SAAS;AAAA,kBACT,QAAQ;AAAA,gBACV;AAAA,gBACA,UAAU;AAAA,gBACV,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA;AAAA,cACE,SACE;AAAA,cACF,OAAO;AAAA,YACT;AAAA,UACF;AAAA,UACA,SACE;AAAA,UACF,QACE;AAAA,UACF,OAAO;AAAA,YACL,SAAS;AAAA,YACT,OAAO;AAAA,UACT;AAAA,UACA,UACE;AAAA,UACF,aAAa;AAAA,QACf;AAAA,MACF,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}