{"version": 3, "sources": ["../../refractor/lang/icu-message-format.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = icuMessageFormat\nicuMessageFormat.displayName = 'icuMessageFormat'\nicuMessageFormat.aliases = []\nfunction icuMessageFormat(Prism) {\n  // https://unicode-org.github.io/icu/userguide/format_parse/messages/\n  // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/MessageFormat.html\n  ;(function (Prism) {\n    /**\n     * @param {string} source\n     * @param {number} level\n     * @returns {string}\n     */\n    function nested(source, level) {\n      if (level <= 0) {\n        return /[]/.source\n      } else {\n        return source.replace(/<SELF>/g, function () {\n          return nested(source, level - 1)\n        })\n      }\n    }\n    var stringPattern = /'[{}:=,](?:[^']|'')*'(?!')/\n    var escape = {\n      pattern: /''/,\n      greedy: true,\n      alias: 'operator'\n    }\n    var string = {\n      pattern: stringPattern,\n      greedy: true,\n      inside: {\n        escape: escape\n      }\n    }\n    var argumentSource = nested(\n      /\\{(?:[^{}']|'(?![{},'])|''|<STR>|<SELF>)*\\}/.source.replace(\n        /<STR>/g,\n        function () {\n          return stringPattern.source\n        }\n      ),\n      8\n    )\n    var nestedMessage = {\n      pattern: RegExp(argumentSource),\n      inside: {\n        message: {\n          pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n          lookbehind: true,\n          inside: null // see below\n        },\n        'message-delimiter': {\n          pattern: /./,\n          alias: 'punctuation'\n        }\n      }\n    }\n    Prism.languages['icu-message-format'] = {\n      argument: {\n        pattern: RegExp(argumentSource),\n        greedy: true,\n        inside: {\n          content: {\n            pattern: /^(\\{)[\\s\\S]+(?=\\}$)/,\n            lookbehind: true,\n            inside: {\n              'argument-name': {\n                pattern: /^(\\s*)[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'choice-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4c/classicu_1_1ChoiceFormat.html#details\n                pattern: /^(\\s*,\\s*choice\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  punctuation: /\\|/,\n                  range: {\n                    pattern: /^(\\s*)[+-]?(?:\\d+(?:\\.\\d*)?|\\u221e)\\s*[<#\\u2264]/,\n                    lookbehind: true,\n                    inside: {\n                      operator: /[<#\\u2264]/,\n                      number: /\\S+/\n                    }\n                  },\n                  rest: null // see below\n                }\n              },\n              'plural-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/PluralFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern:\n                  /^(\\s*,\\s*(?:plural|selectordinal)\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  offset: /^offset:\\s*\\d+/,\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /=\\d+|[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^(?:few|many|one|other|two|zero)$/\n                    }\n                  }\n                }\n              },\n              'select-style': {\n                // https://unicode-org.github.io/icu-docs/apidoc/released/icu4j/com/ibm/icu/text/SelectFormat.html#:~:text=Patterns%20and%20Their%20Interpretation\n                pattern: /^(\\s*,\\s*select\\s*,\\s*)\\S(?:[\\s\\S]*\\S)?/,\n                lookbehind: true,\n                inside: {\n                  'nested-message': nestedMessage,\n                  selector: {\n                    pattern: /[^{}:=,\\s]+/,\n                    inside: {\n                      keyword: /^other$/\n                    }\n                  }\n                }\n              },\n              keyword: /\\b(?:choice|plural|select|selectordinal)\\b/,\n              'arg-type': {\n                pattern: /\\b(?:date|duration|number|ordinal|spellout|time)\\b/,\n                alias: 'keyword'\n              },\n              'arg-skeleton': {\n                pattern: /(,\\s*)::[^{}:=,\\s]+/,\n                lookbehind: true\n              },\n              'arg-style': {\n                pattern:\n                  /(,\\s*)(?:currency|full|integer|long|medium|percent|short)(?=\\s*$)/,\n                lookbehind: true\n              },\n              'arg-style-text': {\n                pattern: RegExp(\n                  /(^\\s*,\\s*(?=\\S))/.source +\n                    nested(/(?:[^{}']|'[^']*'|\\{(?:<SELF>)?\\})+/.source, 8) +\n                    '$'\n                ),\n                lookbehind: true,\n                alias: 'string'\n              },\n              punctuation: /,/\n            }\n          },\n          'argument-delimiter': {\n            pattern: /./,\n            alias: 'operator'\n          }\n        }\n      },\n      escape: escape,\n      string: string\n    }\n    nestedMessage.inside.message.inside = Prism.languages['icu-message-format']\n    Prism.languages['icu-message-format'].argument.inside.content.inside[\n      'choice-style'\n    ].inside.rest = Prism.languages['icu-message-format']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,qBAAiB,cAAc;AAC/B,qBAAiB,UAAU,CAAC;AAC5B,aAAS,iBAAiB,OAAO;AAG/B;AAAC,OAAC,SAAUA,QAAO;AAMjB,iBAAS,OAAO,QAAQ,OAAO;AAC7B,cAAI,SAAS,GAAG;AACd,mBAAO,KAAK;AAAA,UACd,OAAO;AACL,mBAAO,OAAO,QAAQ,WAAW,WAAY;AAC3C,qBAAO,OAAO,QAAQ,QAAQ,CAAC;AAAA,YACjC,CAAC;AAAA,UACH;AAAA,QACF;AACA,YAAI,gBAAgB;AACpB,YAAI,SAAS;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,OAAO;AAAA,QACT;AACA,YAAI,SAAS;AAAA,UACX,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN;AAAA,UACF;AAAA,QACF;AACA,YAAI,iBAAiB;AAAA,UACnB,8CAA8C,OAAO;AAAA,YACnD;AAAA,YACA,WAAY;AACV,qBAAO,cAAc;AAAA,YACvB;AAAA,UACF;AAAA,UACA;AAAA,QACF;AACA,YAAI,gBAAgB;AAAA,UAClB,SAAS,OAAO,cAAc;AAAA,UAC9B,QAAQ;AAAA,YACN,SAAS;AAAA,cACP,SAAS;AAAA,cACT,YAAY;AAAA,cACZ,QAAQ;AAAA;AAAA,YACV;AAAA,YACA,qBAAqB;AAAA,cACnB,SAAS;AAAA,cACT,OAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,oBAAoB,IAAI;AAAA,UACtC,UAAU;AAAA,YACR,SAAS,OAAO,cAAc;AAAA,YAC9B,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,YAAY;AAAA,gBACZ,QAAQ;AAAA,kBACN,iBAAiB;AAAA,oBACf,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,gBAAgB;AAAA;AAAA,oBAEd,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,QAAQ;AAAA,sBACN,aAAa;AAAA,sBACb,OAAO;AAAA,wBACL,SAAS;AAAA,wBACT,YAAY;AAAA,wBACZ,QAAQ;AAAA,0BACN,UAAU;AAAA,0BACV,QAAQ;AAAA,wBACV;AAAA,sBACF;AAAA,sBACA,MAAM;AAAA;AAAA,oBACR;AAAA,kBACF;AAAA,kBACA,gBAAgB;AAAA;AAAA,oBAEd,SACE;AAAA,oBACF,YAAY;AAAA,oBACZ,QAAQ;AAAA,sBACN,QAAQ;AAAA,sBACR,kBAAkB;AAAA,sBAClB,UAAU;AAAA,wBACR,SAAS;AAAA,wBACT,QAAQ;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA,gBAAgB;AAAA;AAAA,oBAEd,SAAS;AAAA,oBACT,YAAY;AAAA,oBACZ,QAAQ;AAAA,sBACN,kBAAkB;AAAA,sBAClB,UAAU;AAAA,wBACR,SAAS;AAAA,wBACT,QAAQ;AAAA,0BACN,SAAS;AAAA,wBACX;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF;AAAA,kBACA,SAAS;AAAA,kBACT,YAAY;AAAA,oBACV,SAAS;AAAA,oBACT,OAAO;AAAA,kBACT;AAAA,kBACA,gBAAgB;AAAA,oBACd,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,aAAa;AAAA,oBACX,SACE;AAAA,oBACF,YAAY;AAAA,kBACd;AAAA,kBACA,kBAAkB;AAAA,oBAChB,SAAS;AAAA,sBACP,mBAAmB,SACjB,OAAO,sCAAsC,QAAQ,CAAC,IACtD;AAAA,oBACJ;AAAA,oBACA,YAAY;AAAA,oBACZ,OAAO;AAAA,kBACT;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,sBAAsB;AAAA,gBACpB,SAAS;AAAA,gBACT,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AACA,sBAAc,OAAO,QAAQ,SAASA,OAAM,UAAU,oBAAoB;AAC1E,QAAAA,OAAM,UAAU,oBAAoB,EAAE,SAAS,OAAO,QAAQ,OAC5D,cACF,EAAE,OAAO,OAAOA,OAAM,UAAU,oBAAoB;AAAA,MACtD,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}