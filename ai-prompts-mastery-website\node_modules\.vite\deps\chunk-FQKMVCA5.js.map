{"version": 3, "sources": ["../../highlight.js/lib/languages/autoit.js"], "sourcesContent": ["/*\nLanguage: AutoIt\nAuthor: <PERSON><PERSON> <<EMAIL>>\nDescription: AutoIt language definition\nCategory: scripting\n*/\n\n/** @type LanguageFn */\nfunction autoit(hljs) {\n  const KEYWORDS = 'ByRef Case Const ContinueCase ContinueLoop ' +\n        'Dim Do Else ElseIf EndFunc EndIf EndSelect ' +\n        'EndSwitch EndWith Enum Exit ExitLoop For Func ' +\n        'Global If In Local Next ReDim Return Select Static ' +\n        'Step Switch Then To Until Volatile WEnd While With';\n\n  const DIRECTIVES = [\n    \"EndRegion\",\n    \"forcedef\",\n    \"forceref\",\n    \"ignorefunc\",\n    \"include\",\n    \"include-once\",\n    \"NoTrayIcon\",\n    \"OnAutoItStartRegister\",\n    \"pragma\",\n    \"Region\",\n    \"RequireAdmin\",\n    \"Tidy_Off\",\n    \"Tidy_On\",\n    \"Tidy_Parameters\"\n  ];\n  \n  const LITERAL = 'True False And Null Not Or Default';\n\n  const BUILT_IN\n          = 'Abs ACos AdlibRegister AdlibUnRegister Asc AscW ASin Assign ATan AutoItSetOption AutoItWinGetTitle AutoItWinSetTitle Beep Binary BinaryLen BinaryMid BinaryToString BitAND BitNOT BitOR BitRotate BitShift BitXOR BlockInput Break Call CDTray Ceiling Chr ChrW ClipGet ClipPut ConsoleRead ConsoleWrite ConsoleWriteError ControlClick ControlCommand ControlDisable ControlEnable ControlFocus ControlGetFocus ControlGetHandle ControlGetPos ControlGetText ControlHide ControlListView ControlMove ControlSend ControlSetText ControlShow ControlTreeView Cos Dec DirCopy DirCreate DirGetSize DirMove DirRemove DllCall DllCallAddress DllCallbackFree DllCallbackGetPtr DllCallbackRegister DllClose DllOpen DllStructCreate DllStructGetData DllStructGetPtr DllStructGetSize DllStructSetData DriveGetDrive DriveGetFileSystem DriveGetLabel DriveGetSerial DriveGetType DriveMapAdd DriveMapDel DriveMapGet DriveSetLabel DriveSpaceFree DriveSpaceTotal DriveStatus EnvGet EnvSet EnvUpdate Eval Execute Exp FileChangeDir FileClose FileCopy FileCreateNTFSLink FileCreateShortcut FileDelete FileExists FileFindFirstFile FileFindNextFile FileFlush FileGetAttrib FileGetEncoding FileGetLongName FileGetPos FileGetShortcut FileGetShortName FileGetSize FileGetTime FileGetVersion FileInstall FileMove FileOpen FileOpenDialog FileRead FileReadLine FileReadToArray FileRecycle FileRecycleEmpty FileSaveDialog FileSelectFolder FileSetAttrib FileSetEnd FileSetPos FileSetTime FileWrite FileWriteLine Floor FtpSetProxy FuncName GUICreate GUICtrlCreateAvi GUICtrlCreateButton GUICtrlCreateCheckbox GUICtrlCreateCombo GUICtrlCreateContextMenu GUICtrlCreateDate GUICtrlCreateDummy GUICtrlCreateEdit GUICtrlCreateGraphic GUICtrlCreateGroup GUICtrlCreateIcon GUICtrlCreateInput GUICtrlCreateLabel GUICtrlCreateList GUICtrlCreateListView GUICtrlCreateListViewItem GUICtrlCreateMenu GUICtrlCreateMenuItem GUICtrlCreateMonthCal GUICtrlCreateObj GUICtrlCreatePic GUICtrlCreateProgress GUICtrlCreateRadio GUICtrlCreateSlider GUICtrlCreateTab GUICtrlCreateTabItem GUICtrlCreateTreeView GUICtrlCreateTreeViewItem GUICtrlCreateUpdown GUICtrlDelete GUICtrlGetHandle GUICtrlGetState GUICtrlRead GUICtrlRecvMsg GUICtrlRegisterListViewSort GUICtrlSendMsg GUICtrlSendToDummy GUICtrlSetBkColor GUICtrlSetColor GUICtrlSetCursor GUICtrlSetData GUICtrlSetDefBkColor GUICtrlSetDefColor GUICtrlSetFont GUICtrlSetGraphic GUICtrlSetImage GUICtrlSetLimit GUICtrlSetOnEvent GUICtrlSetPos GUICtrlSetResizing GUICtrlSetState GUICtrlSetStyle GUICtrlSetTip GUIDelete GUIGetCursorInfo GUIGetMsg GUIGetStyle GUIRegisterMsg GUISetAccelerators GUISetBkColor GUISetCoord GUISetCursor GUISetFont GUISetHelp GUISetIcon GUISetOnEvent GUISetState GUISetStyle GUIStartGroup GUISwitch Hex HotKeySet HttpSetProxy HttpSetUserAgent HWnd InetClose InetGet InetGetInfo InetGetSize InetRead IniDelete IniRead IniReadSection IniReadSectionNames IniRenameSection IniWrite IniWriteSection InputBox Int IsAdmin IsArray IsBinary IsBool IsDeclared IsDllStruct IsFloat IsFunc IsHWnd IsInt IsKeyword IsNumber IsObj IsPtr IsString Log MemGetStats Mod MouseClick MouseClickDrag MouseDown MouseGetCursor MouseGetPos MouseMove MouseUp MouseWheel MsgBox Number ObjCreate ObjCreateInterface ObjEvent ObjGet ObjName OnAutoItExitRegister OnAutoItExitUnRegister Ping PixelChecksum PixelGetColor PixelSearch ProcessClose ProcessExists ProcessGetStats ProcessList ProcessSetPriority ProcessWait ProcessWaitClose ProgressOff ProgressOn ProgressSet Ptr Random RegDelete RegEnumKey RegEnumVal RegRead RegWrite Round Run RunAs RunAsWait RunWait Send SendKeepActive SetError SetExtended ShellExecute ShellExecuteWait Shutdown Sin Sleep SoundPlay SoundSetWaveVolume SplashImageOn SplashOff SplashTextOn Sqrt SRandom StatusbarGetText StderrRead StdinWrite StdioClose StdoutRead String StringAddCR StringCompare StringFormat StringFromASCIIArray StringInStr StringIsAlNum StringIsAlpha StringIsASCII StringIsDigit StringIsFloat StringIsInt StringIsLower StringIsSpace StringIsUpper StringIsXDigit StringLeft StringLen StringLower StringMid StringRegExp StringRegExpReplace StringReplace StringReverse StringRight StringSplit StringStripCR StringStripWS StringToASCIIArray StringToBinary StringTrimLeft StringTrimRight StringUpper Tan TCPAccept TCPCloseSocket TCPConnect TCPListen TCPNameToIP TCPRecv TCPSend TCPShutdown, UDPShutdown TCPStartup, UDPStartup TimerDiff TimerInit ToolTip TrayCreateItem TrayCreateMenu TrayGetMsg TrayItemDelete TrayItemGetHandle TrayItemGetState TrayItemGetText TrayItemSetOnEvent TrayItemSetState TrayItemSetText TraySetClick TraySetIcon TraySetOnEvent TraySetPauseIcon TraySetState TraySetToolTip TrayTip UBound UDPBind UDPCloseSocket UDPOpen UDPRecv UDPSend VarGetType WinActivate WinActive WinClose WinExists WinFlash WinGetCaretPos WinGetClassList WinGetClientSize WinGetHandle WinGetPos WinGetProcess WinGetState WinGetText WinGetTitle WinKill WinList WinMenuSelectItem WinMinimizeAll WinMinimizeAllUndo WinMove WinSetOnTop WinSetState WinSetTitle WinSetTrans WinWait WinWaitActive WinWaitClose WinWaitNotActive';\n\n  const COMMENT = {\n    variants: [\n      hljs.COMMENT(';', '$', {\n        relevance: 0\n      }),\n      hljs.COMMENT('#cs', '#ce'),\n      hljs.COMMENT('#comments-start', '#comments-end')\n    ]\n  };\n\n  const VARIABLE = {\n    begin: '\\\\$[A-z0-9_]+'\n  };\n\n  const STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /\"/,\n        end: /\"/,\n        contains: [{\n          begin: /\"\"/,\n          relevance: 0\n        }]\n      },\n      {\n        begin: /'/,\n        end: /'/,\n        contains: [{\n          begin: /''/,\n          relevance: 0\n        }]\n      }\n    ]\n  };\n\n  const NUMBER = {\n    variants: [\n      hljs.BINARY_NUMBER_MODE,\n      hljs.C_NUMBER_MODE\n    ]\n  };\n\n  const PREPROCESSOR = {\n    className: 'meta',\n    begin: '#',\n    end: '$',\n    keywords: {\n      'meta-keyword': DIRECTIVES\n    },\n    contains: [\n      {\n        begin: /\\\\\\n/,\n        relevance: 0\n      },\n      {\n        beginKeywords: 'include',\n        keywords: {\n          'meta-keyword': 'include'\n        },\n        end: '$',\n        contains: [\n          STRING,\n          {\n            className: 'meta-string',\n            variants: [\n              {\n                begin: '<',\n                end: '>'\n              },\n              {\n                begin: /\"/,\n                end: /\"/,\n                contains: [{\n                  begin: /\"\"/,\n                  relevance: 0\n                }]\n              },\n              {\n                begin: /'/,\n                end: /'/,\n                contains: [{\n                  begin: /''/,\n                  relevance: 0\n                }]\n              }\n            ]\n          }\n        ]\n      },\n      STRING,\n      COMMENT\n    ]\n  };\n\n  const CONSTANT = {\n    className: 'symbol',\n    // begin: '@',\n    // end: '$',\n    // keywords: 'AppDataCommonDir AppDataDir AutoItExe AutoItPID AutoItVersion AutoItX64 COM_EventObj CommonFilesDir Compiled ComputerName ComSpec CPUArch CR CRLF DesktopCommonDir DesktopDepth DesktopDir DesktopHeight DesktopRefresh DesktopWidth DocumentsCommonDir error exitCode exitMethod extended FavoritesCommonDir FavoritesDir GUI_CtrlHandle GUI_CtrlId GUI_DragFile GUI_DragId GUI_DropId GUI_WinHandle HomeDrive HomePath HomeShare HotKeyPressed HOUR IPAddress1 IPAddress2 IPAddress3 IPAddress4 KBLayout LF LocalAppDataDir LogonDNSDomain LogonDomain LogonServer MDAY MIN MON MSEC MUILang MyDocumentsDir NumParams OSArch OSBuild OSLang OSServicePack OSType OSVersion ProgramFilesDir ProgramsCommonDir ProgramsDir ScriptDir ScriptFullPath ScriptLineNumber ScriptName SEC StartMenuCommonDir StartMenuDir StartupCommonDir StartupDir SW_DISABLE SW_ENABLE SW_HIDE SW_LOCK SW_MAXIMIZE SW_MINIMIZE SW_RESTORE SW_SHOW SW_SHOWDEFAULT SW_SHOWMAXIMIZED SW_SHOWMINIMIZED SW_SHOWMINNOACTIVE SW_SHOWNA SW_SHOWNOACTIVATE SW_SHOWNORMAL SW_UNLOCK SystemDir TAB TempDir TRAY_ID TrayIconFlashing TrayIconVisible UserName UserProfileDir WDAY WindowsDir WorkingDir YDAY YEAR',\n    // relevance: 5\n    begin: '@[A-z0-9_]+'\n  };\n\n  const FUNCTION = {\n    className: 'function',\n    beginKeywords: 'Func',\n    end: '$',\n    illegal: '\\\\$|\\\\[|%',\n    contains: [\n      hljs.UNDERSCORE_TITLE_MODE,\n      {\n        className: 'params',\n        begin: '\\\\(',\n        end: '\\\\)',\n        contains: [\n          VARIABLE,\n          STRING,\n          NUMBER\n        ]\n      }\n    ]\n  };\n\n  return {\n    name: 'AutoIt',\n    case_insensitive: true,\n    illegal: /\\/\\*/,\n    keywords: {\n      keyword: KEYWORDS,\n      built_in: BUILT_IN,\n      literal: LITERAL\n    },\n    contains: [\n      COMMENT,\n      VARIABLE,\n      STRING,\n      NUMBER,\n      PREPROCESSOR,\n      CONSTANT,\n      FUNCTION\n    ]\n  };\n}\n\nmodule.exports = autoit;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,OAAO,MAAM;AACpB,YAAM,WAAW;AAMjB,YAAM,aAAa;AAAA,QACjB;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,UAAU;AAEhB,YAAM,WACI;AAEV,YAAM,UAAU;AAAA,QACd,UAAU;AAAA,UACR,KAAK,QAAQ,KAAK,KAAK;AAAA,YACrB,WAAW;AAAA,UACb,CAAC;AAAA,UACD,KAAK,QAAQ,OAAO,KAAK;AAAA,UACzB,KAAK,QAAQ,mBAAmB,eAAe;AAAA,QACjD;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,MACT;AAEA,YAAM,SAAS;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,YACb,CAAC;AAAA,UACH;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU,CAAC;AAAA,cACT,OAAO;AAAA,cACP,WAAW;AAAA,YACb,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,UAAU;AAAA,UACR,KAAK;AAAA,UACL,KAAK;AAAA,QACP;AAAA,MACF;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,UAAU;AAAA,UACR,gBAAgB;AAAA,QAClB;AAAA,QACA,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,eAAe;AAAA,YACf,UAAU;AAAA,cACR,gBAAgB;AAAA,YAClB;AAAA,YACA,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,gBACE,WAAW;AAAA,gBACX,UAAU;AAAA,kBACR;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,kBACP;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,UAAU,CAAC;AAAA,sBACT,OAAO;AAAA,sBACP,WAAW;AAAA,oBACb,CAAC;AAAA,kBACH;AAAA,kBACA;AAAA,oBACE,OAAO;AAAA,oBACP,KAAK;AAAA,oBACL,UAAU,CAAC;AAAA,sBACT,OAAO;AAAA,sBACP,WAAW;AAAA,oBACb,CAAC;AAAA,kBACH;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA;AAAA;AAAA;AAAA;AAAA,QAKX,OAAO;AAAA,MACT;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK;AAAA,UACL;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR;AAAA,cACA;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAEA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,UACV,SAAS;AAAA,QACX;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}