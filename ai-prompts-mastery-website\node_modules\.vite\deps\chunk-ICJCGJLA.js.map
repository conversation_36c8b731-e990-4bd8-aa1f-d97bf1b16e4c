{"version": 3, "sources": ["../../refractor/lang/sass.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = sass\nsass.displayName = 'sass'\nsass.aliases = []\nfunction sass(Prism) {\n  ;(function (Prism) {\n    Prism.languages.sass = Prism.languages.extend('css', {\n      // Sass comments don't need to be closed, only indented\n      comment: {\n        pattern: /^([ \\t]*)\\/[\\/*].*(?:(?:\\r?\\n|\\r)\\1[ \\t].+)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    })\n    Prism.languages.insertBefore('sass', 'atrule', {\n      // We want to consume the whole line\n      'atrule-line': {\n        // Includes support for = and + shortcuts\n        pattern: /^(?:[ \\t]*)[@+=].+/m,\n        greedy: true,\n        inside: {\n          atrule: /(?:@[\\w-]+|[+=])/\n        }\n      }\n    })\n    delete Prism.languages.sass.atrule\n    var variable = /\\$[-\\w]+|#\\{\\$[-\\w]+\\}/\n    var operator = [\n      /[+*\\/%]|[=!]=|<=?|>=?|\\b(?:and|not|or)\\b/,\n      {\n        pattern: /(\\s)-(?=\\s)/,\n        lookbehind: true\n      }\n    ]\n    Prism.languages.insertBefore('sass', 'property', {\n      // We want to consume the whole line\n      'variable-line': {\n        pattern: /^[ \\t]*\\$.+/m,\n        greedy: true,\n        inside: {\n          punctuation: /:/,\n          variable: variable,\n          operator: operator\n        }\n      },\n      // We want to consume the whole line\n      'property-line': {\n        pattern: /^[ \\t]*(?:[^:\\s]+ *:.*|:[^:\\s].*)/m,\n        greedy: true,\n        inside: {\n          property: [\n            /[^:\\s]+(?=\\s*:)/,\n            {\n              pattern: /(:)[^:\\s]+/,\n              lookbehind: true\n            }\n          ],\n          punctuation: /:/,\n          variable: variable,\n          operator: operator,\n          important: Prism.languages.sass.important\n        }\n      }\n    })\n    delete Prism.languages.sass.property\n    delete Prism.languages.sass.important // Now that whole lines for other patterns are consumed,\n    // what's left should be selectors\n    Prism.languages.insertBefore('sass', 'punctuation', {\n      selector: {\n        pattern:\n          /^([ \\t]*)\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*(?:,(?:\\r?\\n|\\r)\\1[ \\t]+\\S(?:,[^,\\r\\n]+|[^,\\r\\n]*)(?:,[^,\\r\\n]+)*)*/m,\n        lookbehind: true,\n        greedy: true\n      }\n    })\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB;AAAC,OAAC,SAAUA,QAAO;AACjB,QAAAA,OAAM,UAAU,OAAOA,OAAM,UAAU,OAAO,OAAO;AAAA;AAAA,UAEnD,SAAS;AAAA,YACP,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AACD,QAAAA,OAAM,UAAU,aAAa,QAAQ,UAAU;AAAA;AAAA,UAE7C,eAAe;AAAA;AAAA,YAEb,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,QAAQ;AAAA,YACV;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAOA,OAAM,UAAU,KAAK;AAC5B,YAAI,WAAW;AACf,YAAI,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA,QACF;AACA,QAAAA,OAAM,UAAU,aAAa,QAAQ,YAAY;AAAA;AAAA,UAE/C,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,aAAa;AAAA,cACb;AAAA,cACA;AAAA,YACF;AAAA,UACF;AAAA;AAAA,UAEA,iBAAiB;AAAA,YACf,SAAS;AAAA,YACT,QAAQ;AAAA,YACR,QAAQ;AAAA,cACN,UAAU;AAAA,gBACR;AAAA,gBACA;AAAA,kBACE,SAAS;AAAA,kBACT,YAAY;AAAA,gBACd;AAAA,cACF;AAAA,cACA,aAAa;AAAA,cACb;AAAA,cACA;AAAA,cACA,WAAWA,OAAM,UAAU,KAAK;AAAA,YAClC;AAAA,UACF;AAAA,QACF,CAAC;AACD,eAAOA,OAAM,UAAU,KAAK;AAC5B,eAAOA,OAAM,UAAU,KAAK;AAE5B,QAAAA,OAAM,UAAU,aAAa,QAAQ,eAAe;AAAA,UAClD,UAAU;AAAA,YACR,SACE;AAAA,YACF,YAAY;AAAA,YACZ,QAAQ;AAAA,UACV;AAAA,QACF,CAAC;AAAA,MACH,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism"]}