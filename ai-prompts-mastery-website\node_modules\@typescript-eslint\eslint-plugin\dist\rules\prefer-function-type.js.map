{"version": 3, "file": "prefer-function-type.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-function-type.ts"], "names": [], "mappings": ";;;AACA,oDAA2E;AAC3E,wEAAsE;AAEtE,kCAAqC;AAExB,QAAA,OAAO,GAAG;IACrB,CAAC,sBAAc,CAAC,aAAa,CAAC,EAAE,cAAc;IAC9C,CAAC,sBAAc,CAAC,sBAAsB,CAAC,EAAE,WAAW;CAC5C,CAAC;AAEX,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,yEAAyE;YAC3E,WAAW,EAAE,WAAW;SACzB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,4BAA4B,EAC1B,6FAA6F;YAC/F,qCAAqC,EACnC,4JAA4J;SAC/J;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,IAAA,4BAAa,EAAC,OAAO,CAAC,CAAC;QAE1C;;;WAGG;QACH,SAAS,eAAe,CAAC,IAAqC;YAC5D,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC9B,OAAO,IAAI,CAAC;YACd,CAAC;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAExC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CACpE,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,oBAAoB,CAAC,MAAiC;YAC7D,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO,KAAK,CAAC;YACf,CAAC;YAED,QAAQ,MAAM,CAAC,IAAI,EAAE,CAAC;gBACpB,KAAK,sBAAc,CAAC,WAAW,CAAC;gBAChC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;gBACvC,KAAK,sBAAc,CAAC,WAAW;oBAC7B,OAAO,IAAI,CAAC;gBACd;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,WAAW,CAClB,MAA4B,EAC5B,IAA8D,EAC9D,cAA4C,IAAI;YAEhD,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,0BAA0B;gBACxD,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,+BAA+B,CAAC;gBACjE,MAAM,CAAC,UAAU,KAAK,SAAS,EAC/B,CAAC;gBACD,IACE,WAAW,EAAE,MAAM;oBACnB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EACnD,CAAC;oBACD,yGAAyG;oBACzG,uHAAuH;oBACvH,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,WAAW,CAAC,CAAC,CAAC;wBACpB,SAAS,EAAE,uCAAuC;wBAClD,IAAI,EAAE;4BACJ,aAAa,EAAE,IAAI,CAAC,EAAE,CAAC,IAAI;yBAC5B;qBACF,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;gBAED,MAAM,OAAO,GACX,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,wBAAwB,CAAC;gBAE/D,MAAM,GAAG,GAAG,OAAO;oBACjB,CAAC,CAAC,IAAI;oBACN,CAAC,CAAC,CAAC,KAAyB,EAAsB,EAAE;wBAChD,MAAM,KAAK,GAAuB,EAAE,CAAC;wBACrC,MAAM,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC9B,MAAM,QAAQ,GAAG,MAAM,CAAC,UAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;wBACrD,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;wBAChE,MAAM,QAAQ,GAAG,UAAU;6BACxB,iBAAiB,CAAC,MAAM,CAAC;6BACzB,MAAM,CAAC,UAAU,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAC;wBAC/C,IAAI,UAAU,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CACzD,QAAQ,GAAG,CAAC,CACb,EAAE,CAAC;wBACJ,MAAM,QAAQ,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;wBACrD,IAAI,QAAQ,EAAE,CAAC;4BACb,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;wBACvC,CAAC;wBACD,IAAI,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC;4BACtC,UAAU,GAAG,IAAI,UAAU,GAAG,CAAC;wBACjC,CAAC;wBAED,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAAE,CAAC;4BACxD,IAAI,IAAI,CAAC,cAAc,KAAK,SAAS,EAAE,CAAC;gCACtC,UAAU,GAAG,QAAQ,UAAU;qCAC5B,OAAO,EAAE;qCACT,KAAK,CACJ,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAChB,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAC7B,MAAM,UAAU,GAAG,QAAQ,EAAE,CAAC;4BACnC,CAAC;iCAAM,CAAC;gCACN,UAAU,GAAG,QAAQ,IAAI,CAAC,EAAE,CAAC,IAAI,MAAM,UAAU,GAAG,QAAQ,EAAE,CAAC;4BACjE,CAAC;wBACH,CAAC;wBAED,MAAM,gBAAgB,GACpB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,CAAC;wBAE7D,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;4BACnD,gBAAgB,EAChB,CAAC;4BACD,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,OAAO,EAAE,EAAE;gCACrD,OAAO,CACL,IAAI;oCACJ,CAAC,OAAO,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI;wCACpC,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,EAAE;wCACtB,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,CAAC;oCAC3B,IAAI,CACL,CAAC;4BACJ,CAAC,EAAE,EAAE,CAAC,CAAC;4BACP,sFAAsF;4BACtF,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC,CAAC;wBAChE,CAAC;6BAAM,CAAC;4BACN,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;gCACzB,IAAI,WAAW,GACb,OAAO,CAAC,IAAI,KAAK,uBAAe,CAAC,IAAI;oCACnC,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,EAAE;oCACtB,CAAC,CAAC,KAAK,OAAO,CAAC,KAAK,IAAI,CAAC;gCAC7B,MAAM,sBAAsB,GAC1B,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;gCACnD,IAAI,CAAC,sBAAsB,EAAE,CAAC;oCAC5B,WAAW,IAAI,IAAI,CAAC;gCACtB,CAAC;qCAAM,CAAC;oCACN,WAAW,IAAI,GAAG,CAAC;gCACrB,CAAC;gCACD,UAAU,GAAG,WAAW,GAAG,UAAU,CAAC;4BACxC,CAAC,CAAC,CAAC;wBACL,CAAC;wBAED,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;wBAC/B,KAAK,CAAC,IAAI,CACR,KAAK,CAAC,gBAAgB,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAC9D,CAAC;wBACF,OAAO,KAAK,CAAC;oBACf,CAAC,CAAC;gBAEN,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,8BAA8B;oBACzC,IAAI,EAAE;wBACJ,kBAAkB,EAAE,eAAO,CAAC,IAAI,CAAC,IAAI,CAAC;qBACvC;oBACD,GAAG;iBACJ,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QACD,IAAI,WAAW,GAAiC,IAAI,CAAC;QACrD,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,OAAO;YACL,sBAAsB;gBACpB,kEAAkE;gBAClE,WAAW,GAAG,EAAE,CAAC;YACnB,CAAC;YACD,mCAAmC,CAAC,IAAyB;gBAC3D,6DAA6D;gBAC7D,kFAAkF;gBAClF,sGAAsG;gBACtG,IAAI,cAAc,KAAK,CAAC,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;oBAChD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YACD,6BAA6B,CAC3B,IAAqC;gBAErC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBAC1D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,WAAW,CAAC,CAAC;gBACpD,CAAC;gBACD,uDAAuD;gBACvD,WAAW,GAAG,IAAI,CAAC;YACrB,CAAC;YACD,+EAA+E;YAC/E,sCAAsC;gBACpC,cAAc,IAAI,CAAC,CAAC;YACtB,CAAC;YACD,2CAA2C;gBACzC,cAAc,IAAI,CAAC,CAAC;YACtB,CAAC;YACD,mCAAmC,CAAC,IAA4B;gBAC9D,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YACrC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}