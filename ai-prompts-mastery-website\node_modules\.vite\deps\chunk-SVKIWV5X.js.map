{"version": 3, "sources": ["../../highlight.js/lib/languages/r.js"], "sourcesContent": ["/**\n * @param {string} value\n * @returns {RegExp}\n * */\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction source(re) {\n  if (!re) return null;\n  if (typeof re === \"string\") return re;\n\n  return re.source;\n}\n\n/**\n * @param {RegExp | string } re\n * @returns {string}\n */\nfunction lookahead(re) {\n  return concat('(?=', re, ')');\n}\n\n/**\n * @param {...(RegExp | string) } args\n * @returns {string}\n */\nfunction concat(...args) {\n  const joined = args.map((x) => source(x)).join(\"\");\n  return joined;\n}\n\n/*\nLanguage: R\nDescription: R is a free software environment for statistical computing and graphics.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <<EMAIL>>\nWebsite: https://www.r-project.org\nCategory: common,scientific\n*/\n\n/** @type LanguageFn */\nfunction r(hljs) {\n  // Identifiers in R cannot start with `_`, but they can start with `.` if it\n  // is not immediately followed by a digit.\n  // R also supports quoted identifiers, which are near-arbitrary sequences\n  // delimited by backticks (`…`), which may contain escape sequences. These are\n  // handled in a separate mode. See `test/markup/r/names.txt` for examples.\n  // FIXME: Support Unicode identifiers.\n  const IDENT_RE = /(?:(?:[a-zA-Z]|\\.[._a-zA-Z])[._a-zA-Z0-9]*)|\\.(?!\\d)/;\n  const SIMPLE_IDENT = /[a-zA-Z][a-zA-Z_0-9]*/;\n\n  return {\n    name: 'R',\n\n    // only in Haskell, not R\n    illegal: /->/,\n    keywords: {\n      $pattern: IDENT_RE,\n      keyword:\n        'function if in break next repeat else for while',\n      literal:\n        'NULL NA TRUE FALSE Inf NaN NA_integer_|10 NA_real_|10 ' +\n        'NA_character_|10 NA_complex_|10',\n      built_in:\n        // Builtin constants\n        'LETTERS letters month.abb month.name pi T F ' +\n        // Primitive functions\n        // These are all the functions in `base` that are implemented as a\n        // `.Primitive`, minus those functions that are also keywords.\n        'abs acos acosh all any anyNA Arg as.call as.character ' +\n        'as.complex as.double as.environment as.integer as.logical ' +\n        'as.null.default as.numeric as.raw asin asinh atan atanh attr ' +\n        'attributes baseenv browser c call ceiling class Conj cos cosh ' +\n        'cospi cummax cummin cumprod cumsum digamma dim dimnames ' +\n        'emptyenv exp expression floor forceAndCall gamma gc.time ' +\n        'globalenv Im interactive invisible is.array is.atomic is.call ' +\n        'is.character is.complex is.double is.environment is.expression ' +\n        'is.finite is.function is.infinite is.integer is.language ' +\n        'is.list is.logical is.matrix is.na is.name is.nan is.null ' +\n        'is.numeric is.object is.pairlist is.raw is.recursive is.single ' +\n        'is.symbol lazyLoadDBfetch length lgamma list log max min ' +\n        'missing Mod names nargs nzchar oldClass on.exit pos.to.env ' +\n        'proc.time prod quote range Re rep retracemem return round ' +\n        'seq_along seq_len seq.int sign signif sin sinh sinpi sqrt ' +\n        'standardGeneric substitute sum switch tan tanh tanpi tracemem ' +\n        'trigamma trunc unclass untracemem UseMethod xtfrm',\n    },\n    compilerExtensions: [\n      // allow beforeMatch to act as a \"qualifier\" for the match\n      // the full match begin must be [beforeMatch][begin]\n      (mode, parent) => {\n        if (!mode.beforeMatch) return;\n        // starts conflicts with endsParent which we need to make sure the child\n        // rule is not matched multiple times\n        if (mode.starts) throw new Error(\"beforeMatch cannot be used with starts\");\n\n        const originalMode = Object.assign({}, mode);\n        Object.keys(mode).forEach((key) => { delete mode[key]; });\n\n        mode.begin = concat(originalMode.beforeMatch, lookahead(originalMode.begin));\n        mode.starts = {\n          relevance: 0,\n          contains: [\n            Object.assign(originalMode, { endsParent: true })\n          ]\n        };\n        mode.relevance = 0;\n\n        delete originalMode.beforeMatch;\n      }\n    ],\n    contains: [\n      // Roxygen comments\n      hljs.COMMENT(\n        /#'/,\n        /$/,\n        {\n          contains: [\n            {\n              // Handle `@examples` separately to cause all subsequent code\n              // until the next `@`-tag on its own line to be kept as-is,\n              // preventing highlighting. This code is example R code, so nested\n              // doctags shouldn’t be treated as such. See\n              // `test/markup/r/roxygen.txt` for an example.\n              className: 'doctag',\n              begin: '@examples',\n              starts: {\n                contains: [\n                  { begin: /\\n/ },\n                  {\n                    begin: /#'\\s*(?=@[a-zA-Z]+)/,\n                    endsParent: true,\n                  },\n                  {\n                    begin: /#'/,\n                    end: /$/,\n                    excludeBegin: true,\n                  }\n                ]\n              }\n            },\n            {\n              // Handle `@param` to highlight the parameter name following\n              // after.\n              className: 'doctag',\n              begin: '@param',\n              end: /$/,\n              contains: [\n                {\n                  className: 'variable',\n                  variants: [\n                    { begin: IDENT_RE },\n                    { begin: /`(?:\\\\.|[^`\\\\])+`/ }\n                  ],\n                  endsParent: true\n                }\n              ]\n            },\n            {\n              className: 'doctag',\n              begin: /@[a-zA-Z]+/\n            },\n            {\n              className: 'meta-keyword',\n              begin: /\\\\[a-zA-Z]+/,\n            }\n          ]\n        }\n      ),\n\n      hljs.HASH_COMMENT_MODE,\n\n      {\n        className: 'string',\n        contains: [hljs.BACKSLASH_ESCAPE],\n        variants: [\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]\"(-*)\\(/, end: /\\)(-*)\"/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]\"(-*)\\{/, end: /\\}(-*)\"/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]\"(-*)\\[/, end: /\\](-*)\"/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\\(/, end: /\\)(-*)'/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\\{/, end: /\\}(-*)'/ }),\n          hljs.END_SAME_AS_BEGIN({ begin: /[rR]'(-*)\\[/, end: /\\](-*)'/ }),\n          {begin: '\"', end: '\"', relevance: 0},\n          {begin: \"'\", end: \"'\", relevance: 0}\n        ],\n      },\n      {\n        className: 'number',\n        relevance: 0,\n        beforeMatch: /([^a-zA-Z0-9._])/, // not part of an identifier\n        variants: [\n          // TODO: replace with negative look-behind when available\n          // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/ },\n          // { begin: /(?<![a-zA-Z0-9._])0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/ },\n          // { begin: /(?<![a-zA-Z0-9._])(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/ }\n          {\n            // Special case: only hexadecimal binary powers can contain fractions.\n            match: /0[xX][0-9a-fA-F]+\\.[0-9a-fA-F]*[pP][+-]?\\d+i?/,\n          },\n          {\n            match: /0[xX][0-9a-fA-F]+([pP][+-]?\\d+)?[Li]?/\n          },\n          {\n            match: /(\\d+(\\.\\d*)?|\\.\\d+)([eE][+-]?\\d+)?[Li]?/,\n          }\n        ],\n      },\n      {\n        // infix operator\n        begin: '%',\n        end: '%'\n      },\n      // relevance boost for assignment\n      {\n        begin: concat(SIMPLE_IDENT, \"\\\\s+<-\\\\s+\")\n      },\n      {\n        // escaped identifier\n        begin: '`',\n        end: '`',\n        contains: [\n          { begin: /\\\\./ }\n        ]\n      }\n    ]\n  };\n}\n\nmodule.exports = r;\n"], "mappings": ";;;;;AAAA;AAAA;AASA,aAAS,OAAO,IAAI;AAClB,UAAI,CAAC,GAAI,QAAO;AAChB,UAAI,OAAO,OAAO,SAAU,QAAO;AAEnC,aAAO,GAAG;AAAA,IACZ;AAMA,aAAS,UAAU,IAAI;AACrB,aAAO,OAAO,OAAO,IAAI,GAAG;AAAA,IAC9B;AAMA,aAAS,UAAU,MAAM;AACvB,YAAM,SAAS,KAAK,IAAI,CAAC,MAAM,OAAO,CAAC,CAAC,EAAE,KAAK,EAAE;AACjD,aAAO;AAAA,IACT;AAYA,aAAS,EAAE,MAAM;AAOf,YAAM,WAAW;AACjB,YAAM,eAAe;AAErB,aAAO;AAAA,QACL,MAAM;AAAA;AAAA,QAGN,SAAS;AAAA,QACT,UAAU;AAAA,UACR,UAAU;AAAA,UACV,SACE;AAAA,UACF,SACE;AAAA,UAEF;AAAA;AAAA,YAEE;AAAA;AAAA,QAqBJ;AAAA,QACA,oBAAoB;AAAA;AAAA;AAAA,UAGlB,CAAC,MAAM,WAAW;AAChB,gBAAI,CAAC,KAAK,YAAa;AAGvB,gBAAI,KAAK,OAAQ,OAAM,IAAI,MAAM,wCAAwC;AAEzE,kBAAM,eAAe,OAAO,OAAO,CAAC,GAAG,IAAI;AAC3C,mBAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,QAAQ;AAAE,qBAAO,KAAK,GAAG;AAAA,YAAG,CAAC;AAExD,iBAAK,QAAQ,OAAO,aAAa,aAAa,UAAU,aAAa,KAAK,CAAC;AAC3E,iBAAK,SAAS;AAAA,cACZ,WAAW;AAAA,cACX,UAAU;AAAA,gBACR,OAAO,OAAO,cAAc,EAAE,YAAY,KAAK,CAAC;AAAA,cAClD;AAAA,YACF;AACA,iBAAK,YAAY;AAEjB,mBAAO,aAAa;AAAA,UACtB;AAAA,QACF;AAAA,QACA,UAAU;AAAA;AAAA,UAER,KAAK;AAAA,YACH;AAAA,YACA;AAAA,YACA;AAAA,cACE,UAAU;AAAA,gBACR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAME,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,QAAQ;AAAA,oBACN,UAAU;AAAA,sBACR,EAAE,OAAO,KAAK;AAAA,sBACd;AAAA,wBACE,OAAO;AAAA,wBACP,YAAY;AAAA,sBACd;AAAA,sBACA;AAAA,wBACE,OAAO;AAAA,wBACP,KAAK;AAAA,wBACL,cAAc;AAAA,sBAChB;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA;AAAA;AAAA;AAAA,kBAGE,WAAW;AAAA,kBACX,OAAO;AAAA,kBACP,KAAK;AAAA,kBACL,UAAU;AAAA,oBACR;AAAA,sBACE,WAAW;AAAA,sBACX,UAAU;AAAA,wBACR,EAAE,OAAO,SAAS;AAAA,wBAClB,EAAE,OAAO,oBAAoB;AAAA,sBAC/B;AAAA,sBACA,YAAY;AAAA,oBACd;AAAA,kBACF;AAAA,gBACF;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,gBACA;AAAA,kBACE,WAAW;AAAA,kBACX,OAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,UAEA,KAAK;AAAA,UAEL;AAAA,YACE,WAAW;AAAA,YACX,UAAU,CAAC,KAAK,gBAAgB;AAAA,YAChC,UAAU;AAAA,cACR,KAAK,kBAAkB,EAAE,OAAO,eAAe,KAAK,UAAU,CAAC;AAAA,cAC/D,KAAK,kBAAkB,EAAE,OAAO,eAAe,KAAK,UAAU,CAAC;AAAA,cAC/D,KAAK,kBAAkB,EAAE,OAAO,eAAe,KAAK,UAAU,CAAC;AAAA,cAC/D,KAAK,kBAAkB,EAAE,OAAO,eAAe,KAAK,UAAU,CAAC;AAAA,cAC/D,KAAK,kBAAkB,EAAE,OAAO,eAAe,KAAK,UAAU,CAAC;AAAA,cAC/D,KAAK,kBAAkB,EAAE,OAAO,eAAe,KAAK,UAAU,CAAC;AAAA,cAC/D,EAAC,OAAO,KAAK,KAAK,KAAK,WAAW,EAAC;AAAA,cACnC,EAAC,OAAO,KAAK,KAAK,KAAK,WAAW,EAAC;AAAA,YACrC;AAAA,UACF;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,aAAa;AAAA;AAAA,YACb,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,cAKR;AAAA;AAAA,gBAEE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,cACA;AAAA,gBACE,OAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA;AAAA,UAEA;AAAA,YACE,OAAO,OAAO,cAAc,YAAY;AAAA,UAC1C;AAAA,UACA;AAAA;AAAA,YAEE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,UAAU;AAAA,cACR,EAAE,OAAO,MAAM;AAAA,YACjB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}