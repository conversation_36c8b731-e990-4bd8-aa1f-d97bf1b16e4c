import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/highlight.js/lib/languages/coffeescript.js
var require_coffeescript = __commonJS({
  "node_modules/highlight.js/lib/languages/coffeescript.js"(exports, module) {
    var KEYWORDS = [
      "as",
      // for exports
      "in",
      "of",
      "if",
      "for",
      "while",
      "finally",
      "var",
      "new",
      "function",
      "do",
      "return",
      "void",
      "else",
      "break",
      "catch",
      "instanceof",
      "with",
      "throw",
      "case",
      "default",
      "try",
      "switch",
      "continue",
      "typeof",
      "delete",
      "let",
      "yield",
      "const",
      "class",
      // <PERSON><PERSON> handles these with a special rule
      // "get",
      // "set",
      "debugger",
      "async",
      "await",
      "static",
      "import",
      "from",
      "export",
      "extends"
    ];
    var LITERALS = [
      "true",
      "false",
      "null",
      "undefined",
      "NaN",
      "Infinity"
    ];
    var TYPES = [
      "Intl",
      "DataView",
      "Number",
      "Math",
      "Date",
      "String",
      "RegExp",
      "Object",
      "Function",
      "Boolean",
      "Error",
      "Symbol",
      "Set",
      "Map",
      "WeakSet",
      "WeakMap",
      "Proxy",
      "Reflect",
      "JSON",
      "Promise",
      "Float64Array",
      "Int16Array",
      "Int32Array",
      "Int8Array",
      "Uint16Array",
      "Uint32Array",
      "Float32Array",
      "Array",
      "Uint8Array",
      "Uint8ClampedArray",
      "ArrayBuffer",
      "BigInt64Array",
      "BigUint64Array",
      "BigInt"
    ];
    var ERROR_TYPES = [
      "EvalError",
      "InternalError",
      "RangeError",
      "ReferenceError",
      "SyntaxError",
      "TypeError",
      "URIError"
    ];
    var BUILT_IN_GLOBALS = [
      "setInterval",
      "setTimeout",
      "clearInterval",
      "clearTimeout",
      "require",
      "exports",
      "eval",
      "isFinite",
      "isNaN",
      "parseFloat",
      "parseInt",
      "decodeURI",
      "decodeURIComponent",
      "encodeURI",
      "encodeURIComponent",
      "escape",
      "unescape"
    ];
    var BUILT_IN_VARIABLES = [
      "arguments",
      "this",
      "super",
      "console",
      "window",
      "document",
      "localStorage",
      "module",
      "global"
      // Node.js
    ];
    var BUILT_INS = [].concat(
      BUILT_IN_GLOBALS,
      BUILT_IN_VARIABLES,
      TYPES,
      ERROR_TYPES
    );
    function coffeescript(hljs) {
      const COFFEE_BUILT_INS = [
        "npm",
        "print"
      ];
      const COFFEE_LITERALS = [
        "yes",
        "no",
        "on",
        "off"
      ];
      const COFFEE_KEYWORDS = [
        "then",
        "unless",
        "until",
        "loop",
        "by",
        "when",
        "and",
        "or",
        "is",
        "isnt",
        "not"
      ];
      const NOT_VALID_KEYWORDS = [
        "var",
        "const",
        "let",
        "function",
        "static"
      ];
      const excluding = (list) => (kw) => !list.includes(kw);
      const KEYWORDS$1 = {
        keyword: KEYWORDS.concat(COFFEE_KEYWORDS).filter(excluding(NOT_VALID_KEYWORDS)),
        literal: LITERALS.concat(COFFEE_LITERALS),
        built_in: BUILT_INS.concat(COFFEE_BUILT_INS)
      };
      const JS_IDENT_RE = "[A-Za-z$_][0-9A-Za-z$_]*";
      const SUBST = {
        className: "subst",
        begin: /#\{/,
        end: /\}/,
        keywords: KEYWORDS$1
      };
      const EXPRESSIONS = [
        hljs.BINARY_NUMBER_MODE,
        hljs.inherit(hljs.C_NUMBER_MODE, {
          starts: {
            end: "(\\s*/)?",
            relevance: 0
          }
        }),
        // a number tries to eat the following slash to prevent treating it as a regexp
        {
          className: "string",
          variants: [
            {
              begin: /'''/,
              end: /'''/,
              contains: [hljs.BACKSLASH_ESCAPE]
            },
            {
              begin: /'/,
              end: /'/,
              contains: [hljs.BACKSLASH_ESCAPE]
            },
            {
              begin: /"""/,
              end: /"""/,
              contains: [
                hljs.BACKSLASH_ESCAPE,
                SUBST
              ]
            },
            {
              begin: /"/,
              end: /"/,
              contains: [
                hljs.BACKSLASH_ESCAPE,
                SUBST
              ]
            }
          ]
        },
        {
          className: "regexp",
          variants: [
            {
              begin: "///",
              end: "///",
              contains: [
                SUBST,
                hljs.HASH_COMMENT_MODE
              ]
            },
            {
              begin: "//[gim]{0,3}(?=\\W)",
              relevance: 0
            },
            {
              // regex can't start with space to parse x / 2 / 3 as two divisions
              // regex can't start with *, and it supports an "illegal" in the main mode
              begin: /\/(?![ *]).*?(?![\\]).\/[gim]{0,3}(?=\W)/
            }
          ]
        },
        {
          begin: "@" + JS_IDENT_RE
          // relevance booster
        },
        {
          subLanguage: "javascript",
          excludeBegin: true,
          excludeEnd: true,
          variants: [
            {
              begin: "```",
              end: "```"
            },
            {
              begin: "`",
              end: "`"
            }
          ]
        }
      ];
      SUBST.contains = EXPRESSIONS;
      const TITLE = hljs.inherit(hljs.TITLE_MODE, {
        begin: JS_IDENT_RE
      });
      const POSSIBLE_PARAMS_RE = "(\\(.*\\)\\s*)?\\B[-=]>";
      const PARAMS = {
        className: "params",
        begin: "\\([^\\(]",
        returnBegin: true,
        /* We need another contained nameless mode to not have every nested
        pair of parens to be called "params" */
        contains: [{
          begin: /\(/,
          end: /\)/,
          keywords: KEYWORDS$1,
          contains: ["self"].concat(EXPRESSIONS)
        }]
      };
      return {
        name: "CoffeeScript",
        aliases: [
          "coffee",
          "cson",
          "iced"
        ],
        keywords: KEYWORDS$1,
        illegal: /\/\*/,
        contains: EXPRESSIONS.concat([
          hljs.COMMENT("###", "###"),
          hljs.HASH_COMMENT_MODE,
          {
            className: "function",
            begin: "^\\s*" + JS_IDENT_RE + "\\s*=\\s*" + POSSIBLE_PARAMS_RE,
            end: "[-=]>",
            returnBegin: true,
            contains: [
              TITLE,
              PARAMS
            ]
          },
          {
            // anonymous function start
            begin: /[:\(,=]\s*/,
            relevance: 0,
            contains: [{
              className: "function",
              begin: POSSIBLE_PARAMS_RE,
              end: "[-=]>",
              returnBegin: true,
              contains: [PARAMS]
            }]
          },
          {
            className: "class",
            beginKeywords: "class",
            end: "$",
            illegal: /[:="\[\]]/,
            contains: [
              {
                beginKeywords: "extends",
                endsWithParent: true,
                illegal: /[:="\[\]]/,
                contains: [TITLE]
              },
              TITLE
            ]
          },
          {
            begin: JS_IDENT_RE + ":",
            end: ":",
            returnBegin: true,
            returnEnd: true,
            relevance: 0
          }
        ])
      };
    }
    module.exports = coffeescript;
  }
});

export {
  require_coffeescript
};
//# sourceMappingURL=chunk-4UNJ3H2Q.js.map
