{"version": 3, "sources": ["../../refractor/lang/textile.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = textile\ntextile.displayName = 'textile'\ntextile.aliases = []\nfunction textile(Prism) {\n  ;(function (Prism) {\n    // We don't allow for pipes inside parentheses\n    // to not break table pattern |(. foo |). bar |\n    var modifierRegex = /\\([^|()\\n]+\\)|\\[[^\\]\\n]+\\]|\\{[^}\\n]+\\}/.source // Opening and closing parentheses which are not a modifier\n    // This pattern is necessary to prevent exponential backtracking\n    var parenthesesRegex = /\\)|\\((?![^|()\\n]+\\))/.source\n    /**\n     * @param {string} source\n     * @param {string} [flags]\n     */\n    function withModifier(source, flags) {\n      return RegExp(\n        source\n          .replace(/<MOD>/g, function () {\n            return '(?:' + modifierRegex + ')'\n          })\n          .replace(/<PAR>/g, function () {\n            return '(?:' + parenthesesRegex + ')'\n          }),\n        flags || ''\n      )\n    }\n    var modifierTokens = {\n      css: {\n        pattern: /\\{[^{}]+\\}/,\n        inside: {\n          rest: Prism.languages.css\n        }\n      },\n      'class-id': {\n        pattern: /(\\()[^()]+(?=\\))/,\n        lookbehind: true,\n        alias: 'attr-value'\n      },\n      lang: {\n        pattern: /(\\[)[^\\[\\]]+(?=\\])/,\n        lookbehind: true,\n        alias: 'attr-value'\n      },\n      // Anything else is punctuation (the first pattern is for row/col spans inside tables)\n      punctuation: /[\\\\\\/]\\d+|\\S/\n    }\n    var textile = (Prism.languages.textile = Prism.languages.extend('markup', {\n      phrase: {\n        pattern: /(^|\\r|\\n)\\S[\\s\\S]*?(?=$|\\r?\\n\\r?\\n|\\r\\r)/,\n        lookbehind: true,\n        inside: {\n          // h1. Header 1\n          'block-tag': {\n            pattern: withModifier(/^[a-z]\\w*(?:<MOD>|<PAR>|[<>=])*\\./.source),\n            inside: {\n              modifier: {\n                pattern: withModifier(\n                  /(^[a-z]\\w*)(?:<MOD>|<PAR>|[<>=])+(?=\\.)/.source\n                ),\n                lookbehind: true,\n                inside: modifierTokens\n              },\n              tag: /^[a-z]\\w*/,\n              punctuation: /\\.$/\n            }\n          },\n          // # List item\n          // * List item\n          list: {\n            pattern: withModifier(/^[*#]+<MOD>*\\s+\\S.*/.source, 'm'),\n            inside: {\n              modifier: {\n                pattern: withModifier(/(^[*#]+)<MOD>+/.source),\n                lookbehind: true,\n                inside: modifierTokens\n              },\n              punctuation: /^[*#]+/\n            }\n          },\n          // | cell | cell | cell |\n          table: {\n            // Modifiers can be applied to the row: {color:red}.|1|2|3|\n            // or the cell: |{color:red}.1|2|3|\n            pattern: withModifier(\n              /^(?:(?:<MOD>|<PAR>|[<>=^~])+\\.\\s*)?(?:\\|(?:(?:<MOD>|<PAR>|[<>=^~_]|[\\\\/]\\d+)+\\.|(?!(?:<MOD>|<PAR>|[<>=^~_]|[\\\\/]\\d+)+\\.))[^|]*)+\\|/\n                .source,\n              'm'\n            ),\n            inside: {\n              modifier: {\n                // Modifiers for rows after the first one are\n                // preceded by a pipe and a line feed\n                pattern: withModifier(\n                  /(^|\\|(?:\\r?\\n|\\r)?)(?:<MOD>|<PAR>|[<>=^~_]|[\\\\/]\\d+)+(?=\\.)/\n                    .source\n                ),\n                lookbehind: true,\n                inside: modifierTokens\n              },\n              punctuation: /\\||^\\./\n            }\n          },\n          inline: {\n            // eslint-disable-next-line regexp/no-super-linear-backtracking\n            pattern: withModifier(\n              /(^|[^a-zA-Z\\d])(\\*\\*|__|\\?\\?|[*_%@+\\-^~])<MOD>*.+?\\2(?![a-zA-Z\\d])/\n                .source\n            ),\n            lookbehind: true,\n            inside: {\n              // Note: superscripts and subscripts are not handled specifically\n              // *bold*, **bold**\n              bold: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^(\\*\\*?)<MOD>*).+?(?=\\2)/.source),\n                lookbehind: true\n              },\n              // _italic_, __italic__\n              italic: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^(__?)<MOD>*).+?(?=\\2)/.source),\n                lookbehind: true\n              },\n              // ??cite??\n              cite: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^\\?\\?<MOD>*).+?(?=\\?\\?)/.source),\n                lookbehind: true,\n                alias: 'string'\n              },\n              // @code@\n              code: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^@<MOD>*).+?(?=@)/.source),\n                lookbehind: true,\n                alias: 'keyword'\n              },\n              // +inserted+\n              inserted: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^\\+<MOD>*).+?(?=\\+)/.source),\n                lookbehind: true\n              },\n              // -deleted-\n              deleted: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^-<MOD>*).+?(?=-)/.source),\n                lookbehind: true\n              },\n              // %span%\n              span: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^%<MOD>*).+?(?=%)/.source),\n                lookbehind: true\n              },\n              modifier: {\n                pattern: withModifier(\n                  /(^\\*\\*|__|\\?\\?|[*_%@+\\-^~])<MOD>+/.source\n                ),\n                lookbehind: true,\n                inside: modifierTokens\n              },\n              punctuation: /[*_%?@+\\-^~]+/\n            }\n          },\n          // [alias]http://example.com\n          'link-ref': {\n            pattern: /^\\[[^\\]]+\\]\\S+$/m,\n            inside: {\n              string: {\n                pattern: /(^\\[)[^\\]]+(?=\\])/,\n                lookbehind: true\n              },\n              url: {\n                pattern: /(^\\])\\S+$/,\n                lookbehind: true\n              },\n              punctuation: /[\\[\\]]/\n            }\n          },\n          // \"text\":http://example.com\n          // \"text\":link-ref\n          link: {\n            // eslint-disable-next-line regexp/no-super-linear-backtracking\n            pattern: withModifier(\n              /\"<MOD>*[^\"]+\":.+?(?=[^\\w/]?(?:\\s|$))/.source\n            ),\n            inside: {\n              text: {\n                // eslint-disable-next-line regexp/no-super-linear-backtracking\n                pattern: withModifier(/(^\"<MOD>*)[^\"]+(?=\")/.source),\n                lookbehind: true\n              },\n              modifier: {\n                pattern: withModifier(/(^\")<MOD>+/.source),\n                lookbehind: true,\n                inside: modifierTokens\n              },\n              url: {\n                pattern: /(:).+/,\n                lookbehind: true\n              },\n              punctuation: /[\":]/\n            }\n          },\n          // !image.jpg!\n          // !image.jpg(Title)!:http://example.com\n          image: {\n            pattern: withModifier(\n              /!(?:<MOD>|<PAR>|[<>=])*(?![<>=])[^!\\s()]+(?:\\([^)]+\\))?!(?::.+?(?=[^\\w/]?(?:\\s|$)))?/\n                .source\n            ),\n            inside: {\n              source: {\n                pattern: withModifier(\n                  /(^!(?:<MOD>|<PAR>|[<>=])*)(?![<>=])[^!\\s()]+(?:\\([^)]+\\))?(?=!)/\n                    .source\n                ),\n                lookbehind: true,\n                alias: 'url'\n              },\n              modifier: {\n                pattern: withModifier(/(^!)(?:<MOD>|<PAR>|[<>=])+/.source),\n                lookbehind: true,\n                inside: modifierTokens\n              },\n              url: {\n                pattern: /(:).+/,\n                lookbehind: true\n              },\n              punctuation: /[!:]/\n            }\n          },\n          // Footnote[1]\n          footnote: {\n            pattern: /\\b\\[\\d+\\]/,\n            alias: 'comment',\n            inside: {\n              punctuation: /\\[|\\]/\n            }\n          },\n          // CSS(Cascading Style Sheet)\n          acronym: {\n            pattern: /\\b[A-Z\\d]+\\([^)]+\\)/,\n            inside: {\n              comment: {\n                pattern: /(\\()[^()]+(?=\\))/,\n                lookbehind: true\n              },\n              punctuation: /[()]/\n            }\n          },\n          // Prism(C)\n          mark: {\n            pattern: /\\b\\((?:C|R|TM)\\)/,\n            alias: 'comment',\n            inside: {\n              punctuation: /[()]/\n            }\n          }\n        }\n      }\n    }))\n    var phraseInside = textile['phrase'].inside\n    var nestedPatterns = {\n      inline: phraseInside['inline'],\n      link: phraseInside['link'],\n      image: phraseInside['image'],\n      footnote: phraseInside['footnote'],\n      acronym: phraseInside['acronym'],\n      mark: phraseInside['mark']\n    } // Only allow alpha-numeric HTML tags, not XML tags\n    textile.tag.pattern =\n      /<\\/?(?!\\d)[a-z0-9]+(?:\\s+[^\\s>\\/=]+(?:=(?:(\"|')(?:\\\\[\\s\\S]|(?!\\1)[^\\\\])*\\1|[^\\s'\">=]+))?)*\\s*\\/?>/i // Allow some nesting\n    var phraseInlineInside = phraseInside['inline'].inside\n    phraseInlineInside['bold'].inside = nestedPatterns\n    phraseInlineInside['italic'].inside = nestedPatterns\n    phraseInlineInside['inserted'].inside = nestedPatterns\n    phraseInlineInside['deleted'].inside = nestedPatterns\n    phraseInlineInside['span'].inside = nestedPatterns // Allow some styles inside table cells\n    var phraseTableInside = phraseInside['table'].inside\n    phraseTableInside['inline'] = nestedPatterns['inline']\n    phraseTableInside['link'] = nestedPatterns['link']\n    phraseTableInside['image'] = nestedPatterns['image']\n    phraseTableInside['footnote'] = nestedPatterns['footnote']\n    phraseTableInside['acronym'] = nestedPatterns['acronym']\n    phraseTableInside['mark'] = nestedPatterns['mark']\n  })(Prism)\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC;AACnB,aAAS,QAAQ,OAAO;AACtB;AAAC,OAAC,SAAUA,QAAO;AAGjB,YAAI,gBAAgB,yCAAyC;AAE7D,YAAI,mBAAmB,uBAAuB;AAK9C,iBAAS,aAAa,QAAQ,OAAO;AACnC,iBAAO;AAAA,YACL,OACG,QAAQ,UAAU,WAAY;AAC7B,qBAAO,QAAQ,gBAAgB;AAAA,YACjC,CAAC,EACA,QAAQ,UAAU,WAAY;AAC7B,qBAAO,QAAQ,mBAAmB;AAAA,YACpC,CAAC;AAAA,YACH,SAAS;AAAA,UACX;AAAA,QACF;AACA,YAAI,iBAAiB;AAAA,UACnB,KAAK;AAAA,YACH,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,MAAMA,OAAM,UAAU;AAAA,YACxB;AAAA,UACF;AAAA,UACA,YAAY;AAAA,YACV,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA,UACA,MAAM;AAAA,YACJ,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,OAAO;AAAA,UACT;AAAA;AAAA,UAEA,aAAa;AAAA,QACf;AACA,YAAIC,WAAWD,OAAM,UAAU,UAAUA,OAAM,UAAU,OAAO,UAAU;AAAA,UACxE,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,YAAY;AAAA,YACZ,QAAQ;AAAA;AAAA,cAEN,aAAa;AAAA,gBACX,SAAS,aAAa,oCAAoC,MAAM;AAAA,gBAChE,QAAQ;AAAA,kBACN,UAAU;AAAA,oBACR,SAAS;AAAA,sBACP,0CAA0C;AAAA,oBAC5C;AAAA,oBACA,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,KAAK;AAAA,kBACL,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA;AAAA,cAGA,MAAM;AAAA,gBACJ,SAAS,aAAa,sBAAsB,QAAQ,GAAG;AAAA,gBACvD,QAAQ;AAAA,kBACN,UAAU;AAAA,oBACR,SAAS,aAAa,iBAAiB,MAAM;AAAA,oBAC7C,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA,cAEA,OAAO;AAAA;AAAA;AAAA,gBAGL,SAAS;AAAA,kBACP,qIACG;AAAA,kBACH;AAAA,gBACF;AAAA,gBACA,QAAQ;AAAA,kBACN,UAAU;AAAA;AAAA;AAAA,oBAGR,SAAS;AAAA,sBACP,8DACG;AAAA,oBACL;AAAA,oBACA,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,cACA,QAAQ;AAAA;AAAA,gBAEN,SAAS;AAAA,kBACP,qEACG;AAAA,gBACL;AAAA,gBACA,YAAY;AAAA,gBACZ,QAAQ;AAAA;AAAA;AAAA,kBAGN,MAAM;AAAA;AAAA,oBAEJ,SAAS,aAAa,4BAA4B,MAAM;AAAA,oBACxD,YAAY;AAAA,kBACd;AAAA;AAAA,kBAEA,QAAQ;AAAA;AAAA,oBAEN,SAAS,aAAa,0BAA0B,MAAM;AAAA,oBACtD,YAAY;AAAA,kBACd;AAAA;AAAA,kBAEA,MAAM;AAAA;AAAA,oBAEJ,SAAS,aAAa,2BAA2B,MAAM;AAAA,oBACvD,YAAY;AAAA,oBACZ,OAAO;AAAA,kBACT;AAAA;AAAA,kBAEA,MAAM;AAAA;AAAA,oBAEJ,SAAS,aAAa,qBAAqB,MAAM;AAAA,oBACjD,YAAY;AAAA,oBACZ,OAAO;AAAA,kBACT;AAAA;AAAA,kBAEA,UAAU;AAAA;AAAA,oBAER,SAAS,aAAa,uBAAuB,MAAM;AAAA,oBACnD,YAAY;AAAA,kBACd;AAAA;AAAA,kBAEA,SAAS;AAAA;AAAA,oBAEP,SAAS,aAAa,qBAAqB,MAAM;AAAA,oBACjD,YAAY;AAAA,kBACd;AAAA;AAAA,kBAEA,MAAM;AAAA;AAAA,oBAEJ,SAAS,aAAa,qBAAqB,MAAM;AAAA,oBACjD,YAAY;AAAA,kBACd;AAAA,kBACA,UAAU;AAAA,oBACR,SAAS;AAAA,sBACP,oCAAoC;AAAA,oBACtC;AAAA,oBACA,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA,cAEA,YAAY;AAAA,gBACV,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,QAAQ;AAAA,oBACN,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,KAAK;AAAA,oBACH,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA;AAAA,cAGA,MAAM;AAAA;AAAA,gBAEJ,SAAS;AAAA,kBACP,uCAAuC;AAAA,gBACzC;AAAA,gBACA,QAAQ;AAAA,kBACN,MAAM;AAAA;AAAA,oBAEJ,SAAS,aAAa,uBAAuB,MAAM;AAAA,oBACnD,YAAY;AAAA,kBACd;AAAA,kBACA,UAAU;AAAA,oBACR,SAAS,aAAa,aAAa,MAAM;AAAA,oBACzC,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,KAAK;AAAA,oBACH,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA;AAAA,cAGA,OAAO;AAAA,gBACL,SAAS;AAAA,kBACP,uFACG;AAAA,gBACL;AAAA,gBACA,QAAQ;AAAA,kBACN,QAAQ;AAAA,oBACN,SAAS;AAAA,sBACP,kEACG;AAAA,oBACL;AAAA,oBACA,YAAY;AAAA,oBACZ,OAAO;AAAA,kBACT;AAAA,kBACA,UAAU;AAAA,oBACR,SAAS,aAAa,6BAA6B,MAAM;AAAA,oBACzD,YAAY;AAAA,oBACZ,QAAQ;AAAA,kBACV;AAAA,kBACA,KAAK;AAAA,oBACH,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA,cAEA,UAAU;AAAA,gBACR,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA,cAEA,SAAS;AAAA,gBACP,SAAS;AAAA,gBACT,QAAQ;AAAA,kBACN,SAAS;AAAA,oBACP,SAAS;AAAA,oBACT,YAAY;AAAA,kBACd;AAAA,kBACA,aAAa;AAAA,gBACf;AAAA,cACF;AAAA;AAAA,cAEA,MAAM;AAAA,gBACJ,SAAS;AAAA,gBACT,OAAO;AAAA,gBACP,QAAQ;AAAA,kBACN,aAAa;AAAA,gBACf;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,CAAC;AACD,YAAI,eAAeC,SAAQ,QAAQ,EAAE;AACrC,YAAI,iBAAiB;AAAA,UACnB,QAAQ,aAAa,QAAQ;AAAA,UAC7B,MAAM,aAAa,MAAM;AAAA,UACzB,OAAO,aAAa,OAAO;AAAA,UAC3B,UAAU,aAAa,UAAU;AAAA,UACjC,SAAS,aAAa,SAAS;AAAA,UAC/B,MAAM,aAAa,MAAM;AAAA,QAC3B;AACA,QAAAA,SAAQ,IAAI,UACV;AACF,YAAI,qBAAqB,aAAa,QAAQ,EAAE;AAChD,2BAAmB,MAAM,EAAE,SAAS;AACpC,2BAAmB,QAAQ,EAAE,SAAS;AACtC,2BAAmB,UAAU,EAAE,SAAS;AACxC,2BAAmB,SAAS,EAAE,SAAS;AACvC,2BAAmB,MAAM,EAAE,SAAS;AACpC,YAAI,oBAAoB,aAAa,OAAO,EAAE;AAC9C,0BAAkB,QAAQ,IAAI,eAAe,QAAQ;AACrD,0BAAkB,MAAM,IAAI,eAAe,MAAM;AACjD,0BAAkB,OAAO,IAAI,eAAe,OAAO;AACnD,0BAAkB,UAAU,IAAI,eAAe,UAAU;AACzD,0BAAkB,SAAS,IAAI,eAAe,SAAS;AACvD,0BAAkB,MAAM,IAAI,eAAe,MAAM;AAAA,MACnD,GAAG,KAAK;AAAA,IACV;AAAA;AAAA;", "names": ["Prism", "textile"]}