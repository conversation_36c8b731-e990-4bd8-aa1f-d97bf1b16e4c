import {
  require_zig
} from "./chunk-I5S5WMHT.js";
import {
  require_core
} from "./chunk-KRXNTS6T.js";
import {
  require_wiki
} from "./chunk-Y2N5J5XE.js";
import {
  require_wolfram
} from "./chunk-Q6FI6QC6.js";
import {
  require_wren
} from "./chunk-3GCBNQWW.js";
import {
  require_xeora
} from "./chunk-KQMLBHH6.js";
import {
  require_xml_doc
} from "./chunk-IE2IXBTX.js";
import {
  require_xojo
} from "./chunk-OYAA6KOM.js";
import {
  require_xquery
} from "./chunk-FH76WVW4.js";
import {
  require_yang
} from "./chunk-R4F73ZQE.js";
import {
  require_velocity
} from "./chunk-RXCFAS2H.js";
import {
  require_verilog
} from "./chunk-L6U5HS7G.js";
import {
  require_vhdl
} from "./chunk-XVT6NDD7.js";
import {
  require_vim
} from "./chunk-EV6YJITG.js";
import {
  require_visual_basic
} from "./chunk-VBLIZ4DM.js";
import {
  require_warpscript
} from "./chunk-NTJ4FU7N.js";
import {
  require_wasm
} from "./chunk-FNF6JDHQ.js";
import {
  require_web_idl
} from "./chunk-LJBQSBOE.js";
import {
  require_tt2
} from "./chunk-YJQFAMAI.js";
import {
  require_twig
} from "./chunk-QOLUE7KU.js";
import {
  require_typoscript
} from "./chunk-EIHVJWWT.js";
import {
  require_unrealscript
} from "./chunk-RVNZSBWL.js";
import {
  require_uorazor
} from "./chunk-XQBWQNG5.js";
import {
  require_uri
} from "./chunk-XUPRKPIF.js";
import {
  require_v
} from "./chunk-3L2UJBPE.js";
import {
  require_vala
} from "./chunk-VEWZ74ZF.js";
import {
  require_t4_vb
} from "./chunk-THZPHPX5.js";
import {
  require_tap
} from "./chunk-B3MPX6EU.js";
import {
  require_yaml
} from "./chunk-7AEXJYR2.js";
import {
  require_tcl
} from "./chunk-WG5DMYFS.js";
import {
  require_textile
} from "./chunk-4PVELXMV.js";
import {
  require_toml
} from "./chunk-OO4ROH66.js";
import {
  require_tremor
} from "./chunk-PKNI42JL.js";
import {
  require_tsx
} from "./chunk-XVAJH2IZ.js";
import {
  require_squirrel
} from "./chunk-CNQLMRRM.js";
import {
  require_stan
} from "./chunk-WBHLS7LY.js";
import {
  require_stylus
} from "./chunk-5SEOHXSC.js";
import {
  require_swift
} from "./chunk-EVABWJ3L.js";
import {
  require_systemd
} from "./chunk-WLJTALI7.js";
import {
  require_t4_cs
} from "./chunk-KA4Y65YY.js";
import {
  require_t4_templating
} from "./chunk-23W5YOMZ.js";
import {
  require_vbnet
} from "./chunk-GV3NCGJS.js";
import {
  require_sml
} from "./chunk-D26SLVM5.js";
import {
  require_solidity
} from "./chunk-4MJLNY23.js";
import {
  require_solution_file
} from "./chunk-WRZOBQSL.js";
import {
  require_soy
} from "./chunk-NEGRUPTH.js";
import {
  require_sparql
} from "./chunk-WORVNFRG.js";
import {
  require_turtle
} from "./chunk-7AGVW6W6.js";
import {
  require_splunk_spl
} from "./chunk-4HVZHQT3.js";
import {
  require_sqf
} from "./chunk-JRD5AU7Q.js";
import {
  require_sas
} from "./chunk-EXFBTGET.js";
import {
  require_sass
} from "./chunk-ICJCGJLA.js";
import {
  require_scala
} from "./chunk-VJQM7OUF.js";
import {
  require_scss
} from "./chunk-WGIKH76F.js";
import {
  require_shell_session
} from "./chunk-5O3DAMFL.js";
import {
  require_smali
} from "./chunk-PCRGMNDR.js";
import {
  require_smalltalk
} from "./chunk-QLPISQUQ.js";
import {
  require_smarty
} from "./chunk-3JPHTXDR.js";
import {
  require_regex
} from "./chunk-5UQOOUDC.js";
import {
  require_rego
} from "./chunk-IZYIOXNZ.js";
import {
  require_renpy
} from "./chunk-G3KTHR5K.js";
import {
  require_rest
} from "./chunk-X6SQROML.js";
import {
  require_rip
} from "./chunk-Q4GCZZMK.js";
import {
  require_roboconf
} from "./chunk-ZNAIQJ7Q.js";
import {
  require_robotframework
} from "./chunk-FZLXYFDF.js";
import {
  require_rust
} from "./chunk-SHPIIFJK.js";
import {
  require_python
} from "./chunk-JQGH4KES.js";
import {
  require_q
} from "./chunk-RGL2FQ77.js";
import {
  require_qml
} from "./chunk-KMALZJU2.js";
import {
  require_qore
} from "./chunk-FVFM4P3I.js";
import {
  require_qsharp
} from "./chunk-6KWWWWJJ.js";
import {
  require_r
} from "./chunk-UINCDHS4.js";
import {
  require_racket
} from "./chunk-X47IUFPW.js";
import {
  require_reason
} from "./chunk-2TJIUQC3.js";
import {
  require_properties
} from "./chunk-X6QHGKVN.js";
import {
  require_protobuf
} from "./chunk-N2YC3TEA.js";
import {
  require_psl
} from "./chunk-KE3LOM57.js";
import {
  require_pug
} from "./chunk-E7AATPJ2.js";
import {
  require_puppet
} from "./chunk-VOODRTOR.js";
import {
  require_pure
} from "./chunk-Y2JNDJJ6.js";
import {
  require_purebasic
} from "./chunk-OKQDORTD.js";
import {
  require_purescript
} from "./chunk-F2FP2BG2.js";
import {
  require_php_extras
} from "./chunk-V35P7773.js";
import {
  require_phpdoc
} from "./chunk-Q6HQYK66.js";
import {
  require_plsql
} from "./chunk-BSM6SQU2.js";
import {
  require_powerquery
} from "./chunk-VGOI6662.js";
import {
  require_powershell
} from "./chunk-JTWTB5O4.js";
import {
  require_processing
} from "./chunk-GRQNMY52.js";
import {
  require_prolog
} from "./chunk-NQBVXMF4.js";
import {
  require_promql
} from "./chunk-YX53MRWX.js";
import {
  require_oz
} from "./chunk-XOTUSY6B.js";
import {
  require_parigp
} from "./chunk-A7R3YSQW.js";
import {
  require_parser
} from "./chunk-X6YQQNXO.js";
import {
  require_pascal
} from "./chunk-BHOVNNZO.js";
import {
  require_pascaligo
} from "./chunk-OOK6VCPF.js";
import {
  require_pcaxis
} from "./chunk-BYQLXEWE.js";
import {
  require_peoplecode
} from "./chunk-45GCH3LQ.js";
import {
  require_perl
} from "./chunk-BCZN2BUA.js";
import {
  require_nginx
} from "./chunk-UEU4SYNV.js";
import {
  require_nim
} from "./chunk-ZDVHYGC2.js";
import {
  require_nix
} from "./chunk-54EBBC23.js";
import {
  require_nsis
} from "./chunk-HJDAT6IC.js";
import {
  require_objectivec
} from "./chunk-FYE2OYTE.js";
import {
  require_ocaml
} from "./chunk-QJG3Z3NV.js";
import {
  require_opencl
} from "./chunk-APGVERRV.js";
import {
  require_openqasm
} from "./chunk-PP7GQQSP.js";
import {
  require_moonscript
} from "./chunk-WQT5KRDZ.js";
import {
  require_n1ql
} from "./chunk-LC7V2N6B.js";
import {
  require_n4js
} from "./chunk-4RJCQXNA.js";
import {
  require_nand2tetris_hdl
} from "./chunk-L24UUANH.js";
import {
  require_naniscript
} from "./chunk-YWKIUQXP.js";
import {
  require_nasm
} from "./chunk-Y36T4SME.js";
import {
  require_neon
} from "./chunk-HKGEERXA.js";
import {
  require_nevod
} from "./chunk-PVGHVXDI.js";
import {
  require_matlab
} from "./chunk-H7VBG23W.js";
import {
  require_maxscript
} from "./chunk-6Y2ZCP25.js";
import {
  require_mel
} from "./chunk-QIPNQPX2.js";
import {
  require_mermaid
} from "./chunk-TICDUL25.js";
import {
  require_mizar
} from "./chunk-CPIW2HST.js";
import {
  require_mongodb
} from "./chunk-QL4PI5G3.js";
import {
  require_monkey
} from "./chunk-ZDQXHF2V.js";
import {
  require_lisp
} from "./chunk-UHZGNECH.js";
import {
  require_livescript
} from "./chunk-XZMDQXVY.js";
import {
  require_llvm
} from "./chunk-XKELEVA4.js";
import {
  require_log
} from "./chunk-EOAL5X7B.js";
import {
  require_lolcode
} from "./chunk-TXFBWMQH.js";
import {
  require_magma
} from "./chunk-UXFJAJBR.js";
import {
  require_makefile
} from "./chunk-7FIUK2OB.js";
import {
  require_markdown
} from "./chunk-P2TSQSVR.js";
import {
  require_kusto
} from "./chunk-5ZQZOJS7.js";
import {
  require_latex
} from "./chunk-XQLZKZIX.js";
import {
  require_latte
} from "./chunk-O25AOGOT.js";
import {
  require_php
} from "./chunk-JJRRKXE4.js";
import {
  require_less
} from "./chunk-NDCWBMF3.js";
import {
  require_lilypond
} from "./chunk-ZUHPX7HQ.js";
import {
  require_scheme
} from "./chunk-Q5RTC35B.js";
import {
  require_liquid
} from "./chunk-R45U42LR.js";
import {
  require_jsonp
} from "./chunk-LCKTZJGD.js";
import {
  require_jsstacktrace
} from "./chunk-SW7OZNVU.js";
import {
  require_jsx
} from "./chunk-ME4I3DI7.js";
import {
  require_julia
} from "./chunk-7JGPL6PJ.js";
import {
  require_keepalived
} from "./chunk-S6JP2BIJ.js";
import {
  require_keyman
} from "./chunk-CRCIXAPK.js";
import {
  require_kotlin
} from "./chunk-AF42ZSWV.js";
import {
  require_kumir
} from "./chunk-DS75TJ7E.js";
import {
  require_jolie
} from "./chunk-KYQ4TWJQ.js";
import {
  require_jq
} from "./chunk-NAWTEX2I.js";
import {
  require_js_extras
} from "./chunk-MUL26VEJ.js";
import {
  require_js_templates
} from "./chunk-OBD2LXCU.js";
import {
  require_jsdoc
} from "./chunk-QGHE5TB2.js";
import {
  require_typescript
} from "./chunk-TT25QEL7.js";
import {
  require_json5
} from "./chunk-ODFIQNXV.js";
import {
  require_json
} from "./chunk-TFMOZUMT.js";
import {
  require_io
} from "./chunk-6DYASSKG.js";
import {
  require_j
} from "./chunk-VBUF7JOF.js";
import {
  require_javadoc
} from "./chunk-ZNBKP5CQ.js";
import {
  require_java
} from "./chunk-U62R7AKD.js";
import {
  require_javadoclike
} from "./chunk-LHRU3L7M.js";
import {
  require_javastacktrace
} from "./chunk-4IGD6XQZ.js";
import {
  require_jexl
} from "./chunk-WB5SRC4E.js";
import {
  require_ichigojam
} from "./chunk-W2N3CPN3.js";
import {
  require_icon
} from "./chunk-CWZTT24J.js";
import {
  require_icu_message_format
} from "./chunk-BVB7KQ6H.js";
import {
  require_idris
} from "./chunk-7OUTDY7S.js";
import {
  require_iecst
} from "./chunk-PHJ4GKFX.js";
import {
  require_ignore
} from "./chunk-GLTDJLBY.js";
import {
  require_inform7
} from "./chunk-RLAFOF65.js";
import {
  require_ini
} from "./chunk-6UTDJRES.js";
import {
  require_haskell
} from "./chunk-F7TZKADA.js";
import {
  require_haxe
} from "./chunk-QOJQML4R.js";
import {
  require_hcl
} from "./chunk-ZLKERU3C.js";
import {
  require_hlsl
} from "./chunk-67AKTLNX.js";
import {
  require_hoon
} from "./chunk-ZUY6WEWG.js";
import {
  require_hpkp
} from "./chunk-GETVLVFN.js";
import {
  require_hsts
} from "./chunk-3OM3VPVO.js";
import {
  require_http
} from "./chunk-GXE7UAYB.js";
import {
  require_gml
} from "./chunk-VKXSAGI6.js";
import {
  require_gn
} from "./chunk-2EGX6TX5.js";
import {
  require_go_module
} from "./chunk-RCMCFLBC.js";
import {
  require_go
} from "./chunk-HLKBHCLI.js";
import {
  require_graphql
} from "./chunk-7MVW2KBO.js";
import {
  require_groovy
} from "./chunk-ED7PZACY.js";
import {
  require_haml
} from "./chunk-4GTSPSRJ.js";
import {
  require_handlebars
} from "./chunk-XM252QKK.js";
import {
  require_ftl
} from "./chunk-TYKIGLTD.js";
import {
  require_gap
} from "./chunk-4GNGL3RM.js";
import {
  require_gcode
} from "./chunk-RIKQSHCU.js";
import {
  require_gdscript
} from "./chunk-Q3CHMOU4.js";
import {
  require_gedcom
} from "./chunk-6O72ZX6J.js";
import {
  require_gherkin
} from "./chunk-JSDAEENF.js";
import {
  require_git
} from "./chunk-62HOLE75.js";
import {
  require_glsl
} from "./chunk-AKGMPVE6.js";
import {
  require_etlua
} from "./chunk-UULG3JGH.js";
import {
  require_excel_formula
} from "./chunk-LRKGYDAK.js";
import {
  require_factor
} from "./chunk-7QPXFU6V.js";
import {
  require_false
} from "./chunk-DSYM47ML.js";
import {
  require_firestore_security_rules
} from "./chunk-QAHZA4JK.js";
import {
  require_flow
} from "./chunk-BISUNE6S.js";
import {
  require_fortran
} from "./chunk-3RXP3ONL.js";
import {
  require_fsharp
} from "./chunk-MXDDJTEY.js";
import {
  require_editorconfig
} from "./chunk-VCQB6YIP.js";
import {
  require_eiffel
} from "./chunk-YKFAE7OK.js";
import {
  require_ejs
} from "./chunk-IJ7TB4XY.js";
import {
  require_elixir
} from "./chunk-WFYXOMO5.js";
import {
  require_elm
} from "./chunk-POSDF4CH.js";
import {
  require_erb
} from "./chunk-BSU6JCIO.js";
import {
  require_erlang
} from "./chunk-EKAVK3DQ.js";
import {
  require_lua
} from "./chunk-6E5X32AP.js";
import {
  require_dhall
} from "./chunk-XXECJ3FO.js";
import {
  require_diff
} from "./chunk-M32VALUU.js";
import {
  require_django
} from "./chunk-JPLRRASU.js";
import {
  require_markup_templating
} from "./chunk-YTUQ6TMB.js";
import {
  require_dns_zone_file
} from "./chunk-OJODGOPH.js";
import {
  require_docker
} from "./chunk-UPZ4EIDZ.js";
import {
  require_dot
} from "./chunk-KM7ZC4S7.js";
import {
  require_ebnf
} from "./chunk-5K7FX5XV.js";
import {
  require_css_extras
} from "./chunk-3VYGPB7E.js";
import {
  require_csv
} from "./chunk-JLI5XFDD.js";
import {
  require_cypher
} from "./chunk-ZFPOWVAH.js";
import {
  require_d
} from "./chunk-OHSI3WUL.js";
import {
  require_dart
} from "./chunk-GSFDYTFB.js";
import {
  require_dataweave
} from "./chunk-RBRM42NX.js";
import {
  require_dax
} from "./chunk-SLSV5K5I.js";
import {
  require_cobol
} from "./chunk-ZB6PRXCP.js";
import {
  require_coffeescript
} from "./chunk-4TKB5U5R.js";
import {
  require_concurnas
} from "./chunk-NSLOCPKJ.js";
import {
  require_coq
} from "./chunk-ONUJ2Z74.js";
import {
  require_crystal
} from "./chunk-HZDY7ZEH.js";
import {
  require_ruby
} from "./chunk-UG4UTNO7.js";
import {
  require_cshtml
} from "./chunk-W7EFD3WT.js";
import {
  require_csp
} from "./chunk-YELEPOMI.js";
import {
  require_bro
} from "./chunk-GQP4RDUU.js";
import {
  require_bsl
} from "./chunk-4OCKXON7.js";
import {
  require_cfscript
} from "./chunk-WPXXKHDX.js";
import {
  require_chaiscript
} from "./chunk-LI5PHY7R.js";
import {
  require_cil
} from "./chunk-YTNA5NOK.js";
import {
  require_clojure
} from "./chunk-KLW6HSE5.js";
import {
  require_cmake
} from "./chunk-GJKD65RW.js";
import {
  require_batch
} from "./chunk-QBMQN4IC.js";
import {
  require_bbcode
} from "./chunk-GQDQYCUO.js";
import {
  require_bicep
} from "./chunk-L36TI7GI.js";
import {
  require_birb
} from "./chunk-557D7PIQ.js";
import {
  require_bison
} from "./chunk-FKELSXUS.js";
import {
  require_bnf
} from "./chunk-ZLQB32QS.js";
import {
  require_brainfuck
} from "./chunk-ZYH7QCKZ.js";
import {
  require_brightscript
} from "./chunk-2KG4FH2G.js";
import {
  require_aspnet
} from "./chunk-Z5IMDQMC.js";
import {
  require_csharp
} from "./chunk-LFMVYF4I.js";
import {
  require_autohotkey
} from "./chunk-L6E4WTSB.js";
import {
  require_autoit
} from "./chunk-QNZAJCHF.js";
import {
  require_avisynth
} from "./chunk-QBRAS2V5.js";
import {
  require_avro_idl
} from "./chunk-GVBPRAB3.js";
import {
  require_bash
} from "./chunk-75XB4YKI.js";
import {
  require_basic
} from "./chunk-NHRJKTT7.js";
import {
  require_aql
} from "./chunk-RTDO3AO3.js";
import {
  require_arduino
} from "./chunk-22P4IC4L.js";
import {
  require_cpp
} from "./chunk-LYB62N4X.js";
import {
  require_c
} from "./chunk-RAHOVZCU.js";
import {
  require_arff
} from "./chunk-C4KPWSRE.js";
import {
  require_asciidoc
} from "./chunk-ND6ECYQC.js";
import {
  require_asm6502
} from "./chunk-2WWKKXAM.js";
import {
  require_asmatmel
} from "./chunk-277V5I2R.js";
import {
  require_agda
} from "./chunk-OAPVBUDR.js";
import {
  require_al
} from "./chunk-HPDZBV7R.js";
import {
  require_antlr4
} from "./chunk-DAQOEDLU.js";
import {
  require_apacheconf
} from "./chunk-JTMS5LSH.js";
import {
  require_apex
} from "./chunk-4CZH4A3D.js";
import {
  require_sql
} from "./chunk-2RH5GJDY.js";
import {
  require_apl
} from "./chunk-QBEIGLIR.js";
import {
  require_applescript
} from "./chunk-DCETCIGC.js";
import {
  require_abap
} from "./chunk-HMU6LFXA.js";
import {
  require_abnf
} from "./chunk-J2U2A4U3.js";
import {
  require_actionscript
} from "./chunk-GDT2KTXR.js";
import {
  require_ada
} from "./chunk-UP7QCG5X.js";
import {
  __commonJS
} from "./chunk-G3PMV62Z.js";

// node_modules/refractor/index.js
var require_refractor = __commonJS({
  "node_modules/refractor/index.js"(exports, module) {
    var refractor = require_core();
    module.exports = refractor;
    refractor.register(require_abap());
    refractor.register(require_abnf());
    refractor.register(require_actionscript());
    refractor.register(require_ada());
    refractor.register(require_agda());
    refractor.register(require_al());
    refractor.register(require_antlr4());
    refractor.register(require_apacheconf());
    refractor.register(require_apex());
    refractor.register(require_apl());
    refractor.register(require_applescript());
    refractor.register(require_aql());
    refractor.register(require_arduino());
    refractor.register(require_arff());
    refractor.register(require_asciidoc());
    refractor.register(require_asm6502());
    refractor.register(require_asmatmel());
    refractor.register(require_aspnet());
    refractor.register(require_autohotkey());
    refractor.register(require_autoit());
    refractor.register(require_avisynth());
    refractor.register(require_avro_idl());
    refractor.register(require_bash());
    refractor.register(require_basic());
    refractor.register(require_batch());
    refractor.register(require_bbcode());
    refractor.register(require_bicep());
    refractor.register(require_birb());
    refractor.register(require_bison());
    refractor.register(require_bnf());
    refractor.register(require_brainfuck());
    refractor.register(require_brightscript());
    refractor.register(require_bro());
    refractor.register(require_bsl());
    refractor.register(require_c());
    refractor.register(require_cfscript());
    refractor.register(require_chaiscript());
    refractor.register(require_cil());
    refractor.register(require_clojure());
    refractor.register(require_cmake());
    refractor.register(require_cobol());
    refractor.register(require_coffeescript());
    refractor.register(require_concurnas());
    refractor.register(require_coq());
    refractor.register(require_cpp());
    refractor.register(require_crystal());
    refractor.register(require_csharp());
    refractor.register(require_cshtml());
    refractor.register(require_csp());
    refractor.register(require_css_extras());
    refractor.register(require_csv());
    refractor.register(require_cypher());
    refractor.register(require_d());
    refractor.register(require_dart());
    refractor.register(require_dataweave());
    refractor.register(require_dax());
    refractor.register(require_dhall());
    refractor.register(require_diff());
    refractor.register(require_django());
    refractor.register(require_dns_zone_file());
    refractor.register(require_docker());
    refractor.register(require_dot());
    refractor.register(require_ebnf());
    refractor.register(require_editorconfig());
    refractor.register(require_eiffel());
    refractor.register(require_ejs());
    refractor.register(require_elixir());
    refractor.register(require_elm());
    refractor.register(require_erb());
    refractor.register(require_erlang());
    refractor.register(require_etlua());
    refractor.register(require_excel_formula());
    refractor.register(require_factor());
    refractor.register(require_false());
    refractor.register(require_firestore_security_rules());
    refractor.register(require_flow());
    refractor.register(require_fortran());
    refractor.register(require_fsharp());
    refractor.register(require_ftl());
    refractor.register(require_gap());
    refractor.register(require_gcode());
    refractor.register(require_gdscript());
    refractor.register(require_gedcom());
    refractor.register(require_gherkin());
    refractor.register(require_git());
    refractor.register(require_glsl());
    refractor.register(require_gml());
    refractor.register(require_gn());
    refractor.register(require_go_module());
    refractor.register(require_go());
    refractor.register(require_graphql());
    refractor.register(require_groovy());
    refractor.register(require_haml());
    refractor.register(require_handlebars());
    refractor.register(require_haskell());
    refractor.register(require_haxe());
    refractor.register(require_hcl());
    refractor.register(require_hlsl());
    refractor.register(require_hoon());
    refractor.register(require_hpkp());
    refractor.register(require_hsts());
    refractor.register(require_http());
    refractor.register(require_ichigojam());
    refractor.register(require_icon());
    refractor.register(require_icu_message_format());
    refractor.register(require_idris());
    refractor.register(require_iecst());
    refractor.register(require_ignore());
    refractor.register(require_inform7());
    refractor.register(require_ini());
    refractor.register(require_io());
    refractor.register(require_j());
    refractor.register(require_java());
    refractor.register(require_javadoc());
    refractor.register(require_javadoclike());
    refractor.register(require_javastacktrace());
    refractor.register(require_jexl());
    refractor.register(require_jolie());
    refractor.register(require_jq());
    refractor.register(require_js_extras());
    refractor.register(require_js_templates());
    refractor.register(require_jsdoc());
    refractor.register(require_json());
    refractor.register(require_json5());
    refractor.register(require_jsonp());
    refractor.register(require_jsstacktrace());
    refractor.register(require_jsx());
    refractor.register(require_julia());
    refractor.register(require_keepalived());
    refractor.register(require_keyman());
    refractor.register(require_kotlin());
    refractor.register(require_kumir());
    refractor.register(require_kusto());
    refractor.register(require_latex());
    refractor.register(require_latte());
    refractor.register(require_less());
    refractor.register(require_lilypond());
    refractor.register(require_liquid());
    refractor.register(require_lisp());
    refractor.register(require_livescript());
    refractor.register(require_llvm());
    refractor.register(require_log());
    refractor.register(require_lolcode());
    refractor.register(require_lua());
    refractor.register(require_magma());
    refractor.register(require_makefile());
    refractor.register(require_markdown());
    refractor.register(require_markup_templating());
    refractor.register(require_matlab());
    refractor.register(require_maxscript());
    refractor.register(require_mel());
    refractor.register(require_mermaid());
    refractor.register(require_mizar());
    refractor.register(require_mongodb());
    refractor.register(require_monkey());
    refractor.register(require_moonscript());
    refractor.register(require_n1ql());
    refractor.register(require_n4js());
    refractor.register(require_nand2tetris_hdl());
    refractor.register(require_naniscript());
    refractor.register(require_nasm());
    refractor.register(require_neon());
    refractor.register(require_nevod());
    refractor.register(require_nginx());
    refractor.register(require_nim());
    refractor.register(require_nix());
    refractor.register(require_nsis());
    refractor.register(require_objectivec());
    refractor.register(require_ocaml());
    refractor.register(require_opencl());
    refractor.register(require_openqasm());
    refractor.register(require_oz());
    refractor.register(require_parigp());
    refractor.register(require_parser());
    refractor.register(require_pascal());
    refractor.register(require_pascaligo());
    refractor.register(require_pcaxis());
    refractor.register(require_peoplecode());
    refractor.register(require_perl());
    refractor.register(require_php_extras());
    refractor.register(require_php());
    refractor.register(require_phpdoc());
    refractor.register(require_plsql());
    refractor.register(require_powerquery());
    refractor.register(require_powershell());
    refractor.register(require_processing());
    refractor.register(require_prolog());
    refractor.register(require_promql());
    refractor.register(require_properties());
    refractor.register(require_protobuf());
    refractor.register(require_psl());
    refractor.register(require_pug());
    refractor.register(require_puppet());
    refractor.register(require_pure());
    refractor.register(require_purebasic());
    refractor.register(require_purescript());
    refractor.register(require_python());
    refractor.register(require_q());
    refractor.register(require_qml());
    refractor.register(require_qore());
    refractor.register(require_qsharp());
    refractor.register(require_r());
    refractor.register(require_racket());
    refractor.register(require_reason());
    refractor.register(require_regex());
    refractor.register(require_rego());
    refractor.register(require_renpy());
    refractor.register(require_rest());
    refractor.register(require_rip());
    refractor.register(require_roboconf());
    refractor.register(require_robotframework());
    refractor.register(require_ruby());
    refractor.register(require_rust());
    refractor.register(require_sas());
    refractor.register(require_sass());
    refractor.register(require_scala());
    refractor.register(require_scheme());
    refractor.register(require_scss());
    refractor.register(require_shell_session());
    refractor.register(require_smali());
    refractor.register(require_smalltalk());
    refractor.register(require_smarty());
    refractor.register(require_sml());
    refractor.register(require_solidity());
    refractor.register(require_solution_file());
    refractor.register(require_soy());
    refractor.register(require_sparql());
    refractor.register(require_splunk_spl());
    refractor.register(require_sqf());
    refractor.register(require_sql());
    refractor.register(require_squirrel());
    refractor.register(require_stan());
    refractor.register(require_stylus());
    refractor.register(require_swift());
    refractor.register(require_systemd());
    refractor.register(require_t4_cs());
    refractor.register(require_t4_templating());
    refractor.register(require_t4_vb());
    refractor.register(require_tap());
    refractor.register(require_tcl());
    refractor.register(require_textile());
    refractor.register(require_toml());
    refractor.register(require_tremor());
    refractor.register(require_tsx());
    refractor.register(require_tt2());
    refractor.register(require_turtle());
    refractor.register(require_twig());
    refractor.register(require_typescript());
    refractor.register(require_typoscript());
    refractor.register(require_unrealscript());
    refractor.register(require_uorazor());
    refractor.register(require_uri());
    refractor.register(require_v());
    refractor.register(require_vala());
    refractor.register(require_vbnet());
    refractor.register(require_velocity());
    refractor.register(require_verilog());
    refractor.register(require_vhdl());
    refractor.register(require_vim());
    refractor.register(require_visual_basic());
    refractor.register(require_warpscript());
    refractor.register(require_wasm());
    refractor.register(require_web_idl());
    refractor.register(require_wiki());
    refractor.register(require_wolfram());
    refractor.register(require_wren());
    refractor.register(require_xeora());
    refractor.register(require_xml_doc());
    refractor.register(require_xojo());
    refractor.register(require_xquery());
    refractor.register(require_yaml());
    refractor.register(require_yang());
    refractor.register(require_zig());
  }
});

export {
  require_refractor
};
//# sourceMappingURL=chunk-264JTAGO.js.map
