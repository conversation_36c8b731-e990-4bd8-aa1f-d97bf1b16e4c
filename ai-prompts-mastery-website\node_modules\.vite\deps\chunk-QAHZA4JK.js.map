{"version": 3, "sources": ["../../refractor/lang/firestore-security-rules.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = firestoreSecurityRules\nfirestoreSecurityRules.displayName = 'firestoreSecurityRules'\nfirestoreSecurityRules.aliases = []\nfunction firestoreSecurityRules(Prism) {\n  Prism.languages['firestore-security-rules'] = Prism.languages.extend(\n    'clike',\n    {\n      comment: /\\/\\/.*/,\n      keyword:\n        /\\b(?:allow|function|if|match|null|return|rules_version|service)\\b/,\n      operator: /&&|\\|\\||[<>!=]=?|[-+*/%]|\\b(?:in|is)\\b/\n    }\n  )\n  delete Prism.languages['firestore-security-rules']['class-name']\n  Prism.languages.insertBefore('firestore-security-rules', 'keyword', {\n    path: {\n      pattern:\n        /(^|[\\s(),])(?:\\/(?:[\\w\\xA0-\\uFFFF]+|\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)))+/,\n      lookbehind: true,\n      greedy: true,\n      inside: {\n        variable: {\n          pattern: /\\{[\\w\\xA0-\\uFFFF]+(?:=\\*\\*)?\\}|\\$\\([\\w\\xA0-\\uFFFF.]+\\)/,\n          inside: {\n            operator: /=/,\n            keyword: /\\*\\*/,\n            punctuation: /[.$(){}]/\n          }\n        },\n        punctuation: /\\//\n      }\n    },\n    method: {\n      // to make the pattern shorter, the actual method names are omitted\n      pattern: /(\\ballow\\s+)[a-z]+(?:\\s*,\\s*[a-z]+)*(?=\\s*[:;])/,\n      lookbehind: true,\n      alias: 'builtin',\n      inside: {\n        punctuation: /,/\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,2BAAuB,cAAc;AACrC,2BAAuB,UAAU,CAAC;AAClC,aAAS,uBAAuB,OAAO;AACrC,YAAM,UAAU,0BAA0B,IAAI,MAAM,UAAU;AAAA,QAC5D;AAAA,QACA;AAAA,UACE,SAAS;AAAA,UACT,SACE;AAAA,UACF,UAAU;AAAA,QACZ;AAAA,MACF;AACA,aAAO,MAAM,UAAU,0BAA0B,EAAE,YAAY;AAC/D,YAAM,UAAU,aAAa,4BAA4B,WAAW;AAAA,QAClE,MAAM;AAAA,UACJ,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,UACR,QAAQ;AAAA,YACN,UAAU;AAAA,cACR,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,UAAU;AAAA,gBACV,SAAS;AAAA,gBACT,aAAa;AAAA,cACf;AAAA,YACF;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,QAAQ;AAAA;AAAA,UAEN,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,OAAO;AAAA,UACP,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}