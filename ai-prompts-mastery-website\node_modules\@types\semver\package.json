{"name": "@types/semver", "version": "7.7.0", "description": "TypeScript definitions for semver", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/semver", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "Bartvds", "url": "https://github.com/Bartvds"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/BendingBender"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Lucian<PERSON>zo"}, {"name": "<PERSON>", "githubUsername": "a<PERSON><PERSON><PERSON>", "url": "https://github.com/ajafff"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/semver"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "2686be620a3b4ba9c210ad83cb60a74eefd9e714cd5942d2ff2f24993ad4b9ec", "typeScriptVersion": "5.0"}