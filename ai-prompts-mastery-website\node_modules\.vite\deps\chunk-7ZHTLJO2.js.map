{"version": 3, "sources": ["../../highlight.js/lib/languages/subunit.js"], "sourcesContent": ["/*\nLanguage: SubUnit\nAuthor: <PERSON> <serge<PERSON><EMAIL>>\nWebsite: https://pypi.org/project/python-subunit/\n*/\n\nfunction subunit(hljs) {\n  const DETAILS = {\n    className: 'string',\n    begin: '\\\\[\\n(multipart)?',\n    end: '\\\\]\\n'\n  };\n  const TIME = {\n    className: 'string',\n    begin: '\\\\d{4}-\\\\d{2}-\\\\d{2}(\\\\s+)\\\\d{2}:\\\\d{2}:\\\\d{2}\\.\\\\d+Z'\n  };\n  const PROGRESSVALUE = {\n    className: 'string',\n    begin: '(\\\\+|-)\\\\d+'\n  };\n  const KEYWORDS = {\n    className: 'keyword',\n    relevance: 10,\n    variants: [\n      {\n        begin: '^(test|testing|success|successful|failure|error|skip|xfail|uxsuccess)(:?)\\\\s+(test)?'\n      },\n      {\n        begin: '^progress(:?)(\\\\s+)?(pop|push)?'\n      },\n      {\n        begin: '^tags:'\n      },\n      {\n        begin: '^time:'\n      }\n    ]\n  };\n  return {\n    name: 'SubUnit',\n    case_insensitive: true,\n    contains: [\n      DETAILS,\n      TIME,\n      PROGRESSVALUE,\n      KEYWORDS\n    ]\n  };\n}\n\nmodule.exports = subunit;\n"], "mappings": ";;;;;AAAA;AAAA;AAMA,aAAS,QAAQ,MAAM;AACrB,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,MACP;AACA,YAAM,OAAO;AAAA,QACX,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,gBAAgB;AAAA,QACpB,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AACA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AACA,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}