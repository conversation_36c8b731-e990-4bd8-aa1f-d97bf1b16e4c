{"version": 3, "file": "relative-color.js", "sources": ["../../../src/js/relative-color.ts"], "sourcesContent": ["/**\n * relative-color\n */\n\nimport { SyntaxFlag, color as colorParser } from '@csstools/css-color-parser';\nimport {\n  ComponentValue,\n  parseComponentValue\n} from '@csstools/css-parser-algorithms';\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { NAMED_COLORS, convertColorToRgb } from './color';\nimport { isString, isStringOrNumber } from './common';\nimport { resolveDimension, serializeCalc } from './css-calc';\nimport { resolveColor } from './resolve';\nimport { roundToPrecision } from './util';\nimport {\n  ColorChannels,\n  MatchedRegExp,\n  Options,\n  StringColorChannels\n} from './typedef';\n\n/* constants */\nimport {\n  CS_LAB,\n  CS_LCH,\n  FN_REL,\n  FN_REL_CAPT,\n  FN_VAR,\n  NONE,\n  SYN_COLOR_TYPE,\n  SYN_FN_MATH_START,\n  SYN_FN_VAR,\n  SYN_MIX,\n  VAL_SPEC\n} from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  Dimension: DIM,\n  EOF,\n  Function: FUNC,\n  Ident: IDENT,\n  Number: NUM,\n  OpenParen: PAREN_OPEN,\n  Percentage: PCT,\n  Whitespace: W_SPACE\n} = TokenType;\nconst { HasNoneKeywords: KEY_NONE } = SyntaxFlag;\nconst NAMESPACE = 'relative-color';\n\n/* numeric constants */\nconst OCT = 8;\nconst DEC = 10;\nconst HEX = 16;\nconst MAX_PCT = 100;\nconst MAX_RGB = 255;\n\n/* type definitions */\n/**\n * @type NumberOrStringColorChannels - color channel\n */\ntype NumberOrStringColorChannels = ColorChannels & StringColorChannels;\n\n/* regexp */\nconst REG_COLOR_CAPT = new RegExp(\n  `^${FN_REL}(${SYN_COLOR_TYPE}|${SYN_MIX})\\\\s+`\n);\nconst REG_CS_HSL = /(?:hsla?|hwb)$/;\nconst REG_CS_CIE = new RegExp(`^(?:${CS_LAB}|${CS_LCH})$`);\nconst REG_FN_MATH_START = new RegExp(SYN_FN_MATH_START);\nconst REG_FN_REL = new RegExp(FN_REL);\nconst REG_FN_REL_CAPT = new RegExp(`^${FN_REL_CAPT}`);\nconst REG_FN_REL_START = new RegExp(`^${FN_REL}`);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve relative color channels\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns resolved color channels\n */\nexport function resolveColorChannels(\n  tokens: CSSToken[],\n  opt: Options = {}\n): NumberOrStringColorChannels | NullObject {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { colorSpace = '', format = '' } = opt;\n  const colorChannels = new Map([\n    ['color', ['r', 'g', 'b', 'alpha']],\n    ['hsl', ['h', 's', 'l', 'alpha']],\n    ['hsla', ['h', 's', 'l', 'alpha']],\n    ['hwb', ['h', 'w', 'b', 'alpha']],\n    ['lab', ['l', 'a', 'b', 'alpha']],\n    ['lch', ['l', 'c', 'h', 'alpha']],\n    ['oklab', ['l', 'a', 'b', 'alpha']],\n    ['oklch', ['l', 'c', 'h', 'alpha']],\n    ['rgb', ['r', 'g', 'b', 'alpha']],\n    ['rgba', ['r', 'g', 'b', 'alpha']]\n  ]);\n  const colorChannel = colorChannels.get(colorSpace);\n  // invalid color channel\n  if (!colorChannel) {\n    return new NullObject();\n  }\n  const mathFunc = new Set();\n  const channels: [\n    (number | string)[],\n    (number | string)[],\n    (number | string)[],\n    (number | string)[]\n  ] = [[], [], [], []];\n  let i = 0;\n  let nest = 0;\n  let func = false;\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type, value, , , detail] = token as [\n      TokenType,\n      string,\n      number,\n      number,\n      { value: string | number } | undefined\n    ];\n    const channel = channels[i];\n    if (Array.isArray(channel)) {\n      switch (type) {\n        case DIM: {\n          const resolvedValue = resolveDimension(token, opt);\n          if (isString(resolvedValue)) {\n            channel.push(resolvedValue);\n          } else {\n            channel.push(value);\n          }\n          break;\n        }\n        case FUNC: {\n          channel.push(value);\n          func = true;\n          nest++;\n          if (REG_FN_MATH_START.test(value)) {\n            mathFunc.add(nest);\n          }\n          break;\n        }\n        case IDENT: {\n          // invalid channel key\n          if (!colorChannel.includes(value)) {\n            return new NullObject();\n          }\n          channel.push(value);\n          if (!func) {\n            i++;\n          }\n          break;\n        }\n        case NUM: {\n          channel.push(Number(detail?.value));\n          if (!func) {\n            i++;\n          }\n          break;\n        }\n        case PAREN_OPEN: {\n          channel.push(value);\n          nest++;\n          break;\n        }\n        case PAREN_CLOSE: {\n          if (func) {\n            const lastValue = channel[channel.length - 1];\n            if (lastValue === ' ') {\n              channel.splice(-1, 1, value);\n            } else {\n              channel.push(value);\n            }\n            if (mathFunc.has(nest)) {\n              mathFunc.delete(nest);\n            }\n            nest--;\n            if (nest === 0) {\n              func = false;\n              i++;\n            }\n          }\n          break;\n        }\n        case PCT: {\n          channel.push(Number(detail?.value) / MAX_PCT);\n          if (!func) {\n            i++;\n          }\n          break;\n        }\n        case W_SPACE: {\n          if (channel.length && func) {\n            const lastValue = channel[channel.length - 1];\n            if (typeof lastValue === 'number') {\n              channel.push(value);\n            } else if (\n              isString(lastValue) &&\n              !lastValue.endsWith('(') &&\n              lastValue !== ' '\n            ) {\n              channel.push(value);\n            }\n          }\n          break;\n        }\n        default: {\n          if (type !== COMMENT && type !== EOF && func) {\n            channel.push(value);\n          }\n        }\n      }\n    }\n  }\n  const channelValues = [];\n  for (const channel of channels) {\n    if (channel.length === 1) {\n      const [resolvedValue] = channel;\n      if (isStringOrNumber(resolvedValue)) {\n        channelValues.push(resolvedValue);\n      }\n    } else if (channel.length) {\n      const resolvedValue = serializeCalc(channel.join(''), {\n        format\n      });\n      channelValues.push(resolvedValue);\n    }\n  }\n  return channelValues as NumberOrStringColorChannels;\n}\n\n/**\n * extract origin color\n * @param value - CSS color value\n * @param [opt] - options\n * @returns origin color value\n */\nexport function extractOriginColor(\n  value: string,\n  opt: Options = {}\n): string | NullObject {\n  const { currentColor = '', format = '' } = opt;\n  if (isString(value)) {\n    value = value.toLowerCase().trim();\n    if (!value) {\n      return new NullObject();\n    }\n    if (!REG_FN_REL_START.test(value)) {\n      return value;\n    }\n  } else {\n    return new NullObject();\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'extractOriginColor',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  if (/currentcolor/.test(value)) {\n    if (currentColor) {\n      value = value.replace(/currentcolor/g, currentColor);\n    } else {\n      setCache(cacheKey, null);\n      return new NullObject();\n    }\n  }\n  let colorSpace = '';\n  if (REG_FN_REL_CAPT.test(value)) {\n    [, colorSpace] = value.match(REG_FN_REL_CAPT) as MatchedRegExp;\n  }\n  opt.colorSpace = colorSpace;\n  if (REG_COLOR_CAPT.test(value)) {\n    const [, originColor] = value.match(REG_COLOR_CAPT) as MatchedRegExp;\n    const [, restValue] = value.split(originColor) as MatchedRegExp;\n    if (/^[a-z]+$/.test(originColor)) {\n      if (\n        !/^transparent$/.test(originColor) &&\n        !Object.prototype.hasOwnProperty.call(NAMED_COLORS, originColor)\n      ) {\n        setCache(cacheKey, null);\n        return new NullObject();\n      }\n    } else if (format === VAL_SPEC) {\n      const resolvedOriginColor = resolveColor(originColor, opt);\n      if (isString(resolvedOriginColor)) {\n        value = value.replace(originColor, resolvedOriginColor);\n      }\n    }\n    if (format === VAL_SPEC) {\n      const tokens = tokenize({ css: restValue });\n      const channelValues = resolveColorChannels(tokens, opt);\n      if (channelValues instanceof NullObject) {\n        setCache(cacheKey, null);\n        return channelValues;\n      }\n      const [v1, v2, v3, v4] = channelValues;\n      let channelValue = '';\n      if (isStringOrNumber(v4)) {\n        channelValue = ` ${v1} ${v2} ${v3} / ${v4})`;\n      } else {\n        channelValue = ` ${channelValues.join(' ')})`;\n      }\n      if (restValue !== channelValue) {\n        value = value.replace(restValue, channelValue);\n      }\n    }\n    // nested relative color\n  } else {\n    const [, restValue] = value.split(REG_FN_REL_START) as MatchedRegExp;\n    const tokens = tokenize({ css: restValue });\n    const originColor: string[] = [];\n    let nest = 0;\n    while (tokens.length) {\n      const [type, tokenValue] = tokens.shift() as [TokenType, string];\n      switch (type) {\n        case FUNC:\n        case PAREN_OPEN: {\n          originColor.push(tokenValue);\n          nest++;\n          break;\n        }\n        case PAREN_CLOSE: {\n          const lastValue = originColor[originColor.length - 1];\n          if (lastValue === ' ') {\n            originColor.splice(-1, 1, tokenValue);\n          } else if (isString(lastValue)) {\n            originColor.push(tokenValue);\n          }\n          nest--;\n          break;\n        }\n        case W_SPACE: {\n          const lastValue = originColor[originColor.length - 1];\n          if (\n            isString(lastValue) &&\n            !lastValue.endsWith('(') &&\n            lastValue !== ' '\n          ) {\n            originColor.push(tokenValue);\n          }\n          break;\n        }\n        default: {\n          if (type !== COMMENT && type !== EOF) {\n            originColor.push(tokenValue);\n          }\n        }\n      }\n      if (nest === 0) {\n        break;\n      }\n    }\n    const resolvedOriginColor = resolveRelativeColor(\n      originColor.join('').trim(),\n      opt\n    );\n    if (resolvedOriginColor instanceof NullObject) {\n      setCache(cacheKey, null);\n      return resolvedOriginColor;\n    }\n    const channelValues = resolveColorChannels(tokens, opt);\n    if (channelValues instanceof NullObject) {\n      setCache(cacheKey, null);\n      return channelValues;\n    }\n    const [v1, v2, v3, v4] = channelValues;\n    let channelValue = '';\n    if (isStringOrNumber(v4)) {\n      channelValue = ` ${v1} ${v2} ${v3} / ${v4})`;\n    } else {\n      channelValue = ` ${channelValues.join(' ')})`;\n    }\n    value = value.replace(restValue, `${resolvedOriginColor}${channelValue}`);\n  }\n  setCache(cacheKey, value);\n  return value;\n}\n\n/**\n * resolve relative color\n * @param value - CSS relative color value\n * @param [opt] - options\n * @returns resolved value\n */\nexport function resolveRelativeColor(\n  value: string,\n  opt: Options = {}\n): string | NullObject {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (REG_FN_VAR.test(value)) {\n      if (format === VAL_SPEC) {\n        return value;\n        // var() must be resolved before resolveRelativeColor()\n      } else {\n        throw new SyntaxError(`Unexpected token ${FN_VAR} found.`);\n      }\n    } else if (!REG_FN_REL.test(value)) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveRelativeColor',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  const originColor = extractOriginColor(value, opt);\n  if (originColor instanceof NullObject) {\n    setCache(cacheKey, null);\n    return originColor;\n  }\n  value = originColor;\n  if (format === VAL_SPEC) {\n    if (value.startsWith('rgba(')) {\n      value = value.replace(/^rgba\\(/, 'rgb(');\n    } else if (value.startsWith('hsla(')) {\n      value = value.replace(/^hsla\\(/, 'hsl(');\n    }\n    return value;\n  }\n  const tokens = tokenize({ css: value });\n  const components = parseComponentValue(tokens) as ComponentValue;\n  const parsedComponents = colorParser(components);\n  if (!parsedComponents) {\n    setCache(cacheKey, null);\n    return new NullObject();\n  }\n  const {\n    alpha: alphaComponent,\n    channels: channelsComponent,\n    colorNotation,\n    syntaxFlags\n  } = parsedComponents;\n  let alpha: number | string;\n  if (Number.isNaN(Number(alphaComponent))) {\n    if (syntaxFlags instanceof Set && syntaxFlags.has(KEY_NONE)) {\n      alpha = NONE;\n    } else {\n      alpha = 0;\n    }\n  } else {\n    alpha = roundToPrecision(Number(alphaComponent), OCT);\n  }\n  let v1: number | string;\n  let v2: number | string;\n  let v3: number | string;\n  [v1, v2, v3] = channelsComponent;\n  let resolvedValue;\n  if (REG_CS_CIE.test(colorNotation)) {\n    const hasNone = syntaxFlags instanceof Set && syntaxFlags.has(KEY_NONE);\n    if (Number.isNaN(v1)) {\n      if (hasNone) {\n        v1 = NONE;\n      } else {\n        v1 = 0;\n      }\n    } else {\n      v1 = roundToPrecision(v1, HEX);\n    }\n    if (Number.isNaN(v2)) {\n      if (hasNone) {\n        v2 = NONE;\n      } else {\n        v2 = 0;\n      }\n    } else {\n      v2 = roundToPrecision(v2, HEX);\n    }\n    if (Number.isNaN(v3)) {\n      if (hasNone) {\n        v3 = NONE;\n      } else {\n        v3 = 0;\n      }\n    } else {\n      v3 = roundToPrecision(v3, HEX);\n    }\n    if (alpha === 1) {\n      resolvedValue = `${colorNotation}(${v1} ${v2} ${v3})`;\n    } else {\n      resolvedValue = `${colorNotation}(${v1} ${v2} ${v3} / ${alpha})`;\n    }\n  } else if (REG_CS_HSL.test(colorNotation)) {\n    if (Number.isNaN(v1)) {\n      v1 = 0;\n    }\n    if (Number.isNaN(v2)) {\n      v2 = 0;\n    }\n    if (Number.isNaN(v3)) {\n      v3 = 0;\n    }\n    let [r, g, b] = convertColorToRgb(\n      `${colorNotation}(${v1} ${v2} ${v3} / ${alpha})`\n    ) as ColorChannels;\n    r = roundToPrecision(r / MAX_RGB, DEC);\n    g = roundToPrecision(g / MAX_RGB, DEC);\n    b = roundToPrecision(b / MAX_RGB, DEC);\n    if (alpha === 1) {\n      resolvedValue = `color(srgb ${r} ${g} ${b})`;\n    } else {\n      resolvedValue = `color(srgb ${r} ${g} ${b} / ${alpha})`;\n    }\n  } else {\n    const cs = colorNotation === 'rgb' ? 'srgb' : colorNotation;\n    const hasNone = syntaxFlags instanceof Set && syntaxFlags.has(KEY_NONE);\n    if (Number.isNaN(v1)) {\n      if (hasNone) {\n        v1 = NONE;\n      } else {\n        v1 = 0;\n      }\n    } else {\n      v1 = roundToPrecision(v1, DEC);\n    }\n    if (Number.isNaN(v2)) {\n      if (hasNone) {\n        v2 = NONE;\n      } else {\n        v2 = 0;\n      }\n    } else {\n      v2 = roundToPrecision(v2, DEC);\n    }\n    if (Number.isNaN(v3)) {\n      if (hasNone) {\n        v3 = NONE;\n      } else {\n        v3 = 0;\n      }\n    } else {\n      v3 = roundToPrecision(v3, DEC);\n    }\n    if (alpha === 1) {\n      resolvedValue = `color(${cs} ${v1} ${v2} ${v3})`;\n    } else {\n      resolvedValue = `color(${cs} ${v1} ${v2} ${v3} / ${alpha})`;\n    }\n  }\n  setCache(cacheKey, resolvedValue);\n  return resolvedValue;\n}\n"], "names": ["color<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AA2CA,MAAM;AAAA,EACJ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX;AAAA,EACA,UAAU;AAAA,EACV,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY;AACd,IAAI;AACJ,MAAM,EAAE,iBAAiB,SAAA,IAAa;AACtC,MAAM,YAAY;AAGlB,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,UAAU;AAChB,MAAM,UAAU;AAShB,MAAM,iBAAiB,IAAI;AAAA,EACzB,IAAI,MAAM,IAAI,cAAc,IAAI,OAAO;AACzC;AACA,MAAM,aAAa;AACnB,MAAM,aAAa,IAAI,OAAO,OAAO,MAAM,IAAI,MAAM,IAAI;AACzD,MAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,MAAM,aAAa,IAAI,OAAO,MAAM;AACpC,MAAM,kBAAkB,IAAI,OAAO,IAAI,WAAW,EAAE;AACpD,MAAM,mBAAmB,IAAI,OAAO,IAAI,MAAM,EAAE;AAChD,MAAM,aAAa,IAAI,OAAO,UAAU;AAQjC,SAAS,qBACd,QACA,MAAe,IAC2B;AAC1C,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAAA;AAElD,QAAM,EAAE,aAAa,IAAI,SAAS,GAAO,IAAA;AACnC,QAAA,oCAAoB,IAAI;AAAA,IAC5B,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IACjC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClC,CAAC,SAAS,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAClC,CAAC,OAAO,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,IAChC,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,OAAO,CAAC;AAAA,EAAA,CAClC;AACK,QAAA,eAAe,cAAc,IAAI,UAAU;AAEjD,MAAI,CAAC,cAAc;AACjB,WAAO,IAAI,WAAW;AAAA,EAAA;AAElB,QAAA,+BAAe,IAAI;AACnB,QAAA,WAKF,CAAC,CAAA,GAAI,CAAA,GAAI,CAAA,GAAI,CAAA,CAAE;AACnB,MAAI,IAAI;AACR,MAAI,OAAO;AACX,MAAI,OAAO;AACX,SAAO,OAAO,QAAQ;AACd,UAAA,QAAQ,OAAO,MAAM;AAC3B,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IAAA;AAEjD,UAAM,CAAC,MAAM,OAAW,EAAA,EAAA,MAAM,IAAI;AAO5B,UAAA,UAAU,SAAS,CAAC;AACtB,QAAA,MAAM,QAAQ,OAAO,GAAG;AAC1B,cAAQ,MAAM;AAAA,QACZ,KAAK,KAAK;AACF,gBAAA,gBAAgB,iBAAiB,OAAO,GAAG;AAC7C,cAAA,SAAS,aAAa,GAAG;AAC3B,oBAAQ,KAAK,aAAa;AAAA,UAAA,OACrB;AACL,oBAAQ,KAAK,KAAK;AAAA,UAAA;AAEpB;AAAA,QAAA;AAAA,QAEF,KAAK,MAAM;AACT,kBAAQ,KAAK,KAAK;AACX,iBAAA;AACP;AACI,cAAA,kBAAkB,KAAK,KAAK,GAAG;AACjC,qBAAS,IAAI,IAAI;AAAA,UAAA;AAEnB;AAAA,QAAA;AAAA,QAEF,KAAK,OAAO;AAEV,cAAI,CAAC,aAAa,SAAS,KAAK,GAAG;AACjC,mBAAO,IAAI,WAAW;AAAA,UAAA;AAExB,kBAAQ,KAAK,KAAK;AAClB,cAAI,CAAC,MAAM;AACT;AAAA,UAAA;AAEF;AAAA,QAAA;AAAA,QAEF,KAAK,KAAK;AACR,kBAAQ,KAAK,OAAO,iCAAQ,KAAK,CAAC;AAClC,cAAI,CAAC,MAAM;AACT;AAAA,UAAA;AAEF;AAAA,QAAA;AAAA,QAEF,KAAK,YAAY;AACf,kBAAQ,KAAK,KAAK;AAClB;AACA;AAAA,QAAA;AAAA,QAEF,KAAK,aAAa;AAChB,cAAI,MAAM;AACR,kBAAM,YAAY,QAAQ,QAAQ,SAAS,CAAC;AAC5C,gBAAI,cAAc,KAAK;AACb,sBAAA,OAAO,IAAI,GAAG,KAAK;AAAA,YAAA,OACtB;AACL,sBAAQ,KAAK,KAAK;AAAA,YAAA;AAEhB,gBAAA,SAAS,IAAI,IAAI,GAAG;AACtB,uBAAS,OAAO,IAAI;AAAA,YAAA;AAEtB;AACA,gBAAI,SAAS,GAAG;AACP,qBAAA;AACP;AAAA,YAAA;AAAA,UACF;AAEF;AAAA,QAAA;AAAA,QAEF,KAAK,KAAK;AACR,kBAAQ,KAAK,OAAO,iCAAQ,KAAK,IAAI,OAAO;AAC5C,cAAI,CAAC,MAAM;AACT;AAAA,UAAA;AAEF;AAAA,QAAA;AAAA,QAEF,KAAK,SAAS;AACR,cAAA,QAAQ,UAAU,MAAM;AAC1B,kBAAM,YAAY,QAAQ,QAAQ,SAAS,CAAC;AACxC,gBAAA,OAAO,cAAc,UAAU;AACjC,sBAAQ,KAAK,KAAK;AAAA,YAAA,WAElB,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,sBAAQ,KAAK,KAAK;AAAA,YAAA;AAAA,UACpB;AAEF;AAAA,QAAA;AAAA,QAEF,SAAS;AACP,cAAI,SAAS,WAAW,SAAS,OAAO,MAAM;AAC5C,oBAAQ,KAAK,KAAK;AAAA,UAAA;AAAA,QACpB;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEF,QAAM,gBAAgB,CAAC;AACvB,aAAW,WAAW,UAAU;AAC1B,QAAA,QAAQ,WAAW,GAAG;AAClB,YAAA,CAAC,aAAa,IAAI;AACpB,UAAA,iBAAiB,aAAa,GAAG;AACnC,sBAAc,KAAK,aAAa;AAAA,MAAA;AAAA,IAClC,WACS,QAAQ,QAAQ;AACzB,YAAM,gBAAgB,cAAc,QAAQ,KAAK,EAAE,GAAG;AAAA,QACpD;AAAA,MAAA,CACD;AACD,oBAAc,KAAK,aAAa;AAAA,IAAA;AAAA,EAClC;AAEK,SAAA;AACT;AAQO,SAAS,mBACd,OACA,MAAe,IACM;AACrB,QAAM,EAAE,eAAe,IAAI,SAAS,GAAO,IAAA;AACvC,MAAA,SAAS,KAAK,GAAG;AACX,YAAA,MAAM,YAAY,EAAE,KAAK;AACjC,QAAI,CAAC,OAAO;AACV,aAAO,IAAI,WAAW;AAAA,IAAA;AAExB,QAAI,CAAC,iBAAiB,KAAK,KAAK,GAAG;AAC1B,aAAA;AAAA,IAAA;AAAA,EACT,OACK;AACL,WAAO,IAAI,WAAW;AAAA,EAAA;AAExB,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,WAAO,aAAa;AAAA,EAAA;AAElB,MAAA,eAAe,KAAK,KAAK,GAAG;AAC9B,QAAI,cAAc;AACR,cAAA,MAAM,QAAQ,iBAAiB,YAAY;AAAA,IAAA,OAC9C;AACL,eAAS,UAAU,IAAI;AACvB,aAAO,IAAI,WAAW;AAAA,IAAA;AAAA,EACxB;AAEF,MAAI,aAAa;AACb,MAAA,gBAAgB,KAAK,KAAK,GAAG;AAC/B,KAAA,EAAG,UAAU,IAAI,MAAM,MAAM,eAAe;AAAA,EAAA;AAE9C,MAAI,aAAa;AACb,MAAA,eAAe,KAAK,KAAK,GAAG;AAC9B,UAAM,CAAG,EAAA,WAAW,IAAI,MAAM,MAAM,cAAc;AAClD,UAAM,CAAG,EAAA,SAAS,IAAI,MAAM,MAAM,WAAW;AACzC,QAAA,WAAW,KAAK,WAAW,GAAG;AAChC,UACE,CAAC,gBAAgB,KAAK,WAAW,KACjC,CAAC,OAAO,UAAU,eAAe,KAAK,cAAc,WAAW,GAC/D;AACA,iBAAS,UAAU,IAAI;AACvB,eAAO,IAAI,WAAW;AAAA,MAAA;AAAA,IACxB,WACS,WAAW,UAAU;AACxB,YAAA,sBAAsB,aAAa,aAAa,GAAG;AACrD,UAAA,SAAS,mBAAmB,GAAG;AACzB,gBAAA,MAAM,QAAQ,aAAa,mBAAmB;AAAA,MAAA;AAAA,IACxD;AAEF,QAAI,WAAW,UAAU;AACvB,YAAM,SAAS,SAAS,EAAE,KAAK,WAAW;AACpC,YAAA,gBAAgB,qBAAqB,QAAQ,GAAG;AACtD,UAAI,yBAAyB,YAAY;AACvC,iBAAS,UAAU,IAAI;AAChB,eAAA;AAAA,MAAA;AAET,YAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,UAAI,eAAe;AACf,UAAA,iBAAiB,EAAE,GAAG;AACxB,uBAAe,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,MAAA,OACpC;AACL,uBAAe,IAAI,cAAc,KAAK,GAAG,CAAC;AAAA,MAAA;AAE5C,UAAI,cAAc,cAAc;AACtB,gBAAA,MAAM,QAAQ,WAAW,YAAY;AAAA,MAAA;AAAA,IAC/C;AAAA,EACF,OAEK;AACL,UAAM,CAAG,EAAA,SAAS,IAAI,MAAM,MAAM,gBAAgB;AAClD,UAAM,SAAS,SAAS,EAAE,KAAK,WAAW;AAC1C,UAAM,cAAwB,CAAC;AAC/B,QAAI,OAAO;AACX,WAAO,OAAO,QAAQ;AACpB,YAAM,CAAC,MAAM,UAAU,IAAI,OAAO,MAAM;AACxC,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK,YAAY;AACf,sBAAY,KAAK,UAAU;AAC3B;AACA;AAAA,QAAA;AAAA,QAEF,KAAK,aAAa;AAChB,gBAAM,YAAY,YAAY,YAAY,SAAS,CAAC;AACpD,cAAI,cAAc,KAAK;AACT,wBAAA,OAAO,IAAI,GAAG,UAAU;AAAA,UAAA,WAC3B,SAAS,SAAS,GAAG;AAC9B,wBAAY,KAAK,UAAU;AAAA,UAAA;AAE7B;AACA;AAAA,QAAA;AAAA,QAEF,KAAK,SAAS;AACZ,gBAAM,YAAY,YAAY,YAAY,SAAS,CAAC;AAElD,cAAA,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,wBAAY,KAAK,UAAU;AAAA,UAAA;AAE7B;AAAA,QAAA;AAAA,QAEF,SAAS;AACH,cAAA,SAAS,WAAW,SAAS,KAAK;AACpC,wBAAY,KAAK,UAAU;AAAA,UAAA;AAAA,QAC7B;AAAA,MACF;AAEF,UAAI,SAAS,GAAG;AACd;AAAA,MAAA;AAAA,IACF;AAEF,UAAM,sBAAsB;AAAA,MAC1B,YAAY,KAAK,EAAE,EAAE,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,+BAA+B,YAAY;AAC7C,eAAS,UAAU,IAAI;AAChB,aAAA;AAAA,IAAA;AAEH,UAAA,gBAAgB,qBAAqB,QAAQ,GAAG;AACtD,QAAI,yBAAyB,YAAY;AACvC,eAAS,UAAU,IAAI;AAChB,aAAA;AAAA,IAAA;AAET,UAAM,CAAC,IAAI,IAAI,IAAI,EAAE,IAAI;AACzB,QAAI,eAAe;AACf,QAAA,iBAAiB,EAAE,GAAG;AACxB,qBAAe,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE;AAAA,IAAA,OACpC;AACL,qBAAe,IAAI,cAAc,KAAK,GAAG,CAAC;AAAA,IAAA;AAE5C,YAAQ,MAAM,QAAQ,WAAW,GAAG,mBAAmB,GAAG,YAAY,EAAE;AAAA,EAAA;AAE1E,WAAS,UAAU,KAAK;AACjB,SAAA;AACT;AAQO,SAAS,qBACd,OACA,MAAe,IACM;AACf,QAAA,EAAE,SAAS,GAAA,IAAO;AACpB,MAAA,SAAS,KAAK,GAAG;AACf,QAAA,WAAW,KAAK,KAAK,GAAG;AAC1B,UAAI,WAAW,UAAU;AAChB,eAAA;AAAA,MAAA,OAEF;AACL,cAAM,IAAI,YAAY,oBAAoB,MAAM,SAAS;AAAA,MAAA;AAAA,IAElD,WAAA,CAAC,WAAW,KAAK,KAAK,GAAG;AAC3B,aAAA;AAAA,IAAA;AAED,YAAA,MAAM,YAAY,EAAE,KAAK;AAAA,EAAA,OAC5B;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EAAA;AAEjD,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IACF;AAAA,IACA;AAAA,EACF;AACM,QAAA,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AAChB,aAAA;AAAA,IAAA;AAET,WAAO,aAAa;AAAA,EAAA;AAEhB,QAAA,cAAc,mBAAmB,OAAO,GAAG;AACjD,MAAI,uBAAuB,YAAY;AACrC,aAAS,UAAU,IAAI;AAChB,WAAA;AAAA,EAAA;AAED,UAAA;AACR,MAAI,WAAW,UAAU;AACnB,QAAA,MAAM,WAAW,OAAO,GAAG;AACrB,cAAA,MAAM,QAAQ,WAAW,MAAM;AAAA,IAC9B,WAAA,MAAM,WAAW,OAAO,GAAG;AAC5B,cAAA,MAAM,QAAQ,WAAW,MAAM;AAAA,IAAA;AAElC,WAAA;AAAA,EAAA;AAET,QAAM,SAAS,SAAS,EAAE,KAAK,OAAO;AAChC,QAAA,aAAa,oBAAoB,MAAM;AACvC,QAAA,mBAAmBA,MAAY,UAAU;AAC/C,MAAI,CAAC,kBAAkB;AACrB,aAAS,UAAU,IAAI;AACvB,WAAO,IAAI,WAAW;AAAA,EAAA;AAElB,QAAA;AAAA,IACJ,OAAO;AAAA,IACP,UAAU;AAAA,IACV;AAAA,IACA;AAAA,EAAA,IACE;AACA,MAAA;AACJ,MAAI,OAAO,MAAM,OAAO,cAAc,CAAC,GAAG;AACxC,QAAI,uBAAuB,OAAO,YAAY,IAAI,QAAQ,GAAG;AACnD,cAAA;AAAA,IAAA,OACH;AACG,cAAA;AAAA,IAAA;AAAA,EACV,OACK;AACL,YAAQ,iBAAiB,OAAO,cAAc,GAAG,GAAG;AAAA,EAAA;AAElD,MAAA;AACA,MAAA;AACA,MAAA;AACH,GAAA,IAAI,IAAI,EAAE,IAAI;AACX,MAAA;AACA,MAAA,WAAW,KAAK,aAAa,GAAG;AAClC,UAAM,UAAU,uBAAuB,OAAO,YAAY,IAAI,QAAQ;AAClE,QAAA,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACN,aAAA;AAAA,MAAA,OACA;AACA,aAAA;AAAA,MAAA;AAAA,IACP,OACK;AACA,WAAA,iBAAiB,IAAI,GAAG;AAAA,IAAA;AAE3B,QAAA,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACN,aAAA;AAAA,MAAA,OACA;AACA,aAAA;AAAA,MAAA;AAAA,IACP,OACK;AACA,WAAA,iBAAiB,IAAI,GAAG;AAAA,IAAA;AAE3B,QAAA,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACN,aAAA;AAAA,MAAA,OACA;AACA,aAAA;AAAA,MAAA;AAAA,IACP,OACK;AACA,WAAA,iBAAiB,IAAI,GAAG;AAAA,IAAA;AAE/B,QAAI,UAAU,GAAG;AACf,sBAAgB,GAAG,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,IAAA,OAC7C;AACW,sBAAA,GAAG,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,KAAK;AAAA,IAAA;AAAA,EAEtD,WAAA,WAAW,KAAK,aAAa,GAAG;AACrC,QAAA,OAAO,MAAM,EAAE,GAAG;AACf,WAAA;AAAA,IAAA;AAEH,QAAA,OAAO,MAAM,EAAE,GAAG;AACf,WAAA;AAAA,IAAA;AAEH,QAAA,OAAO,MAAM,EAAE,GAAG;AACf,WAAA;AAAA,IAAA;AAEP,QAAI,CAAC,GAAG,GAAG,CAAC,IAAI;AAAA,MACd,GAAG,aAAa,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,KAAK;AAAA,IAC/C;AACI,QAAA,iBAAiB,IAAI,SAAS,GAAG;AACjC,QAAA,iBAAiB,IAAI,SAAS,GAAG;AACjC,QAAA,iBAAiB,IAAI,SAAS,GAAG;AACrC,QAAI,UAAU,GAAG;AACf,sBAAgB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,IAAA,OACpC;AACL,sBAAgB,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;AAAA,IAAA;AAAA,EACtD,OACK;AACC,UAAA,KAAK,kBAAkB,QAAQ,SAAS;AAC9C,UAAM,UAAU,uBAAuB,OAAO,YAAY,IAAI,QAAQ;AAClE,QAAA,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACN,aAAA;AAAA,MAAA,OACA;AACA,aAAA;AAAA,MAAA;AAAA,IACP,OACK;AACA,WAAA,iBAAiB,IAAI,GAAG;AAAA,IAAA;AAE3B,QAAA,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACN,aAAA;AAAA,MAAA,OACA;AACA,aAAA;AAAA,MAAA;AAAA,IACP,OACK;AACA,WAAA,iBAAiB,IAAI,GAAG;AAAA,IAAA;AAE3B,QAAA,OAAO,MAAM,EAAE,GAAG;AACpB,UAAI,SAAS;AACN,aAAA;AAAA,MAAA,OACA;AACA,aAAA;AAAA,MAAA;AAAA,IACP,OACK;AACA,WAAA,iBAAiB,IAAI,GAAG;AAAA,IAAA;AAE/B,QAAI,UAAU,GAAG;AACf,sBAAgB,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE;AAAA,IAAA,OACxC;AACW,sBAAA,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,KAAK;AAAA,IAAA;AAAA,EAC1D;AAEF,WAAS,UAAU,aAAa;AACzB,SAAA;AACT;"}