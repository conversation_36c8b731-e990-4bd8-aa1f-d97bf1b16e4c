{"version": 3, "sources": ["../../highlight.js/lib/languages/powershell.js"], "sourcesContent": ["/*\nLanguage: PowerShell\nDescription: PowerShell is a task-based command-line shell and scripting language built on .NET.\nAuthor: <PERSON> <<EMAIL>>\nContributors: <PERSON> <nb<PERSON><PERSON>@nblumhardt.com>, <PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>\nWebsite: https://docs.microsoft.com/en-us/powershell/\n*/\n\nfunction powershell(hljs) {\n  const TYPES = [\n    \"string\",\n    \"char\",\n    \"byte\",\n    \"int\",\n    \"long\",\n    \"bool\",\n    \"decimal\",\n    \"single\",\n    \"double\",\n    \"DateTime\",\n    \"xml\",\n    \"array\",\n    \"hashtable\",\n    \"void\"\n  ];\n\n  // https://docs.microsoft.com/en-us/powershell/scripting/developer/cmdlet/approved-verbs-for-windows-powershell-commands\n  const VALID_VERBS =\n    'Add|Clear|Close|Copy|Enter|Exit|Find|Format|Get|Hide|Join|Lock|' +\n    'Move|New|Open|Optimize|Pop|Push|Redo|Remove|Rename|Reset|Resize|' +\n    'Search|Select|Set|Show|Skip|Split|Step|Switch|Undo|Unlock|' +\n    'Watch|Backup|Checkpoint|Compare|Compress|Convert|ConvertFrom|' +\n    'ConvertTo|Dismount|Edit|Expand|Export|Group|Import|Initialize|' +\n    'Limit|Merge|Mount|Out|Publish|Restore|Save|Sync|Unpublish|Update|' +\n    'Approve|Assert|Build|Complete|Confirm|Deny|Deploy|Disable|Enable|Install|Invoke|' +\n    'Register|Request|Restart|Resume|Start|Stop|Submit|Suspend|Uninstall|' +\n    'Unregister|Wait|Debug|Measure|Ping|Repair|Resolve|Test|Trace|Connect|' +\n    'Disconnect|Read|Receive|Send|Write|Block|Grant|Protect|Revoke|Unblock|' +\n    'Unprotect|Use|ForEach|Sort|Tee|Where';\n\n  const COMPARISON_OPERATORS =\n    '-and|-as|-band|-bnot|-bor|-bxor|-casesensitive|-ccontains|-ceq|-cge|-cgt|' +\n    '-cle|-clike|-clt|-cmatch|-cne|-cnotcontains|-cnotlike|-cnotmatch|-contains|' +\n    '-creplace|-csplit|-eq|-exact|-f|-file|-ge|-gt|-icontains|-ieq|-ige|-igt|' +\n    '-ile|-ilike|-ilt|-imatch|-in|-ine|-inotcontains|-inotlike|-inotmatch|' +\n    '-ireplace|-is|-isnot|-isplit|-join|-le|-like|-lt|-match|-ne|-not|' +\n    '-notcontains|-notin|-notlike|-notmatch|-or|-regex|-replace|-shl|-shr|' +\n    '-split|-wildcard|-xor';\n\n  const KEYWORDS = {\n    $pattern: /-?[A-z\\.\\-]+\\b/,\n    keyword:\n      'if else foreach return do while until elseif begin for trap data dynamicparam ' +\n      'end break throw param continue finally in switch exit filter try process catch ' +\n      'hidden static parameter',\n    // \"echo\" relevance has been set to 0 to avoid auto-detect conflicts with shell transcripts\n    built_in:\n      'ac asnp cat cd CFS chdir clc clear clhy cli clp cls clv cnsn compare copy cp ' +\n      'cpi cpp curl cvpa dbp del diff dir dnsn ebp echo|0 epal epcsv epsn erase etsn exsn fc fhx ' +\n      'fl ft fw gal gbp gc gcb gci gcm gcs gdr gerr ghy gi gin gjb gl gm gmo gp gps gpv group ' +\n      'gsn gsnp gsv gtz gu gv gwmi h history icm iex ihy ii ipal ipcsv ipmo ipsn irm ise iwmi ' +\n      'iwr kill lp ls man md measure mi mount move mp mv nal ndr ni nmo npssc nsn nv ogv oh ' +\n      'popd ps pushd pwd r rbp rcjb rcsn rd rdr ren ri rjb rm rmdir rmo rni rnp rp rsn rsnp ' +\n      'rujb rv rvpa rwmi sajb sal saps sasv sbp sc scb select set shcm si sl sleep sls sort sp ' +\n      'spjb spps spsv start stz sujb sv swmi tee trcm type wget where wjb write'\n    // TODO: 'validate[A-Z]+' can't work in keywords\n  };\n\n  const TITLE_NAME_RE = /\\w[\\w\\d]*((-)[\\w\\d]+)*/;\n\n  const BACKTICK_ESCAPE = {\n    begin: '`[\\\\s\\\\S]',\n    relevance: 0\n  };\n\n  const VAR = {\n    className: 'variable',\n    variants: [\n      {\n        begin: /\\$\\B/\n      },\n      {\n        className: 'keyword',\n        begin: /\\$this/\n      },\n      {\n        begin: /\\$[\\w\\d][\\w\\d_:]*/\n      }\n    ]\n  };\n\n  const LITERAL = {\n    className: 'literal',\n    begin: /\\$(null|true|false)\\b/\n  };\n\n  const QUOTE_STRING = {\n    className: \"string\",\n    variants: [\n      {\n        begin: /\"/,\n        end: /\"/\n      },\n      {\n        begin: /@\"/,\n        end: /^\"@/\n      }\n    ],\n    contains: [\n      BACKTICK_ESCAPE,\n      VAR,\n      {\n        className: 'variable',\n        begin: /\\$[A-z]/,\n        end: /[^A-z]/\n      }\n    ]\n  };\n\n  const APOS_STRING = {\n    className: 'string',\n    variants: [\n      {\n        begin: /'/,\n        end: /'/\n      },\n      {\n        begin: /@'/,\n        end: /^'@/\n      }\n    ]\n  };\n\n  const PS_HELPTAGS = {\n    className: \"doctag\",\n    variants: [\n      /* no paramater help tags */\n      {\n        begin: /\\.(synopsis|description|example|inputs|outputs|notes|link|component|role|functionality)/\n      },\n      /* one parameter help tags */\n      {\n        begin: /\\.(parameter|forwardhelptargetname|forwardhelpcategory|remotehelprunspace|externalhelp)\\s+\\S+/\n      }\n    ]\n  };\n\n  const PS_COMMENT = hljs.inherit(\n    hljs.COMMENT(null, null),\n    {\n      variants: [\n        /* single-line comment */\n        {\n          begin: /#/,\n          end: /$/\n        },\n        /* multi-line comment */\n        {\n          begin: /<#/,\n          end: /#>/\n        }\n      ],\n      contains: [ PS_HELPTAGS ]\n    }\n  );\n\n  const CMDLETS = {\n    className: 'built_in',\n    variants: [\n      {\n        begin: '('.concat(VALID_VERBS, ')+(-)[\\\\w\\\\d]+')\n      }\n    ]\n  };\n\n  const PS_CLASS = {\n    className: 'class',\n    beginKeywords: 'class enum',\n    end: /\\s*[{]/,\n    excludeEnd: true,\n    relevance: 0,\n    contains: [ hljs.TITLE_MODE ]\n  };\n\n  const PS_FUNCTION = {\n    className: 'function',\n    begin: /function\\s+/,\n    end: /\\s*\\{|$/,\n    excludeEnd: true,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        begin: \"function\",\n        relevance: 0,\n        className: \"keyword\"\n      },\n      {\n        className: \"title\",\n        begin: TITLE_NAME_RE,\n        relevance: 0\n      },\n      {\n        begin: /\\(/,\n        end: /\\)/,\n        className: \"params\",\n        relevance: 0,\n        contains: [ VAR ]\n      }\n      // CMDLETS\n    ]\n  };\n\n  // Using statment, plus type, plus assembly name.\n  const PS_USING = {\n    begin: /using\\s/,\n    end: /$/,\n    returnBegin: true,\n    contains: [\n      QUOTE_STRING,\n      APOS_STRING,\n      {\n        className: 'keyword',\n        begin: /(using|assembly|command|module|namespace|type)/\n      }\n    ]\n  };\n\n  // Comperison operators & function named parameters.\n  const PS_ARGUMENTS = {\n    variants: [\n      // PS literals are pretty verbose so it's a good idea to accent them a bit.\n      {\n        className: 'operator',\n        begin: '('.concat(COMPARISON_OPERATORS, ')\\\\b')\n      },\n      {\n        className: 'literal',\n        begin: /(-)[\\w\\d]+/,\n        relevance: 0\n      }\n    ]\n  };\n\n  const HASH_SIGNS = {\n    className: 'selector-tag',\n    begin: /@\\B/,\n    relevance: 0\n  };\n\n  // It's a very general rule so I'll narrow it a bit with some strict boundaries\n  // to avoid any possible false-positive collisions!\n  const PS_METHODS = {\n    className: 'function',\n    begin: /\\[.*\\]\\s*[\\w]+[ ]??\\(/,\n    end: /$/,\n    returnBegin: true,\n    relevance: 0,\n    contains: [\n      {\n        className: 'keyword',\n        begin: '('.concat(\n          KEYWORDS.keyword.toString().replace(/\\s/g, '|'\n          ), ')\\\\b'),\n        endsParent: true,\n        relevance: 0\n      },\n      hljs.inherit(hljs.TITLE_MODE, {\n        endsParent: true\n      })\n    ]\n  };\n\n  const GENTLEMANS_SET = [\n    // STATIC_MEMBER,\n    PS_METHODS,\n    PS_COMMENT,\n    BACKTICK_ESCAPE,\n    hljs.NUMBER_MODE,\n    QUOTE_STRING,\n    APOS_STRING,\n    // PS_NEW_OBJECT_TYPE,\n    CMDLETS,\n    VAR,\n    LITERAL,\n    HASH_SIGNS\n  ];\n\n  const PS_TYPE = {\n    begin: /\\[/,\n    end: /\\]/,\n    excludeBegin: true,\n    excludeEnd: true,\n    relevance: 0,\n    contains: [].concat(\n      'self',\n      GENTLEMANS_SET,\n      {\n        begin: \"(\" + TYPES.join(\"|\") + \")\",\n        className: \"built_in\",\n        relevance: 0\n      },\n      {\n        className: 'type',\n        begin: /[\\.\\w\\d]+/,\n        relevance: 0\n      }\n    )\n  };\n\n  PS_METHODS.contains.unshift(PS_TYPE);\n\n  return {\n    name: 'PowerShell',\n    aliases: [\n      \"ps\",\n      \"ps1\"\n    ],\n    case_insensitive: true,\n    keywords: KEYWORDS,\n    contains: GENTLEMANS_SET.concat(\n      PS_CLASS,\n      PS_FUNCTION,\n      PS_USING,\n      PS_ARGUMENTS,\n      PS_TYPE\n    )\n  };\n}\n\nmodule.exports = powershell;\n"], "mappings": ";;;;;AAAA;AAAA;AAQA,aAAS,WAAW,MAAM;AACxB,YAAM,QAAQ;AAAA,QACZ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAGA,YAAM,cACJ;AAYF,YAAM,uBACJ;AAQF,YAAM,WAAW;AAAA,QACf,UAAU;AAAA,QACV,SACE;AAAA;AAAA,QAIF,UACE;AAAA;AAAA,MASJ;AAEA,YAAM,gBAAgB;AAEtB,YAAM,kBAAkB;AAAA,QACtB,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAEA,YAAM,MAAM;AAAA,QACV,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,UACA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,OAAO;AAAA,MACT;AAEA,YAAM,eAAe;AAAA,QACnB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,UACP;AAAA,QACF;AAAA,MACF;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,UAAU;AAAA;AAAA,UAER;AAAA,YACE,OAAO;AAAA,UACT;AAAA;AAAA,UAEA;AAAA,YACE,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa,KAAK;AAAA,QACtB,KAAK,QAAQ,MAAM,IAAI;AAAA,QACvB;AAAA,UACE,UAAU;AAAA;AAAA,YAER;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA;AAAA,YAEA;AAAA,cACE,OAAO;AAAA,cACP,KAAK;AAAA,YACP;AAAA,UACF;AAAA,UACA,UAAU,CAAE,WAAY;AAAA,QAC1B;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO,IAAI,OAAO,aAAa,gBAAgB;AAAA,UACjD;AAAA,QACF;AAAA,MACF;AAEA,YAAM,WAAW;AAAA,QACf,WAAW;AAAA,QACX,eAAe;AAAA,QACf,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU,CAAE,KAAK,UAAW;AAAA,MAC9B;AAEA,YAAM,cAAc;AAAA,QAClB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,YAAY;AAAA,QACZ,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,OAAO;AAAA,YACP,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,OAAO;AAAA,YACP,KAAK;AAAA,YACL,WAAW;AAAA,YACX,WAAW;AAAA,YACX,UAAU,CAAE,GAAI;AAAA,UAClB;AAAA;AAAA,QAEF;AAAA,MACF;AAGA,YAAM,WAAW;AAAA,QACf,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,UAAU;AAAA,UACR;AAAA,UACA;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAGA,YAAM,eAAe;AAAA,QACnB,UAAU;AAAA;AAAA,UAER;AAAA,YACE,WAAW;AAAA,YACX,OAAO,IAAI,OAAO,sBAAsB,MAAM;AAAA,UAChD;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,WAAW;AAAA,MACb;AAIA,YAAM,aAAa;AAAA,QACjB,WAAW;AAAA,QACX,OAAO;AAAA,QACP,KAAK;AAAA,QACL,aAAa;AAAA,QACb,WAAW;AAAA,QACX,UAAU;AAAA,UACR;AAAA,YACE,WAAW;AAAA,YACX,OAAO,IAAI;AAAA,cACT,SAAS,QAAQ,SAAS,EAAE;AAAA,gBAAQ;AAAA,gBAAO;AAAA,cAC3C;AAAA,cAAG;AAAA,YAAM;AAAA,YACX,YAAY;AAAA,YACZ,WAAW;AAAA,UACb;AAAA,UACA,KAAK,QAAQ,KAAK,YAAY;AAAA,YAC5B,YAAY;AAAA,UACd,CAAC;AAAA,QACH;AAAA,MACF;AAEA,YAAM,iBAAiB;AAAA;AAAA,QAErB;AAAA,QACA;AAAA,QACA;AAAA,QACA,KAAK;AAAA,QACL;AAAA,QACA;AAAA;AAAA,QAEA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAEA,YAAM,UAAU;AAAA,QACd,OAAO;AAAA,QACP,KAAK;AAAA,QACL,cAAc;AAAA,QACd,YAAY;AAAA,QACZ,WAAW;AAAA,QACX,UAAU,CAAC,EAAE;AAAA,UACX;AAAA,UACA;AAAA,UACA;AAAA,YACE,OAAO,MAAM,MAAM,KAAK,GAAG,IAAI;AAAA,YAC/B,WAAW;AAAA,YACX,WAAW;AAAA,UACb;AAAA,UACA;AAAA,YACE,WAAW;AAAA,YACX,OAAO;AAAA,YACP,WAAW;AAAA,UACb;AAAA,QACF;AAAA,MACF;AAEA,iBAAW,SAAS,QAAQ,OAAO;AAEnC,aAAO;AAAA,QACL,MAAM;AAAA,QACN,SAAS;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,QACA,kBAAkB;AAAA,QAClB,UAAU;AAAA,QACV,UAAU,eAAe;AAAA,UACvB;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}