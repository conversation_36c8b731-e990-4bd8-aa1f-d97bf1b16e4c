{"name": "deep-eql", "version": "4.1.4", "description": "Improved deep equality testing for Node.js and the browser.", "keywords": ["chai util", "deep equal", "object equal", "testing"], "repository": {"type": "git", "url": "**************:chaijs/deep-eql.git"}, "license": "MIT", "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> (https://github.com/keithamus)", "dougluce (https://github.com/dougluce)", "<PERSON><PERSON><PERSON> (https://github.com/flowlo)"], "main": "./index", "files": ["index.js", "deep-eql.js"], "scripts": {"bench": "node bench", "build": "browserify $npm_package_main --standalone deepEqual -o deep-eql.js", "lint": "eslint --ignore-path .gitignore .", "prepublish": "npm run build", "semantic-release": "semantic-release pre && npm publish && semantic-release post", "pretest": "npm run lint", "test": "npm run test:node && npm run test:browser", "test:browser": "karma start --singleRun=true", "test:node": "istanbul cover _mocha", "upload-coverage": "lcov-result-merger 'coverage/**/lcov.info' | coveralls; exit 0", "watch": "karma start --auto-watch --singleRun=false"}, "eslintConfig": {"extends": ["strict/es5"], "rules": {"complexity": 0, "no-underscore-dangle": 0, "no-use-before-define": 0, "spaced-comment": 0}}, "dependencies": {"type-detect": "^4.0.0"}, "devDependencies": {"@js-temporal/polyfill": "^0.4.1", "benchmark": "^2.1.0", "browserify": "^17.0.0", "browserify-istanbul": "^3.0.1", "coveralls": "^3.1.1", "eslint": "^7.32.0", "eslint-config-strict": "^14.0.1", "eslint-plugin-filenames": "^1.3.2", "istanbul": "^0.4.2", "karma": "^6.3.4", "karma-browserify": "^8.1.0", "karma-chrome-launcher": "^3.1.0", "karma-coverage": "^2.0.3", "karma-mocha": "^2.0.1", "karma-sauce-launcher": "^4.1.4", "kewlr": "^0.4.1", "lcov-result-merger": "^1.0.2", "lodash.isequal": "^4.4.0", "mocha": "^9.1.1", "simple-assert": "^1.0.0"}, "engines": {"node": ">=6"}}