{"version": 3, "sources": ["../../highlight.js/lib/languages/properties.js"], "sourcesContent": ["/*\nLanguage: .properties\nContributors: <PERSON><PERSON> <<EMAIL>>, <PERSON><PERSON> <<EMAIL>>\nWebsite: https://en.wikipedia.org/wiki/.properties\nCategory: common, config\n*/\n\nfunction properties(hljs) {\n\n  // whitespaces: space, tab, formfeed\n  var WS0 = '[ \\\\t\\\\f]*';\n  var WS1 = '[ \\\\t\\\\f]+';\n  // delimiter\n  var EQUAL_DELIM = WS0+'[:=]'+WS0;\n  var WS_DELIM = WS1;\n  var DELIM = '(' + EQUAL_DELIM + '|' + WS_DELIM + ')';\n  var KEY_ALPHANUM = '([^\\\\\\\\\\\\W:= \\\\t\\\\f\\\\n]|\\\\\\\\.)+';\n  var KEY_OTHER = '([^\\\\\\\\:= \\\\t\\\\f\\\\n]|\\\\\\\\.)+';\n\n  var DELIM_AND_VALUE = {\n          // skip DELIM\n          end: DELIM,\n          relevance: 0,\n          starts: {\n            // value: everything until end of line (again, taking into account backslashes)\n            className: 'string',\n            end: /$/,\n            relevance: 0,\n            contains: [\n              { begin: '\\\\\\\\\\\\\\\\'},\n              { begin: '\\\\\\\\\\\\n' }\n            ]\n          }\n        };\n\n  return {\n    name: '.properties',\n    case_insensitive: true,\n    illegal: /\\S/,\n    contains: [\n      hljs.COMMENT('^\\\\s*[!#]', '$'),\n      // key: everything until whitespace or = or : (taking into account backslashes)\n      // case of a \"normal\" key\n      {\n        returnBegin: true,\n        variants: [\n          { begin: KEY_ALPHANUM + EQUAL_DELIM, relevance: 1 },\n          { begin: KEY_ALPHANUM + WS_DELIM, relevance: 0 }\n        ],\n        contains: [\n          {\n            className: 'attr',\n            begin: KEY_ALPHANUM,\n            endsParent: true,\n            relevance: 0\n          }\n        ],\n        starts: DELIM_AND_VALUE\n      },\n      // case of key containing non-alphanumeric chars => relevance = 0\n      {\n        begin: KEY_OTHER + DELIM,\n        returnBegin: true,\n        relevance: 0,\n        contains: [\n          {\n            className: 'meta',\n            begin: KEY_OTHER,\n            endsParent: true,\n            relevance: 0\n          }\n        ],\n        starts: DELIM_AND_VALUE\n      },\n      // case of an empty key\n      {\n        className: 'attr',\n        relevance: 0,\n        begin: KEY_OTHER + WS0 + '$'\n      }\n    ]\n  };\n}\n\nmodule.exports = properties;\n"], "mappings": ";;;;;AAAA;AAAA;AAOA,aAAS,WAAW,MAAM;AAGxB,UAAI,MAAM;AACV,UAAI,MAAM;AAEV,UAAI,cAAc,MAAI,SAAO;AAC7B,UAAI,WAAW;AACf,UAAI,QAAQ,MAAM,cAAc,MAAM,WAAW;AACjD,UAAI,eAAe;AACnB,UAAI,YAAY;AAEhB,UAAI,kBAAkB;AAAA;AAAA,QAEd,KAAK;AAAA,QACL,WAAW;AAAA,QACX,QAAQ;AAAA;AAAA,UAEN,WAAW;AAAA,UACX,KAAK;AAAA,UACL,WAAW;AAAA,UACX,UAAU;AAAA,YACR,EAAE,OAAO,WAAU;AAAA,YACnB,EAAE,OAAO,UAAU;AAAA,UACrB;AAAA,QACF;AAAA,MACF;AAEN,aAAO;AAAA,QACL,MAAM;AAAA,QACN,kBAAkB;AAAA,QAClB,SAAS;AAAA,QACT,UAAU;AAAA,UACR,KAAK,QAAQ,aAAa,GAAG;AAAA;AAAA;AAAA,UAG7B;AAAA,YACE,aAAa;AAAA,YACb,UAAU;AAAA,cACR,EAAE,OAAO,eAAe,aAAa,WAAW,EAAE;AAAA,cAClD,EAAE,OAAO,eAAe,UAAU,WAAW,EAAE;AAAA,YACjD;AAAA,YACA,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,YAAY;AAAA,gBACZ,WAAW;AAAA,cACb;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA;AAAA,UAEA;AAAA,YACE,OAAO,YAAY;AAAA,YACnB,aAAa;AAAA,YACb,WAAW;AAAA,YACX,UAAU;AAAA,cACR;AAAA,gBACE,WAAW;AAAA,gBACX,OAAO;AAAA,gBACP,YAAY;AAAA,gBACZ,WAAW;AAAA,cACb;AAAA,YACF;AAAA,YACA,QAAQ;AAAA,UACV;AAAA;AAAA,UAEA;AAAA,YACE,WAAW;AAAA,YACX,WAAW;AAAA,YACX,OAAO,YAAY,MAAM;AAAA,UAC3B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;", "names": []}