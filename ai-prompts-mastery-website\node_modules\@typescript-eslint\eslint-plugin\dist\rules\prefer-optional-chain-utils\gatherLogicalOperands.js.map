{"version": 3, "file": "gatherLogicalOperands.js", "sourceRoot": "", "sources": ["../../../src/rules/prefer-optional-chain-utils/gatherLogicalOperands.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAIA,oDAA0D;AAC1D,+CAMsB;AACtB,+CAAiC;AAEjC,qCAA2C;AA6C3C,MAAM,aAAa,GAAG,EAAE,CAAC,SAAS,CAAC,IAAI,GAAG,EAAE,CAAC,SAAS,CAAC,SAAS,CAAC;AACjE,SAAS,4BAA4B,CACnC,IAAmB,EACnB,QAAgD,EAChD,SAA2B,EAC3B,cAAiD,EACjD,OAAmC;IAEnC,MAAM,IAAI,GAAG,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACpD,MAAM,KAAK,GAAG,IAAA,6BAAc,EAAC,IAAI,CAAC,CAAC;IAEnC,MAAM,qBAAqB,GACzB,CAAC,QAAQ,KAAK,IAAI,IAAI,SAAS,KAAK,OAAO,CAAC;QAC5C,CAAC,QAAQ,KAAK,IAAI,IAAI,SAAS,KAAK,MAAM,CAAC,CAAC;IAC9C,IAAI,qBAAqB,EAAE,CAAC;QAC1B;;;;;;;;;;UAUE;QACF,IACE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,mCAAoB,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,aAAa,KAAK,OAAO,CAAC;YACvE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,kCAAmB,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC;YACzD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,kCAAmB,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC;YACxD,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,kCAAmB,EAAC,CAAC,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,WAAW,KAAK,GAAG,CAAC,EACtE,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,oBAAa,EAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,YAAY,GAAG,aAAa,GAAG,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC;IACvD,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;QAC9B,YAAY,IAAI,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC;IACnC,CAAC;IACD,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;QAClC,YAAY,IAAI,EAAE,CAAC,SAAS,CAAC,OAAO,CAAC;IACvC,CAAC;IACD,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;QACjC,YAAY,IAAI,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;IAC1C,CAAC;IACD,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;QACjC,YAAY,IAAI,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;IAC1C,CAAC;IACD,IAAI,OAAO,CAAC,YAAY,KAAK,IAAI,EAAE,CAAC;QAClC,YAAY,IAAI,EAAE,CAAC,SAAS,CAAC,WAAW,CAAC;IAC3C,CAAC;IACD,IAAI,OAAO,CAAC,WAAW,KAAK,IAAI,EAAE,CAAC;QACjC,YAAY,IAAI,EAAE,CAAC,SAAS,CAAC,UAAU,CAAC;IAC1C,CAAC;IACD,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,IAAA,oBAAa,EAAC,CAAC,EAAE,YAAY,CAAC,CAAC,CAAC;AAC1D,CAAC;AAED,SAAgB,qBAAqB,CACnC,IAAgC,EAChC,cAAiD,EACjD,OAAmC;IAKnC,MAAM,MAAM,GAAc,EAAE,CAAC;IAC7B,MAAM,EAAE,QAAQ,EAAE,iBAAiB,EAAE,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAErE,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;QAC/B,QAAQ,OAAO,CAAC,IAAI,EAAE,CAAC;YACrB,KAAK,sBAAc,CAAC,gBAAgB,CAAC,CAAC,CAAC;gBACrC,4CAA4C;gBAE5C,MAAM,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE;oBAC1D,sEAAsE;oBACtE,MAAM,kBAAkB,GAAG,sBAAsB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBACjE,IAAI,kBAAkB,EAAE,CAAC;wBACvB,OAAO;4BACL,kBAAkB,EAAE,OAAO,CAAC,IAAI;4BAChC,aAAa,EAAE,kBAAkB;4BACjC,MAAM,EAAE,KAAK;yBACd,CAAC;oBACJ,CAAC;oBACD,OAAO;wBACL,kBAAkB,EAAE,OAAO,CAAC,KAAK;wBACjC,aAAa,EAAE,sBAAsB,CAAC,OAAO,CAAC,IAAI,CAAC;wBACnD,MAAM,EAAE,IAAI;qBACb,CAAC;gBACJ,CAAC,CAAC,EAAE,CAAC;gBAEL,IAAI,aAAa,8EAA+C,EAAE,CAAC;oBACjE,IACE,kBAAkB,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;wBAC1D,kBAAkB,CAAC,QAAQ,KAAK,QAAQ,EACxC,CAAC;wBACD,2BAA2B;wBAC3B,MAAM,CAAC,IAAI,CAAC;4BACV,IAAI,qCAAuB;4BAC3B,YAAY,EAAE,kBAAkB,CAAC,QAAQ;4BACzC,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;gCAC9C,CAAC;gCACD,CAAC,wEAA2C;4BAC9C,MAAM;4BACN,IAAI,EAAE,OAAO;yBACd,CAAC,CAAC;wBACH,SAAS;oBACX,CAAC;oBAED,oBAAoB;oBACpB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;oBAC/C,SAAS;gBACX,CAAC;gBAED,QAAQ,OAAO,CAAC,QAAQ,EAAE,CAAC;oBACzB,KAAK,IAAI,CAAC;oBACV,KAAK,IAAI;wBACP,IACE,aAAa,0CAA6B;4BAC1C,aAAa,oDAAkC,EAC/C,CAAC;4BACD,4BAA4B;4BAC5B,MAAM,CAAC,IAAI,CAAC;gCACV,IAAI,qCAAuB;gCAC3B,YAAY,EAAE,kBAAkB;gCAChC,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;oCAC9C,CAAC;oCACD,CAAC,wEAA2C;gCAC9C,MAAM;gCACN,IAAI,EAAE,OAAO;6BACd,CAAC,CAAC;4BACH,SAAS;wBACX,CAAC;wBACD,oBAAoB;wBACpB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;wBAC/C,SAAS;oBAEX,KAAK,KAAK,CAAC;oBACX,KAAK,KAAK,CAAC,CAAC,CAAC;wBACX,MAAM,YAAY,GAAG,kBAAkB,CAAC;wBACxC,QAAQ,aAAa,EAAE,CAAC;4BACtB;gCACE,MAAM,CAAC,IAAI,CAAC;oCACV,IAAI,qCAAuB;oCAC3B,YAAY;oCACZ,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;wCAC9C,CAAC;wCACD,CAAC,8DAAsC;oCACzC,MAAM;oCACN,IAAI,EAAE,OAAO;iCACd,CAAC,CAAC;gCACH,SAAS;4BAEX;gCACE,MAAM,CAAC,IAAI,CAAC;oCACV,IAAI,qCAAuB;oCAC3B,YAAY;oCACZ,cAAc,EAAE,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,GAAG,CAAC;wCAC9C,CAAC;wCACD,CAAC,wEAA2C;oCAC9C,MAAM;oCACN,IAAI,EAAE,OAAO;iCACd,CAAC,CAAC;gCACH,SAAS;4BAEX;gCACE,qBAAqB;gCACrB,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;gCAC/C,SAAS;wBACb,CAAC;oBACH,CAAC;gBACH,CAAC;gBAED,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;gBAC/C,SAAS;YACX,CAAC;YAED,KAAK,sBAAc,CAAC,eAAe;gBACjC,IACE,OAAO,CAAC,QAAQ,KAAK,GAAG;oBACxB,4BAA4B,CAC1B,OAAO,CAAC,QAAQ,EAChB,IAAI,CAAC,QAAQ,EACb,OAAO,EACP,cAAc,EACd,OAAO,CACR,EACD,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,qCAAuB;wBAC3B,YAAY,EAAE,OAAO,CAAC,QAAQ;wBAC9B,cAAc,qDAAkC;wBAChD,MAAM,EAAE,KAAK;wBACb,IAAI,EAAE,OAAO;qBACd,CAAC,CAAC;oBACH,SAAS;gBACX,CAAC;gBACD,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;gBAC/C,SAAS;YAEX,KAAK,sBAAc,CAAC,iBAAiB;gBACnC,uDAAuD;gBACvD,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;gBAC/C,SAAS;YAEX;gBACE,IACE,4BAA4B,CAC1B,OAAO,EACP,IAAI,CAAC,QAAQ,EACb,MAAM,EACN,cAAc,EACd,OAAO,CACR,EACD,CAAC;oBACD,MAAM,CAAC,IAAI,CAAC;wBACV,IAAI,qCAAuB;wBAC3B,YAAY,EAAE,OAAO;wBACrB,cAAc,+CAA+B;wBAC7C,MAAM,EAAE,KAAK;wBACb,IAAI,EAAE,OAAO;qBACd,CAAC,CAAC;gBACL,CAAC;qBAAM,CAAC;oBACN,MAAM,CAAC,IAAI,CAAC,EAAE,IAAI,yCAAyB,EAAE,CAAC,CAAC;gBACjD,CAAC;gBACD,SAAS;QACb,CAAC;IACH,CAAC;IAED,OAAO;QACL,QAAQ,EAAE,MAAM;QAChB,iBAAiB;KAClB,CAAC;IAEF;;;;;;;;;;;;;;;;;;;;MAoBE;IACF,SAAS,sBAAsB,CAAC,IAAgC;QAI9D,MAAM,QAAQ,GAA0B,EAAE,CAAC;QAC3C,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAA6B,CAAC,IAAI,CAAC,CAAC,CAAC;QAEtE,MAAM,KAAK,GAA0B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QAC7D,IAAI,OAAwC,CAAC;QAC7C,OAAO,CAAC,OAAO,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC;YAC/B,IACE,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;gBACjD,OAAO,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAClC,CAAC;gBACD,iBAAiB,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;gBAC/B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBAC1B,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC3B,CAAC;iBAAM,CAAC;gBACN,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,OAAO;YACL,QAAQ;YACR,iBAAiB;SAClB,CAAC;IACJ,CAAC;IAED,SAAS,sBAAsB,CAC7B,IAAmB;QAEnB,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;YAClB,KAAK,sBAAc,CAAC,OAAO;gBACzB,+EAA+E;gBAC/E,IAAI,IAAI,CAAC,KAAK,KAAK,IAAI,IAAI,IAAI,CAAC,GAAG,KAAK,MAAM,EAAE,CAAC;oBAC/C,6CAAgC;gBAClC,CAAC;gBACD,IAAI,IAAI,CAAC,KAAK,KAAK,WAAW,EAAE,CAAC;oBAC/B,iFAAkD;gBACpD,CAAC;gBACD,OAAO,IAAI,CAAC;YAEd,KAAK,sBAAc,CAAC,UAAU;gBAC5B,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;oBAC9B,uDAAqC;gBACvC,CAAC;gBACD,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,OAAO,IAAI,CAAC;IACd,CAAC;AACH,CAAC;AAxPD,sDAwPC"}