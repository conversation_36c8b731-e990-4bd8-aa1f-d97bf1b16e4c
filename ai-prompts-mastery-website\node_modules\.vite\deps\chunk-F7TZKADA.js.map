{"version": 3, "sources": ["../../refractor/lang/haskell.js"], "sourcesContent": ["'use strict'\n\nmodule.exports = haskell\nhaskell.displayName = 'haskell'\nhaskell.aliases = ['hs']\nfunction haskell(Prism) {\n  Prism.languages.haskell = {\n    comment: {\n      pattern:\n        /(^|[^-!#$%*+=?&@|~.:<>^\\\\\\/])(?:--(?:(?=.)[^-!#$%*+=?&@|~.:<>^\\\\\\/].*|$)|\\{-[\\s\\S]*?-\\})/m,\n      lookbehind: true\n    },\n    char: {\n      pattern:\n        /'(?:[^\\\\']|\\\\(?:[abfnrtv\\\\\"'&]|\\^[A-Z@[\\]^_]|ACK|BEL|BS|CAN|CR|DC1|DC2|DC3|DC4|DEL|DLE|EM|ENQ|EOT|ESC|ETB|ETX|FF|FS|GS|HT|LF|NAK|NUL|RS|SI|SO|SOH|SP|STX|SUB|SYN|US|VT|\\d+|o[0-7]+|x[0-9a-fA-F]+))'/,\n      alias: 'string'\n    },\n    string: {\n      pattern: /\"(?:[^\\\\\"]|\\\\(?:\\S|\\s+\\\\))*\"/,\n      greedy: true\n    },\n    keyword:\n      /\\b(?:case|class|data|deriving|do|else|if|in|infixl|infixr|instance|let|module|newtype|of|primitive|then|type|where)\\b/,\n    'import-statement': {\n      // The imported or hidden names are not included in this import\n      // statement. This is because we want to highlight those exactly like\n      // we do for the names in the program.\n      pattern:\n        /(^[\\t ]*)import\\s+(?:qualified\\s+)?(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*(?:\\s+as\\s+(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*)?(?:\\s+hiding\\b)?/m,\n      lookbehind: true,\n      inside: {\n        keyword: /\\b(?:as|hiding|import|qualified)\\b/,\n        punctuation: /\\./\n      }\n    },\n    // These are builtin variables only. Constructors are highlighted later as a constant.\n    builtin:\n      /\\b(?:abs|acos|acosh|all|and|any|appendFile|approxRational|asTypeOf|asin|asinh|atan|atan2|atanh|basicIORun|break|catch|ceiling|chr|compare|concat|concatMap|const|cos|cosh|curry|cycle|decodeFloat|denominator|digitToInt|div|divMod|drop|dropWhile|either|elem|encodeFloat|enumFrom|enumFromThen|enumFromThenTo|enumFromTo|error|even|exp|exponent|fail|filter|flip|floatDigits|floatRadix|floatRange|floor|fmap|foldl|foldl1|foldr|foldr1|fromDouble|fromEnum|fromInt|fromInteger|fromIntegral|fromRational|fst|gcd|getChar|getContents|getLine|group|head|id|inRange|index|init|intToDigit|interact|ioError|isAlpha|isAlphaNum|isAscii|isControl|isDenormalized|isDigit|isHexDigit|isIEEE|isInfinite|isLower|isNaN|isNegativeZero|isOctDigit|isPrint|isSpace|isUpper|iterate|last|lcm|length|lex|lexDigits|lexLitChar|lines|log|logBase|lookup|map|mapM|mapM_|max|maxBound|maximum|maybe|min|minBound|minimum|mod|negate|not|notElem|null|numerator|odd|or|ord|otherwise|pack|pi|pred|primExitWith|print|product|properFraction|putChar|putStr|putStrLn|quot|quotRem|range|rangeSize|read|readDec|readFile|readFloat|readHex|readIO|readInt|readList|readLitChar|readLn|readOct|readParen|readSigned|reads|readsPrec|realToFrac|recip|rem|repeat|replicate|return|reverse|round|scaleFloat|scanl|scanl1|scanr|scanr1|seq|sequence|sequence_|show|showChar|showInt|showList|showLitChar|showParen|showSigned|showString|shows|showsPrec|significand|signum|sin|sinh|snd|sort|span|splitAt|sqrt|subtract|succ|sum|tail|take|takeWhile|tan|tanh|threadToIOResult|toEnum|toInt|toInteger|toLower|toRational|toUpper|truncate|uncurry|undefined|unlines|until|unwords|unzip|unzip3|userError|words|writeFile|zip|zip3|zipWith|zipWith3)\\b/,\n    // decimal integers and floating point numbers | octal integers | hexadecimal integers\n    number: /\\b(?:\\d+(?:\\.\\d+)?(?:e[+-]?\\d+)?|0o[0-7]+|0x[0-9a-f]+)\\b/i,\n    operator: [\n      {\n        // infix operator\n        pattern: /`(?:[A-Z][\\w']*\\.)*[_a-z][\\w']*`/,\n        greedy: true\n      },\n      {\n        // function composition\n        pattern: /(\\s)\\.(?=\\s)/,\n        lookbehind: true\n      }, // Most of this is needed because of the meaning of a single '.'.\n      // If it stands alone freely, it is the function composition.\n      // It may also be a separator between a module name and an identifier => no\n      // operator. If it comes together with other special characters it is an\n      // operator too.\n      //\n      // This regex means: /[-!#$%*+=?&@|~.:<>^\\\\\\/]+/ without /\\./.\n      /[-!#$%*+=?&@|~:<>^\\\\\\/][-!#$%*+=?&@|~.:<>^\\\\\\/]*|\\.[-!#$%*+=?&@|~.:<>^\\\\\\/]+/\n    ],\n    // In Haskell, nearly everything is a variable, do not highlight these.\n    hvariable: {\n      pattern: /\\b(?:[A-Z][\\w']*\\.)*[_a-z][\\w']*/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    constant: {\n      pattern: /\\b(?:[A-Z][\\w']*\\.)*[A-Z][\\w']*/,\n      inside: {\n        punctuation: /\\./\n      }\n    },\n    punctuation: /[{}[\\];(),.:]/\n  }\n  Prism.languages.hs = Prism.languages.haskell\n}\n"], "mappings": ";;;;;AAAA;AAAA;AAEA,WAAO,UAAU;AACjB,YAAQ,cAAc;AACtB,YAAQ,UAAU,CAAC,IAAI;AACvB,aAAS,QAAQ,OAAO;AACtB,YAAM,UAAU,UAAU;AAAA,QACxB,SAAS;AAAA,UACP,SACE;AAAA,UACF,YAAY;AAAA,QACd;AAAA,QACA,MAAM;AAAA,UACJ,SACE;AAAA,UACF,OAAO;AAAA,QACT;AAAA,QACA,QAAQ;AAAA,UACN,SAAS;AAAA,UACT,QAAQ;AAAA,QACV;AAAA,QACA,SACE;AAAA,QACF,oBAAoB;AAAA;AAAA;AAAA;AAAA,UAIlB,SACE;AAAA,UACF,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,SAAS;AAAA,YACT,aAAa;AAAA,UACf;AAAA,QACF;AAAA;AAAA,QAEA,SACE;AAAA;AAAA,QAEF,QAAQ;AAAA,QACR,UAAU;AAAA,UACR;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,QAAQ;AAAA,UACV;AAAA,UACA;AAAA;AAAA,YAEE,SAAS;AAAA,YACT,YAAY;AAAA,UACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAOA;AAAA,QACF;AAAA;AAAA,QAEA,WAAW;AAAA,UACT,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,UAAU;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,QACA,aAAa;AAAA,MACf;AACA,YAAM,UAAU,KAAK,MAAM,UAAU;AAAA,IACvC;AAAA;AAAA;", "names": []}