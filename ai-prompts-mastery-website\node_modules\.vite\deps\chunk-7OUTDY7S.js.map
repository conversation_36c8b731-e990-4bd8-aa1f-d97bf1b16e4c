{"version": 3, "sources": ["../../refractor/lang/idris.js"], "sourcesContent": ["'use strict'\nvar refractorHaskell = require('./haskell.js')\nmodule.exports = idris\nidris.displayName = 'idris'\nidris.aliases = ['idr']\nfunction idris(Prism) {\n  Prism.register(refractorHaskell)\n  Prism.languages.idris = Prism.languages.extend('haskell', {\n    comment: {\n      pattern: /(?:(?:--|\\|\\|\\|).*$|\\{-[\\s\\S]*?-\\})/m\n    },\n    keyword:\n      /\\b(?:Type|case|class|codata|constructor|corecord|data|do|dsl|else|export|if|implementation|implicit|import|impossible|in|infix|infixl|infixr|instance|interface|let|module|mutual|namespace|of|parameters|partial|postulate|private|proof|public|quoteGoal|record|rewrite|syntax|then|total|using|where|with)\\b/,\n    builtin: undefined\n  })\n  Prism.languages.insertBefore('idris', 'keyword', {\n    'import-statement': {\n      pattern: /(^\\s*import\\s+)(?:[A-Z][\\w']*)(?:\\.[A-Z][\\w']*)*/m,\n      lookbehind: true,\n      inside: {\n        punctuation: /\\./\n      }\n    }\n  })\n  Prism.languages.idr = Prism.languages.idris\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,mBAAmB;AACvB,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC,KAAK;AACtB,aAAS,MAAM,OAAO;AACpB,YAAM,SAAS,gBAAgB;AAC/B,YAAM,UAAU,QAAQ,MAAM,UAAU,OAAO,WAAW;AAAA,QACxD,SAAS;AAAA,UACP,SAAS;AAAA,QACX;AAAA,QACA,SACE;AAAA,QACF,SAAS;AAAA,MACX,CAAC;AACD,YAAM,UAAU,aAAa,SAAS,WAAW;AAAA,QAC/C,oBAAoB;AAAA,UAClB,SAAS;AAAA,UACT,YAAY;AAAA,UACZ,QAAQ;AAAA,YACN,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AACD,YAAM,UAAU,MAAM,MAAM,UAAU;AAAA,IACxC;AAAA;AAAA;", "names": []}