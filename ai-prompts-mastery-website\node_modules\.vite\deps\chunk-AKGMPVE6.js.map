{"version": 3, "sources": ["../../refractor/lang/glsl.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = glsl\nglsl.displayName = 'glsl'\nglsl.aliases = []\nfunction glsl(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.glsl = Prism.languages.extend('c', {\n    keyword:\n      /\\b(?:active|asm|atomic_uint|attribute|[ibdu]?vec[234]|bool|break|buffer|case|cast|centroid|class|coherent|common|const|continue|d?mat[234](?:x[234])?|default|discard|do|double|else|enum|extern|external|false|filter|fixed|flat|float|for|fvec[234]|goto|half|highp|hvec[234]|[iu]?sampler2DMS(?:Array)?|[iu]?sampler2DRect|[iu]?samplerBuffer|[iu]?samplerCube|[iu]?samplerCubeArray|[iu]?sampler[123]D|[iu]?sampler[12]DArray|[iu]?image2DMS(?:Array)?|[iu]?image2DRect|[iu]?imageBuffer|[iu]?imageCube|[iu]?imageCubeArray|[iu]?image[123]D|[iu]?image[12]DArray|if|in|inline|inout|input|int|interface|invariant|layout|long|lowp|mediump|namespace|noinline|noperspective|out|output|partition|patch|precise|precision|public|readonly|resource|restrict|return|sample|sampler[12]DArrayShadow|sampler[12]DShadow|sampler2DRectShadow|sampler3DRect|samplerCubeArrayShadow|samplerCubeShadow|shared|short|sizeof|smooth|static|struct|subroutine|superp|switch|template|this|true|typedef|uint|uniform|union|unsigned|using|varying|void|volatile|while|writeonly)\\b/\n  })\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,aAAa;AACjB,WAAO,UAAU;AACjB,SAAK,cAAc;AACnB,SAAK,UAAU,CAAC;AAChB,aAAS,KAAK,OAAO;AACnB,YAAM,SAAS,UAAU;AACzB,YAAM,UAAU,OAAO,MAAM,UAAU,OAAO,KAAK;AAAA,QACjD,SACE;AAAA,MACJ,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}