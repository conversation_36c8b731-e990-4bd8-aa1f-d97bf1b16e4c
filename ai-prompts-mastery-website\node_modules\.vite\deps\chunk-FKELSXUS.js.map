{"version": 3, "sources": ["../../refractor/lang/bison.js"], "sourcesContent": ["'use strict'\nvar refractorC = require('./c.js')\nmodule.exports = bison\nbison.displayName = 'bison'\nbison.aliases = []\nfunction bison(Prism) {\n  Prism.register(refractorC)\n  Prism.languages.bison = Prism.languages.extend('c', {})\n  Prism.languages.insertBefore('bison', 'comment', {\n    bison: {\n      // This should match all the beginning of the file\n      // including the prologue(s), the bison declarations and\n      // the grammar rules.\n      pattern: /^(?:[^%]|%(?!%))*%%[\\s\\S]*?%%/,\n      inside: {\n        c: {\n          // Allow for one level of nested braces\n          pattern: /%\\{[\\s\\S]*?%\\}|\\{(?:\\{[^}]*\\}|[^{}])*\\}/,\n          inside: {\n            delimiter: {\n              pattern: /^%?\\{|%?\\}$/,\n              alias: 'punctuation'\n            },\n            'bison-variable': {\n              pattern: /[$@](?:<[^\\s>]+>)?[\\w$]+/,\n              alias: 'variable',\n              inside: {\n                punctuation: /<|>/\n              }\n            },\n            rest: Prism.languages.c\n          }\n        },\n        comment: Prism.languages.c.comment,\n        string: Prism.languages.c.string,\n        property: /\\S+(?=:)/,\n        keyword: /%\\w+/,\n        number: {\n          pattern: /(^|[^@])\\b(?:0x[\\da-f]+|\\d+)/i,\n          lookbehind: true\n        },\n        punctuation: /%[%?]|[|:;\\[\\]<>]/\n      }\n    }\n  })\n}\n"], "mappings": ";;;;;;;;AAAA;AAAA;AACA,QAAI,aAAa;AACjB,WAAO,UAAU;AACjB,UAAM,cAAc;AACpB,UAAM,UAAU,CAAC;AACjB,aAAS,MAAM,OAAO;AACpB,YAAM,SAAS,UAAU;AACzB,YAAM,UAAU,QAAQ,MAAM,UAAU,OAAO,KAAK,CAAC,CAAC;AACtD,YAAM,UAAU,aAAa,SAAS,WAAW;AAAA,QAC/C,OAAO;AAAA;AAAA;AAAA;AAAA,UAIL,SAAS;AAAA,UACT,QAAQ;AAAA,YACN,GAAG;AAAA;AAAA,cAED,SAAS;AAAA,cACT,QAAQ;AAAA,gBACN,WAAW;AAAA,kBACT,SAAS;AAAA,kBACT,OAAO;AAAA,gBACT;AAAA,gBACA,kBAAkB;AAAA,kBAChB,SAAS;AAAA,kBACT,OAAO;AAAA,kBACP,QAAQ;AAAA,oBACN,aAAa;AAAA,kBACf;AAAA,gBACF;AAAA,gBACA,MAAM,MAAM,UAAU;AAAA,cACxB;AAAA,YACF;AAAA,YACA,SAAS,MAAM,UAAU,EAAE;AAAA,YAC3B,QAAQ,MAAM,UAAU,EAAE;AAAA,YAC1B,UAAU;AAAA,YACV,SAAS;AAAA,YACT,QAAQ;AAAA,cACN,SAAS;AAAA,cACT,YAAY;AAAA,YACd;AAAA,YACA,aAAa;AAAA,UACf;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA;AAAA;", "names": []}